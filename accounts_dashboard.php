<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>حسابات - Bassam Media</title>

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f8fafc;
            color: #1a202c;
            line-height: 1.6;
        }

        /* Header Styles */
        .header {
            background: white;
            border-bottom: 1px solid #e5e7eb;
            padding: 20px 0;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
        }

        .header-container {
            max-width: 1400px;
            margin: 0 auto;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 24px;
        }

        .logo-section {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .logo-section img {
            width: 60px;
            height: 60px;
            object-fit: contain;
        }

        .logo-text {
            display: flex;
            flex-direction: column;
        }

        .logo-text span {
            color: #6b7280;
            font-size: 12px;
            font-weight: 500;
            line-height: 1;
        }

        /* Main Content */
        .main-content {
            margin-top: 120px;
            padding: 40px 20px;
            max-width: 1000px;
            margin-left: auto;
            margin-right: auto;
        }

        /* Cards Grid */
        .cards-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 20px;
            margin-top: 40px;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }

        /* Last card spans full width */
        .account-card:last-child {
            grid-column: 1 / -1;
            max-width: 280px;
            margin: 0 auto;
        }

        .account-card {
            background: #e0e4ff;
            border-radius: 25px;
            padding: 40px 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            border: none;
            text-decoration: none;
            color: #4f46e5;
            min-height: 120px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .account-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(79, 70, 229, 0.15);
            text-decoration: none;
            color: #4f46e5;
            background: #d1d9ff;
        }

        .card-title {
            font-size: 18px;
            font-weight: 600;
            color: #4f46e5;
            margin: 0;
        }

        /* All cards have the same style */

        /* Responsive Design */
        @media (max-width: 768px) {
            .cards-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .header-container {
                padding: 12px;
            }

            .main-content {
                margin-top: 100px;
                padding: 20px 10px;
            }

            .account-card {
                padding: 25px;
            }

            .card-title {
                font-size: 18px;
            }

            .card-icon {
                font-size: 28px;
            }
        }

        /* Animation */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .account-card {
            animation: fadeInUp 0.6s ease forwards;
        }

        .account-card:nth-child(1) { animation-delay: 0.1s; }
        .account-card:nth-child(2) { animation-delay: 0.2s; }
        .account-card:nth-child(3) { animation-delay: 0.3s; }
        .account-card:nth-child(4) { animation-delay: 0.4s; }
        .account-card:nth-child(5) { animation-delay: 0.5s; }
        .account-card:nth-child(6) { animation-delay: 0.6s; }
        .account-card:nth-child(7) { animation-delay: 0.7s; }
    </style>
</head>
<body>
    <!-- Header Section -->
    <div class="header">
        <div class="header-container">
            <!-- Logo Section -->
            <div class="logo-section">
                <img src="assets/images/logo.png" alt="Bassam Media Logo">
                <div class="logo-text">
                    <span>BASSAM MEDIA</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <div class="cards-grid">
            <!-- Row 1 -->
            <!-- Visa History -->
            <a href="visa_purchases.php" class="account-card">
                <div class="card-title">Visa History</div>
            </a>

            <!-- Credit Cards -->
            <a href="credit_cards_table.php" class="account-card">
                <div class="card-title">كريديت كارد</div>
            </a>

            <!-- Row 2 -->
            <!-- Bank Balances -->
            <a href="bank_balances.php" class="account-card">
                <div class="card-title">ارصدة البنوك</div>
            </a>

            <!-- Vodafone Cash -->
            <div class="account-card" onclick="showVodafoneModal()">
                <div class="card-title">فودافون كاش</div>
            </div>

            <!-- Row 3 -->
            <!-- Cash -->
            <div class="account-card" onclick="showCashModal()">
                <div class="card-title">كاش</div>
            </div>

            <!-- Fawry -->
            <div class="account-card" onclick="showFawryModal()">
                <div class="card-title">فوري</div>
            </div>

            <!-- Row 4 -->
            <!-- Accounts Total -->
            <a href="accounts_total.php" class="account-card">
                <div class="card-title">اجمالي الحسابات</div>
            </a>
        </div>
    </div>

    <!-- Fawry Report Modal -->
    <div class="modal-overlay" id="fawryModal" style="display: none;">
        <div class="fawry-modal">
            <div class="modal-header">
                <button class="modal-close" onclick="closeFawryModal()">&times;</button>
                <h5 class="modal-title">تقرير فوري</h5>
            </div>
            <div class="modal-body">
                <div class="report-container">
                    <div class="report-row">
                        <span class="report-label">رصيد اساسي</span>
                        <span class="report-value editable"
                              contenteditable="true"
                              data-field="basic_balance"
                              id="basicBalance">14,034</span>
                    </div>

                    <div class="report-row">
                        <span class="report-label">كاش اوت</span>
                        <span class="report-value editable"
                              contenteditable="true"
                              data-field="cash_out"
                              id="cashOut">0</span>
                    </div>

                    <div class="report-row total-row">
                        <span class="report-label">اجمالي رصيد فوري</span>
                        <span class="report-value" id="totalBalance">14,034</span>
                    </div>
                </div>

                <button class="save-btn" onclick="saveFawryData()">حفظ التغييرات</button>
                <div class="success-message" id="successMessage">تم حفظ البيانات بنجاح!</div>
            </div>
        </div>
    </div>

    <!-- Cash Report Modal -->
    <div class="modal-overlay" id="cashModal" style="display: none;">
        <div class="cash-modal">
            <div class="modal-header">
                <button class="modal-close" onclick="closeCashModal()">&times;</button>
                <h5 class="modal-title">تقرير كاش</h5>
            </div>
            <div class="modal-body">
                <div class="cash-container">
                    <div id="cashAccountsList">
                        <!-- سيتم تحميل الحسابات هنا -->
                    </div>

                    <div class="total-row">
                        <span class="report-label">اجمالي رصيد كاش</span>
                        <span class="report-value total-value" id="cashTotalBalance">0</span>
                    </div>

                    <div class="add-account-section">
                        <button class="add-account-btn" onclick="showAddAccountForm()">+ إضافة حساب كاش جديد</button>

                        <div class="add-account-form" id="addAccountForm" style="display: none;">
                            <input type="text" id="newAccountName" placeholder="اسم الحساب الجديد" class="form-input">
                            <input type="number" id="newAccountBalance" placeholder="الرصيد الابتدائي" class="form-input" step="0.01">
                            <div class="form-buttons">
                                <button class="save-account-btn" onclick="addNewAccount()">إضافة</button>
                                <button class="cancel-btn" onclick="hideAddAccountForm()">إلغاء</button>
                            </div>
                        </div>
                    </div>
                </div>

                <button class="save-btn" onclick="saveCashData()">حفظ التغييرات</button>
                <div class="success-message" id="cashSuccessMessage">تم حفظ البيانات بنجاح!</div>
            </div>
        </div>
    </div>

    <!-- Vodafone Cash Modal -->
    <div class="modal-overlay" id="vodafoneModal" style="display: none;">
        <div class="vodafone-modal">
            <div class="modal-header">
                <button class="modal-close" onclick="closeVodafoneModal()">&times;</button>
                <h5 class="modal-title">تقرير فودافون كاش</h5>
            </div>
            <div class="modal-body">
                <div class="vodafone-container">
                    <div class="vodafone-header">
                        <div class="header-item">الرقم</div>
                        <div class="header-item">الرصيد</div>
                    </div>

                    <div id="vodafoneAccountsList">
                        <!-- سيتم تحميل الحسابات هنا -->
                    </div>

                    <div class="vodafone-total">
                        <div class="add-vodafone-btn" onclick="showAddVodafoneForm()">
                            <span class="plus-icon">+</span>
                        </div>
                        <div class="total-section">
                            <span class="total-value" id="vodafoneTotalBalance">43,405</span>
                            <span class="total-label">اجمالي رصيد فودافون</span>
                        </div>
                    </div>

                    <div class="add-vodafone-form" id="addVodafoneForm" style="display: none;">
                        <input type="text" id="newVodafoneNumber" placeholder="رقم الهاتف" class="form-input">
                        <input type="number" id="newVodafoneBalance" placeholder="الرصيد الابتدائي" class="form-input" step="0.01">
                        <div class="form-buttons">
                            <button class="save-account-btn" onclick="addNewVodafoneAccount()">إضافة</button>
                            <button class="cancel-btn" onclick="hideAddVodafoneForm()">إلغاء</button>
                        </div>
                    </div>
                </div>

                <button class="save-btn" onclick="saveVodafoneData()">حفظ التغييرات</button>
                <div class="success-message" id="vodafoneSuccessMessage">تم حفظ البيانات بنجاح!</div>
            </div>
        </div>
    </div>

    <style>
        /* Modal Overlay */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* Modal Content */
        .fawry-modal, .cash-modal, .vodafone-modal {
            background-color: white;
            border-radius: 15px;
            padding: 0;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            width: 400px;
            max-width: 90%;
            height: 500px;
            max-height: 80vh;
            position: relative;
            display: flex;
            flex-direction: column;
        }

        /* Modal Header */
        .modal-header {
            background-color: #e8ecf4;
            padding: 20px;
            border-radius: 15px 15px 0 0;
            text-align: center;
            position: relative;
        }

        .modal-title {
            color: #4a69bd;
            font-size: 18px;
            font-weight: 600;
            margin: 0;
        }

        .modal-close {
            position: absolute;
            top: 15px;
            left: 20px;
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            color: #666;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-close:hover {
            color: #333;
        }

        /* Modal Body */
        .modal-body {
            padding: 20px 30px 30px 30px;
            flex: 1;
            overflow-y: auto;
            overflow-x: hidden;
        }

        /* Report Container */
        .report-container {
            border: 2px solid #ffd700;
            border-radius: 10px;
            padding: 20px;
            background-color: #fffef7;
        }

        .report-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding: 10px 0;
        }

        .report-row:not(:last-child) {
            border-bottom: 1px solid #e0e0e0;
        }

        .report-label {
            font-size: 16px;
            color: #333;
            font-weight: 500;
        }

        .report-value {
            font-size: 16px;
            color: #4a69bd;
            font-weight: 600;
            cursor: pointer;
            padding: 5px 10px;
            border-radius: 5px;
            transition: background-color 0.2s;
        }

        .report-value:hover {
            background-color: #f0f0f0;
        }

        .report-value.editable {
            border: 1px solid transparent;
        }

        .report-value.editing {
            background-color: white;
            border: 1px solid #4a69bd;
            outline: none;
        }

        .total-row {
            border-top: 2px solid #ffd700;
            margin-top: 10px;
            padding-top: 15px;
            font-weight: 700;
            font-size: 18px;
        }

        .total-row .report-value {
            color: #2c5aa0;
        }

        /* Save Button */
        .save-btn {
            background-color: #4a69bd;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            font-size: 14px;
            cursor: pointer;
            margin-top: 15px;
            width: 100%;
            transition: background-color 0.2s;
            flex-shrink: 0;
        }

        .save-btn:hover {
            background-color: #3d5aa0;
        }

        .save-btn:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }

        /* Success Message */
        .success-message {
            background-color: #d4edda;
            color: #155724;
            padding: 10px;
            border-radius: 5px;
            margin-top: 10px;
            text-align: center;
            display: none;
        }

        /* Cash Modal Specific Styles */
        .cash-container {
            border: 2px solid #ffd700;
            border-radius: 10px;
            padding: 20px;
            background-color: #fffef7;
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        #cashAccountsList {
            max-height: 200px;
            overflow-y: auto;
            margin-bottom: 15px;
        }

        .cash-account-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding: 10px 0;
            border-bottom: 1px solid #e0e0e0;
        }

        .cash-account-row:last-of-type {
            border-bottom: none;
        }

        .account-name {
            font-size: 16px;
            color: #333;
            font-weight: 500;
        }

        .account-balance {
            font-size: 16px;
            color: #4a69bd;
            font-weight: 600;
            cursor: pointer;
            padding: 5px 10px;
            border-radius: 5px;
            transition: background-color 0.2s;
            border: 1px solid transparent;
        }

        .account-balance:hover {
            background-color: #f0f0f0;
        }

        .account-balance[contenteditable="true"]:focus {
            background-color: white;
            border: 1px solid #4a69bd;
            outline: none;
        }

        .delete-account {
            color: #dc3545;
            cursor: pointer;
            margin-left: 10px;
            font-size: 14px;
        }

        .delete-account:hover {
            color: #c82333;
        }

        /* Add Account Section */
        .add-account-section {
            margin-top: 20px;
            padding-top: 15px;
            border-top: 2px solid #ffd700;
        }

        .add-account-btn {
            background-color: #28a745;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 5px;
            font-size: 14px;
            cursor: pointer;
            width: 100%;
            margin-bottom: 15px;
        }

        .add-account-btn:hover {
            background-color: #218838;
        }

        .add-account-form {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-top: 10px;
        }

        .form-input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            margin-bottom: 10px;
            font-size: 14px;
        }

        .form-buttons {
            display: flex;
            gap: 10px;
        }

        .save-account-btn {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 5px;
            font-size: 14px;
            cursor: pointer;
            flex: 1;
        }

        .cancel-btn {
            background-color: #6c757d;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 5px;
            font-size: 14px;
            cursor: pointer;
            flex: 1;
        }

        .total-value {
            color: #2c5aa0 !important;
            font-size: 18px !important;
        }

        /* Vodafone Cash Modal Specific Styles */
        .vodafone-container {
            border: 2px solid #ffd700;
            border-radius: 10px;
            padding: 20px;
            background-color: #fffef7;
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        #vodafoneAccountsList {
            max-height: 200px;
            overflow-y: auto;
            margin-bottom: 15px;
        }

        .vodafone-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding: 10px 0;
            border-bottom: 2px solid #ffd700;
            font-weight: 600;
            color: #4a69bd;
        }

        .header-item {
            font-size: 16px;
            text-align: center;
            flex: 1;
        }

        .vodafone-account-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
            padding: 8px 0;
            border-bottom: 1px solid #e0e0e0;
        }

        .vodafone-account-row:last-of-type {
            border-bottom: none;
        }

        .vodafone-number {
            font-size: 14px;
            color: #333;
            font-weight: 500;
            flex: 1;
            text-align: center;
        }

        .vodafone-balance {
            font-size: 16px;
            color: #28a745;
            font-weight: 600;
            cursor: pointer;
            padding: 5px 10px;
            border-radius: 5px;
            transition: background-color 0.2s;
            border: 1px solid transparent;
            flex: 1;
            text-align: center;
        }

        .vodafone-balance:hover {
            background-color: #f0f0f0;
        }

        .vodafone-balance[contenteditable="true"]:focus {
            background-color: white;
            border: 1px solid #28a745;
            outline: none;
        }

        .vodafone-total {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 15px;
            padding-top: 15px;
            border-top: 2px solid #ffd700;
        }

        .add-vodafone-btn {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: #28a745;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-size: 24px;
            font-weight: bold;
            transition: background-color 0.2s;
        }

        .add-vodafone-btn:hover {
            background-color: #218838;
        }

        .total-section {
            display: flex;
            flex-direction: column;
            align-items: center;
            flex: 1;
        }

        .total-label {
            font-size: 14px;
            color: #333;
            font-weight: 500;
            margin-top: 5px;
        }

        .add-vodafone-form {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-top: 15px;
            border-top: 1px solid #ddd;
        }

        .delete-vodafone {
            color: #dc3545;
            cursor: pointer;
            margin-left: 10px;
            font-size: 12px;
        }

        .delete-vodafone:hover {
            color: #c82333;
        }

        /* Custom Scrollbar */
        .modal-body::-webkit-scrollbar,
        .cash-container::-webkit-scrollbar,
        .vodafone-container::-webkit-scrollbar,
        #cashAccountsList::-webkit-scrollbar,
        #vodafoneAccountsList::-webkit-scrollbar {
            width: 6px;
        }

        .modal-body::-webkit-scrollbar-track,
        .cash-container::-webkit-scrollbar-track,
        .vodafone-container::-webkit-scrollbar-track,
        #cashAccountsList::-webkit-scrollbar-track,
        #vodafoneAccountsList::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 3px;
        }

        .modal-body::-webkit-scrollbar-thumb,
        .cash-container::-webkit-scrollbar-thumb,
        .vodafone-container::-webkit-scrollbar-thumb,
        #cashAccountsList::-webkit-scrollbar-thumb,
        #vodafoneAccountsList::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 3px;
        }

        .modal-body::-webkit-scrollbar-thumb:hover,
        .cash-container::-webkit-scrollbar-thumb:hover,
        .vodafone-container::-webkit-scrollbar-thumb:hover,
        #cashAccountsList::-webkit-scrollbar-thumb:hover,
        #vodafoneAccountsList::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }

        /* Firefox Scrollbar */
        .modal-body,
        .cash-container,
        .vodafone-container,
        #cashAccountsList,
        #vodafoneAccountsList {
            scrollbar-width: thin;
            scrollbar-color: #c1c1c1 #f1f1f1;
        }
    </style>

    <script>
        // جلب البيانات من قاعدة البيانات
        function loadFawryData() {
            fetch('api/fawry/get_report.php')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        document.getElementById('basicBalance').textContent = data.data.basic_balance.toLocaleString();
                        document.getElementById('cashOut').textContent = data.data.cash_out.toLocaleString();
                        document.getElementById('totalBalance').textContent = data.data.total_balance.toLocaleString();
                    }
                })
                .catch(error => {
                    console.error('Error loading data:', error);
                });
        }

        function showFawryModal() {
            loadFawryData();
            document.getElementById('fawryModal').style.display = 'flex';
        }

        function closeFawryModal() {
            document.getElementById('fawryModal').style.display = 'none';
        }

        // تحديث الإجمالي عند تغيير القيم
        function updateTotal() {
            const basicBalance = parseFloat(document.getElementById('basicBalance').textContent.replace(/,/g, '')) || 0;
            const cashOut = parseFloat(document.getElementById('cashOut').textContent.replace(/,/g, '')) || 0;
            const total = basicBalance - cashOut;

            document.getElementById('totalBalance').textContent = total.toLocaleString();
        }

        // إضافة مستمعين للأحداث عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('basicBalance').addEventListener('input', updateTotal);
            document.getElementById('cashOut').addEventListener('input', updateTotal);
        });

        // حفظ البيانات
        function saveFawryData() {
            const basicBalance = parseFloat(document.getElementById('basicBalance').textContent.replace(/,/g, '')) || 0;
            const cashOut = parseFloat(document.getElementById('cashOut').textContent.replace(/,/g, '')) || 0;
            const totalBalance = basicBalance - cashOut;

            const data = {
                basic_balance: basicBalance,
                cash_out: cashOut,
                total_balance: totalBalance
            };

            fetch('api/fawry/update_report.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    document.getElementById('successMessage').style.display = 'block';
                    setTimeout(() => {
                        document.getElementById('successMessage').style.display = 'none';
                    }, 3000);
                } else {
                    alert('حدث خطأ في حفظ البيانات');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('حدث خطأ في الاتصال');
            });
        }

        // إغلاق المودال عند النقر خارجه
        document.getElementById('fawryModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeFawryModal();
            }
        });

        // إغلاق المودال بمفتاح Escape
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeFawryModal();
                closeCashModal();
                closeVodafoneModal();
            }
        });

        // ========== Cash Modal Functions ==========

        // جلب بيانات الكاش
        function loadCashData() {
            fetch('api/cash/get_accounts.php')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        displayCashAccounts(data.accounts);
                        updateCashTotal();
                    }
                })
                .catch(error => {
                    console.error('Error loading cash data:', error);
                });
        }

        // عرض حسابات الكاش
        function displayCashAccounts(accounts) {
            const container = document.getElementById('cashAccountsList');
            container.innerHTML = '';

            accounts.forEach(account => {
                const row = document.createElement('div');
                row.className = 'cash-account-row';
                row.innerHTML = `
                    <span class="account-name">${account.account_name}</span>
                    <div>
                        <span class="account-balance"
                              contenteditable="true"
                              data-account-id="${account.id}"
                              onblur="updateCashTotal()"
                              oninput="updateCashTotal()">${parseFloat(account.balance).toLocaleString()}</span>
                        <span class="delete-account" onclick="deleteAccount(${account.id})" title="حذف الحساب">🗑️</span>
                    </div>
                `;
                container.appendChild(row);
            });
        }

        // تحديث إجمالي الكاش
        function updateCashTotal() {
            const balances = document.querySelectorAll('.account-balance');
            let total = 0;

            balances.forEach(balance => {
                const value = parseFloat(balance.textContent.replace(/,/g, '')) || 0;
                total += value;
            });

            document.getElementById('cashTotalBalance').textContent = total.toLocaleString();
        }

        // إظهار مودال الكاش
        function showCashModal() {
            loadCashData();
            document.getElementById('cashModal').style.display = 'flex';
        }

        // إغلاق مودال الكاش
        function closeCashModal() {
            document.getElementById('cashModal').style.display = 'none';
            hideAddAccountForm();
        }

        // إظهار نموذج إضافة حساب
        function showAddAccountForm() {
            document.getElementById('addAccountForm').style.display = 'block';
        }

        // إخفاء نموذج إضافة حساب
        function hideAddAccountForm() {
            document.getElementById('addAccountForm').style.display = 'none';
            document.getElementById('newAccountName').value = '';
            document.getElementById('newAccountBalance').value = '';
        }

        // إضافة حساب جديد
        function addNewAccount() {
            const name = document.getElementById('newAccountName').value.trim();
            const balance = parseFloat(document.getElementById('newAccountBalance').value) || 0;

            if (!name) {
                alert('يرجى إدخال اسم الحساب');
                return;
            }

            const data = {
                account_name: name,
                balance: balance
            };

            fetch('api/cash/add_account.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    hideAddAccountForm();
                    loadCashData();
                } else {
                    alert('حدث خطأ في إضافة الحساب');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('حدث خطأ في الاتصال');
            });
        }

        // حذف حساب
        function deleteAccount(accountId) {
            if (!confirm('هل أنت متأكد من حذف هذا الحساب؟')) {
                return;
            }

            fetch('api/cash/delete_account.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({id: accountId})
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    loadCashData();
                } else {
                    alert('حدث خطأ في حذف الحساب');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('حدث خطأ في الاتصال');
            });
        }

        // حفظ بيانات الكاش
        function saveCashData() {
            const accounts = [];
            const rows = document.querySelectorAll('.cash-account-row');

            rows.forEach(row => {
                const balanceElement = row.querySelector('.account-balance');
                const accountId = balanceElement.getAttribute('data-account-id');
                const balance = parseFloat(balanceElement.textContent.replace(/,/g, '')) || 0;

                accounts.push({
                    id: accountId,
                    balance: balance
                });
            });

            fetch('api/cash/update_accounts.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({accounts: accounts})
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    document.getElementById('cashSuccessMessage').style.display = 'block';
                    setTimeout(() => {
                        document.getElementById('cashSuccessMessage').style.display = 'none';
                    }, 3000);
                } else {
                    alert('حدث خطأ في حفظ البيانات');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('حدث خطأ في الاتصال');
            });
        }

        // إغلاق مودال الكاش عند النقر خارجه
        document.getElementById('cashModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeCashModal();
            }
        });

        // ========== Vodafone Cash Modal Functions ==========

        // جلب بيانات فودافون كاش
        function loadVodafoneData() {
            fetch('api/vodafone/get_accounts.php')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        displayVodafoneAccounts(data.accounts);
                        updateVodafoneTotal();
                    }
                })
                .catch(error => {
                    console.error('Error loading vodafone data:', error);
                });
        }

        // عرض حسابات فودافون كاش
        function displayVodafoneAccounts(accounts) {
            const container = document.getElementById('vodafoneAccountsList');
            container.innerHTML = '';

            accounts.forEach(account => {
                const row = document.createElement('div');
                row.className = 'vodafone-account-row';
                row.innerHTML = `
                    <div class="vodafone-number">${account.phone_number} وسام</div>
                    <div style="display: flex; align-items: center;">
                        <span class="vodafone-balance"
                              contenteditable="true"
                              data-account-id="${account.id}"
                              onblur="updateVodafoneTotal()"
                              oninput="updateVodafoneTotal()">${parseFloat(account.balance).toLocaleString()}</span>
                        <span class="delete-vodafone" onclick="deleteVodafoneAccount(${account.id})" title="حذف الحساب">🗑️</span>
                    </div>
                `;
                container.appendChild(row);
            });
        }

        // تحديث إجمالي فودافون كاش
        function updateVodafoneTotal() {
            const balances = document.querySelectorAll('.vodafone-balance');
            let total = 0;

            balances.forEach(balance => {
                const value = parseFloat(balance.textContent.replace(/,/g, '')) || 0;
                total += value;
            });

            document.getElementById('vodafoneTotalBalance').textContent = total.toLocaleString();
        }

        // إظهار مودال فودافون كاش
        function showVodafoneModal() {
            loadVodafoneData();
            document.getElementById('vodafoneModal').style.display = 'flex';
        }

        // إغلاق مودال فودافون كاش
        function closeVodafoneModal() {
            document.getElementById('vodafoneModal').style.display = 'none';
            hideAddVodafoneForm();
        }

        // إظهار نموذج إضافة حساب فودافون
        function showAddVodafoneForm() {
            document.getElementById('addVodafoneForm').style.display = 'block';
        }

        // إخفاء نموذج إضافة حساب فودافون
        function hideAddVodafoneForm() {
            document.getElementById('addVodafoneForm').style.display = 'none';
            document.getElementById('newVodafoneNumber').value = '';
            document.getElementById('newVodafoneBalance').value = '';
        }

        // إضافة حساب فودافون جديد
        function addNewVodafoneAccount() {
            const phoneNumber = document.getElementById('newVodafoneNumber').value.trim();
            const balance = parseFloat(document.getElementById('newVodafoneBalance').value) || 0;

            if (!phoneNumber) {
                alert('يرجى إدخال رقم الهاتف');
                return;
            }

            const data = {
                phone_number: phoneNumber,
                balance: balance
            };

            fetch('api/vodafone/add_account.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    hideAddVodafoneForm();
                    loadVodafoneData();
                } else {
                    alert('حدث خطأ في إضافة الحساب');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('حدث خطأ في الاتصال');
            });
        }

        // حذف حساب فودافون
        function deleteVodafoneAccount(accountId) {
            if (!confirm('هل أنت متأكد من حذف هذا الحساب؟')) {
                return;
            }

            fetch('api/vodafone/delete_account.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({id: accountId})
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    loadVodafoneData();
                } else {
                    alert('حدث خطأ في حذف الحساب');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('حدث خطأ في الاتصال');
            });
        }

        // حفظ بيانات فودافون كاش
        function saveVodafoneData() {
            const accounts = [];
            const rows = document.querySelectorAll('.vodafone-account-row');

            rows.forEach(row => {
                const balanceElement = row.querySelector('.vodafone-balance');
                const accountId = balanceElement.getAttribute('data-account-id');
                const balance = parseFloat(balanceElement.textContent.replace(/,/g, '')) || 0;

                accounts.push({
                    id: accountId,
                    balance: balance
                });
            });

            fetch('api/vodafone/update_accounts.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({accounts: accounts})
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    document.getElementById('vodafoneSuccessMessage').style.display = 'block';
                    setTimeout(() => {
                        document.getElementById('vodafoneSuccessMessage').style.display = 'none';
                    }, 3000);
                } else {
                    alert('حدث خطأ في حفظ البيانات');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('حدث خطأ في الاتصال');
            });
        }

        // إغلاق مودال فودافون عند النقر خارجه
        document.getElementById('vodafoneModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeVodafoneModal();
            }
        });
    </script>
</body>
</html>
