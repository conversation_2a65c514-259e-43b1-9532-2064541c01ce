<?php
// Include configuration
require_once 'config/config.php';

// Include database connection
require_once 'includes/db.php';

// Include helper functions
require_once 'includes/functions.php';

// Include authentication functions
require_once 'includes/auth.php';

// Include permissions functions
require_once 'includes/permissions.php';

// Check if user is logged in
if (!isLoggedIn()) {
    $_SESSION['flash_message'] = 'يجب تسجيل الدخول للوصول إلى اكونتات فوري';
    $_SESSION['flash_type'] = 'danger';
    redirect(BASE_URL . 'index.php');
}

// جلب بيانات الحسابات الإعلانية (فقط الحسابات غير المرتبطة)
try {
    $stmt = $db->prepare("
        SELECT * FROM ad_accounts
        WHERE linked_account_type = 'none' OR linked_account_type IS NULL
        ORDER BY name ASC
    ");
    $stmt->execute();
    $fawryAccounts = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    // التحقق من وجود الجدول
    $tableExists = false;
    $tables = $db->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);

    if (in_array('ad_accounts', $tables)) {
        $tableExists = true;
    }

    // إنشاء الجدول إذا لم يكن موجودًا
    if (!$tableExists) {
        $db->exec("CREATE TABLE ad_accounts (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            status VARCHAR(50) NOT NULL DEFAULT 'نشط',
            balance DECIMAL(10,2) NOT NULL DEFAULT 0.00,
            spending_limit DECIMAL(10,2) NOT NULL DEFAULT 0.00,
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )");

        // إضافة بيانات تجريبية
        $db->exec("INSERT INTO ad_accounts (name, status, balance, spending_limit, notes) VALUES
            ('ليلى اسامة', 'نشط', 5000.00, 10000.00, 'حساب إعلاني رئيسي'),
            ('محمد أحمد', 'نشط', 3000.00, 8000.00, 'حساب إعلاني ثانوي'),
            ('سارة محمود', 'متوقف', 1000.00, 5000.00, 'تم إيقاف الحساب مؤقتًا')
        ");
    }

    // محاولة جلب البيانات مرة أخرى (فقط الحسابات غير المرتبطة)
    $stmt = $db->prepare("
        SELECT * FROM ad_accounts
        WHERE linked_account_type = 'none' OR linked_account_type IS NULL
        ORDER BY name ASC
    ");
    $stmt->execute();
    $fawryAccounts = $stmt->fetchAll(PDO::FETCH_ASSOC);
}

// جلب الإعلانات المرتبطة بكل حساب
try {
    // نستخدم استعلام مع JOIN لجلب اسم العميل من جدول ad_clients أو clients
    $stmt = $db->prepare("
        SELECT a.*,
               COALESCE(ac.name, c.name) as client_name,
               COALESCE(ac.type, 'عميل عادي') as client_type
        FROM ads a
        LEFT JOIN ad_clients ac ON a.client_id = ac.id
        LEFT JOIN clients c ON a.client_id = c.id AND ac.id IS NULL
        WHERE a.ad_account_id IS NOT NULL
        ORDER BY a.date DESC
    ");

    $stmt->execute();
    $transactions = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // إذا لم تكن هناك إعلانات مرتبطة بحسابات إعلانية، نقوم بتحديث بعض الإعلانات
    if (empty($transactions)) {
        // نتحقق من وجود إعلانات
        $adsQuery = $db->prepare("SELECT COUNT(*) FROM ads");
        $adsQuery->execute();
        $adsCount = $adsQuery->fetchColumn();

        if ($adsCount > 0) {
            // نقوم بتحديث بعض الإعلانات لتكون مرتبطة بحسابات إعلانية
            $db->exec("UPDATE ads SET ad_account_id = 1 WHERE id % 3 = 0 AND ad_account_id IS NULL");
            $db->exec("UPDATE ads SET ad_account_id = 2 WHERE id % 3 = 1 AND ad_account_id IS NULL");
            $db->exec("UPDATE ads SET ad_account_id = 3 WHERE id % 3 = 2 AND ad_account_id IS NULL");

            // جلب الإعلانات مرة أخرى
            $stmt = $db->prepare("
                SELECT a.*,
                       COALESCE(ac.name, c.name) as client_name,
                       COALESCE(ac.type, 'عميل عادي') as client_type
                FROM ads a
                LEFT JOIN ad_clients ac ON a.client_id = ac.id
                LEFT JOIN clients c ON a.client_id = c.id AND ac.id IS NULL
                WHERE a.ad_account_id IS NOT NULL
                ORDER BY a.date DESC
            ");
            $stmt->execute();
            $transactions = $stmt->fetchAll(PDO::FETCH_ASSOC);
        }
    }




} catch (PDOException $e) {
    // في حالة حدوث خطأ، نستخدم مصفوفة فارغة
    $transactions = [];

    // طباعة رسالة الخطأ للتصحيح
    // echo "خطأ في قاعدة البيانات: " . $e->getMessage();
}

// تنظيم الإعلانات حسب الحساب
$accountTransactions = [];
foreach ($fawryAccounts as $account) {
    $accountTransactions[$account['id']] = [];
}

foreach ($transactions as $transaction) {
    if (isset($accountTransactions[$transaction['ad_account_id']])) {
        // حساب المبلغ اليومي
        $daily_amount = $transaction['days'] > 0 ? $transaction['cost'] / $transaction['days'] : $transaction['cost'];

        // إضافة المبلغ اليومي للمعاملة
        $transaction['daily_amount'] = $daily_amount;

        // تغيير اسم المفتاح من ad_account_id إلى account_id للتوافق مع الكود الحالي
        $transaction['account_id'] = $transaction['ad_account_id'];

        // إضافة المعاملة فقط إذا كانت حالتها نشطة
        $accountTransactions[$transaction['ad_account_id']][] = $transaction;
    }
}

// حساب الإجماليات لكل حساب
$accountTotals = [];
foreach ($fawryAccounts as $account) {
    $accountTotals[$account['id']] = [
        'total_cost' => 0,                // إجمالي التكلفة
        'total_exchange' => 0,            // إجمالي الصرف بالمصري
        'total_exchange_ratio' => 0,      // إجمالي الصرف بالنسبة
        'total_daily' => 0,               // إجمالي الصرف اليومي
        'remaining_exchange' => 0,        // المتبقي صرفه
        'required_charge' => 0,           // مطلوب شحن
        'current_balance' => 0,           // حساب حالي (سيتم حسابه لاحقًا)
        'balance_after_ads' => 0          // رصيد الحساب بعد الإعلانات
    ];
}

foreach ($transactions as $transaction) {
    // التحقق من وجود حساب إعلاني وأن الإعلان ليس متوقفًا
    if (isset($accountTotals[$transaction['ad_account_id']]) &&
        strtolower($transaction['status']) != 'متوقف') {

        // حساب المبلغ اليومي
        $daily_amount = $transaction['days'] > 0 ? $transaction['cost'] / $transaction['days'] : $transaction['cost'];

        // حساب الصرف بالنسبة
        $exchange_rate = $transaction['exchange_rate'] ?? 0;
        $percentage = $transaction['percentage'] ?? 0;
        $exchange_ratio = $transaction['exchange_rate_with_percentage'] ??
                         $transaction['exchange_rate_percentage'] ??
                         $exchange_rate + $exchange_rate * $percentage / 100;

        // إضافة إلى الإجماليات
        $accountTotals[$transaction['ad_account_id']]['total_cost'] += $transaction['cost'];
        $accountTotals[$transaction['ad_account_id']]['total_exchange'] += $exchange_rate;
        $accountTotals[$transaction['ad_account_id']]['total_exchange_ratio'] += $exchange_ratio;
        $accountTotals[$transaction['ad_account_id']]['total_daily'] += $daily_amount;

        // لا نطبع معلومات تصحيح لكل إعلان لتجنب الإزعاج
    }
}

// مصفوفة لتخزين معلومات التصحيح
$debugInfo = [];

// حساب الإجماليات النهائية
foreach ($fawryAccounts as $account) {
    // المتبقي صرفه = إجمالي التكلفة - إجمالي الصرف
    // أي المبلغ المتبقي الذي يجب صرفه = إجمالي تكلفة الإعلانات - المبلغ الذي تم صرفه بالفعل
    $accountTotals[$account['id']]['remaining_exchange'] = $accountTotals[$account['id']]['total_cost'] - $accountTotals[$account['id']]['total_exchange'];

    // مطلوب شحن = إجمالي تكلفة الإعلانات - الرصيد الحالي (إذا كان إيجابي)
    // أي المبلغ المطلوب شحنه = إجمالي تكلفة الإعلانات - الفلوس التي على الحساب
    $required_charge = $accountTotals[$account['id']]['total_cost'] - $account['balance'];
    $accountTotals[$account['id']]['required_charge'] = $required_charge > 0 ? $required_charge : 0;

    // حساب حالي = الفلوس التي تم صرفها - الفلوس التي على الحساب
    $accountTotals[$account['id']]['current_balance'] = $account['balance'] - $accountTotals[$account['id']]['total_exchange'];

    // رصيد الحساب بعد الإعلانات = إجمالي التكلفة - الأموال التي على الحساب
    $accountTotals[$account['id']]['balance_after_ads'] =   $account['balance'] - $accountTotals[$account['id']]['total_cost'];

    // نضيف معلومات الإجماليات إلى مصفوفة للعرض لاحقًا
    $debugInfo[] = [
        'account_id' => $account['id'],
        'account_name' => $account['name'],
        'totals' => $accountTotals[$account['id']]
    ];
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اكونتات فوري - <?php echo SITE_TITLE; ?></title>

    <!-- Bootstrap RTL CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Google Fonts - Cairo -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>assets/css/styles.css">
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>assets/css/credit_card.css">

    <style>
    /* تنسيق نوافذ إضافة وتعديل الإعلان */
    .ad-form-header {
        font-weight: bold;
        margin-bottom: 10px;
        text-align: center;
        color: #0d6efd;
    }

    .ad-form-inputs {
        margin-bottom: 20px;
    }

    .ad-form-divider {
        border-color: #dcf343;
        margin: 20px 0;
    }

    .modal-dialog.modal-lg {
        max-width: 90%;
    }

    .modal-header {
        background-color: #f0f8ff;
        border-bottom: 2px solid #dcf343;
    }

    .modal-title {
        color: #0d6efd;
        font-weight: bold;
    }

    /* تنسيق الصفوف القابلة للنقر */
    .transactions-table tbody tr {
        cursor: pointer;
        transition: background-color 0.2s;
    }

    .transactions-table tbody tr:hover {
        background-color: #f5f5f5;
    }

    /* تنسيق زر الإضافة */
    .add-ad-submit,
    .edit-ad-submit {
        background-color: #0d6efd;
        border-color: #0d6efd;
        padding: 8px 30px;
        font-weight: bold;
    }

    /* تنسيق نافذة تفاصيل الحساب */
    .modal-dialog.modal-xl {
        max-width: 95%;
    }

    .account-details-container {
        border: 1px solid #dcf343;
        border-radius: 10px;
        padding: 15px;
        background-color: #fff;
    }

    .account-details-table-container {
        max-height: 400px;
        overflow-y: auto;
        margin-bottom: 20px;
    }

    .account-details-table {
        width: 100%;
        border-collapse: collapse;
    }

    .account-details-table th {
        background-color: #f8f9fa;
        color: #0d6efd;
        font-weight: bold;
        text-align: center;
        padding: 10px;
        border-bottom: 2px solid #dcf343;
    }

    .account-details-table td {
        text-align: center;
        padding: 10px;
        border-bottom: 1px solid #dee2e6;
    }

    .account-details-footer {
        margin-top: 20px;
        border-top: 2px solid #dcf343;
        padding-top: 15px;
    }

    .daily-label {
        font-weight: bold;
        margin-right: 10px;
    }

    .daily-amount {
        font-weight: bold;
        color: #0d6efd;
    }

    .toggle-details {
        margin-right: 10px;
    }

    .account-details-add-button {
        position: absolute;
        bottom: 10px;
        right: 10px;
        width: 30px;
        height: 30px;
        background-color: #4CAF50;
        color: white;
        border-radius: 50%;
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;
    }

    .footer-row {
        display: grid;
        grid-template-columns: 1fr 1fr 1fr;
        gap: 10px;
        margin-bottom: 15px;
    }

    .footer-cell {
        border: 1px solid #dcf343;
        padding: 10px;
        text-align: center;
        position: relative;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
    }

    .footer-label {
        font-weight: bold;
        color: #0d6efd;
        margin-bottom: 5px;
    }

    .footer-value {
        font-size: 1.2em;
        font-weight: bold;
    }
    </style>
</head>

<body>
    <div class="page-container">
        <div class="page-header">
            <a href="<?php echo BASE_URL; ?>" class="logo-link">
                <img src="<?php echo BASE_URL; ?>assets/images/logo.png" alt="<?php echo SITE_TITLE; ?>"
                    class="page-logo">
            </a>
            <div class="header-links">
                <a href="#" class="header-link" id="showBalanceModalBtn">رصيد حسابات فوري</a>
                <a href="#" class="header-link" id="showChargeHistoryBtn">شحن فوري</a>
            </div>
        </div>

        <div class="search-container">
            <a href="<?php echo BASE_URL; ?>fawry_payment_accounts.php" style="text-decoration: auto;">
                <div class="account-label">
                    <i class="fas fa-credit-card"></i>
                    اكونتات الدفع فوري
                </div>
            </a>

            <div class="search-box">
                <input type="text" id="searchInput" placeholder="البحث..." class="search-input">
                <i class="fas fa-search search-icon"></i>
            </div>
        </div>

        <div class="accounts-container">
            <?php foreach ($fawryAccounts as $account): ?>
            <div class="account-section account-card" data-account-id="<?php echo $account['id']; ?>"
                style="border: 1px solid #dcf343; margin-bottom: 20px; box-shadow: 0 2px 5px rgba(0,0,0,0.1);">
                <div class="account-header"
                    style="background-color: #4a4ad4; color: white; padding: 10px; display: flex; justify-content: space-between; align-items: center;">
                    <div style="width: 30px;">
                        <i class="fas fa-chevron-down"></i>
                    </div>
                    <div class="account-name"
                        style="cursor: pointer; font-weight: bold; text-align: center; flex-grow: 1;">
                        <?php echo htmlspecialchars($account['name']); ?></div>
                    <div class="account-actions" style="width: 30px; text-align: right;">
                        <i class="fas fa-ellipsis-h"></i>
                    </div>
                </div>
                <div class="transactions-table-container">
                    <table class="transactions-table"
                        style="width: 100%; border-collapse: collapse; border: 1px solid #dcf343; background-color: #fff;">
                        <thead>
                            <tr style="background-color: #f8f9fa; border-bottom: 1px solid #dcf343;">
                                <th
                                    style="padding: 8px; text-align: center; color: #4a4ad4; font-weight: bold; border-left: 1px solid #dcf343;">
                                    الصفحة</th>
                                <th
                                    style="padding: 8px; text-align: center; color: #4a4ad4; font-weight: bold; border-left: 1px solid #dcf343;">
                                    البوست</th>
                                <th
                                    style="padding: 8px; text-align: center; color: #4a4ad4; font-weight: bold; border-left: 1px solid #dcf343;">
                                    المبلغ</th>
                                <th
                                    style="padding: 8px; text-align: center; color: #4a4ad4; font-weight: bold; border-left: 1px solid #dcf343;">
                                    الصرف</th>
                                <th
                                    style="padding: 8px; text-align: center; color: #4a4ad4; font-weight: bold; border-left: 1px solid #dcf343;">
                                    يوم</th>
                                <th
                                    style="padding: 8px; text-align: center; color: #4a4ad4; font-weight: bold; border-left: 1px solid #dcf343;">
                                    يومي</th>
                                <th style="padding: 8px; text-align: center; color: #4a4ad4; font-weight: bold;">م الصرف
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (isset($accountTransactions[$account['id']]) && !empty($accountTransactions[$account['id']])): ?>
                            <?php foreach ($accountTransactions[$account['id']] as $transaction): ?>
                            <?php
                                    // محاولة الحصول على اسم العميل من حقل client_name
                                    $clientName = !empty($transaction['client_name']) ? $transaction['client_name'] :
                                                 (!empty($transaction['client_type']) ? $transaction['client_type'] : 'غير محدد');
                                ?>
                            <tr data-ad-id="<?php echo $transaction['id']; ?>"
                                style="border-bottom: 1px solid #dcf343; background-color: #fffef0;">
                                <td style="padding: 8px; text-align: center; border-left: 1px solid #dcf343;">
                                    <?php echo htmlspecialchars($clientName); ?></td>
                                <td style="padding: 8px; text-align: center; border-left: 1px solid #dcf343;">
                                    <?php echo htmlspecialchars($transaction['post'] ?? 'الطفل'); ?></td>
                                <td style="padding: 8px; text-align: center; border-left: 1px solid #dcf343;">
                                    <?php echo number_format($transaction['cost'], 3); ?></td>
                                <td style="padding: 8px; text-align: center; border-left: 1px solid #dcf343;">
                                    <?php echo number_format($transaction['exchange_rate'], 3); ?></td>
                                <td style="padding: 8px; text-align: center; border-left: 1px solid #dcf343;">
                                    <?php echo $transaction['days'] ?? 21; ?></td>
                                <td style="padding: 8px; text-align: center; border-left: 1px solid #dcf343;">
                                    <?php echo number_format($transaction['daily_amount'], 2); ?></td>
                                <td style="padding: 8px; text-align: center;">
                                    <?php echo number_format($transaction['exchange_rate'], 3); ?>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                            <?php else: ?>
                            <tr style="background-color: #fffef0;">
                                <td colspan="7"
                                    style="padding: 10px; text-align: center; border-left: 1px solid #dcf343;">لا توجد
                                    إعلانات</td>
                            </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
                <div class="account-footer" style="margin-top: 0;">
                    <table
                        style="width: 100%; border-collapse: collapse; border: 1px solid #dcf343; background-color: #fff;">
                        <tr>
                            <td style="padding: 8px; text-align: center; border: 1px solid #dcf343; width: 33%;">
                                <div style="display: flex; justify-content: center; align-items: center;">
                                    <div style="color: #4a4ad4; font-weight: bold; margin-left: 5px;">الصرف اليومي</div>
                                    <div style="font-weight: bold; font-size: 1.1em;">
                                        <?php echo number_format($accountTotals[$account['id']]['total_daily'], 2); ?>
                                    </div>
                                </div>
                            </td>
                            <td style="padding: 8px; text-align: center; border: 1px solid #dcf343; width: 33%;">
                                <div style="display: flex; justify-content: center; align-items: center;">
                                    <div style="color: #4a4ad4; font-weight: bold; margin-left: 5px;">المتبقي صرفه</div>
                                    <div style="font-weight: bold; font-size: 1.1em;">
                                        <?php echo number_format($accountTotals[$account['id']]['remaining_exchange'], 2); ?>
                                    </div>
                                </div>
                            </td>
                            <td style="padding: 8px; text-align: center; border: 1px solid #dcf343; width: 33%;">
                                <div style="display: flex; justify-content: center; align-items: center;">
                                    <div style="color: #4a4ad4; font-weight: bold; margin-left: 5px;">مطلوب شحن</div>
                                    <div style="font-weight: bold; font-size: 1.1em;">
                                        <?php echo number_format($accountTotals[$account['id']]['required_charge'], 2); ?>
                                    </div>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td style="padding: 8px; text-align: center; border: 1px solid #dcf343; width: 33%;">
                                <div style="display: flex; justify-content: center; align-items: center;">
                                    <div style="color: #4a4ad4; font-weight: bold; margin-left: 5px;">حساب حالي</div>
                                    <div style="font-weight: bold; font-size: 1.1em;">
                                        <?php echo number_format($accountTotals[$account['id']]['current_balance'], 2); ?>
                                    </div>
                                </div>
                            </td>
                            <td colspan="2"
                                style="padding: 8px; text-align: center; border: 1px solid #dcf343; position: relative;">
                                <div style="display: flex; justify-content: center; align-items: center;">
                                    <div style="color: #4a4ad4; font-weight: bold; margin-left: 5px;">رصيد الحساب بعد
                                        الاعلانات</div>
                                    <div style="font-weight: bold; font-size: 1.1em;">
                                        <?php echo number_format($accountTotals[$account['id']]['balance_after_ads'], 2); ?>
                                    </div>
                                </div>
                                <div class="add-transaction-button charge-button"
                                    style="position: absolute; bottom: 5px; right: 5px; width: 25px; height: 25px; background-color: #4CAF50; color: white; border-radius: 50%; display: flex; justify-content: center; align-items: center; cursor: pointer;">
                                    <i class="fas fa-plus"></i>
                                </div>
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
    </div>



    <!-- jQuery and Bootstrap JS -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Custom JS -->
    <script src="<?php echo BASE_URL; ?>assets/js/accounts_fawry.js"></script>



    <!-- JavaScript لنافذة تفاصيل الحساب -->
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // تهيئة النقر على اسم الحساب
        initAccountNameClick();

        // تهيئة زر التبديل في نافذة التفاصيل
        initToggleDetailsButton();
    });

    /**
     * تهيئة النقر على اسم الحساب
     */
    function initAccountNameClick() {
        const accountNames = document.querySelectorAll('.account-name');

        accountNames.forEach(name => {
            name.addEventListener('click', function() {
                const accountSection = this.closest('.account-card');
                const accountId = accountSection.getAttribute('data-account-id');
                const accountName = this.textContent.trim();

                // تعيين اسم الحساب في العنوان
                const modalLabel = document.getElementById('accountDetailsModalLabel');
                modalLabel.textContent = accountName;
                modalLabel.setAttribute('data-account-id', accountId);

                // جلب تفاصيل الحساب من الصفحة
                fetchAccountDetails(accountId);

                // جلب إعلانات الحساب من الخادم
                fetchClientAdsFromServer(accountId);

                // عرض النافذة المنبثقة
                const modal = new bootstrap.Modal(document.getElementById('accountDetailsModal'));
                modal.show();
            });
        });
    }

    /**
     * جلب إعلانات الحساب الإعلاني من الخادم
     */
    function fetchClientAdsFromServer(accountId) {
        console.log(`جلب إعلانات الحساب الإعلاني من الخادم للمعرف: ${accountId}`);

        // إظهار رسالة تحميل في الجدول
        const tableBody = document.getElementById('accountDetailsTableBody');
        if (tableBody) {
            tableBody.innerHTML = '<tr><td colspan="10" style="text-align: center;">جاري تحميل البيانات...</td></tr>';
        }

        // جلب البيانات من الخادم - استخدام getByAccount بدلاً من getByClient
        fetch(`api/ads/getByAccount.php?accountId=${accountId}`)
            .then(response => {
                console.log('استجابة الخادم:', response);
                return response.json();
            })
            .then(data => {
                console.log('بيانات الاستجابة:', data);
                // طباعة معلومات تصحيح الأخطاء
                if (data.debug) {
                    console.log('معلومات تصحيح الأخطاء:', data.debug);
                }
                if (data.client_check) {
                    console.log('معلومات العميل:', data.client_check);
                }
                if (data.success) {
                    console.log('تم جلب الإعلانات بنجاح:', data.ads);
                    displayClientAds(data.ads, accountId);
                } else {
                    console.error('خطأ في جلب الإعلانات:', data.message);
                    // عرض رسالة خطأ في الجدول
                    if (tableBody) {
                        tableBody.innerHTML = '<tr><td colspan="10" style="text-align: center;">حدث خطأ أثناء جلب البيانات</td></tr>';
                    }
                }
            })
            .catch(error => {
                console.error('خطأ في الاتصال بالخادم:', error);
                // عرض رسالة خطأ في الجدول
                if (tableBody) {
                    tableBody.innerHTML = '<tr><td colspan="10" style="text-align: center;">حدث خطأ أثناء الاتصال بالخادم</td></tr>';
                }
            });
    }

    /**
     * عرض إعلانات الحساب الإعلاني في الجدول
     */
    function displayClientAds(ads, accountId) {
        const tableBody = document.getElementById('accountDetailsTableBody');
        if (!tableBody) return;

        // تفريغ الجدول
        tableBody.innerHTML = '';

        // إذا لم تكن هناك إعلانات
        if (!ads || ads.length === 0) {
            tableBody.innerHTML = '<tr><td colspan="10" style="text-align: center;">لا توجد إعلانات لهذا الحساب الإعلاني</td></tr>';
            return;
        }

        console.log('عرض الإعلانات:', ads);

        // إضافة الإعلانات إلى الجدول
        ads.forEach(ad => {
            try {
                const row = document.createElement('tr');

                // تاريخ البدء (استخدام تاريخ ثابت إذا لم يكن موجودًا)
                const startDate = ad.date ? ad.date : '25-8';

                // حساب الصرف اليومي (التأكد من أن القيم رقمية)
                const days = parseFloat(ad.days || 1);
                const exchangeRate = parseFloat(ad.exchange_rate || 0);
                const dailyAmount = days > 0 ? (exchangeRate / days).toFixed(2) : '0.00';

                // التأكد من وجود قيم لجميع الحقول
                const cost = ad.cost ? parseFloat(ad.cost).toFixed(2) : '0.00';

                // طباعة بيانات الإعلان للتصحيح
                console.log('بيانات الإعلان:', {
                    client_name: ad.client_name,
                    client_id: ad.client_id,
                    client_type: ad.client_type,
                    post: ad.post,
                    type: ad.type,
                    ad_account_id: ad.ad_account_id,
                    id: ad.id
                });

                // إنشاء الخلايا
                row.innerHTML = `
                    <td>${startDate}</td>
                    <td>${ad.client_name || 'غير محدد'}</td>
                    <td>${ad.post || ''}</td>
                    <td>${ad.type || 'تفاعل'}</td>
                    <td>${cost}</td>
                    <td>${exchangeRate.toFixed(2)}</td>
                    <td>${days}</td>
                    <td>${startDate}</td>
                    <td>${dailyAmount}</td>
                    <td>${exchangeRate.toFixed(2)}</td>
                `;

                tableBody.appendChild(row);
            } catch (error) {
                console.error('خطأ في إنشاء صف الإعلان:', error, ad);
            }
        });

        // حساب وعرض الإجماليات
        calculateAndDisplayTotals(ads);
    }

    /**
     * حساب وعرض إجماليات الإعلانات
     * ملاحظة: تم تعطيل هذه الدالة لأننا نستخدم الإجماليات من الصفحة الرئيسية
     */
    function calculateAndDisplayTotals(ads) {
        // حساب الإجماليات
        let totalDailyExchange = 0;
        let totalRemainingExchange = 0;
        let totalCost = 0;

        if (ads && ads.length > 0) {
            ads.forEach(ad => {
                try {
                    // التأكد من أن القيم رقمية
                    const days = parseFloat(ad.days || 1);
                    const exchangeRate = parseFloat(ad.exchange_rate || 0);
                    const cost = parseFloat(ad.cost || 0);

                    // حساب الصرف اليومي
                    const dailyAmount = days > 0 ? exchangeRate / days : 0;

                    // إضافة إلى المجموع
                    totalDailyExchange += dailyAmount;
                    totalRemainingExchange += exchangeRate;
                    totalCost += cost;
                } catch (error) {
                    console.error('خطأ في حساب إجماليات الإعلان:', error, ad);
                }
            });
        }

        console.log('إجماليات الإعلانات المحسوبة (لا تستخدم):', {
            totalDailyExchange,
            totalRemainingExchange,
            totalCost
        });

        // لا نقوم بتحديث الإجماليات هنا لأننا نستخدم القيم من الصفحة الرئيسية
        // تم نقل هذه الوظيفة إلى دالة fetchAccountDetails
    }

    /**
     * جلب تفاصيل الحساب من الصفحة الرئيسية
     */
    function fetchAccountDetails(accountId) {
        // البحث عن قسم الحساب في الصفحة
        const accountSection = document.querySelector(`.account-card[data-account-id="${accountId}"]`);
        if (!accountSection) {
            console.error(`لم يتم العثور على قسم الحساب للمعرف: ${accountId}`);
            return;
        }

        console.log(`جلب تفاصيل الحساب للمعرف: ${accountId}`);

        try {
            // جلب الإجماليات من تذييل الحساب في الصفحة الرئيسية
            console.log('جلب الإجماليات من الصفحة الرئيسية');

            // البحث عن جدول الإجماليات
            const footerTable = accountSection.querySelector('table.footer-table');
            if (!footerTable) {
                console.error('لم يتم العثور على جدول الإجماليات');
                return;
            }

            // طباعة هيكل الجدول للتصحيح
            console.log('هيكل جدول الإجماليات:', footerTable.outerHTML);

            // جلب القيم من الجدول
            // الصف الأول: اجمالي الصرف اليومي، اجمالي المتبقي صرفه، مطلوب شحن
            const firstRowCells = footerTable.querySelectorAll('tr:first-child td');
            console.log('عدد خلايا الصف الأول:', firstRowCells.length);

            let dailyExchange = '0.00 EGP';
            let remainingExchange = '0.00 EGP';
            let requiredCharge = '0.00 EGP';

            if (firstRowCells.length >= 3) {
                const dailyExchangeCell = firstRowCells[0].querySelector('div div:last-child');
                const remainingExchangeCell = firstRowCells[1].querySelector('div div:last-child');
                const requiredChargeCell = firstRowCells[2].querySelector('div div:last-child');

                if (dailyExchangeCell) dailyExchange = dailyExchangeCell.textContent.trim();
                if (remainingExchangeCell) remainingExchange = remainingExchangeCell.textContent.trim();
                if (requiredChargeCell) requiredCharge = requiredChargeCell.textContent.trim();
            }

            // الصف الثاني: رصيد الحساب الحالي، رصيد الحساب بعد الاعلانات
            const secondRowCells = footerTable.querySelectorAll('tr:last-child td');
            console.log('عدد خلايا الصف الثاني:', secondRowCells.length);

            let currentBalance = '0.00 EGP';
            let balanceAfterAds = '0.00 EGP';

            if (secondRowCells.length >= 2) {
                const currentBalanceCell = secondRowCells[0].querySelector('div div:last-child');
                const balanceAfterAdsCell = secondRowCells[1].querySelector('div div:last-child');

                if (currentBalanceCell) currentBalance = currentBalanceCell.textContent.trim();
                if (balanceAfterAdsCell) balanceAfterAds = balanceAfterAdsCell.textContent.trim();
            }

            console.log('القيم المستخرجة:', {
                dailyExchange,
                remainingExchange,
                requiredCharge,
                currentBalance,
                balanceAfterAds
            });

            // تعيين القيم في النافذة المنبثقة
            // تعيين القيمة اليومية في الرأس
            const dailyAmountElement = document.querySelector('.daily-amount');
            if (dailyAmountElement) {
                dailyAmountElement.textContent = dailyExchange;
            }

            // تعيين الإجماليات في التذييل
            const totalDailyExchangeElement = document.querySelector('.total-daily-exchange');
            if (totalDailyExchangeElement) {
                totalDailyExchangeElement.textContent = dailyExchange;
            }

            const totalRemainingExchangeElement = document.querySelector('.total-remaining-exchange');
            if (totalRemainingExchangeElement) {
                totalRemainingExchangeElement.textContent = remainingExchange;
            }

            const requiredChargeElement = document.querySelector('.required-charge');
            if (requiredChargeElement) {
                requiredChargeElement.textContent = requiredCharge;
            }

            const currentBalanceElement = document.querySelector('.current-balance');
            if (currentBalanceElement) {
                currentBalanceElement.textContent = currentBalance;
            }

            const balanceAfterAdsElement = document.querySelector('.balance-after-ads');
            if (balanceAfterAdsElement) {
                balanceAfterAdsElement.textContent = balanceAfterAds;
            }
        } catch (error) {
            console.error('خطأ في جلب تفاصيل الحساب:', error);
        }
    }

    /**
     * تهيئة زر التبديل في نافذة التفاصيل
     */
    function initToggleDetailsButton() {
        const toggleButton = document.querySelector('.toggle-details');
        if (toggleButton) {
            toggleButton.addEventListener('click', function() {
                const icon = this.querySelector('i');
                if (icon.classList.contains('fa-chevron-down')) {
                    icon.classList.remove('fa-chevron-down');
                    icon.classList.add('fa-chevron-up');
                } else {
                    icon.classList.remove('fa-chevron-up');
                    icon.classList.add('fa-chevron-down');
                }
            });
        }
    }
    </script>

    <!-- نافذة إضافة إعلان -->
    <div class="modal fade" id="addAdModal" tabindex="-1" aria-labelledby="addAdModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addAdModalLabel">إضافة اعلان</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="addAdForm">
                        <input type="hidden" id="adAccountIdHidden" name="adAccountIdHidden">

                        <!-- عناوين الأعمدة -->
                        <div class="row ad-form-header">
                            <div class="col">تاريخ</div>
                            <div class="col">نوع</div>
                            <div class="col">يومي / اجمالي</div>
                            <div class="col">بوست</div>
                            <div class="col">عدد ايام</div>
                            <div class="col">التكلفة</div>
                            <div class="col">الصرف</div>
                            <div class="col">الاكونت</div>
                        </div>

                        <!-- حقول الإدخال -->
                        <div class="row ad-form-inputs">
                            <div class="col">
                                <input type="date" class="form-control" id="adDate" name="adDate" required
                                    value="<?php echo date('Y-m-d'); ?>">
                            </div>
                            <div class="col">
                                <input type="text" class="form-control" id="adType" name="adType" required>
                            </div>
                            <div class="col">
                                <select class="form-select" id="adDailyTotal" name="adDailyTotal" required>
                                    <option value="يومي">يومي</option>
                                    <option value="اجمالي">اجمالي</option>
                                </select>
                            </div>
                            <div class="col">
                                <input type="text" class="form-control" id="adPost" name="adPost">
                            </div>
                            <div class="col">
                                <input type="number" class="form-control" id="adDays" name="adDays" min="1" value="1"
                                    required>
                            </div>
                            <div class="col">
                                <input type="number" class="form-control" id="adCost" name="adCost" step="0.01"
                                    required>
                            </div>
                            <div class="col">
                                <input type="number" class="form-control" id="adExchangeRate" name="adExchangeRate"
                                    step="0.01" required>
                            </div>
                            <div class="col">
                                <select class="form-select" id="adClient" name="adClient" required>
                                    <option value="">اختر العميل</option>
                                    <?php
                                    // جلب العملاء من جدول ad_clients
                                    try {
                                        $stmt = $db->prepare("SELECT id, name FROM ad_clients ORDER BY name");
                                        $stmt->execute();
                                        $adClients = $stmt->fetchAll(PDO::FETCH_ASSOC);

                                        foreach ($adClients as $client) {
                                            echo "<option value='{$client['id']}'>{$client['name']}</option>";
                                        }
                                    } catch (PDOException $e) {
                                        // تجاهل الخطأ
                                    }

                                    // جلب العملاء من جدول clients
                                    try {
                                        $stmt = $db->prepare("SELECT id, name FROM clients ORDER BY name");
                                        $stmt->execute();
                                        $clients = $stmt->fetchAll(PDO::FETCH_ASSOC);

                                        foreach ($clients as $client) {
                                            echo "<option value='{$client['id']}' data-type='client'>{$client['name']}</option>";
                                        }
                                    } catch (PDOException $e) {
                                        // تجاهل الخطأ
                                    }

                                                                    // تعيين الحساب الإعلاني الحالي سيتم تنفيذه عبر JavaScript

                                    ?>
                                </select>
                            </div>
                        </div>

                        <!-- حقل الاكونت مخفي لأننا نضيف الإعلان من الحساب الإعلاني نفسه -->
                        <input type="hidden" id="adAccount" name="adAccount" required>

                        <hr class="ad-form-divider">

                        <div class="text-center mt-4">
                            <button type="submit" class="btn btn-primary add-ad-submit">إضافة اعلان</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة تفاصيل الحساب -->
    <div class="modal fade" id="accountDetailsModal" tabindex="-1" aria-labelledby="accountDetailsModalLabel" role="dialog">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <div class="d-flex justify-content-between align-items-center w-100">
                        <div>
                            <button class="btn btn-sm btn-outline-primary toggle-details">
                                <i class="fas fa-chevron-down"></i>
                            </button>
                            <span class="daily-label">Daily</span>
                            <span class="daily-amount">0.00 EGP</span>
                        </div>
                        <h5 class="modal-title" id="accountDetailsModalLabel" data-account-id="">اسم الحساب</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                </div>
                <div class="modal-body">
                    <div class="account-details-container">
                        <div class="account-details-table-container">
                            <table class="account-details-table">
                                <thead>
                                    <tr>
                                        <th>بدء</th>
                                        <th>الصفحة</th>
                                        <th>البوست</th>
                                        <th>نوع</th>
                                        <th>المبلغ</th>
                                        <th>الصرف</th>
                                        <th>ايام</th>
                                        <th>بالنتج</th>
                                        <th>صرف يومي</th>
                                        <th>باقي بصرف</th>
                                    </tr>
                                </thead>
                                <tbody id="accountDetailsTableBody">
                                    <!-- سيتم ملء هذا الجزء ديناميكيًا -->
                                </tbody>
                            </table>
                        </div>

                        <div class="account-details-footer">
                            <div class="footer-row">
                                <div class="footer-cell">
                                    <span class="footer-label">اجمالي الصرف اليومي</span>
                                    <span class="footer-value total-daily-exchange">0.00 EGP</span>
                                </div>
                                <div class="footer-cell">
                                    <span class="footer-label">اجمالي المتبقي صرفه</span>
                                    <span class="footer-value total-remaining-exchange">0.00 EGP</span>
                                </div>
                                <div class="footer-cell">
                                    <span class="footer-label">مطلوب شحن</span>
                                    <span class="footer-value required-charge">0.00 EGP</span>
                                </div>
                            </div>
                            <div class="footer-row">
                                <div class="footer-cell">
                                    <span class="footer-label">رصيد الحساب الحالي</span>
                                    <span class="footer-value current-balance">0.00 EGP</span>
                                </div>
                                <div class="footer-cell" style="grid-column: span 2;">
                                    <span class="footer-label">رصيد الحساب بعد الاعلانات</span>
                                    <span class="footer-value balance-after-ads">0.00 EGP</span>

                                    <div class="charge-button account-details-add-button" onclick="showChargeHistoryModal(document.getElementById('accountDetailsModalLabel').getAttribute('data-account-id'))">
                                        <i class="fas fa-plus"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة تعديل الإعلان -->
    <div class="modal fade" id="editAdModal" tabindex="-1" aria-labelledby="editAdModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editAdModalLabel">تعديل اعلان</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="editAdForm">
                        <input type="hidden" id="editAdId" name="editAdId">
                        <input type="hidden" id="editAdAccountId" name="editAdAccountId">

                        <!-- عناوين الأعمدة -->
                        <div class="row ad-form-header">
                            <div class="col">تاريخ</div>
                            <div class="col">نوع</div>
                            <div class="col">يومي / اجمالي</div>
                            <div class="col">بوست</div>
                            <div class="col">عدد ايام</div>
                            <div class="col">التكلفة</div>
                            <div class="col">الصرف</div>
                            <div class="col">الاكونت</div>
                        </div>

                        <!-- حقول الإدخال -->
                        <div class="row ad-form-inputs">
                            <div class="col">
                                <input type="date" class="form-control" id="editAdDate" name="editAdDate" required>
                            </div>
                            <div class="col">
                                <input type="text" class="form-control" id="editAdType" name="editAdType" required>
                            </div>
                            <div class="col">
                                <select class="form-select" id="editAdDailyTotal" name="editAdDailyTotal" required>
                                    <option value="يومي">يومي</option>
                                    <option value="اجمالي">اجمالي</option>
                                </select>
                            </div>
                            <div class="col">
                                <input type="text" class="form-control" id="editAdPost" name="editAdPost">
                            </div>
                            <div class="col">
                                <input type="number" class="form-control" id="editAdDays" name="editAdDays" min="1"
                                    value="1" required>
                            </div>
                            <div class="col">
                                <input type="number" class="form-control" id="editAdCost" name="editAdCost" step="0.01"
                                    required>
                            </div>
                            <div class="col">
                                <input type="number" class="form-control" id="editAdExchangeRate"
                                    name="editAdExchangeRate" step="0.01" required>
                            </div>
                            <div class="col">
                                <select class="form-select" id="editAdClient" name="editAdClient" required>
                                    <option value="">اختر العميل</option>
                                    <?php
                                    // جلب العملاء من جدول ad_clients
                                    try {
                                        $stmt = $db->prepare("SELECT id, name FROM ad_clients ORDER BY name");
                                        $stmt->execute();
                                        $adClients = $stmt->fetchAll(PDO::FETCH_ASSOC);

                                        foreach ($adClients as $client) {
                                            echo "<option value='{$client['id']}'>{$client['name']}</option>";
                                        }
                                    } catch (PDOException $e) {
                                        // تجاهل الخطأ
                                    }

                                    // جلب العملاء من جدول clients
                                    try {
                                        $stmt = $db->prepare("SELECT id, name FROM clients ORDER BY name");
                                        $stmt->execute();
                                        $clients = $stmt->fetchAll(PDO::FETCH_ASSOC);

                                        foreach ($clients as $client) {
                                            echo "<option value='{$client['id']}' data-type='client'>{$client['name']}</option>";
                                        }
                                    } catch (PDOException $e) {
                                        // تجاهل الخطأ
                                    }
                                    ?>
                                </select>
                            </div>
                        </div>

                        <!-- حقل الحالة -->
                        <div class="row mt-3">
                            <div class="col-3">
                                <label for="editAdStatus" class="form-label">الحالة</label>
                                <select class="form-select" id="editAdStatus" name="editAdStatus" required>
                                    <option value="نشط">نشط</option>
                                    <option value="متوقف">متوقف</option>
                                </select>
                            </div>
                        </div>

                        <hr class="ad-form-divider">

                        <div class="text-center mt-4">
                            <button type="submit" class="btn btn-primary edit-ad-submit">حفظ التعديلات</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة عرض تاريخ الشحن -->
    <div class="modal fade" id="chargeHistoryModal" tabindex="-1" aria-labelledby="chargeHistoryModalLabel"
        aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header" style="background-color: #f0f8ff; border-bottom: 2px solid #dcf343;">
                    <div class="d-flex justify-content-between align-items-center w-100">
                        <div style="width: 100px;"><?php echo date('d-m-Y'); ?></div>
                        <h5 class="modal-title" id="chargeHistoryModalLabel" style="color: #4a4ad4; font-weight: bold;">
                            شحن فوري</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                </div>
                <div class="modal-body">
                    <div style="border: 1px solid #dcf343; border-radius: 10px; padding: 15px; margin-bottom: 15px;">
                        <table style="width: 100%; border-collapse: collapse;">
                            <thead>
                                <tr style="border-bottom: 1px solid #dcf343;">
                                    <th style="padding: 10px; text-align: center; color: #4a4ad4; font-weight: bold;">
                                        حالة الشحن</th>
                                    <th style="padding: 10px; text-align: center; color: #4a4ad4; font-weight: bold;">
                                        مبلغ الشحن</th>
                                    <th style="padding: 10px; text-align: center; color: #4a4ad4; font-weight: bold;">
                                        كود</th>
                                    <th style="padding: 10px; text-align: center; color: #4a4ad4; font-weight: bold;">
                                        اسم الاكونت</th>
                                </tr>
                            </thead>
                            <tbody id="chargeHistoryTableBody">
                                <!-- سيتم ملء هذا الجزء ديناميكيًا -->
                                <tr style="border-bottom: 1px solid #f0f0f0;">
                                    <td colspan="4" style="padding: 10px; text-align: center;">جاري تحميل البيانات...
                                    </td>
                                </tr>
                            </tbody>
                        </table>

                        <hr style="border-color: #dcf343; margin: 20px 0;">

                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <div style="display: flex; align-items: center;">
                                <div class="add-new-charge-button"
                                    style="width: 40px; height: 40px; background-color: #4CAF50; color: white; border-radius: 50%; display: flex; justify-content: center; align-items: center; margin-left: 10px; cursor: pointer;">
                                    <i class="fas fa-plus"></i>
                                </div>
                            </div>
                            <div style="display: flex; flex-direction: column; align-items: flex-end;"
                                class="modal-footer-totals">
                                <div style="display: flex; margin-bottom: 10px;">
                                    <div style="font-weight: bold; color: #4a4ad4; margin-left: 10px;">اجمالي المبلغ
                                        المطلوب شحنه</div>
                                    <div style="font-weight: bold; font-size: 1.1em;" class="required-total">0.000</div>
                                </div>
                                <div style="display: flex;">
                                    <div style="font-weight: bold; color: #4a4ad4; margin-left: 10px;">اجمالي المبلغ تم
                                        شحنه</div>
                                    <div style="font-weight: bold; font-size: 1.1em;" class="charged-total">0.000</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة إضافة شحن فوري جديد -->
    <div class="modal fade" id="addChargeModal" tabindex="-1" aria-labelledby="addChargeModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-md">
            <div class="modal-content">
                <div class="modal-header" style="background-color: #f0f8ff; border-bottom: 2px solid #dcf343;">
                    <div class="d-flex justify-content-between align-items-center w-100">
                        <div style="width: 30px;"></div>
                        <h5 class="modal-title" id="addChargeModalLabel" style="color: #4a4ad4; font-weight: bold;">
                            إضافة شحن فوري</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                </div>
                <div class="modal-body">
                    <form id="addChargeForm">
                        <div class="mb-3 row">
                            <div class="col-md-3">
                                <label class="form-label" style="color: #4a4ad4; font-weight: bold;">التاريخ</label>
                            </div>
                            <div class="col-md-9">
                                <input type="text" class="form-control" id="chargeDate" name="chargeDate" value="25-8"
                                    readonly>
                            </div>
                        </div>
                        <div class="mb-3 row">
                            <div class="col-md-3">
                                <label class="form-label" style="color: #4a4ad4; font-weight: bold;">اسم الاكونت</label>
                            </div>
                            <div class="col-md-9">
                                <select class="form-select" id="chargeAccount" name="chargeAccount" required>
                                    <option value="">اختر الاكونت</option>
                                    <?php foreach ($fawryAccounts as $account): ?>
                                    <option value="<?php echo $account['id']; ?>">
                                        <?php echo htmlspecialchars($account['name']); ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                        <div class="mb-3 row">
                            <div class="col-md-3">
                                <label class="form-label" style="color: #4a4ad4; font-weight: bold;">كود</label>
                            </div>
                            <div class="col-md-9">
                                <input type="text" class="form-control" id="chargeCode" name="chargeCode" value="3076">
                            </div>
                        </div>
                        <div class="mb-3 row">
                            <div class="col-md-3">
                                <label class="form-label" style="color: #4a4ad4; font-weight: bold;">مبلغ الشحن</label>
                            </div>
                            <div class="col-md-9">
                                <input type="number" class="form-control" id="chargeAmount" name="chargeAmount"
                                    step="0.01" required>
                            </div>
                        </div>

                        <div class="mb-3 row">
                            <div class="col-md-3">
                                <label class="form-label" style="color: #4a4ad4; font-weight: bold;">حالة الشحن</label>
                            </div>
                            <div class="col-md-9">
                                <select class="form-select" id="chargeStatus" name="chargeStatus" required>
                                    <option value="لم يتم">لم يتم</option>
                                    <option value="في انتظار الشحن">في انتظار الشحن</option>
                                    <option value="تم الشحن" selected>تم الشحن</option>
                                </select>
                            </div>
                        </div>

                        <hr style="border-color: #dcf343; margin: 20px 0;">

                        <div class="text-center">
                            <button type="submit" class="btn btn-primary"
                                style="background-color: #4a4ad4; border-color: #4a4ad4; padding: 8px 30px;">إضافة
                                الشحن</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة عرض رصيد حسابات فوري -->
    <div class="modal fade" id="balanceModal" tabindex="-1" aria-labelledby="balanceModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-md">
            <div class="modal-content">
                <div class="modal-header" style="background-color: #f0f8ff; border-bottom: 2px solid #dcf343;">
                    <div class="d-flex justify-content-between align-items-center w-100">
                        <div style="width: 30px;"></div>
                        <h5 class="modal-title" id="balanceModalLabel" style="color: #4a4ad4; font-weight: bold;">رصيد
                            حسابات فوري</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                </div>
                <div class="modal-body">
                    <div style="border: 1px solid #dcf343; border-radius: 10px; padding: 15px; margin-bottom: 15px;">
                        <!-- الرصيد فوري -->
                        <div class="mb-4">
                            <h5 class="text-center mb-3" style="color: #4a4ad4; font-weight: bold;">الرصيد فوري</h5>
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="text-start" style="font-weight: bold; font-size: 1.2em;" id="balanceLeft">
                                    42.301</div>
                                <div class="text-center" style="color: #4CAF50; font-size: 1.5em;">←</div>
                                <div class="text-end" style="font-weight: bold; font-size: 1.2em;" id="balanceRight">
                                    29.801</div>
                            </div>
                        </div>

                        <!-- الصرف اليومي -->
                        <div class="mb-4">
                            <h5 class="text-center mb-3" style="color: #4a4ad4; font-weight: bold;">الصرف اليومي</h5>
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="text-start" style="font-weight: bold; font-size: 1.2em;"
                                    id="dailyExchangeLeft">42.301</div>
                                <div class="text-center" style="color: #4CAF50; font-size: 1.5em;">←</div>
                                <div class="text-end" style="font-weight: bold; font-size: 1.2em;"
                                    id="dailyExchangeRight">29.801</div>
                            </div>
                        </div>

                        <!-- الرصيد بعد الإعلانات بعد مرور يوم -->
                        <div class="mb-4">
                            <h5 class="text-center mb-3" style="color: #4a4ad4; font-weight: bold;">الرصيد بعد الإعلانات
                                بعد مرور يوم</h5>
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="text-start" style="font-weight: bold; font-size: 1.2em;"
                                    id="balanceAfterDayLeft">42.301</div>
                                <div class="text-center" style="color: #4CAF50; font-size: 1.5em;">←</div>
                                <div class="text-end" style="font-weight: bold; font-size: 1.2em;"
                                    id="balanceAfterDayRight">29.801</div>
                            </div>
                        </div>

                        <!-- رصيد محطة الشحن -->
                        <div class="mb-2">
                            <h5 class="text-center mb-3" style="color: #4a4ad4; font-weight: bold;">رصيد محطة الشحن</h5>
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="text-start" style="font-weight: bold; font-size: 1.2em; cursor: pointer;"
                                    id="chargeStationLeft" ondblclick="editChargeStationBalance(this)">42.301</div>
                                <div class="text-center" style="color: #4CAF50; font-size: 1.5em;">←</div>
                                <div class="text-end" style="font-weight: bold; font-size: 1.2em; cursor: pointer;"
                                    id="chargeStationRight" ondblclick="editChargeStationBalance(this)">29.801</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
    // دالة لإزالة خلفية النوافذ المنبثقة عند إغلاقها
    function removeModalBackdrop() {
        try {
            const backdrop = document.querySelector('.modal-backdrop');
            if (backdrop) {
                backdrop.remove();
            }
            document.body.classList.remove('modal-open');
            document.body.style.overflow = '';
            document.body.style.paddingRight = '';
        } catch (error) {
            console.error('خطأ في إزالة خلفية النافذة المنبثقة:', error);
        }
    }

    document.addEventListener('DOMContentLoaded', function() {
        // إضافة مستمعي أحداث لإزالة الخلفية عند إغلاق النوافذ المنبثقة
        document.querySelectorAll('.modal').forEach(modal => {
            modal.addEventListener('hidden.bs.modal', function() {
                removeModalBackdrop();
            });
        });

        // تعيين قيمة حقل adAccount عند تغيير قيمة adAccountIdHidden
        const adAccountIdHidden = document.getElementById('adAccountIdHidden');
        if (adAccountIdHidden) {
            adAccountIdHidden.addEventListener('change', function() {
                const adAccount = document.getElementById('adAccount');
                if (adAccount) {
                    adAccount.value = this.value;
                }
            });
        }

        // إضافة مستمع لأحداث النقر على اسم الحساب
        document.querySelectorAll('.account-name').forEach(function(element) {
            element.addEventListener('click', function() {
                const accountSection = this.closest('.account-card');
                const accountId = accountSection.getAttribute('data-account-id');
                fetchAccountDetails(accountId, accountSection);
            });
        });

        // إضافة مستمع لأحداث النقر على زر إضافة معاملة
        document.querySelectorAll('.add-transaction-button').forEach(function(element) {
            element.addEventListener('click', function() {
                const accountSection = this.closest('.account-card');
                const accountId = accountSection.getAttribute('data-account-id');
                showAddTransactionModal(accountId);
            });
        });

        // إضافة مستمع لأحداث النقر على زر الشحن
        document.querySelectorAll('.charge-button').forEach(function(element) {
            element.addEventListener('click', function() {
                const accountSection = this.closest('.account-card');
                const accountId = accountSection.getAttribute('data-account-id');
                showChargeHistoryModal(accountId);
            });
        });

        // إضافة مستمع لأحداث النقر على زر إضافة شحنة جديدة
        document.querySelector('.add-new-charge-button')?.addEventListener('click', function() {
            showAddChargeModal();
        });

        // إضافة مستمع لأحداث النقر على زر شحن فوري في الهيدر
        document.getElementById('showChargeHistoryBtn').addEventListener('click', function(e) {
            e.preventDefault();
            showChargeHistoryModal();
        });

        // إضافة مستمع لأحداث النقر على زر رصيد حسابات فوري في الهيدر
        document.getElementById('showBalanceModalBtn').addEventListener('click', function(e) {
            e.preventDefault();
            showBalanceModal();
        });

        // إضافة مستمع لأحداث النقر على صف الإعلان
        document.querySelectorAll('tr[data-ad-id]').forEach(function(row) {
            row.addEventListener('dblclick', function() {
                const adId = this.dataset.adId;
                editAd(adId);
            });
        });

        // تحديث حالة الإعلان
        document.querySelectorAll('.ad-status-toggle').forEach(function(element) {
            element.addEventListener('click', function(e) {
                e.preventDefault();
                const adId = this.dataset.adId;
                const currentStatus = this.dataset.status;
                const newStatus = currentStatus === 'نشط' ? 'متوقف' : 'نشط';
                updateAdStatus(adId, newStatus);
            });
        });

        // معالجة نموذج تعديل الإعلان
        document.getElementById('editAdForm').addEventListener('submit', function(e) {
            e.preventDefault();
            saveAdChanges();
        });

        // معالجة نموذج إضافة شحن جديد
        document.getElementById('addChargeForm').addEventListener('submit', function(e) {
            e.preventDefault();
            saveNewCharge();
        });
    });

    // دالة لجلب تفاصيل الحساب
    function fetchAccountDetails(accountId, accountSection) {
        if (!accountSection) {
            accountSection = document.querySelector(`.account-card[data-account-id="${accountId}"]`);
            if (!accountSection) {
                console.error('لم يتم العثور على قسم الحساب');
                return;
            }
        }

        // جلب الإجماليات من تذييل الحساب (التصميم الجديد يستخدم جدول مع عرض أفقي للعنوان والقيمة)
        let dailyExchange = "0.00";
        let remainingExchange = "0.00";
        let requiredCharge = "0.00";
        let currentBalance = "0.00";
        let balanceAfterAds = "0.00";

        try {
            dailyExchange = accountSection.querySelector('table tr:first-child td:nth-child(1) div div:last-child')
                .textContent.trim();
            remainingExchange = accountSection.querySelector(
                'table tr:first-child td:nth-child(2) div div:last-child').textContent.trim();
            requiredCharge = accountSection.querySelector('table tr:first-child td:nth-child(3) div div:last-child')
                .textContent.trim();
            currentBalance = accountSection.querySelector('table tr:last-child td:first-child div div:last-child')
                .textContent.trim();
            balanceAfterAds = accountSection.querySelector('table tr:last-child td:last-child div div:last-child')
                .textContent.trim();

        } catch (error) {
            console.error('خطأ في جلب تفاصيل الحساب:', error);
            // نستمر بالقيم الافتراضية
        }

        // استخدام النافذة المنبثقة الموجودة بالفعل في الصفحة بدلاً من إنشاء واحدة جديدة
        // تعيين القيمة اليومية في الرأس
        const dailyAmountElement = document.querySelector('.daily-amount');
        if (dailyAmountElement) {
            dailyAmountElement.textContent = dailyExchange;
        }

        // تعيين الإجماليات في التذييل
        const totalDailyExchangeElement = document.querySelector('.total-daily-exchange');
        if (totalDailyExchangeElement) {
            totalDailyExchangeElement.textContent = dailyExchange;
        }

        const totalRemainingExchangeElement = document.querySelector('.total-remaining-exchange');
        if (totalRemainingExchangeElement) {
            totalRemainingExchangeElement.textContent = remainingExchange;
        }

        const requiredChargeElement = document.querySelector('.required-charge');
        if (requiredChargeElement) {
            requiredChargeElement.textContent = requiredCharge;
        }

        const currentBalanceElement = document.querySelector('.current-balance');
        if (currentBalanceElement) {
            currentBalanceElement.textContent = currentBalance;
        }

        const balanceAfterAdsElement = document.querySelector('.balance-after-ads');
        if (balanceAfterAdsElement) {
            balanceAfterAdsElement.textContent = balanceAfterAds;
        }

        // تعيين معرف الحساب في عنوان النافذة المنبثقة
        const modalLabel = document.getElementById('accountDetailsModalLabel');
        if (modalLabel) {
            modalLabel.setAttribute('data-account-id', accountId);
        }

        // عرض النافذة المنبثقة الموجودة
        const modal = new bootstrap.Modal(document.getElementById('accountDetailsModal'));
        modal.show();
    }

    // دالة لعرض نافذة إضافة معاملة
    function showAddTransactionModal(accountId) {
        // إزالة أي خلفية موجودة للنوافذ المنبثقة
        removeModalBackdrop();

        // تعيين معرف الحساب الإعلاني في النموذج
        const adAccountIdHidden = document.getElementById('adAccountIdHidden');
        if (adAccountIdHidden) {
            adAccountIdHidden.value = accountId;

            // تحديث قيمة حقل adAccount
            const adAccount = document.getElementById('adAccount');
            if (adAccount) {
                adAccount.value = accountId;
            }
        }

        // عرض نافذة إضافة إعلان
        const modal = new bootstrap.Modal(document.getElementById('addAdModal'));
        modal.show();
    }

    // دالة لعرض نافذة تاريخ الشحن
    function showChargeHistoryModal(accountId) {
        // إزالة أي خلفية موجودة للنوافذ المنبثقة
        removeModalBackdrop();

        // عرض نافذة تاريخ الشحن
        const modal = new bootstrap.Modal(document.getElementById('chargeHistoryModal'));
        modal.show();

        // جلب بيانات الشحن من الخادم
        let url = 'api/charges.php?action=getAll';
        if (accountId) {
            url = `api/charges.php?action=getByAccount&accountId=${accountId}`;
            console.log('عرض تاريخ الشحن للحساب: ' + accountId);
        }

        fetch(url)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // تحديث جدول الشحن
                    updateChargeHistoryTable(data.charges);

                    // حساب الإجماليات
                    calculateChargeTotals(data.charges);
                } else {
                    console.error('Error:', data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
            });
    }

    // دالة لتحديث جدول تاريخ الشحن
    function updateChargeHistoryTable(charges) {
        const tableBody = document.getElementById('chargeHistoryTableBody');
        tableBody.innerHTML = '';

        if (charges && charges.length > 0) {
            charges.forEach(charge => {
                const row = document.createElement('tr');
                row.style.borderBottom = '1px solid #f0f0f0';
                row.setAttribute('data-charge-id', charge.id);

                // تحديد لون الحالة
                let statusClass = '';
                if (charge.status === 'تم الشحن') {
                    statusClass = 'text-success';
                } else if (charge.status === 'في انتظار الشحن') {
                    statusClass = 'text-warning';
                } else {
                    statusClass = 'text-danger';
                }

                row.innerHTML = `
                        <td style="padding: 10px; text-align: center;">
                            <span class="charge-status ${statusClass}" data-charge-id="${charge.id}" data-status="${charge.status}" data-account-id="${charge.account_id}" data-amount="${charge.amount}" style="cursor: pointer;">
                                ${charge.status}
                            </span>
                        </td>
                        <td style="padding: 10px; text-align: center;">${parseFloat(charge.amount).toFixed(3)}</td>
                        <td style="padding: 10px; text-align: center;">${charge.code}</td>
                        <td style="padding: 10px; text-align: center;">${charge.account_name}</td>
                    `;

                tableBody.appendChild(row);
            });

            // إضافة مستمعي الأحداث لتغيير حالة الشحن
            document.querySelectorAll('.charge-status').forEach(statusElement => {
                statusElement.addEventListener('click', function() {
                    const chargeId = this.getAttribute('data-charge-id');
                    const currentStatus = this.getAttribute('data-status');

                    // تحديد الحالة التالية
                    let nextStatus = '';
                    if (currentStatus === 'لم يتم') {
                        nextStatus = 'في انتظار الشحن';
                    } else if (currentStatus === 'في انتظار الشحن') {
                        nextStatus = 'تم الشحن';
                    } else {
                        nextStatus = 'لم يتم';
                    }

                    // تأكيد تغيير الحالة
                    if (confirm(
                        `هل تريد تغيير حالة الشحنة من "${currentStatus}" إلى "${nextStatus}"؟`)) {
                        updateChargeStatus(chargeId, nextStatus, accountId, amount, currentStatus);
                    }
                });
            });
        } else {
            const row = document.createElement('tr');
            row.innerHTML = `
                    <td colspan="4" style="padding: 10px; text-align: center;">لا توجد بيانات شحن</td>
                `;
            tableBody.appendChild(row);
        }
    }

    // دالة لتحديث حالة الشحن
    function updateChargeStatus(chargeId, newStatus, accountId, amount, currentStatus) {
        // إرسال طلب لتحديث حالة الشحن
        fetch('api/charges.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `action=updateStatus&id=${chargeId}&status=${newStatus}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // تحديث واجهة المستخدم مباشرة
                    updateChargeStatusUI(chargeId, newStatus);

                    // تحديث رصيد الحساب إذا تغيرت الحالة من/إلى "تم الشحن"
                    if (newStatus === 'تم الشحن' && currentStatus !== 'تم الشحن') {
                        // إضافة المبلغ إلى الرصيد
                        updateAccountBalanceUI(accountId, amount);
                    } else if (currentStatus === 'تم الشحن' && newStatus !== 'تم الشحن') {
                        // طرح المبلغ من الرصيد
                        updateAccountBalanceUI(accountId, -amount);
                    }

                    // إعادة حساب الإجماليات
                    recalculateChargeTotals();
                } else {
                    alert('حدث خطأ أثناء تحديث حالة الشحنة: ' + (data.message || 'خطأ غير معروف'));
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('حدث خطأ أثناء الاتصال بالخادم');
            });
    }

    // دالة لتحديث واجهة المستخدم بعد تغيير حالة الشحن
    function updateChargeStatusUI(chargeId, newStatus) {
        const statusElement = document.querySelector(`.charge-status[data-charge-id="${chargeId}"]`);
        if (!statusElement) return;

        // تحديث النص والبيانات
        statusElement.textContent = newStatus;
        statusElement.setAttribute('data-status', newStatus);

        // تحديث اللون
        statusElement.classList.remove('text-success', 'text-warning', 'text-danger');
        if (newStatus === 'تم الشحن') {
            statusElement.classList.add('text-success');
        } else if (newStatus === 'في انتظار الشحن') {
            statusElement.classList.add('text-warning');
        } else {
            statusElement.classList.add('text-danger');
        }
    }

    // دالة لإعادة حساب إجماليات الشحن
    function recalculateChargeTotals() {
        const chargeElements = document.querySelectorAll('.charge-status');
        let totalRequired = 0;
        let totalCharged = 0;

        chargeElements.forEach(element => {
            const amount = parseFloat(element.getAttribute('data-amount'));
            const status = element.getAttribute('data-status');

            totalRequired += amount;

            if (status === 'تم الشحن') {
                totalCharged += amount;
            }
        });

        // تحديث الإجماليات في النافذة
        const requiredElement = document.querySelector(
            '#chargeHistoryModal .modal-body .modal-footer-totals .required-total');
        const chargedElement = document.querySelector(
            '#chargeHistoryModal .modal-body .modal-footer-totals .charged-total');

        if (requiredElement) {
            requiredElement.textContent = totalRequired.toFixed(3);
        }

        if (chargedElement) {
            chargedElement.textContent = totalCharged.toFixed(3);
        }
    }

    // دالة لحساب إجماليات الشحن
    function calculateChargeTotals(charges) {
        let totalRequired = 0;
        let totalCharged = 0;

        if (charges && charges.length > 0) {
            charges.forEach(charge => {
                const amount = parseFloat(charge.amount);
                totalRequired += amount;

                if (charge.status === 'تم الشحن') {
                    totalCharged += amount;
                }
            });
        }

        // تحديث الإجماليات في النافذة
        const requiredElement = document.querySelector(
            '#chargeHistoryModal .modal-body .modal-footer-totals .required-total');
        const chargedElement = document.querySelector(
            '#chargeHistoryModal .modal-body .modal-footer-totals .charged-total');

        if (requiredElement) {
            requiredElement.textContent = totalRequired.toFixed(3);
        }

        if (chargedElement) {
            chargedElement.textContent = totalCharged.toFixed(3);
        }
    }

    // دالة لعرض نافذة إضافة شحن جديد
    function showAddChargeModal() {
        // إزالة أي خلفية موجودة للنوافذ المنبثقة
        removeModalBackdrop();

        // عرض نافذة إضافة شحن جديد
        const modal = new bootstrap.Modal(document.getElementById('addChargeModal'));
        modal.show();
    }

    // دالة لعرض نافذة رصيد حسابات فوري
    function showBalanceModal() {
        // إزالة أي خلفية موجودة للنوافذ المنبثقة
        removeModalBackdrop();

        // حساب إجماليات الأرصدة
        calculateBalanceTotals();

        // عرض نافذة رصيد حسابات فوري
        const modal = new bootstrap.Modal(document.getElementById('balanceModal'));
        modal.show();
    }

    // دالة لحساب إجماليات الأرصدة
    function calculateBalanceTotals() {
        // حساب إجمالي الرصيد
        let totalBalance = 0;
        let totalDailyExchange = 0;
        let totalBalanceAfterDay = 0;

        // جمع الأرصدة من جميع الحسابات
        <?php foreach ($fawryAccounts as $account): ?>
        // الرصيد فوري: كل الأموال التي على الحسابات بعد صرف اليوم
        totalBalance += <?php echo $account['balance']; ?>;

        // الصرف اليومي: مجموع الصرف اليومي لكل الإعلانات النشطة
        totalDailyExchange += <?php echo $accountTotals[$account['id']]['total_daily']; ?>;

        // الرصيد بعد الإعلانات بعد مرور يوم: الرصيد الحالي ناقص الصرف اليومي
        totalBalanceAfterDay += <?php echo $account['balance'] - $accountTotals[$account['id']]['total_daily']; ?>;
        <?php endforeach; ?>

        // تحديث القيم في النافذة
        document.getElementById('balanceLeft').textContent = totalBalance.toFixed(3);
        document.getElementById('balanceRight').textContent = (totalBalance / 2).toFixed(3);

        document.getElementById('dailyExchangeLeft').textContent = totalDailyExchange.toFixed(3);
        document.getElementById('dailyExchangeRight').textContent = (totalDailyExchange / 2).toFixed(3);

        document.getElementById('balanceAfterDayLeft').textContent = totalBalanceAfterDay.toFixed(3);
        document.getElementById('balanceAfterDayRight').textContent = (totalBalanceAfterDay / 2).toFixed(3);

        // استرجاع قيمة رصيد محطة الشحن من localStorage إذا كانت موجودة
        const savedChargeStationBalance = localStorage.getItem('chargeStationBalance');
        if (savedChargeStationBalance) {
            const chargeStationBalance = parseFloat(savedChargeStationBalance);
            document.getElementById('chargeStationLeft').textContent = chargeStationBalance.toFixed(3);
            document.getElementById('chargeStationRight').textContent = (chargeStationBalance / 2).toFixed(3);
        } else {
            // قيمة افتراضية إذا لم تكن موجودة
            document.getElementById('chargeStationLeft').textContent = "0.000";
            document.getElementById('chargeStationRight').textContent = "0.000";
        }
    }

    // دالة لتعديل رصيد محطة الشحن
    function editChargeStationBalance(element) {
        const currentValue = parseFloat(element.textContent);
        const newValue = prompt('أدخل رصيد محطة الشحن الجديد:', currentValue.toFixed(3));

        if (newValue !== null && !isNaN(parseFloat(newValue))) {
            const parsedValue = parseFloat(newValue);

            // تحديث القيم في النافذة
            document.getElementById('chargeStationLeft').textContent = parsedValue.toFixed(3);
            document.getElementById('chargeStationRight').textContent = (parsedValue / 2).toFixed(3);

            // حفظ القيمة في localStorage
            localStorage.setItem('chargeStationBalance', parsedValue);
        }
    }

    // دالة لحفظ شحنة جديدة
    function saveNewCharge() {
        const form = document.getElementById('addChargeForm');
        const formData = new FormData(form);
        formData.append('action', 'addCharge');

        // الحصول على معرف الحساب وحالة الشحن
        const accountId = formData.get('chargeAccount');
        const chargeStatus = formData.get('chargeStatus');
        const chargeAmount = parseFloat(formData.get('chargeAmount'));

        // تحويل FormData إلى URLSearchParams
        const params = new URLSearchParams();
        for (const pair of formData) {
            params.append(pair[0], pair[1]);
        }

        // إرسال البيانات إلى الخادم
        fetch('api/charges.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: params
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // إغلاق النافذة المنبثقة
                    const addChargeModal = bootstrap.Modal.getInstance(document.getElementById('addChargeModal'));
                    addChargeModal.hide();

                    // إزالة خلفية النافذة المنبثقة
                    removeModalBackdrop();

                    // إعادة فتح نافذة تاريخ الشحن وتحديث البيانات
                    setTimeout(() => {
                        showChargeHistoryModal();
                    }, 300);

                    // تحديث واجهة المستخدم مباشرة إذا كانت حالة الشحن "تم الشحن"
                    if (chargeStatus === 'تم الشحن') {
                        updateAccountBalanceUI(accountId, chargeAmount);
                    }
                } else {
                    alert('حدث خطأ أثناء حفظ الشحنة: ' + (data.message || 'خطأ غير معروف'));
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('حدث خطأ أثناء الاتصال بالخادم');
            });
    }

    // دالة لتحديث واجهة المستخدم بعد إضافة شحنة
    function updateAccountBalanceUI(accountId, amount) {
        console.log(`تحديث رصيد الحساب ${accountId} بمبلغ ${amount}`);

        // البحث عن قسم الحساب المناسب في الصفحة الرئيسية
        const accountSection = document.querySelector(`.account-card[data-account-id="${accountId}"]`);
        if (!accountSection) {
            console.error(`لم يتم العثور على قسم الحساب للمعرف: ${accountId}`);
            return;
        }

        // تحديث الرصيد الحالي في الصفحة الرئيسية
        const currentBalanceElement = accountSection.querySelector(
            'table tr:last-child td:first-child div div:last-child');
        if (currentBalanceElement) {
            const currentBalanceText = currentBalanceElement.textContent.trim();
            const currentBalance = parseFloat(currentBalanceText.replace(/[^\d.-]/g, ''));
            const newBalance = currentBalance + amount;
            currentBalanceElement.textContent = newBalance.toFixed(2) + ' EGP';
            console.log(`تم تحديث الرصيد الحالي من ${currentBalance} إلى ${newBalance}`);
        } else {
            console.error('لم يتم العثور على عنصر الرصيد الحالي');
        }

        // تحديث مطلوب شحن في الصفحة الرئيسية
        const requiredChargeElement = accountSection.querySelector(
            'table tr:first-child td:nth-child(3) div div:last-child');
        if (requiredChargeElement) {
            const requiredChargeText = requiredChargeElement.textContent.trim();
            const requiredCharge = parseFloat(requiredChargeText.replace(/[^\d.-]/g, ''));
            const newRequiredCharge = Math.max(0, requiredCharge - amount);
            requiredChargeElement.textContent = newRequiredCharge.toFixed(2) + ' EGP';
            console.log(`تم تحديث مطلوب شحن من ${requiredCharge} إلى ${newRequiredCharge}`);
        } else {
            console.error('لم يتم العثور على عنصر مطلوب شحن');
        }

        // تحديث رصيد الحساب بعد الإعلانات في الصفحة الرئيسية
        const balanceAfterAdsElement = accountSection.querySelector(
            'table tr:last-child td:last-child div div:last-child');
        if (balanceAfterAdsElement) {
            const balanceAfterAdsText = balanceAfterAdsElement.textContent.trim();
            const balanceAfterAds = parseFloat(balanceAfterAdsText.replace(/[^\d.-]/g, ''));
            const newBalanceAfterAds = balanceAfterAds + amount;
            balanceAfterAdsElement.textContent = newBalanceAfterAds.toFixed(2) + ' EGP';
            console.log(`تم تحديث رصيد الحساب بعد الإعلانات من ${balanceAfterAds} إلى ${newBalanceAfterAds}`);
        } else {
            console.error('لم يتم العثور على عنصر رصيد الحساب بعد الإعلانات');
        }

        // تحديث النافذة المنبثقة إذا كانت مفتوحة
        const modalLabel = document.getElementById('accountDetailsModalLabel');
        if (modalLabel && modalLabel.getAttribute('data-account-id') === accountId) {
            console.log('تحديث النافذة المنبثقة');

            // تحديث الرصيد الحالي في النافذة المنبثقة
            const modalCurrentBalanceElement = document.querySelector('.current-balance');
            if (modalCurrentBalanceElement) {
                const modalCurrentBalanceText = modalCurrentBalanceElement.textContent.trim();
                const modalCurrentBalance = parseFloat(modalCurrentBalanceText.replace(/[^\d.-]/g, ''));
                const newModalBalance = modalCurrentBalance + amount;
                modalCurrentBalanceElement.textContent = newModalBalance.toFixed(2) + ' EGP';
                console.log(`تم تحديث الرصيد الحالي في النافذة المنبثقة من ${modalCurrentBalance} إلى ${newModalBalance}`);
            }

            // تحديث مطلوب شحن في النافذة المنبثقة
            const modalRequiredChargeElement = document.querySelector('.required-charge');
            if (modalRequiredChargeElement) {
                const modalRequiredChargeText = modalRequiredChargeElement.textContent.trim();
                const modalRequiredCharge = parseFloat(modalRequiredChargeText.replace(/[^\d.-]/g, ''));
                const newModalRequiredCharge = Math.max(0, modalRequiredCharge - amount);
                modalRequiredChargeElement.textContent = newModalRequiredCharge.toFixed(2) + ' EGP';
                console.log(`تم تحديث مطلوب شحن في النافذة المنبثقة من ${modalRequiredCharge} إلى ${newModalRequiredCharge}`);
            }

            // تحديث رصيد الحساب بعد الإعلانات في النافذة المنبثقة
            const modalBalanceAfterAdsElement = document.querySelector('.balance-after-ads');
            if (modalBalanceAfterAdsElement) {
                const modalBalanceAfterAdsText = modalBalanceAfterAdsElement.textContent.trim();
                const modalBalanceAfterAds = parseFloat(modalBalanceAfterAdsText.replace(/[^\d.-]/g, ''));
                const newModalBalanceAfterAds = modalBalanceAfterAds + amount;
                modalBalanceAfterAdsElement.textContent = newModalBalanceAfterAds.toFixed(2) + ' EGP';
                console.log(`تم تحديث رصيد الحساب بعد الإعلانات في النافذة المنبثقة من ${modalBalanceAfterAds} إلى ${newModalBalanceAfterAds}`);
            }
        }
    }

    // دالة لتعديل الإعلان
    function editAd(adId) {
        // جلب بيانات الإعلان من الخادم
        fetch(`api/ads/index.php?action=get&id=${adId}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const ad = data.ad;

                    // ملء النموذج بالبيانات
                    document.getElementById('editAdId').value = ad.id;
                    document.getElementById('editAdAccountId').value = ad.ad_account_id;
                    document.getElementById('editAdDate').value = ad.date;
                    document.getElementById('editAdType').value = ad.type || '';
                    document.getElementById('editAdDailyTotal').value = ad.daily_total || 'اجمالي';
                    document.getElementById('editAdPost').value = ad.post || '';
                    document.getElementById('editAdDays').value = ad.days || 1;
                    document.getElementById('editAdCost').value = ad.cost || 0;
                    document.getElementById('editAdExchangeRate').value = ad.exchange_rate || 0;
                    document.getElementById('editAdClient').value = ad.client_id || '';
                    document.getElementById('editAdStatus').value = ad.status || 'نشط';

                    // عرض النافذة المنبثقة
                    const modal = new bootstrap.Modal(document.getElementById('editAdModal'));
                    modal.show();
                } else {
                    alert('حدث خطأ أثناء جلب بيانات الإعلان');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('حدث خطأ أثناء الاتصال بالخادم');
            });
    }

    // دالة لتحديث حالة الإعلان
    function updateAdStatus(adId, newStatus) {
        // إرسال طلب لتحديث حالة الإعلانعايز الشحن يسمع في  الحساب من غير ما اعمل اعادة تحميل
        fetch('api/ads/index.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `action=updateStatus&id=${adId}&status=${newStatus}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // تحديث واجهة المستخدم
                    const statusElement = document.querySelector(`.ad-status-toggle[data-ad-id="${adId}"]`);
                    if (statusElement) {
                        statusElement.textContent = newStatus;
                        statusElement.dataset.status = newStatus;

                        // تحديث لون الحالة
                        if (newStatus === 'نشط') {
                            statusElement.classList.remove('status-inactive');
                            statusElement.classList.add('status-active');
                        } else {
                            statusElement.classList.remove('status-active');
                            statusElement.classList.add('status-inactive');
                        }
                    }

                    // تحديث الصف بالكامل إذا لزم الأمر
                    const row = document.querySelector(`tr[data-ad-id="${adId}"]`);
                    if (row) {
                        if (newStatus === 'نشط') {
                            row.classList.remove('inactive-ad');
                        } else {
                            row.classList.add('inactive-ad');
                        }
                    }

                    // إعادة حساب الإجماليات
                    // يمكن هنا إضافة كود لإعادة حساب الإجماليات أو إعادة تحميل الصفحة
                } else {
                    alert('حدث خطأ أثناء تحديث حالة الإعلان');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('حدث خطأ أثناء الاتصال بالخادم');
            });
    }

    // دالة لحفظ تغييرات الإعلان
    function saveAdChanges() {
        const form = document.getElementById('editAdForm');
        const formData = new FormData(form);
        formData.append('action', 'update');

        // تحويل FormData إلى URLSearchParams
        const params = new URLSearchParams();
        for (const pair of formData) {
            params.append(pair[0], pair[1]);
        }

        // إرسال البيانات إلى الخادم
        fetch('api/ads/index.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: params
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // إغلاق النافذة المنبثقة
                    const modal = bootstrap.Modal.getInstance(document.getElementById('editAdModal'));
                    modal.hide();

                    // إعادة تحميل الصفحة لعرض التغييرات
                    window.location.reload();
                } else {
                    alert('حدث خطأ أثناء حفظ التغييرات: ' + (data.message || 'خطأ غير معروف'));
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('حدث خطأ أثناء الاتصال بالخادم');
            });
    }
    </script>
</body>

</html>