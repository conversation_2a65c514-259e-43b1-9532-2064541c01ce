<?php
// Include configuration
require_once 'config/config.php';

// Include database connection
require_once 'includes/db.php';

// Include helper functions
require_once 'includes/functions.php';

// Include authentication functions
require_once 'includes/auth.php';

// Check if user is logged in
if (!isLoggedIn()) {
    $_SESSION['flash_message'] = 'يجب تسجيل الدخول للوصول إلى هذه الصفحة';
    $_SESSION['flash_type'] = 'danger';
    redirect(BASE_URL . 'index.php');
}

// جلب بيانات العملاء والإعلانات
try {
    // جلب جميع العملاء الذين لديهم إعلانات
    $clientsQuery = "SELECT DISTINCT c.id, c.name
                    FROM clients c
                    JOIN ads a ON a.client_id = c.id
                    ORDER BY c.name ASC";
    $clientsStmt = $db->prepare($clientsQuery);
    $clientsStmt->execute();
    $clients = $clientsStmt->fetchAll(PDO::FETCH_ASSOC);

    // جلب جميع الحسابات الإعلانية
    $accountsQuery = "SELECT id, name FROM ad_accounts ORDER BY name ASC";
    $accountsStmt = $db->prepare($accountsQuery);
    $accountsStmt->execute();
    $adAccounts = $accountsStmt->fetchAll(PDO::FETCH_ASSOC);

    // جلب جميع الإعلانات مع بيانات العملاء والحسابات
    $today = date('Y-m-d');
        $adsQuery = "
            SELECT 
                a.id, 
                a.type, 
                a.status, 
                a.date AS start_date, 
                a.cost AS daily_budget, 
                a.days, 
                (a.cost * a.days) AS total_cost,
                COALESCE(ac.name, c.name) as client_name,
                COALESCE(ac.type, 'عميل عادي') as client_type,
                aa.name AS account_name,
                CASE 
                    WHEN DATE(a.updated_at) = :today THEN 1 
                    ELSE 0 
                END as updated_today
            FROM ads a
            LEFT JOIN ad_clients ac ON a.client_id = ac.id
            LEFT JOIN clients c ON a.client_id = c.id AND ac.id IS NULL
            LEFT JOIN ad_accounts aa ON a.ad_account_id = aa.id
            ORDER BY client_name ASC, a.status DESC, a.date DESC
        ";

    $adsStmt = $db->prepare($adsQuery);
    $adsStmt->bindParam(':today', $today);
    $adsStmt->execute();
    $ads = $adsStmt->fetchAll(PDO::FETCH_ASSOC);

    // تنظيم الإعلانات حسب العميل والحساب الإعلاني
    $adsGrouped = [];
    foreach ($ads as $ad) {
        $clientName = $ad['client_name'] ?? 'العميل غير محدد ';
        $accountName = $ad['account_name'] ?? 'غير محدد';

        if (!isset($adsGrouped[$clientName])) {
            $adsGrouped[$clientName] = [];
        }

        if (!isset($adsGrouped[$clientName][$accountName])) {
            $adsGrouped[$clientName][$accountName] = [];
        }

        $adsGrouped[$clientName][$accountName][] = $ad;
    }
} catch (PDOException $e) {
    $error = 'خطأ في قاعدة البيانات: ' . $e->getMessage();
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تحضير الحسابات - <?php echo SITE_TITLE; ?></title>

    <!-- Bootstrap RTL CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Google Fonts - Cairo -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap">

    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f8f9fa;
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 20px;
            background-color: white;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .logo {
            height: 40px;
        }

        .page-title {
            color: #4a56e2;
            font-size: 24px;
            font-weight: 600;
            text-align: center;
            margin: 0;
        }

        .content-container {
            max-width: 1200px;
            margin: 30px auto;
            padding: 20px;
        }

        .section-title {
            color: #4a56e2;
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #e6e9ff;
        }

        .search-container {
            position: relative;
            margin-bottom: 20px;
        }

        .search-input {
            width: 100%;
            padding: 10px 15px 10px 40px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }

        .search-icon {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #aaa;
        }

        .ads-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 30px;
        }

        .ads-table th {
            background-color: #4a56e2;
            color: white;
            padding: 12px 15px;
            text-align: center;
            font-weight: 600;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .ads-table td {
            padding: 10px 15px;
            text-align: center;
            border: 1px solid #dcf343;
            vertical-align: middle;
        }

        .ads-table tr:hover {
            background-color: #f5f5f5;
        }

        /* تنسيق الإعلانات حسب حالة التعديل */
        .edited-today {
            background-color: #e6ffe6 !important; /* أخضر فاتح للإعلانات المعدلة اليوم */
        }

        tr:not(.edited-today) {
            background-color: #ffe6e6 !important; /* أحمر فاتح للإعلانات غير المعدلة اليوم */
        }

        tr:not(.active-ad) {
            background-color: #ffffff !important; /* أبيض للإعلانات المتوقفة */
        }

        .client-column {
            background-color: #f8f9ff;
            font-weight: 600;
            color: #4a56e2;
            border-right: 3px solid #4a56e2;
        }

        .status-active {
            color: #28a745;
            font-weight: 600;
        }

        .status-inactive {
            color: #dc3545;
            font-weight: 600;
        }

        .editable {
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .editable:hover {
            background-color: #e9ecef;
        }

        .edit-input {
            width: 100%;
            padding: 5px;
            text-align: center;
            border: 2px solid #4a56e2;
            border-radius: 4px;
        }

        .account-name {
            font-weight: 600;
            color: #6c757d;
            background-color: #f8f9ff;
        }
    </style>
</head>
<body>
    <div class="header">
        <a href="<?php echo BASE_URL; ?>dashboard.php">
            <img src="<?php echo BASE_URL; ?>assets/images/logo.png" alt="<?php echo SITE_TITLE; ?>" class="logo">
        </a>
        <h1 class="page-title">تحضير الحسابات</h1>
        <div></div>
    </div>

    <div class="content-container">
        <div class="section-title">تحضير الحسابات</div>

        <?php if (isset($error)): ?>
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-circle me-2"></i>
                <?php echo $error; ?>
            </div>
        <?php else: ?>
            <div class="search-container">
                <input type="text" id="searchInput" class="search-input" placeholder="البحث...">
                <i class="fas fa-search search-icon"></i>
            </div>

            <div class="table-responsive">
                <table class="ads-table" id="adsTable">
                    <thead>
                        <tr>
                            <th>العميل</th>
                            <th>الاكونت</th>
                            <th>تاريخ بدء</th>
                            <th>نوع</th>
                            <th>حالة</th>
                            <th>يومي / كلي</th>
                            <th>عدد أيام</th>
                            <th>مبلغ</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (empty($adsGrouped)): ?>
                            <tr>
                                <td colspan="8" class="text-center">لا توجد إعلانات لعرضها</td>
                            </tr>
                        <?php else: ?>
                            <?php foreach ($adsGrouped as $clientName => $accounts): ?>
                                <?php $clientRowspan = 0; ?>
                                <?php foreach ($accounts as $accountName => $clientAds): ?>
                                    <?php $clientRowspan += count($clientAds); ?>
                                <?php endforeach; ?>

                                <?php $isFirstAccount = true; ?>
                                <?php foreach ($accounts as $accountName => $clientAds): ?>
                                    <?php $accountRowspan = count($clientAds); ?>
                                    <?php $isFirstAd = true; ?>

                                    <?php foreach ($clientAds as $index => $ad): ?>
                                        <?php
                                            // تحديد لون الخلفية بناءً على ما إذا كان الإعلان تم تعديله اليوم ونشط
                                            $isActive = $ad['status'] == 'مستمر';
                                            $isEditedToday = isset($ad['updated_today']) && $ad['updated_today'];

                                            // نستخدم الفئات CSS بدلاً من تعيين اللون مباشرة
                                            // إعلان نشط تم تعديله اليوم = أخضر
                                            // إعلان نشط لم يتم تعديله اليوم = أحمر
                                            // إعلان غير نشط = أبيض

                                            $editedClass = $isEditedToday ? 'edited-today' : '';
                                            $activeClass = $isActive ? 'active-ad' : '';
                                            $rowClass = trim("$activeClass $editedClass");
                                        ?>
                                        <tr data-ad-id="<?php echo $ad['id']; ?>" class="<?php echo $rowClass; ?>" data-edited="<?php echo $isEditedToday ? '1' : '0'; ?>" data-active="<?php echo $isActive ? '1' : '0'; ?>" style="background-color: <?php echo $isEditedToday ? '#e6ffe6' : ($isActive ? '#ffe6e6' : '#ffffff'); ?>">
                                            <?php if ($isFirstAccount && $isFirstAd): ?>
                                                <td class="client-column" rowspan="<?php echo $clientRowspan; ?>"><?php echo htmlspecialchars($clientName); ?></td>
                                                <?php $isFirstAccount = false; ?>
                                            <?php endif; ?>

                                            <?php if ($isFirstAd): ?>
                                                <td class="account-name" rowspan="<?php echo $accountRowspan; ?>"><?php echo htmlspecialchars($accountName); ?></td>
                                                <?php $isFirstAd = false; ?>
                                            <?php endif; ?>

                                            <td><?php echo date('d-m', strtotime($ad['start_date'])); ?></td>
                                            <td><?php echo htmlspecialchars($ad['type']); ?></td>
                                            <td class="<?php echo $ad['status'] == 'مستمر' ? 'status-active' : 'status-inactive'; ?> editable" data-field="status">
                                                <?php echo htmlspecialchars($ad['status']); ?>
                                            </td>
                                            <td class="editable" data-field="cost"><?php echo htmlspecialchars($ad['daily_budget']); ?></td>
                                            <td class="editable" data-field="days"><?php echo htmlspecialchars($ad['days']); ?></td>
                                            <td class="editable" data-field="total_cost"><?php echo number_format($ad['total_cost'], 3); ?>
                                                <?php if (isset($ad['updated_today']) && $ad['updated_today']): ?>
                                                    <span class="badge bg-success" style="font-size: 8px; margin-right: 3px;">تم التعديل</span>
                                                <?php else: ?>
                                                    <?php if ($ad['status'] == 'مستمر'): ?>
                                                        <span class="badge bg-danger" style="font-size: 8px; margin-right: 3px;">لم يتم التعديل</span>
                                                    <?php else: ?>
                                                        <span class="badge bg-secondary" style="font-size: 8px; margin-right: 3px;">متوقف</span>
                                                    <?php endif; ?>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php endforeach; ?>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        $(document).ready(function() {
            // البحث في الجدول
            $("#searchInput").on("keyup", function() {
                const value = $(this).val().toLowerCase();
                $("#adsTable tbody tr").filter(function() {
                    $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1);
                });
            });

            // تحرير الخلايا بالنقر المزدوج
            $(".editable").on("dblclick", function() {
                const cell = $(this);
                const field = cell.data("field");
                const adId = cell.closest("tr").data("ad-id");
                const currentValue = cell.text().trim();

                // إنشاء حقل الإدخال المناسب
                let inputElement;

                if (field === "status") {
                    // إنشاء قائمة منسدلة للحالة
                    inputElement = $("<select>").addClass("edit-input");
                    const options = ["مستمر", "متوقف"];

                    options.forEach(option => {
                        const optionElement = $("<option>").val(option).text(option);
                        if (option === currentValue) {
                            optionElement.attr("selected", "selected");
                        }
                        inputElement.append(optionElement);
                    });
                } else {
                    // إنشاء حقل نصي للقيم الأخرى
                    inputElement = $("<input>")
                        .attr("type", field === "days" ? "number" : "text")
                        .addClass("edit-input")
                        .val(currentValue);
                }

                // استبدال النص بحقل الإدخال
                cell.html(inputElement);
                inputElement.focus();

                // معالجة الضغط على Enter أو فقدان التركيز
                const handleUpdate = function() {
                    const newValue = inputElement.val();

                    // إرسال البيانات إلى الخادم
                    $.ajax({
                        url: "update_ad.php",
                        type: "POST",
                        data: {
                            ad_id: adId,
                            field: field,
                            value: newValue
                        },
                        success: function(response) {
                            if (response.success) {
                                // تحديث الخلية بالقيمة الجديدة
                                if (field === "status") {
                                    cell.removeClass("status-active status-inactive")
                                        .addClass(newValue === "مستمر" ? "status-active" : "status-inactive");
                                }

                                cell.text(newValue);

                                // تحديث المبلغ الإجمالي إذا تم تغيير التكلفة أو عدد الأيام
                                if (field === "cost" || field === "days") {
                                    const row = cell.closest("tr");
                                    const dailyBudget = parseFloat(row.find("[data-field='cost']").text());
                                    const days = parseInt(row.find("[data-field='days']").text());
                                    const totalCost = dailyBudget * days;

                                    // تحديث خلية المبلغ الإجمالي
                                    row.find("td:last").text(totalCost.toFixed(3));
                                }
                            } else {
                                // إعادة القيمة القديمة في حالة الفشل
                                alert("حدث خطأ أثناء تحديث البيانات: " + response.message);
                                cell.text(currentValue);
                            }
                        },
                        error: function() {
                            alert("حدث خطأ أثناء الاتصال بالخادم");
                            cell.text(currentValue);
                        }
                    });
                };

                // معالجة الضغط على Enter
                inputElement.on("keydown", function(e) {
                    if (e.key === "Enter") {
                        e.preventDefault();
                        handleUpdate();
                    } else if (e.key === "Escape") {
                        // إلغاء التحرير عند الضغط على Escape
                        cell.text(currentValue);
                    }
                });

                // معالجة فقدان التركيز
                inputElement.on("blur", handleUpdate);
            });
        });
    </script>
</body>
</html>
