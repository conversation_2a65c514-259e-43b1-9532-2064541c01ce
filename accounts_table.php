<?php
// تضمين ملف الاتصال بقاعدة البيانات - نفس الطريقة المستخدمة في credit_cards.php
require_once 'config/database.php';

// إنشاء اتصال بقاعدة البيانات
$database = new Database();
$db = $database->getConnection();

// التحقق من وجود اتصال بقاعدة البيانات
if (!isset($db)) {
    die("خطأ في الاتصال بقاعدة البيانات");
}

// جلب بيانات بطاقات الائتمان فقط - نفس الطريقة المستخدمة في credit_cards.php
try {
    $creditCards = [];

    // جلب بطاقات الائتمان
    try {
        $stmt = $db->prepare("
            SELECT *,
                   'credit_card' as card_type,
                   DATEDIFF(payment_due_date, CURDATE()) as days_until_payment
            FROM credit_cards
            ORDER BY name
        ");
        $stmt->execute();
        $creditCards = $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        echo "جدول credit_cards غير موجود: " . $e->getMessage();
    }

    // ترتيب النتائج حسب الاسم
    if (!empty($creditCards)) {
        usort($creditCards, function($a, $b) {
            return strcmp($a['name'], $b['name']);
        });
    }

} catch (PDOException $e) {
    echo "حدث خطأ عند جلب البطاقات: " . $e->getMessage();
    $creditCards = [];
}

// جلب الحسابات الإعلانية المرتبطة بكل بطاقة ائتمان - نفس الطريقة المستخدمة في credit_cards.php
$linkedAccounts = [];
$cardTransactions = [];

try {
    // جلب الحسابات والإعلانات لكل بطاقة
    foreach ($creditCards as $card) {
        $cardId = $card['id'];
        $cardType = 'credit_card'; // نستخدم فقط credit_card

        // جلب الحسابات الإعلانية المرتبطة بهذه البطاقة
        $stmt = $db->prepare("
            SELECT * FROM ad_accounts
            WHERE linked_account_type = :card_type
            AND linked_account_id = :card_id
            ORDER BY name ASC
        ");
        $stmt->bindParam(':card_id', $cardId);
        $stmt->bindParam(':card_type', $cardType);
        $stmt->execute();
        $cardAccounts = $stmt->fetchAll(PDO::FETCH_ASSOC);

        if (!empty($cardAccounts)) {
            $linkedAccounts[$cardId] = $cardAccounts;
            $accountIds = array_column($cardAccounts, 'id');

            if (!empty($accountIds)) {
                $accountIdsStr = implode(',', $accountIds);

                $stmt = $db->prepare("
                    SELECT a.*, aa.name as account_name,
                           COALESCE(ac.name, c.name, aa.name, 'غير محدد') as client_name,
                           a.date as ad_date,
                           a.type as ad_type,
                           a.post as ad_post,
                           a.cost as ad_cost,
                           a.egyptian_cost as ad_spent,
                           a.days as ad_days,
                           a.status as ad_status
                    FROM ads a
                    JOIN ad_accounts aa ON a.ad_account_id = aa.id
                    LEFT JOIN ad_clients ac ON a.client_id = ac.id
                    LEFT JOIN clients c ON a.client_id = c.id
                    WHERE a.ad_account_id IN ($accountIdsStr)
                    ORDER BY a.date DESC
                ");

                if ($stmt->execute()) {
                    $cardAds = $stmt->fetchAll(PDO::FETCH_ASSOC);

                    if (!empty($cardAds)) {
                        $cardTransactions[$cardId] = $cardAds;

                        // حساب القيم الإجمالية لكل الإعلانات (نشطة وغير نشطة)
                        $totalDailySpend = 0;
                        $totalRemainingSpend = 0;
                        $totalSpent = 0;

                        foreach ($cardAds as $ad) {
                            $cost = floatval($ad['cost']);
                            $spent = floatval($ad['egyptian_cost']);
                            $days = intval($ad['days']);

                            // الصرف اليومي = التكلفة ÷ الأيام (مجموع كل الأرقام في عمود "يومي")
                            $dailySpend = $days > 0 ? $cost / $days : 0;
                            $totalDailySpend += $dailySpend;

                            // المتبقي صرفه = التكلفة - المصروف (مجموع كل الأرقام في عمود "متبقي الصرف")
                            $remainingSpend = $cost - $spent;
                            $totalRemainingSpend += $remainingSpend;

                            // إجمالي المصروف (لحساب المديونية)
                            $totalSpent += $spent;
                        }

                        // حفظ القيم المحسوبة في مصفوفة البطاقة
                        foreach ($creditCards as &$cardRef) {
                            if ($cardRef['id'] == $cardId) {
                                $cardRef['calculated_daily_spend'] = $totalDailySpend;
                                $cardRef['calculated_remaining_spend'] = $totalRemainingSpend;
                                $cardRef['calculated_total_spent'] = $totalSpent;

                                // حساب المديونية = إجمالي الصرف - حد الصرف
                                $spendingLimit = floatval($cardRef['balance']); // حد الصرف
                                $debt = $totalSpent - $spendingLimit;
                                $cardRef['calculated_debt'] = $debt > 0 ? $debt : 0;

                                // حساب المستحق = حد الصرف - المديونية
                                $cardRef['calculated_due'] = $spendingLimit - $cardRef['calculated_debt'];

                                // حساب طلب السداد
                                $daysUntilPayment = intval($cardRef['days_until_payment']);
                                $cardRef['payment_request'] = ($daysUntilPayment <= 3 && $daysUntilPayment >= 0) ? $cardRef['calculated_debt'] : 0;

                                break;
                            }
                        }
                    }
                }
            }
        }
    }

} catch (PDOException $e) {
    // في حالة حدوث خطأ، نستخدم مصفوفة فارغة
    $linkedAccounts = [];
    $cardTransactions = [];
}

// استخدام creditCards بدلاً من accounts للتطابق مع credit_cards.php
$accounts = $creditCards;
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اكونتات كريديت كارد - بسام ميديا</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <style>

        .accounts-table thead th
        {
                color: white;
                font-weight: 600;
                text-align: center;
                padding: 15px 12px;
                border: none;
                font-size: 14px;
                white-space: nowrap;
                position: relative;
                background: linear-gradient(135deg, #5a5aff 0%, #4040d4 100%) !important;;

            }
    </style>
    <!-- Google Fonts - Cairo -->

    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap">
</head>
<body>
    <div class="container-fluid p-0">
        <!-- الهيدر مطابق للتصميم بالضبط -->
        <header class="simple-header">
            <!-- الجزء الرئيسي من الهيدر -->
            <div class="header-main">
                <!-- اللوجو -->
                <div class="logo-container">
                    
                        <img src="assets/images/logo.png" alt="" style="width: 70px;">
                   
                </div>

                <!-- العنوان الأزرق في الوسط -->
                <div class="center-title">
                    <h1 class="blue-title">اكونتات كريديت كارد</h1>
                </div>

                <!-- شريط البحث -->
                <div class="search-container">
                    <div class="search-box">
                        <input type="text" class="search-field" placeholder="البحث" id="searchInput">
                        <i class="fas fa-search search-btn"></i>
                    </div>
                </div>
            </div>

            <!-- الشريط السفلي مع أيقونة المجلد -->
            <div class="bottom-bar">
                <div class="folder-section">
                    <i class="fas fa-folder folder-icon"></i>
                    <span class="folder-text" onclick="showTotalsModal()" style="cursor: pointer;">اجماليات</span>
                </div>
            </div>
        </header>

        <!-- جدول الحسابات -->
        <div class="accounts-container">
            <div class="table-responsive">
                <table class="table accounts-table" id="accountsTable">
                    <thead>
                        <tr class="xx">
                            <th>الحسابات</th>
                            <th>حد الدفع</th>
                            <th>المستحق</th>
                            <th>طلب التسديد</th>
                            <th>الصرف اليومي</th>
                            <th>اجمالي المجموعة</th>
                            <th>المتبقي صرفه</th>
                            <th>Pages</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($accounts as $account): ?>
                        <?php
                        // استخدام القيم المحسوبة من credit_cards.php أو القيم الافتراضية
                        $creditLimit = floatval($account['balance']); // حد الدفع = الرصيد

                        // القيم المحسوبة أو القيم الافتراضية
                        $dailySpend = isset($account['calculated_daily_spend']) ? $account['calculated_daily_spend'] : 0;
                        $remainingSpend = isset($account['calculated_remaining_spend']) ? $account['calculated_remaining_spend'] : 0;
                        $debt = isset($account['calculated_debt']) ? $account['calculated_debt'] : 0;
                        $due = isset($account['calculated_due']) ? $account['calculated_due'] : $creditLimit;
                        $paymentRequest = isset($account['payment_request']) ? $account['payment_request'] : 0;
                        $totalCost = isset($account['calculated_total_spent']) ? $account['calculated_total_spent'] : 0;

                        // تحديد لون الصف حسب نوع البطاقة
                        $rowClass = (isset($account['card_type']) && $account['card_type'] === 'visa') ? 'visa-row' : 'credit-row';
                        ?>
                        <tr class="<?php echo $rowClass; ?>">
                            <td class="account-name"><?php echo htmlspecialchars($account['name']); ?></td>
                            <td>EGP <?php echo number_format($creditLimit, 2); ?></td>
                            <td>EGP <?php echo number_format($due, 2); ?></td>
                            <td>EGP <?php echo number_format($paymentRequest, 2); ?></td>
                            <td>EGP <?php echo number_format($dailySpend, 2); ?></td>
                            <td>EGP <?php echo number_format($totalCost, 2); ?></td>
                            <td>EGP <?php echo number_format($remainingSpend, 2); ?></td>
                            <td>
                                <a href="credit_cards.php" class="pages-link">
                                    <span class="pages-text">جديد</span>
                                </a>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- نافذة الإجماليات -->
    <div class="modal fade" id="totalsModal" tabindex="-1" aria-labelledby="totalsModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="totalsModalLabel">إجماليات الحسابات</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="totals-container">
                        <div class="totals-grid">
                            <!-- الصف الأول -->
                            <div class="total-box">
                                <div class="total-label">اجمالي حد الدفع</div>
                                <div class="total-value" id="totalCreditLimit">0</div>
                            </div>
                            <div class="total-box">
                                <div class="total-label">اجمالي المتبقي صرفه</div>
                                <div class="total-value" id="totalRemaining">0</div>
                            </div>
                            <div class="total-box">
                                <div class="total-label">اجمالي الصرف اليومي</div>
                                <div class="total-value" id="totalDailySpend">0</div>
                            </div>
                            <div class="total-box">
                                <div class="total-label">اجمالي المستحق</div>
                                <div class="total-value" id="totalDue">0</div>
                            </div>

                            <!-- الصف الثاني -->
                            <div class="total-box wide">
                                <div class="total-label">اجمالي المديونية الكاملة</div>
                                <div class="total-value" id="totalDebt">0</div>
                            </div>
                            <div class="total-box wide">
                                <div class="total-label">اجمالي طلب التسديد</div>
                                <div class="total-value" id="totalPaymentRequest">0</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- JavaScript للبحث والإجماليات -->
    <script>
    // البحث في الجدول
    document.getElementById('searchInput').addEventListener('keyup', function() {
        const searchValue = this.value.toLowerCase();
        const tableRows = document.querySelectorAll('#accountsTable tbody tr');

        tableRows.forEach(row => {
            const accountName = row.querySelector('.account-name').textContent.toLowerCase();
            if (accountName.includes(searchValue)) {
                row.style.display = '';
            } else {
                row.style.display = 'none';
            }
        });
    });

    // حساب الإجماليات وإظهار البوب أب
    function showTotalsModal() {
        const tableRows = document.querySelectorAll('#accountsTable tbody tr');

        let totalCreditLimit = 0;
        let totalDue = 0;
        let totalPaymentRequest = 0;
        let totalDailySpend = 0;
        let totalCost = 0;
        let totalRemaining = 0;

        tableRows.forEach(row => {
            if (row.style.display !== 'none') { // فقط الصفوف المرئية
                const cells = row.querySelectorAll('td');

                // استخراج الأرقام من النصوص (إزالة EGP والفواصل)
                const creditLimit = parseFloat(cells[1].textContent.replace('EGP ', '').replace(/,/g, '')) || 0;
                const due = parseFloat(cells[2].textContent.replace('EGP ', '').replace(/,/g, '')) || 0;
                const paymentRequest = parseFloat(cells[3].textContent.replace('EGP ', '').replace(/,/g, '')) || 0;
                const dailySpend = parseFloat(cells[4].textContent.replace('EGP ', '').replace(/,/g, '')) || 0;
                const cost = parseFloat(cells[5].textContent.replace('EGP ', '').replace(/,/g, '')) || 0;
                const remaining = parseFloat(cells[6].textContent.replace('EGP ', '').replace(/,/g, '')) || 0;

                totalCreditLimit += creditLimit;
                totalDue += due;
                totalPaymentRequest += paymentRequest;
                totalDailySpend += dailySpend;
                totalCost += cost;
                totalRemaining += remaining;
            }
        });

        // تحديث القيم في البوب أب
        document.getElementById('totalCreditLimit').textContent = formatNumber(totalCreditLimit);
        document.getElementById('totalDue').textContent = formatNumber(totalDue);
        document.getElementById('totalPaymentRequest').textContent = formatNumber(totalPaymentRequest);
        document.getElementById('totalDailySpend').textContent = formatNumber(totalDailySpend);
        document.getElementById('totalDebt').textContent = formatNumber(totalCost);
        document.getElementById('totalRemaining').textContent = formatNumber(totalRemaining);

        // إظهار البوب أب
        const modal = new bootstrap.Modal(document.getElementById('totalsModal'));
        modal.show();
    }

    // تنسيق الأرقام
    function formatNumber(num) {
        return new Intl.NumberFormat('en-US', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        }).format(num);
    }
    </script>

<style>
body {
    background-color: white;
    font-family: 'Cairo', sans-serif;
    margin: 0;
    padding: 0;
}

/* تنسيقات الهيدر مطابق للتصميم بالضبط */
.simple-header {
    width: 100%;
    background-color: #ffffff;
    border-bottom: 2px solid #e0e0e0;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    position: relative;
    z-index: 1000;
    display: block !important;
    visibility: visible !important;
    margin-bottom: 20px;
}

/* الجزء الرئيسي من الهيدر */
.header-main {
    background-color: #ffffff;
    padding: 20px 30px;
    display: flex !important;
    align-items: center;
    justify-content: space-between;
    min-height: 80px;
    width: 100%;
}

.logo-container {
    flex: 0 0 auto;
}

.logo-box {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #4CAF50, #8BC34A);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.logo-icon {
    color: white;
    font-size: 24px;
}

.center-title {
    flex: 1;
    text-align: center;
}

.blue-title {
    color: #4a4ad4;
    font-weight: 600;
    font-size: 24px;
    margin: 0;
    font-family: 'Cairo', sans-serif;
}

.search-container {
    flex: 0 0 auto;
}

/* الشريط السفلي */
.bottom-bar {
    background-color: #f8f9fa;
    padding: 12px 30px;
    border-bottom: 1px solid #e0e0e0;
    min-height: 45px;
    display: flex;
    align-items: center;
}

.folder-section {
    display: flex;
    align-items: center;
    gap: 8px;
}

.folder-icon {
    color: #666;
    font-size: 16px;
}

.folder-text {
    color: #666;
    font-size: 14px;
    font-family: 'Cairo', sans-serif;
}

.search-box {
    display: flex;
    align-items: center;
    background-color: white;
    border: 1px solid #ddd;
    border-radius: 20px;
    padding: 8px 15px;
    min-width: 250px;
}

.search-field {
    border: none;
    outline: none;
    background: transparent;
    flex: 1;
    font-size: 14px;
    color: #333;
    font-family: 'Cairo', sans-serif;
}

.search-field::placeholder {
    color: #999;
}

.search-btn {
    color: #999;
    font-size: 14px;
    cursor: pointer;
    margin-left: 8px;
}

/* تنسيقات الجدول مطابقة للتصميم المطلوب بالضبط */
.accounts-container {
    padding: 0;
    margin: 0;
    width: 100%;
}

.accounts-table {
    width: 100%;
    border-collapse: collapse;
    background-color: white;
    margin: 0;
    font-family: 'Cairo', sans-serif;
    border: none;
}

.accounts-table thead {
    background: linear-gradient(135deg, #5a5aff 0%, #4040d4 100%);
}

.accounts-table thead th {
    color: white;
    font-weight: 600;
    text-align: center;
    padding: 15px 12px;
    border: none;
    font-size: 14px;
    white-space: nowrap;
    position: relative;
}

.accounts-table thead th:not(:last-child)::after {
    content: '';
    position: absolute;
    right: 0;
    top: 20%;
    bottom: 20%;
    width: 1px;
    background-color: rgba(255,255,255,0.3);
}

.accounts-table tbody td {
    text-align: center;
    padding: 12px 8px;
    border: none;
    font-size: 13px;
    color: #333;
    border-bottom: 1px solid #e8e8e8;
    position: relative;
}

.accounts-table tbody td:not(:last-child)::after {
    content: '';
    position: absolute;
    right: 0;
    top: 10%;
    bottom: 10%;
    width: 1px;
    background-color: #e8e8e8;
}

.accounts-table tbody tr {
    background-color: white;
}

.accounts-table tbody tr:nth-child(even) {
    background-color: #f9f9f9;
}

.accounts-table tbody tr:hover {
    background-color: #f0f8ff;
}

.account-name {
    font-weight: 500;
    color: #4a4ad4;
    font-size: 13px;
    direction: rtl;
    text-align: center;
}

/* إزالة الألوان المختلفة للصفوف */
.visa-row, .credit-row {
    background-color: inherit;
}

.visa-row:nth-child(even), .credit-row:nth-child(even) {
    background-color: #f9f9f9;
}

.pages-link {
    text-decoration: none;
    color: #4a4ad4;
    font-weight: 500;
    font-size: 13px;
    display: inline-block;
    padding: 4px 8px;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.pages-link:hover {
    color: #3030b0;
    background-color: #f0f8ff;
    text-decoration: none;
}

.pages-text {
    color: #4a4ad4;
    font-weight: 500;
    font-size: 13px;
}

.pages-text:hover {
    color: #3030b0;
}

/* تنسيق الأرقام والنصوص */
.accounts-table tbody td {
    font-family: 'Cairo', sans-serif;
    direction: ltr;
    text-align: center;
    vertical-align: middle;
}

/* تنسيق خاص لعمود اسم الحساب */
.accounts-table tbody td:last-child {
    direction: rtl;
    text-align: center;
    font-weight: 500;
}

/* تنسيق خاص لعمود Pages */
.accounts-table tbody td:first-child {
    direction: ltr;
    text-align: center;
}

/* تنسيقات نافذة الإجماليات */
.totals-container {
    padding: 20px;
}

.totals-grid {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr 1fr;
    gap: 15px;
    margin-bottom: 20px;
}

.total-box {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 2px solid #4a4ad4;
    border-radius: 8px;
    padding: 15px;
    text-align: center;
    transition: all 0.3s ease;
}

.total-box:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(74, 74, 212, 0.2);
}

.total-box.wide {
    grid-column: span 2;
}

.total-label {
    font-size: 14px;
    color: #4a4ad4;
    font-weight: 600;
    margin-bottom: 8px;
    font-family: 'Cairo', sans-serif;
}

.total-value {
    font-size: 18px;
    color: #28a745;
    font-weight: bold;
    font-family: 'Cairo', sans-serif;
}

/* تنسيق هيدر البوب أب */
.modal-header {
    background: linear-gradient(135deg, #4a4ad4 0%, #3030b0 100%);
    color: white;
    border-bottom: none;
}

.modal-title {
    font-family: 'Cairo', sans-serif;
    font-weight: 600;
}

.btn-close {
    filter: invert(1);
}

/* تنسيق محتوى البوب أب */
.modal-body {
    padding: 0;
}

.modal-content {
    border-radius: 12px;
    overflow: hidden;
    border: none;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

/* تحسين التجاوب */
@media (max-width: 768px) {
    .totals-grid {
        grid-template-columns: 1fr 1fr;
        gap: 10px;
    }

    .total-box.wide {
        grid-column: span 2;
    }

    .total-label {
        font-size: 12px;
    }

    .total-value {
        font-size: 16px;
    }
}
</style>

</body>
</html>
