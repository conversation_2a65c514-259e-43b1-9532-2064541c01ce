<?php
/**
 * صفحة إدارة الحسابات الإعلانية
 */

// بدء الجلسة
session_start();

// تضمين ملف التكوين
require_once 'config/config.php';

// تضمين اتصال قاعدة البيانات
require_once 'includes/db.php';

// تعريف دوال المصادقة محلياً لتجاوز التحقق من الصلاحيات
function isLoggedIn() {
    return true; // دائماً يعتبر المستخدم مسجل الدخول
}

function isAdmin() {
    return true; // دائماً يعتبر المستخدم مدير
}

// تعريف دالة التحويل لتجنب التحويل إلى صفحة أخرى
function redirect($url) {
    // لا تفعل شيئاً
}

// إنشاء جلسة وهمية للمستخدم
$_SESSION['user_id'] = 1;
$_SESSION['user_name'] = 'Admin';
$_SESSION['user_email'] = '<EMAIL>';
$_SESSION['user_is_admin'] = 1;
$_SESSION['logged_in'] = true;

// جلب الحسابات الإعلانية
try {
    $stmt = $db->prepare("
        SELECT a.*,
               CASE
                   WHEN a.linked_account_type = 'credit_card' THEN cc.name
                   WHEN a.linked_account_type = 'fawry' THEN fa.name
                   ELSE NULL
               END as linked_account_name
        FROM ad_accounts a
        LEFT JOIN credit_cards cc ON a.linked_account_id = cc.id AND a.linked_account_type = 'credit_card'
        LEFT JOIN fawry_accounts fa ON a.linked_account_id = fa.id AND a.linked_account_type = 'fawry'
        ORDER BY a.name ASC
    ");
    $stmt->execute();
    $adAccounts = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $_SESSION['flash_message'] = 'حدث خطأ أثناء جلب الحسابات الإعلانية: ' . $e->getMessage();
    $_SESSION['flash_type'] = 'danger';
    $adAccounts = [];
}

// جلب بطاقات الائتمان
try {
    $stmt = $db->prepare("SELECT id, name, balance FROM credit_cards ORDER BY name ASC");
    $stmt->execute();
    $creditCards = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $_SESSION['flash_message'] = 'حدث خطأ أثناء جلب بطاقات الائتمان: ' . $e->getMessage();
    $_SESSION['flash_type'] = 'danger';
    $creditCards = [];
}

// جلب حسابات فوري
try {
    $stmt = $db->prepare("SELECT id, name, balance FROM fawry_accounts ORDER BY name ASC");
    $stmt->execute();
    $fawryAccounts = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $_SESSION['flash_message'] = 'حدث خطأ أثناء جلب حسابات فوري: ' . $e->getMessage();
    $_SESSION['flash_type'] = 'danger';
    $fawryAccounts = [];
}

// جلب الإعلانات المرتبطة بكل حساب
try {
    $stmt = $db->prepare("
        SELECT ad_account_id, COUNT(*) as ad_count, SUM(cost) as total_cost
        FROM ads
        WHERE ad_account_id IS NOT NULL
        GROUP BY ad_account_id
    ");
    $stmt->execute();
    $adStats = [];
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $adStats[$row['ad_account_id']] = [
            'ad_count' => $row['ad_count'],
            'total_cost' => $row['total_cost']
        ];
    }
} catch (PDOException $e) {
    $_SESSION['flash_message'] = 'حدث خطأ أثناء جلب إحصائيات الإعلانات: ' . $e->getMessage();
    $_SESSION['flash_type'] = 'danger';
    $adStats = [];
}

// جلب العملاء المرتبطين بكل حساب
try {
    $stmt = $db->prepare("
        SELECT a.ad_account_id, COUNT(DISTINCT a.client_id) as client_count
        FROM ads a
        WHERE a.ad_account_id IS NOT NULL
        GROUP BY a.ad_account_id
    ");
    $stmt->execute();
    $clientStats = [];
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $clientStats[$row['ad_account_id']] = $row['client_count'];
    }
} catch (PDOException $e) {
    $_SESSION['flash_message'] = 'حدث خطأ أثناء جلب إحصائيات العملاء: ' . $e->getMessage();
    $_SESSION['flash_type'] = 'danger';
    $clientStats = [];
}
?>
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الحسابات الإعلانية - <?php echo SITE_TITLE; ?></title>

    <!-- Bootstrap RTL CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Google Fonts - Cairo -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="assets/css/ad_accounts.css">
</head>
<body>
    <!-- Header -->
    <header class="mb-4">
        <nav class="navbar navbar-expand-lg navbar-light bg-light">
            <div class="container-fluid">
                <a class="navbar-brand" href="<?php echo BASE_URL; ?>">
                    <img src="<?php echo BASE_URL; ?>assets/images/logo.svg" alt="<?php echo SITE_TITLE; ?>" height="40">
                    <?php echo SITE_TITLE; ?>
                </a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav me-auto mb-2 mb-lg-0">
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo BASE_URL; ?>">الرئيسية</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo BASE_URL; ?>dashboard.php">لوحة التحكم</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo BASE_URL; ?>ad_clients.php">عملاء الإعلانات</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="<?php echo BASE_URL; ?>ad_accounts.php">الحسابات الإعلانية</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo BASE_URL; ?>ad_accounts_report.php">حسابات الإعلانات</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo BASE_URL; ?>accounts_fawry.php">اكونتات فوري</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo BASE_URL; ?>salaries.php">المرتبات</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo BASE_URL; ?>admin/">الإدارة</a>
                        </li>
                    </ul>
                    <ul class="navbar-nav">
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-user"></i> Admin
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="navbarDropdown">
                                <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>profile.php">الملف الشخصي</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>logout.php">تسجيل الخروج</a></li>
                            </ul>
                        </li>
                    </ul>
                </div>
            </div>
        </nav>
    </header>
    <?php if (isset($_SESSION['flash_message'])): ?>
        <div class="alert alert-<?php echo $_SESSION['flash_type']; ?> alert-dismissible fade show" role="alert">
            <?php echo $_SESSION['flash_message']; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        <?php
            unset($_SESSION['flash_message']);
            unset($_SESSION['flash_type']);
        ?>
    <?php endif; ?>

    <!-- Main Content -->
    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">إدارة الحسابات الإعلانية</h5>
                        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addAccountModal">
                            <i class="fas fa-plus"></i> إضافة حساب جديد
                        </button>
                    </div>
                    <div class="card-body">
                        <!-- Search Box -->
                        <div class="search-container mb-4">
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-search"></i></span>
                                <input type="text" id="searchInput" class="form-control" placeholder="البحث في الحسابات الإعلانية...">
                            </div>
                        </div>

                        <!-- Accounts Table -->
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>اسم الحساب</th>
                                        <th>الحالة</th>
                                        <th>الرصيد</th>
                                        <th>حد الصرف</th>
                                        <th>مرتبط بـ</th>
                                        <th>عدد الإعلانات</th>
                                        <th>عدد العملاء</th>
                                        <th>ملاحظات</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="accountsTableBody">
                                    <?php if (empty($adAccounts)): ?>
                                        <tr>
                                            <td colspan="10" class="text-center">لا توجد حسابات إعلانية</td>
                                        </tr>
                                    <?php else: ?>
                                        <?php foreach ($adAccounts as $index => $account): ?>
                                            <tr>
                                                <td><?php echo $index + 1; ?></td>
                                                <td><?php echo htmlspecialchars($account['name']); ?></td>
                                                <td>
                                                    <span class="badge <?php echo $account['status'] === 'نشط' ? 'bg-success' : 'bg-danger'; ?>">
                                                        <?php echo htmlspecialchars($account['status']); ?>
                                                    </span>
                                                </td>
                                                <td><?php echo number_format($account['balance'], 2); ?></td>
                                                <td><?php echo number_format($account['spending_limit'], 2); ?></td>
                                                <td>
                                                    <?php if ($account['linked_account_type'] != 'none' && !empty($account['linked_account_name'])): ?>
                                                        <span class="badge bg-info">
                                                            <?php echo $account['linked_account_type'] == 'credit_card' ? 'كريديت كارد' : 'فوري'; ?>:
                                                            <?php echo htmlspecialchars($account['linked_account_name']); ?>
                                                        </span>
                                                    <?php else: ?>
                                                        <span class="badge bg-secondary">غير مرتبط</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td><?php echo $adStats[$account['id']]['ad_count'] ?? 0; ?></td>
                                                <td><?php echo $clientStats[$account['id']] ?? 0; ?></td>
                                                <td><?php echo htmlspecialchars($account['notes'] ?? ''); ?></td>
                                                <td>
                                                    <div class="btn-group">
                                                        <button type="button" class="btn btn-sm btn-primary edit-account-btn"
                                                                data-id="<?php echo $account['id']; ?>"
                                                                data-name="<?php echo htmlspecialchars($account['name']); ?>"
                                                                data-status="<?php echo htmlspecialchars($account['status']); ?>"
                                                                data-balance="<?php echo $account['balance']; ?>"
                                                                data-spending-limit="<?php echo $account['spending_limit']; ?>"
                                                                data-notes="<?php echo htmlspecialchars($account['notes'] ?? ''); ?>"
                                                                data-linked-type="<?php echo htmlspecialchars($account['linked_account_type']); ?>"
                                                                data-linked-id="<?php echo $account['linked_account_id'] ?? ''; ?>">
                                                            <i class="fas fa-edit"></i>
                                                        </button>
                                                        <button type="button" class="btn btn-sm btn-danger delete-account-btn"
                                                                data-id="<?php echo $account['id']; ?>"
                                                                data-name="<?php echo htmlspecialchars($account['name']); ?>">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal إضافة حساب جديد -->
    <div class="modal fade" id="addAccountModal" tabindex="-1" aria-labelledby="addAccountModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addAccountModalLabel">إضافة حساب إعلاني جديد</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="addAccountForm">
                        <div class="mb-3">
                            <label for="accountName" class="form-label">اسم الحساب</label>
                            <input type="text" class="form-control" id="accountName" name="name" required>
                        </div>

                        <div class="mb-3">
                            <label for="accountStatus" class="form-label">الحالة</label>
                            <select class="form-control" id="accountStatus" name="status" required>
                                <option value="نشط">نشط</option>
                                <option value="متوقف">متوقف</option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="accountBalance" class="form-label">الرصيد</label>
                            <input type="number" class="form-control" id="accountBalance" name="balance" step="0.01" required>
                        </div>

                        <div class="mb-3">
                            <label for="accountSpendingLimit" class="form-label">حد الصرف</label>
                            <input type="number" class="form-control" id="accountSpendingLimit" name="spending_limit" step="0.01" required>
                        </div>

                        <div class="mb-3">
                            <label for="accountLinkedType" class="form-label">ربط الحساب مع</label>
                            <select class="form-control" id="accountLinkedType" name="linked_account_type">
                                <option value="none">غير مرتبط</option>
                                <option value="credit_card">كريديت كارد</option>
                                <option value="fawry">فوري</option>
                            </select>
                        </div>

                        <div class="mb-3 linked-account-container" id="creditCardContainer" style="display: none;">
                            <label for="accountCreditCard" class="form-label">اختر بطاقة الائتمان</label>
                            <select class="form-control" id="accountCreditCard" name="credit_card_id">
                                <option value="">اختر بطاقة الائتمان</option>
                                <?php foreach ($creditCards as $card): ?>
                                <option value="<?php echo $card['id']; ?>"><?php echo htmlspecialchars($card['name']); ?> (<?php echo number_format($card['balance'], 2); ?>)</option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="mb-3 linked-account-container" id="fawryContainer" style="display: none;">
                            <label for="accountFawry" class="form-label">اختر حساب فوري</label>
                            <select class="form-control" id="accountFawry" name="fawry_id">
                                <option value="">اختر حساب فوري</option>
                                <?php foreach ($fawryAccounts as $fawry): ?>
                                <option value="<?php echo $fawry['id']; ?>"><?php echo htmlspecialchars($fawry['name']); ?> (<?php echo number_format($fawry['balance'], 2); ?>)</option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="accountNotes" class="form-label">ملاحظات</label>
                            <textarea class="form-control" id="accountNotes" name="notes" rows="3"></textarea>
                        </div>

                        <div class="text-center">
                            <button type="submit" class="btn btn-primary">إضافة الحساب</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal تعديل حساب -->
    <div class="modal fade" id="editAccountModal" tabindex="-1" aria-labelledby="editAccountModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editAccountModalLabel">تعديل حساب إعلاني</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="editAccountForm">
                        <input type="hidden" id="editAccountId" name="id">

                        <div class="mb-3">
                            <label for="editAccountName" class="form-label">اسم الحساب</label>
                            <input type="text" class="form-control" id="editAccountName" name="name" required>
                        </div>

                        <div class="mb-3">
                            <label for="editAccountStatus" class="form-label">الحالة</label>
                            <select class="form-control" id="editAccountStatus" name="status" required>
                                <option value="نشط">نشط</option>
                                <option value="متوقف">متوقف</option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="editAccountBalance" class="form-label">الرصيد</label>
                            <input type="number" class="form-control" id="editAccountBalance" name="balance" step="0.01" required>
                        </div>

                        <div class="mb-3">
                            <label for="editAccountSpendingLimit" class="form-label">حد الصرف</label>
                            <input type="number" class="form-control" id="editAccountSpendingLimit" name="spending_limit" step="0.01" required>
                        </div>

                        <div class="mb-3">
                            <label for="editAccountLinkedType" class="form-label">ربط الحساب مع</label>
                            <select class="form-control" id="editAccountLinkedType" name="linked_account_type">
                                <option value="none">غير مرتبط</option>
                                <option value="credit_card">كريديت كارد</option>
                                <option value="fawry">فوري</option>
                            </select>
                        </div>

                        <div class="mb-3 linked-account-container" id="editCreditCardContainer" style="display: none;">
                            <label for="editAccountCreditCard" class="form-label">اختر بطاقة الائتمان</label>
                            <select class="form-control" id="editAccountCreditCard" name="credit_card_id">
                                <option value="">اختر بطاقة الائتمان</option>
                                <?php foreach ($creditCards as $card): ?>
                                <option value="<?php echo $card['id']; ?>"><?php echo htmlspecialchars($card['name']); ?> (<?php echo number_format($card['balance'], 2); ?>)</option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="mb-3 linked-account-container" id="editFawryContainer" style="display: none;">
                            <label for="editAccountFawry" class="form-label">اختر حساب فوري</label>
                            <select class="form-control" id="editAccountFawry" name="fawry_id">
                                <option value="">اختر حساب فوري</option>
                                <?php foreach ($fawryAccounts as $fawry): ?>
                                <option value="<?php echo $fawry['id']; ?>"><?php echo htmlspecialchars($fawry['name']); ?> (<?php echo number_format($fawry['balance'], 2); ?>)</option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="editAccountNotes" class="form-label">ملاحظات</label>
                            <textarea class="form-control" id="editAccountNotes" name="notes" rows="3"></textarea>
                        </div>

                        <div class="text-center">
                            <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal حذف حساب -->
    <div class="modal fade" id="deleteAccountModal" tabindex="-1" aria-labelledby="deleteAccountModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="deleteAccountModalLabel">حذف حساب إعلاني</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>هل أنت متأكد من حذف الحساب الإعلاني "<span id="deleteAccountName"></span>"؟</p>
                    <p class="text-danger">سيتم إزالة ارتباط هذا الحساب من جميع الإعلانات المرتبطة به.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-danger" id="confirmDeleteBtn">حذف</button>
                </div>
            </div>
        </div>
    </div>

    <!-- jQuery and Bootstrap JS -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Custom JS -->
    <script src="assets/js/ad_accounts.js"></script>
</body>
</html>
