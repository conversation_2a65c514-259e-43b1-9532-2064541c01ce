<?php
/**
 * صفحة إدارة الحسابات الإعلانية - نسخة مباشرة بدون تحقق من الصلاحيات
 */

// بدء الجلسة
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// تضمين ملف التكوين
require_once 'config/config.php';

// تضمين اتصال قاعدة البيانات
require_once 'includes/db.php';

// تعريف دوال المصادقة محلياً لتجاوز التحقق من الصلاحيات
function isLoggedIn() {
    return true; // دائماً يعتبر المستخدم مسجل الدخول
}

function isAdmin() {
    return true; // دائماً يعتبر المستخدم مدير
}

// تعريف دالة التحويل لتجنب التحويل إلى صفحة أخرى
function redirect($url) {
    // لا تفعل شيئاً
}

// إنشاء جلسة وهمية للمستخدم
$_SESSION['user_id'] = 1;
$_SESSION['user_name'] = 'Admin';
$_SESSION['user_email'] = '<EMAIL>';
$_SESSION['user_is_admin'] = 1;
$_SESSION['logged_in'] = true;

// جلب الحسابات الإعلانية
try {
    $stmt = $db->prepare("
        SELECT a.*,
               CASE
                   WHEN a.linked_account_type = 'credit_card' THEN cc.name
                   WHEN a.linked_account_type = 'visa' THEN vc.name
                   WHEN a.linked_account_type = 'fawry' THEN fa.name
                   ELSE NULL
               END as linked_account_name
        FROM ad_accounts a
        LEFT JOIN credit_cards cc ON a.linked_account_id = cc.id AND a.linked_account_type = 'credit_card'
        LEFT JOIN visa_cards vc ON a.linked_account_id = vc.id AND a.linked_account_type = 'visa'
        LEFT JOIN fawry_accounts fa ON a.linked_account_id = fa.id AND a.linked_account_type = 'fawry'
        ORDER BY a.name ASC
    ");
    $stmt->execute();
    $adAccounts = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $_SESSION['flash_message'] = 'حدث خطأ أثناء جلب الحسابات الإعلانية: ' . $e->getMessage();
    $_SESSION['flash_type'] = 'danger';
    $adAccounts = [];
}

// جلب بطاقات الائتمان
try {
    $stmt = $db->prepare("SELECT id, name, balance FROM credit_cards ORDER BY name ASC");
    $stmt->execute();
    $creditCards = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $_SESSION['flash_message'] = 'حدث خطأ أثناء جلب بطاقات الائتمان: ' . $e->getMessage();
    $_SESSION['flash_type'] = 'danger';
    $creditCards = [];
}

// جلب حسابات فوري
try {
    $stmt = $db->prepare("SELECT id, name, balance FROM fawry_accounts ORDER BY name ASC");
    $stmt->execute();
    $fawryAccounts = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $_SESSION['flash_message'] = 'حدث خطأ أثناء جلب حسابات فوري: ' . $e->getMessage();
    $_SESSION['flash_type'] = 'danger';
    $fawryAccounts = [];
}

// جلب الإعلانات المرتبطة بكل حساب
try {
    $stmt = $db->prepare("
        SELECT ad_account_id, COUNT(*) as ad_count, SUM(cost) as total_cost
        FROM ads
        WHERE ad_account_id IS NOT NULL
        GROUP BY ad_account_id
    ");
    $stmt->execute();
    $adStats = [];
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $adStats[$row['ad_account_id']] = [
            'ad_count' => $row['ad_count'],
            'total_cost' => $row['total_cost']
        ];
    }
} catch (PDOException $e) {
    $_SESSION['flash_message'] = 'حدث خطأ أثناء جلب إحصائيات الإعلانات: ' . $e->getMessage();
    $_SESSION['flash_type'] = 'danger';
    $adStats = [];
}

// جلب العملاء المرتبطين بكل حساب
try {
    $stmt = $db->prepare("
        SELECT a.ad_account_id, COUNT(DISTINCT a.client_id) as client_count
        FROM ads a
        WHERE a.ad_account_id IS NOT NULL
        GROUP BY a.ad_account_id
    ");
    $stmt->execute();
    $clientStats = [];
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $clientStats[$row['ad_account_id']] = $row['client_count'];
    }
} catch (PDOException $e) {
    $_SESSION['flash_message'] = 'حدث خطأ أثناء جلب إحصائيات العملاء: ' . $e->getMessage();
    $_SESSION['flash_type'] = 'danger';
    $clientStats = [];
}
?>
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الحسابات الإعلانية - <?php echo SITE_TITLE; ?></title>

    <!-- Bootstrap RTL CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Google Fonts - Cairo -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="assets/css/ad_accounts.css">
</head>
<body>
    <!-- Header -->
    <header class="mb-4">
        <nav class="navbar navbar-expand-lg navbar-light bg-light">
            <div class="container-fluid">
                <a class="navbar-brand" href="<?php echo BASE_URL; ?>">
                    <img src="<?php echo BASE_URL; ?>assets/images/logo.svg" alt="<?php echo SITE_TITLE; ?>" height="40">
                    <?php echo SITE_TITLE; ?>
                </a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav me-auto mb-2 mb-lg-0">
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo BASE_URL; ?>">الرئيسية</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo BASE_URL; ?>dashboard.php">لوحة التحكم</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo BASE_URL; ?>ad_clients.php">عملاء الإعلانات</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="<?php echo BASE_URL; ?>ad_accounts_direct.php">الحسابات الإعلانية</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo BASE_URL; ?>ad_accounts_report.php">حسابات الإعلانات</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo BASE_URL; ?>accounts_fawry.php">اكونتات فوري</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo BASE_URL; ?>salaries.php">المرتبات</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo BASE_URL; ?>admin/">الإدارة</a>
                        </li>
                    </ul>
                    <ul class="navbar-nav">
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-user"></i> Admin
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="navbarDropdown">
                                <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>profile.php">الملف الشخصي</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>logout.php">تسجيل الخروج</a></li>
                            </ul>
                        </li>
                    </ul>
                </div>
            </div>
        </nav>
    </header>
    <?php if (isset($_SESSION['flash_message'])): ?>
        <div class="alert alert-<?php echo $_SESSION['flash_type']; ?> alert-dismissible fade show" role="alert">
            <?php echo $_SESSION['flash_message']; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        <?php
            unset($_SESSION['flash_message']);
            unset($_SESSION['flash_type']);
        ?>
    <?php endif; ?>

    <!-- Main Content -->
    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">إدارة الحسابات الإعلانية</h5>
                        <div class="d-flex gap-2 flex-wrap">
                            <!-- أزرار الشحن -->
                            <div class="btn-group" role="group" aria-label="أزرار الشحن">
                                <button type="button" class="btn btn-outline-success" data-bs-toggle="modal" data-bs-target="#chargeCreditCardModal">
                                    <i class="fas fa-plus-circle"></i> شحن كريديت كارد
                                </button>
                                <button type="button" class="btn btn-outline-warning" data-bs-toggle="modal" data-bs-target="#chargeVisaModal">
                                    <i class="fas fa-plus-circle"></i> شحن فيزا
                                </button>
                                <button type="button" class="btn btn-outline-info" data-bs-toggle="modal" data-bs-target="#chargeFawryModal">
                                    <i class="fas fa-plus-circle"></i> شحن فوري
                                </button>
                            </div>

                            <!-- أزرار الإضافة -->
                            <div class="btn-group" role="group" aria-label="أزرار الإضافة">
                                <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#addCreditCardModal">
                                    <i class="fas fa-credit-card"></i> إضافة كريديت كارد
                                </button>
                                <button type="button" class="btn btn-warning" data-bs-toggle="modal" data-bs-target="#addVisaModal">
                                    <i class="fas fa-credit-card"></i> إضافة فيزا
                                </button>
                                <button type="button" class="btn btn-info" data-bs-toggle="modal" data-bs-target="#addFawryAccountModal">
                                    <i class="fas fa-wallet"></i> إضافة حساب فوري
                                </button>
                                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addAccountModal">
                                    <i class="fas fa-plus"></i> إضافة حساب جديد
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <!-- Search Box -->
                        <div class="search-container mb-4">
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-search"></i></span>
                                <input type="text" id="searchInput" class="form-control" placeholder="البحث في الحسابات الإعلانية...">
                            </div>
                        </div>

                        <!-- Accounts Table -->
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>اسم الحساب</th>
                                        <th>الحالة</th>
                                        <th>الرصيد</th>
                                        <th>حد الصرف</th>
                                        <th>مرتبط بـ</th>
                                        <th>عدد الإعلانات</th>
                                        <th>عدد العملاء</th>
                                        <th>ملاحظات</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="accountsTableBody">
                                    <?php if (empty($adAccounts)): ?>
                                        <tr>
                                            <td colspan="10" class="text-center">لا توجد حسابات إعلانية</td>
                                        </tr>
                                    <?php else: ?>
                                        <?php foreach ($adAccounts as $index => $account): ?>
                                            <tr>
                                                <td><?php echo $index + 1; ?></td>
                                                <td><?php echo htmlspecialchars($account['name']); ?></td>
                                                <td>
                                                    <span class="badge <?php echo $account['status'] === 'نشط' ? 'bg-success' : 'bg-danger'; ?>">
                                                        <?php echo htmlspecialchars($account['status']); ?>
                                                    </span>
                                                </td>
                                                <td><?php echo number_format($account['balance'], 2); ?></td>
                                                <td><?php echo number_format($account['spending_limit'], 2); ?></td>
                                                <td>
                                                    <?php if ($account['linked_account_type'] != 'none' && !empty($account['linked_account_name'])): ?>
                                                        <span class="badge bg-info">
                                                            <?php
                                                                $typeNames = [
                                                                    'credit_card' => 'كريديت كارد',
                                                                    'visa' => 'فيزا',
                                                                    'fawry' => 'فوري'
                                                                ];
                                                                echo $typeNames[$account['linked_account_type']] ?? $account['linked_account_type'];
                                                            ?>:
                                                            <?php echo htmlspecialchars($account['linked_account_name']); ?>
                                                        </span>
                                                    <?php else: ?>
                                                        <span class="badge bg-secondary">غير مرتبط</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td><?php echo $adStats[$account['id']]['ad_count'] ?? 0; ?></td>
                                                <td><?php echo $clientStats[$account['id']] ?? 0; ?></td>
                                                <td><?php echo htmlspecialchars($account['notes'] ?? ''); ?></td>
                                                <td>
                                                    <div class="btn-group">
                                                        <button type="button" class="btn btn-sm btn-success charge-account-btn"
                                                                data-id="<?php echo $account['id']; ?>"
                                                                data-name="<?php echo htmlspecialchars($account['name']); ?>"
                                                                data-balance="<?php echo $account['balance']; ?>"
                                                                data-linked-type="<?php echo htmlspecialchars($account['linked_account_type']); ?>"
                                                                data-linked-id="<?php echo $account['linked_account_id'] ?? ''; ?>"
                                                                title="شحن الحساب">
                                                            <i class="fas fa-plus-circle"></i>
                                                        </button>
                                                        <button type="button" class="btn btn-sm btn-primary edit-account-btn"
                                                                data-id="<?php echo $account['id']; ?>"
                                                                data-name="<?php echo htmlspecialchars($account['name']); ?>"
                                                                data-status="<?php echo htmlspecialchars($account['status']); ?>"
                                                                data-balance="<?php echo $account['balance']; ?>"
                                                                data-spending-limit="<?php echo $account['spending_limit']; ?>"
                                                                data-notes="<?php echo htmlspecialchars($account['notes'] ?? ''); ?>"
                                                                data-linked-type="<?php echo htmlspecialchars($account['linked_account_type']); ?>"
                                                                data-linked-id="<?php echo $account['linked_account_id'] ?? ''; ?>"
                                                                title="تعديل الحساب">
                                                            <i class="fas fa-edit"></i>
                                                        </button>
                                                        <button type="button" class="btn btn-sm btn-danger delete-account-btn"
                                                                data-id="<?php echo $account['id']; ?>"
                                                                data-name="<?php echo htmlspecialchars($account['name']); ?>"
                                                                title="حذف الحساب">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal إضافة حساب جديد -->
    <div class="modal fade" id="addAccountModal" tabindex="-1" aria-labelledby="addAccountModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addAccountModalLabel">إضافة حساب إعلاني جديد</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="addAccountForm">
                        <div class="mb-3">
                            <label for="accountName" class="form-label">اسم الحساب</label>
                            <input type="text" class="form-control" id="accountName" name="name" required>
                        </div>

                        <div class="mb-3">
                            <label for="accountStatus" class="form-label">الحالة</label>
                            <select class="form-control" id="accountStatus" name="status" required>
                                <option value="نشط">نشط</option>
                                <option value="متوقف">متوقف</option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="accountBalance" class="form-label">الرصيد</label>
                            <input type="number" class="form-control" id="accountBalance" name="balance" step="0.01" required>
                        </div>

                        <div class="mb-3">
                            <label for="accountSpendingLimit" class="form-label">حد الصرف</label>
                            <input type="number" class="form-control" id="accountSpendingLimit" name="spending_limit" step="0.01" required>
                        </div>

                        <div class="mb-3">
                            <label for="accountLinkedType" class="form-label">ربط الحساب مع</label>
                            <select class="form-control" id="accountLinkedType" name="linked_account_type">
                                <option value="none">غير مرتبط</option>
                                <option value="credit_card">كريديت كارد</option>
                                <option value="visa">فيزا</option>
                                <option value="fawry">فوري</option>
                            </select>
                        </div>

                        <div class="mb-3 linked-account-container" id="creditCardContainer" style="display: none;">
                            <label for="accountCreditCard" class="form-label">اختر بطاقة الائتمان</label>
                            <select class="form-control" id="accountCreditCard" name="credit_card_id">
                                <option value="">اختر بطاقة الائتمان</option>
                                <?php foreach ($creditCards as $card): ?>
                                <option value="<?php echo $card['id']; ?>"><?php echo htmlspecialchars($card['name']); ?> (<?php echo number_format($card['balance'], 2); ?>)</option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="mb-3 linked-account-container" id="visaContainer" style="display: none;">
                            <label for="accountVisa" class="form-label">اختر فيزا</label>
                            <select class="form-control" id="accountVisa" name="visa_id">
                                <option value="">اختر فيزا</option>
                                <?php
                                // جلب بطاقات الفيزا
                                try {
                                    $stmt = $db->prepare("SELECT id, name, base_balance FROM visa_cards ORDER BY name ASC");
                                    $stmt->execute();
                                    $visaCards = $stmt->fetchAll(PDO::FETCH_ASSOC);
                                } catch (PDOException $e) {
                                    $visaCards = [];
                                }

                                foreach ($visaCards as $visa):
                                ?>
                                <option value="<?php echo $visa['id']; ?>"><?php echo htmlspecialchars($visa['name']); ?> (<?php echo number_format($visa['base_balance'], 2); ?>)</option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="mb-3 linked-account-container" id="fawryContainer" style="display: none;">
                            <label for="accountFawry" class="form-label">اختر حساب فوري</label>
                            <select class="form-control" id="accountFawry" name="fawry_id">
                                <option value="">اختر حساب فوري</option>
                                <?php foreach ($fawryAccounts as $fawry): ?>
                                <option value="<?php echo $fawry['id']; ?>"><?php echo htmlspecialchars($fawry['name']); ?> (<?php echo number_format($fawry['balance'], 2); ?>)</option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="accountNotes" class="form-label">ملاحظات</label>
                            <textarea class="form-control" id="accountNotes" name="notes" rows="3"></textarea>
                        </div>

                        <div class="text-center">
                            <button type="submit" class="btn btn-primary">إضافة الحساب</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal تعديل حساب -->
    <div class="modal fade" id="editAccountModal" tabindex="-1" aria-labelledby="editAccountModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editAccountModalLabel">تعديل حساب إعلاني</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="editAccountForm">
                        <input type="hidden" id="editAccountId" name="id">

                        <div class="mb-3">
                            <label for="editAccountName" class="form-label">اسم الحساب</label>
                            <input type="text" class="form-control" id="editAccountName" name="name" required>
                        </div>

                        <div class="mb-3">
                            <label for="editAccountStatus" class="form-label">الحالة</label>
                            <select class="form-control" id="editAccountStatus" name="status" required>
                                <option value="نشط">نشط</option>
                                <option value="متوقف">متوقف</option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="editAccountBalance" class="form-label">الرصيد</label>
                            <input type="number" class="form-control" id="editAccountBalance" name="balance" step="0.01" required>
                        </div>

                        <div class="mb-3">
                            <label for="editAccountSpendingLimit" class="form-label">حد الصرف</label>
                            <input type="number" class="form-control" id="editAccountSpendingLimit" name="spending_limit" step="0.01" required>
                        </div>

                        <div class="mb-3">
                            <label for="editAccountLinkedType" class="form-label">ربط الحساب مع</label>
                            <select class="form-control" id="editAccountLinkedType" name="linked_account_type">
                                <option value="none">غير مرتبط</option>
                                <option value="credit_card">كريديت كارد</option>
                                <option value="visa">فيزا</option>
                                <option value="fawry">فوري</option>
                            </select>
                        </div>

                        <div class="mb-3 linked-account-container" id="editCreditCardContainer" style="display: none;">
                            <label for="editAccountCreditCard" class="form-label">اختر بطاقة الائتمان</label>
                            <select class="form-control" id="editAccountCreditCard" name="credit_card_id">
                                <option value="">اختر بطاقة الائتمان</option>
                                <?php foreach ($creditCards as $card): ?>
                                <option value="<?php echo $card['id']; ?>"><?php echo htmlspecialchars($card['name']); ?> (<?php echo number_format($card['balance'], 2); ?>)</option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="mb-3 linked-account-container" id="editVisaContainer" style="display: none;">
                            <label for="editAccountVisa" class="form-label">اختر فيزا</label>
                            <select class="form-control" id="editAccountVisa" name="visa_id">
                                <option value="">اختر فيزا</option>
                                <?php foreach ($visaCards as $visa): ?>
                                <option value="<?php echo $visa['id']; ?>"><?php echo htmlspecialchars($visa['name']); ?> (<?php echo number_format($visa['base_balance'], 2); ?>)</option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="mb-3 linked-account-container" id="editFawryContainer" style="display: none;">
                            <label for="editAccountFawry" class="form-label">اختر حساب فوري</label>
                            <select class="form-control" id="editAccountFawry" name="fawry_id">
                                <option value="">اختر حساب فوري</option>
                                <?php foreach ($fawryAccounts as $fawry): ?>
                                <option value="<?php echo $fawry['id']; ?>"><?php echo htmlspecialchars($fawry['name']); ?> (<?php echo number_format($fawry['balance'], 2); ?>)</option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="editAccountNotes" class="form-label">ملاحظات</label>
                            <textarea class="form-control" id="editAccountNotes" name="notes" rows="3"></textarea>
                        </div>

                        <div class="text-center">
                            <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal حذف حساب -->
    <div class="modal fade" id="deleteAccountModal" tabindex="-1" aria-labelledby="deleteAccountModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="deleteAccountModalLabel">حذف حساب إعلاني</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>هل أنت متأكد من حذف الحساب الإعلاني "<span id="deleteAccountName"></span>"؟</p>
                    <p class="text-danger">سيتم إزالة ارتباط هذا الحساب من جميع الإعلانات المرتبطة به.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-danger" id="confirmDeleteBtn">حذف</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal إضافة كريديت كارد -->
    <div class="modal fade" id="addCreditCardModal" tabindex="-1" aria-labelledby="addCreditCardModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addCreditCardModalLabel">إضافة بطاقة ائتمان جديدة</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="addCreditCardForm">
                        <div class="mb-3">
                            <label for="creditCardName" class="form-label">اسم البطاقة</label>
                            <input type="text" class="form-control" id="creditCardName" name="name" required>
                        </div>

                        <div class="mb-3">
                            <label for="creditCardBalance" class="form-label">الرصيد</label>
                            <input type="number" class="form-control" id="creditCardBalance" name="balance" step="0.01" required>
                        </div>

                        <div class="mb-3">
                            <label for="creditCardDailySpend" class="form-label">الصرف اليومي</label>
                            <input type="number" class="form-control" id="creditCardDailySpend" name="daily_spend" step="0.01" required>
                        </div>

                        <div class="mb-3">
                            <label for="creditCardRemainingBalance" class="form-label">المتبقي صرفه</label>
                            <input type="number" class="form-control" id="creditCardRemainingBalance" name="remaining_balance" step="0.01" required>
                        </div>

                        <div class="mb-3">
                            <label for="creditCardTotalDebt" class="form-label">إجمالي المديونية</label>
                            <input type="number" class="form-control" id="creditCardTotalDebt" name="total_debt" step="0.01" required>
                        </div>

                        <div class="text-center">
                            <button type="submit" class="btn btn-success">إضافة البطاقة</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal إضافة حساب فوري -->
    <div class="modal fade" id="addFawryAccountModal" tabindex="-1" aria-labelledby="addFawryAccountModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addFawryAccountModalLabel">إضافة حساب فوري جديد</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="addFawryAccountForm">
                        <div class="mb-3">
                            <label for="fawryAccountName" class="form-label">اسم الحساب</label>
                            <input type="text" class="form-control" id="fawryAccountName" name="name" required>
                        </div>

                        <div class="mb-3">
                            <label for="fawryAccountStatus" class="form-label">الحالة</label>
                            <select class="form-control" id="fawryAccountStatus" name="status" required>
                                <option value="نشط">نشط</option>
                                <option value="متوقف">متوقف</option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="fawryAccountBalance" class="form-label">الرصيد</label>
                            <input type="number" class="form-control" id="fawryAccountBalance" name="balance" step="0.01" required>
                        </div>

                        <div class="text-center">
                            <button type="submit" class="btn btn-info">إضافة الحساب</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal إضافة فيزا -->
    <div class="modal fade" id="addVisaModal" tabindex="-1" aria-labelledby="addVisaModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addVisaModalLabel">إضافة فيزا جديدة</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="addVisaForm">
                        <div class="mb-3">
                            <label for="visaName" class="form-label">اسم الفيزا</label>
                            <input type="text" class="form-control" id="visaName" name="name" required>
                        </div>

                        <div class="mb-3">
                            <label for="visaBaseBalance" class="form-label">رصيد فيزا أساسي</label>
                            <input type="number" class="form-control" id="visaBaseBalance" name="base_balance" step="0.01" required>
                            <small class="form-text text-muted">الرصيد الذي تصرفه الفيزا في الشهر</small>
                        </div>

                        <div class="mb-3">
                            <label for="visaDailyLimit" class="form-label">رصيد دلي (الحد الأقصى للصرف الدولي)</label>
                            <input type="number" class="form-control" id="visaDailyLimit" name="daily_limit" step="0.01" required>
                        </div>

                        <div class="mb-3">
                            <label for="visaRemainingBalance" class="form-label">المتبقي صرفه</label>
                            <input type="number" class="form-control" id="visaRemainingBalance" name="remaining_balance" step="0.01" required>
                        </div>

                        <div class="mb-3">
                            <label for="visaTotalDebt" class="form-label">إجمالي المديونية</label>
                            <input type="number" class="form-control" id="visaTotalDebt" name="total_debt" step="0.01" required>
                        </div>

                        <div class="text-center">
                            <button type="submit" class="btn btn-warning">إضافة الفيزا</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal شحن الحساب -->
    <div class="modal fade" id="chargeAccountModal" tabindex="-1" aria-labelledby="chargeAccountModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header bg-success text-white">
                    <h5 class="modal-title" id="chargeAccountModalLabel">شحن الحساب الإعلاني</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="chargeAccountForm">
                        <input type="hidden" id="chargeAccountId" name="account_id">

                        <!-- معلومات الحساب -->
                        <div class="account-info mb-4 p-3 bg-light rounded">
                            <h6 class="mb-2">معلومات الحساب:</h6>
                            <p class="mb-1"><strong>اسم الحساب:</strong> <span id="chargeAccountName"></span></p>
                            <p class="mb-0"><strong>الرصيد الحالي:</strong> <span id="chargeCurrentBalance"></span> جنيه</p>
                        </div>

                        <!-- مبلغ الشحن -->
                        <div class="mb-3">
                            <label for="chargeAmount" class="form-label">مبلغ الشحن</label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="chargeAmount" name="amount" step="0.01" min="0.01" required>
                                <span class="input-group-text">جنيه</span>
                            </div>
                        </div>

                        <!-- مصدر الشحن -->
                        <div class="mb-3">
                            <label for="chargeSource" class="form-label">مصدر الشحن</label>
                            <select class="form-control" id="chargeSource" name="source" required>
                                <option value="">اختر مصدر الشحن</option>
                                <option value="credit_card">كريديت كارد</option>
                                <option value="visa">فيزا</option>
                                <option value="fawry">فوري</option>
                                <option value="cash">نقدي</option>
                            </select>
                        </div>

                        <!-- اختيار الكريديت كارد -->
                        <div class="mb-3 source-container" id="creditCardSourceContainer" style="display: none;">
                            <label for="chargeCreditCard" class="form-label">اختر بطاقة الائتمان</label>
                            <select class="form-control" id="chargeCreditCard" name="credit_card_id">
                                <option value="">اختر بطاقة الائتمان</option>
                                <?php foreach ($creditCards as $card): ?>
                                <option value="<?php echo $card['id']; ?>" data-balance="<?php echo $card['balance']; ?>">
                                    <?php echo htmlspecialchars($card['name']); ?> (رصيد: <?php echo number_format($card['balance'], 2); ?> جنيه)
                                </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <!-- اختيار الفيزا -->
                        <div class="mb-3 source-container" id="visaSourceContainer" style="display: none;">
                            <label for="chargeVisa" class="form-label">اختر فيزا</label>
                            <select class="form-control" id="chargeVisa" name="visa_id">
                                <option value="">اختر فيزا</option>
                                <?php foreach ($visaCards as $visa): ?>
                                <option value="<?php echo $visa['id']; ?>" data-balance="<?php echo $visa['base_balance']; ?>">
                                    <?php echo htmlspecialchars($visa['name']); ?> (رصيد: <?php echo number_format($visa['base_balance'], 2); ?> جنيه)
                                </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <!-- اختيار حساب فوري -->
                        <div class="mb-3 source-container" id="fawrySourceContainer" style="display: none;">
                            <label for="chargeFawry" class="form-label">اختر حساب فوري</label>
                            <select class="form-control" id="chargeFawry" name="fawry_id">
                                <option value="">اختر حساب فوري</option>
                                <?php foreach ($fawryAccounts as $fawry): ?>
                                <option value="<?php echo $fawry['id']; ?>" data-balance="<?php echo $fawry['balance']; ?>">
                                    <?php echo htmlspecialchars($fawry['name']); ?> (رصيد: <?php echo number_format($fawry['balance'], 2); ?> جنيه)
                                </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <!-- ملاحظات -->
                        <div class="mb-3">
                            <label for="chargeNotes" class="form-label">ملاحظات (اختياري)</label>
                            <textarea class="form-control" id="chargeNotes" name="notes" rows="3" placeholder="أدخل أي ملاحظات حول عملية الشحن..."></textarea>
                        </div>

                        <!-- تحذير الرصيد -->
                        <div class="alert alert-warning" id="balanceWarning" style="display: none;">
                            <i class="fas fa-exclamation-triangle"></i>
                            <strong>تحذير:</strong> الرصيد المتاح في المصدر المحدد غير كافي لهذه العملية.
                        </div>

                        <!-- ملخص العملية -->
                        <div class="operation-summary p-3 bg-info bg-opacity-10 rounded" id="operationSummary" style="display: none;">
                            <h6 class="text-info mb-2">ملخص العملية:</h6>
                            <p class="mb-1">الرصيد الحالي: <span id="summaryCurrentBalance"></span> جنيه</p>
                            <p class="mb-1">مبلغ الشحن: <span id="summaryChargeAmount"></span> جنيه</p>
                            <p class="mb-0"><strong>الرصيد بعد الشحن: <span id="summaryNewBalance"></span> جنيه</strong></p>
                        </div>

                        <div class="text-center mt-4">
                            <button type="submit" class="btn btn-success btn-lg">
                                <i class="fas fa-plus-circle"></i> تأكيد الشحن
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal شحن كريديت كارد -->
    <div class="modal fade" id="chargeCreditCardModal" tabindex="-1" aria-labelledby="chargeCreditCardModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header bg-success text-white">
                    <h5 class="modal-title" id="chargeCreditCardModalLabel">شحن كريديت كارد</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="chargeCreditCardForm">
                        <!-- اختيار الكريديت كارد -->
                        <div class="mb-3">
                            <label for="creditCardSelect" class="form-label">اختر كريديت كارد</label>
                            <select class="form-control" id="creditCardSelect" name="credit_card_id" required>
                                <option value="">اختر كريديت كارد</option>
                                <?php foreach ($creditCards as $card): ?>
                                <option value="<?php echo $card['id']; ?>" data-balance="<?php echo $card['balance']; ?>" data-name="<?php echo htmlspecialchars($card['name']); ?>">
                                    <?php echo htmlspecialchars($card['name']); ?> (رصيد: <?php echo number_format($card['balance'], 2); ?> جنيه)
                                </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <!-- معلومات البطاقة -->
                        <div class="card-info mb-4 p-3 bg-light rounded" id="creditCardInfo" style="display: none;">
                            <h6 class="mb-2">معلومات البطاقة:</h6>
                            <p class="mb-1"><strong>اسم البطاقة:</strong> <span id="creditCardName"></span></p>
                            <p class="mb-0"><strong>الرصيد الحالي:</strong> <span id="creditCardCurrentBalance"></span> جنيه</p>
                        </div>

                        <!-- مبلغ الشحن -->
                        <div class="mb-3">
                            <label for="creditCardChargeAmount" class="form-label">مبلغ الشحن</label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="creditCardChargeAmount" name="amount" step="0.01" min="0.01" required>
                                <span class="input-group-text">جنيه</span>
                            </div>
                        </div>

                        <!-- مصدر الشحن -->
                        <div class="mb-3">
                            <label for="creditCardChargeSource" class="form-label">مصدر الشحن</label>
                            <select class="form-control" id="creditCardChargeSource" name="source" required>
                                <option value="">اختر مصدر الشحن</option>
                                <option value="fawry">فوري</option>
                                <option value="cash">نقدي</option>
                                <option value="bank_transfer">تحويل بنكي</option>
                            </select>
                        </div>

                        <!-- اختيار حساب فوري -->
                        <div class="mb-3" id="creditCardFawryContainer" style="display: none;">
                            <label for="creditCardFawrySelect" class="form-label">اختر حساب فوري</label>
                            <select class="form-control" id="creditCardFawrySelect" name="fawry_id">
                                <option value="">اختر حساب فوري</option>
                                <?php foreach ($fawryAccounts as $fawry): ?>
                                <option value="<?php echo $fawry['id']; ?>" data-balance="<?php echo $fawry['balance']; ?>">
                                    <?php echo htmlspecialchars($fawry['name']); ?> (رصيد: <?php echo number_format($fawry['balance'], 2); ?> جنيه)
                                </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <!-- ملاحظات -->
                        <div class="mb-3">
                            <label for="creditCardChargeNotes" class="form-label">ملاحظات (اختياري)</label>
                            <textarea class="form-control" id="creditCardChargeNotes" name="notes" rows="3" placeholder="أدخل أي ملاحظات حول عملية الشحن..."></textarea>
                        </div>

                        <!-- ملخص العملية -->
                        <div class="operation-summary p-3 bg-info bg-opacity-10 rounded" id="creditCardOperationSummary" style="display: none;">
                            <h6 class="text-info mb-2">ملخص العملية:</h6>
                            <p class="mb-1">الرصيد الحالي: <span id="creditCardSummaryCurrentBalance"></span> جنيه</p>
                            <p class="mb-1">مبلغ الشحن: <span id="creditCardSummaryChargeAmount"></span> جنيه</p>
                            <p class="mb-0"><strong>الرصيد بعد الشحن: <span id="creditCardSummaryNewBalance"></span> جنيه</strong></p>
                        </div>

                        <div class="text-center mt-4">
                            <button type="submit" class="btn btn-success btn-lg">
                                <i class="fas fa-plus-circle"></i> تأكيد شحن الكريديت كارد
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal شحن فيزا -->
    <div class="modal fade" id="chargeVisaModal" tabindex="-1" aria-labelledby="chargeVisaModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header bg-warning text-dark">
                    <h5 class="modal-title" id="chargeVisaModalLabel">شحن فيزا</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="chargeVisaForm">
                        <!-- اختيار الفيزا -->
                        <div class="mb-3">
                            <label for="visaSelect" class="form-label">اختر فيزا</label>
                            <select class="form-control" id="visaSelect" name="visa_id" required>
                                <option value="">اختر فيزا</option>
                                <?php foreach ($visaCards as $visa): ?>
                                <option value="<?php echo $visa['id']; ?>" data-balance="<?php echo $visa['base_balance']; ?>" data-name="<?php echo htmlspecialchars($visa['name']); ?>">
                                    <?php echo htmlspecialchars($visa['name']); ?> (رصيد: <?php echo number_format($visa['base_balance'], 2); ?> جنيه)
                                </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <!-- معلومات الفيزا -->
                        <div class="card-info mb-4 p-3 bg-light rounded" id="visaInfo" style="display: none;">
                            <h6 class="mb-2">معلومات الفيزا:</h6>
                            <p class="mb-1"><strong>اسم الفيزا:</strong> <span id="visaName"></span></p>
                            <p class="mb-0"><strong>الرصيد الحالي:</strong> <span id="visaCurrentBalance"></span> جنيه</p>
                        </div>

                        <!-- مبلغ الشحن -->
                        <div class="mb-3">
                            <label for="visaChargeAmount" class="form-label">مبلغ الشحن</label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="visaChargeAmount" name="amount" step="0.01" min="0.01" required>
                                <span class="input-group-text">جنيه</span>
                            </div>
                        </div>

                        <!-- مصدر الشحن -->
                        <div class="mb-3">
                            <label for="visaChargeSource" class="form-label">مصدر الشحن</label>
                            <select class="form-control" id="visaChargeSource" name="source" required>
                                <option value="">اختر مصدر الشحن</option>
                                <option value="fawry">فوري</option>
                                <option value="cash">نقدي</option>
                                <option value="bank_transfer">تحويل بنكي</option>
                            </select>
                        </div>

                        <!-- اختيار حساب فوري -->
                        <div class="mb-3" id="visaFawryContainer" style="display: none;">
                            <label for="visaFawrySelect" class="form-label">اختر حساب فوري</label>
                            <select class="form-control" id="visaFawrySelect" name="fawry_id">
                                <option value="">اختر حساب فوري</option>
                                <?php foreach ($fawryAccounts as $fawry): ?>
                                <option value="<?php echo $fawry['id']; ?>" data-balance="<?php echo $fawry['balance']; ?>">
                                    <?php echo htmlspecialchars($fawry['name']); ?> (رصيد: <?php echo number_format($fawry['balance'], 2); ?> جنيه)
                                </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <!-- ملاحظات -->
                        <div class="mb-3">
                            <label for="visaChargeNotes" class="form-label">ملاحظات (اختياري)</label>
                            <textarea class="form-control" id="visaChargeNotes" name="notes" rows="3" placeholder="أدخل أي ملاحظات حول عملية الشحن..."></textarea>
                        </div>

                        <!-- ملخص العملية -->
                        <div class="operation-summary p-3 bg-info bg-opacity-10 rounded" id="visaOperationSummary" style="display: none;">
                            <h6 class="text-info mb-2">ملخص العملية:</h6>
                            <p class="mb-1">الرصيد الحالي: <span id="visaSummaryCurrentBalance"></span> جنيه</p>
                            <p class="mb-1">مبلغ الشحن: <span id="visaSummaryChargeAmount"></span> جنيه</p>
                            <p class="mb-0"><strong>الرصيد بعد الشحن: <span id="visaSummaryNewBalance"></span> جنيه</strong></p>
                        </div>

                        <div class="text-center mt-4">
                            <button type="submit" class="btn btn-warning btn-lg">
                                <i class="fas fa-plus-circle"></i> تأكيد شحن الفيزا
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal شحن فوري -->
    <div class="modal fade" id="chargeFawryModal" tabindex="-1" aria-labelledby="chargeFawryModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header bg-info text-white">
                    <h5 class="modal-title" id="chargeFawryModalLabel">شحن حساب فوري</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="chargeFawryForm">
                        <!-- اختيار حساب فوري -->
                        <div class="mb-3">
                            <label for="fawrySelect" class="form-label">اختر حساب فوري</label>
                            <select class="form-control" id="fawrySelect" name="fawry_id" required>
                                <option value="">اختر حساب فوري</option>
                                <?php foreach ($fawryAccounts as $fawry): ?>
                                <option value="<?php echo $fawry['id']; ?>" data-balance="<?php echo $fawry['balance']; ?>" data-name="<?php echo htmlspecialchars($fawry['name']); ?>">
                                    <?php echo htmlspecialchars($fawry['name']); ?> (رصيد: <?php echo number_format($fawry['balance'], 2); ?> جنيه)
                                </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <!-- معلومات الحساب -->
                        <div class="card-info mb-4 p-3 bg-light rounded" id="fawryInfo" style="display: none;">
                            <h6 class="mb-2">معلومات الحساب:</h6>
                            <p class="mb-1"><strong>اسم الحساب:</strong> <span id="fawryName"></span></p>
                            <p class="mb-0"><strong>الرصيد الحالي:</strong> <span id="fawryCurrentBalance"></span> جنيه</p>
                        </div>

                        <!-- مبلغ الشحن -->
                        <div class="mb-3">
                            <label for="fawryChargeAmount" class="form-label">مبلغ الشحن</label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="fawryChargeAmount" name="amount" step="0.01" min="0.01" required>
                                <span class="input-group-text">جنيه</span>
                            </div>
                        </div>

                        <!-- مصدر الشحن -->
                        <div class="mb-3">
                            <label for="fawryChargeSource" class="form-label">مصدر الشحن</label>
                            <select class="form-control" id="fawryChargeSource" name="source" required>
                                <option value="">اختر مصدر الشحن</option>
                                <option value="cash">نقدي</option>
                                <option value="bank_transfer">تحويل بنكي</option>
                                <option value="mobile_wallet">محفظة إلكترونية</option>
                            </select>
                        </div>

                        <!-- ملاحظات -->
                        <div class="mb-3">
                            <label for="fawryChargeNotes" class="form-label">ملاحظات (اختياري)</label>
                            <textarea class="form-control" id="fawryChargeNotes" name="notes" rows="3" placeholder="أدخل أي ملاحظات حول عملية الشحن..."></textarea>
                        </div>

                        <!-- ملخص العملية -->
                        <div class="operation-summary p-3 bg-info bg-opacity-10 rounded" id="fawryOperationSummary" style="display: none;">
                            <h6 class="text-info mb-2">ملخص العملية:</h6>
                            <p class="mb-1">الرصيد الحالي: <span id="fawrySummaryCurrentBalance"></span> جنيه</p>
                            <p class="mb-1">مبلغ الشحن: <span id="fawrySummaryChargeAmount"></span> جنيه</p>
                            <p class="mb-0"><strong>الرصيد بعد الشحن: <span id="fawrySummaryNewBalance"></span> جنيه</strong></p>
                        </div>

                        <div class="text-center mt-4">
                            <button type="submit" class="btn btn-info btn-lg">
                                <i class="fas fa-plus-circle"></i> تأكيد شحن الحساب
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- jQuery and Bootstrap JS -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Custom JS -->
    <script src="assets/js/ad_accounts.js"></script>

    <script>
        // وظيفة شحن الحساب
        document.addEventListener('DOMContentLoaded', function() {
            // التعامل مع تغيير نوع الربط في نموذج الإضافة
            const accountLinkedType = document.getElementById('accountLinkedType');
            if (accountLinkedType) {
                accountLinkedType.addEventListener('change', function() {
                    const selectedType = this.value;

                    // إخفاء جميع الحاويات
                    document.querySelectorAll('.linked-account-container').forEach(container => {
                        container.style.display = 'none';
                    });

                    // إظهار الحاوية المناسبة
                    if (selectedType === 'credit_card') {
                        const container = document.getElementById('creditCardContainer');
                        if (container) container.style.display = 'block';
                    } else if (selectedType === 'visa') {
                        const container = document.getElementById('visaContainer');
                        if (container) container.style.display = 'block';
                    } else if (selectedType === 'fawry') {
                        const container = document.getElementById('fawryContainer');
                        if (container) container.style.display = 'block';
                    }
                });
            }

            // التعامل مع تغيير نوع الربط في نموذج التعديل
            const editAccountLinkedType = document.getElementById('editAccountLinkedType');
            if (editAccountLinkedType) {
                editAccountLinkedType.addEventListener('change', function() {
                    const selectedType = this.value;

                    // إخفاء جميع الحاويات في نموذج التعديل
                    const editCreditCardContainer = document.getElementById('editCreditCardContainer');
                    const editVisaContainer = document.getElementById('editVisaContainer');
                    const editFawryContainer = document.getElementById('editFawryContainer');

                    if (editCreditCardContainer) editCreditCardContainer.style.display = 'none';
                    if (editVisaContainer) editVisaContainer.style.display = 'none';
                    if (editFawryContainer) editFawryContainer.style.display = 'none';

                    // إظهار الحاوية المناسبة
                    if (selectedType === 'credit_card' && editCreditCardContainer) {
                        editCreditCardContainer.style.display = 'block';
                    } else if (selectedType === 'visa' && editVisaContainer) {
                        editVisaContainer.style.display = 'block';
                    } else if (selectedType === 'fawry' && editFawryContainer) {
                        editFawryContainer.style.display = 'block';
                    }
                });
            }

            // البحث في الجدول
            const searchInput = document.getElementById('searchInput');
            if (searchInput) {
                searchInput.addEventListener('keyup', function() {
                    const value = this.value.toLowerCase();
                    const rows = document.querySelectorAll('#accountsTableBody tr');

                    rows.forEach(row => {
                        const text = row.textContent.toLowerCase();
                        row.style.display = text.includes(value) ? '' : 'none';
                    });
                });
            }
            // معالج النقر على زر شحن الحساب
            document.querySelectorAll('.charge-account-btn').forEach(button => {
                button.addEventListener('click', function() {
                    const accountId = this.getAttribute('data-id');
                    const accountName = this.getAttribute('data-name');
                    const currentBalance = parseFloat(this.getAttribute('data-balance'));

                    // تعبئة بيانات الحساب في النافذة
                    document.getElementById('chargeAccountId').value = accountId;
                    document.getElementById('chargeAccountName').textContent = accountName;
                    document.getElementById('chargeCurrentBalance').textContent = currentBalance.toFixed(2);
                    document.getElementById('summaryCurrentBalance').textContent = currentBalance.toFixed(2);

                    // إعادة تعيين النموذج
                    document.getElementById('chargeAccountForm').reset();
                    document.getElementById('chargeAccountId').value = accountId;
                    hideAllSourceContainers();
                    hideOperationSummary();
                    hideBalanceWarning();

                    // إظهار النافذة
                    const modal = new bootstrap.Modal(document.getElementById('chargeAccountModal'));
                    modal.show();
                });
            });

            // معالج تغيير مصدر الشحن
            document.getElementById('chargeSource').addEventListener('change', function() {
                const source = this.value;
                hideAllSourceContainers();

                if (source === 'credit_card') {
                    document.getElementById('creditCardSourceContainer').style.display = 'block';
                } else if (source === 'visa') {
                    document.getElementById('visaSourceContainer').style.display = 'block';
                } else if (source === 'fawry') {
                    document.getElementById('fawrySourceContainer').style.display = 'block';
                }

                updateOperationSummary();
            });

            // معالج تغيير مبلغ الشحن
            document.getElementById('chargeAmount').addEventListener('input', updateOperationSummary);

            // معالج تغيير المصدر المحدد
            document.getElementById('chargeCreditCard').addEventListener('change', updateOperationSummary);
            document.getElementById('chargeVisa').addEventListener('change', updateOperationSummary);
            document.getElementById('chargeFawry').addEventListener('change', updateOperationSummary);

            // إخفاء جميع حاويات المصادر
            function hideAllSourceContainers() {
                document.querySelectorAll('.source-container').forEach(container => {
                    container.style.display = 'none';
                });
            }

            // إخفاء ملخص العملية
            function hideOperationSummary() {
                document.getElementById('operationSummary').style.display = 'none';
            }

            // إخفاء تحذير الرصيد
            function hideBalanceWarning() {
                document.getElementById('balanceWarning').style.display = 'none';
            }

            // تحديث ملخص العملية
            function updateOperationSummary() {
                const chargeAmount = parseFloat(document.getElementById('chargeAmount').value) || 0;
                const currentBalance = parseFloat(document.getElementById('chargeCurrentBalance').textContent) || 0;
                const source = document.getElementById('chargeSource').value;

                if (chargeAmount > 0 && source) {
                    const newBalance = currentBalance + chargeAmount;

                    document.getElementById('summaryChargeAmount').textContent = chargeAmount.toFixed(2);
                    document.getElementById('summaryNewBalance').textContent = newBalance.toFixed(2);
                    document.getElementById('operationSummary').style.display = 'block';

                    // التحقق من رصيد المصدر
                    checkSourceBalance(source, chargeAmount);
                } else {
                    hideOperationSummary();
                    hideBalanceWarning();
                }
            }

            // التحقق من رصيد المصدر
            function checkSourceBalance(source, amount) {
                let sourceBalance = 0;
                let selectedOption = null;

                if (source === 'credit_card') {
                    selectedOption = document.getElementById('chargeCreditCard').selectedOptions[0];
                } else if (source === 'visa') {
                    selectedOption = document.getElementById('chargeVisa').selectedOptions[0];
                } else if (source === 'fawry') {
                    selectedOption = document.getElementById('chargeFawry').selectedOptions[0];
                } else if (source === 'cash') {
                    // النقدي لا يحتاج تحقق من الرصيد
                    hideBalanceWarning();
                    return;
                }

                if (selectedOption && selectedOption.getAttribute('data-balance')) {
                    sourceBalance = parseFloat(selectedOption.getAttribute('data-balance'));

                    if (amount > sourceBalance) {
                        document.getElementById('balanceWarning').style.display = 'block';
                    } else {
                        hideBalanceWarning();
                    }
                } else {
                    hideBalanceWarning();
                }
            }
        });

        // معالج إرسال نموذج الشحن
        document.getElementById('chargeAccountForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(this);
            const data = Object.fromEntries(formData);

            // التحقق من صحة البيانات
            if (!data.amount || parseFloat(data.amount) <= 0) {
                alert('يرجى إدخال مبلغ صحيح للشحن');
                return;
            }

            if (!data.source) {
                alert('يرجى اختيار مصدر الشحن');
                return;
            }

            if (data.source !== 'cash' && !getSourceId(data)) {
                alert('يرجى اختيار المصدر المحدد للشحن');
                return;
            }

            // إرسال البيانات
            fetch('api/ad_accounts/charge.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(result => {
                if (result.success) {
                    alert('تم شحن الحساب بنجاح!');

                    // إغلاق النافذة
                    const modal = bootstrap.Modal.getInstance(document.getElementById('chargeAccountModal'));
                    modal.hide();

                    // إعادة تحميل الصفحة لتحديث البيانات
                    window.location.reload();
                } else {
                    alert('حدث خطأ: ' + result.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('حدث خطأ أثناء شحن الحساب');
            });
        });

        // الحصول على معرف المصدر
        function getSourceId(data) {
            if (data.source === 'credit_card') {
                return data.credit_card_id;
            } else if (data.source === 'visa') {
                return data.visa_id;
            } else if (data.source === 'fawry') {
                return data.fawry_id;
            }
            return null;
        }

        // وظائف شحن الكريديت كارد
        document.getElementById('creditCardSelect').addEventListener('change', function() {
            const selectedOption = this.selectedOptions[0];
            if (selectedOption && selectedOption.value) {
                const cardName = selectedOption.getAttribute('data-name');
                const balance = parseFloat(selectedOption.getAttribute('data-balance'));

                document.getElementById('creditCardName').textContent = cardName;
                document.getElementById('creditCardCurrentBalance').textContent = balance.toFixed(2);
                document.getElementById('creditCardSummaryCurrentBalance').textContent = balance.toFixed(2);
                document.getElementById('creditCardInfo').style.display = 'block';
            } else {
                document.getElementById('creditCardInfo').style.display = 'none';
                document.getElementById('creditCardOperationSummary').style.display = 'none';
            }
        });

        document.getElementById('creditCardChargeAmount').addEventListener('input', updateCreditCardSummary);
        document.getElementById('creditCardChargeSource').addEventListener('change', function() {
            if (this.value === 'fawry') {
                document.getElementById('creditCardFawryContainer').style.display = 'block';
            } else {
                document.getElementById('creditCardFawryContainer').style.display = 'none';
            }
            updateCreditCardSummary();
        });

        function updateCreditCardSummary() {
            const amount = parseFloat(document.getElementById('creditCardChargeAmount').value) || 0;
            const currentBalance = parseFloat(document.getElementById('creditCardCurrentBalance').textContent) || 0;

            if (amount > 0) {
                const newBalance = currentBalance + amount;
                document.getElementById('creditCardSummaryChargeAmount').textContent = amount.toFixed(2);
                document.getElementById('creditCardSummaryNewBalance').textContent = newBalance.toFixed(2);
                document.getElementById('creditCardOperationSummary').style.display = 'block';
            } else {
                document.getElementById('creditCardOperationSummary').style.display = 'none';
            }
        }

        // وظائف شحن الفيزا
        document.getElementById('visaSelect').addEventListener('change', function() {
            const selectedOption = this.selectedOptions[0];
            if (selectedOption && selectedOption.value) {
                const visaName = selectedOption.getAttribute('data-name');
                const balance = parseFloat(selectedOption.getAttribute('data-balance'));

                document.getElementById('visaName').textContent = visaName;
                document.getElementById('visaCurrentBalance').textContent = balance.toFixed(2);
                document.getElementById('visaSummaryCurrentBalance').textContent = balance.toFixed(2);
                document.getElementById('visaInfo').style.display = 'block';
            } else {
                document.getElementById('visaInfo').style.display = 'none';
                document.getElementById('visaOperationSummary').style.display = 'none';
            }
        });

        document.getElementById('visaChargeAmount').addEventListener('input', updateVisaSummary);
        document.getElementById('visaChargeSource').addEventListener('change', function() {
            if (this.value === 'fawry') {
                document.getElementById('visaFawryContainer').style.display = 'block';
            } else {
                document.getElementById('visaFawryContainer').style.display = 'none';
            }
            updateVisaSummary();
        });

        function updateVisaSummary() {
            const amount = parseFloat(document.getElementById('visaChargeAmount').value) || 0;
            const currentBalance = parseFloat(document.getElementById('visaCurrentBalance').textContent) || 0;

            if (amount > 0) {
                const newBalance = currentBalance + amount;
                document.getElementById('visaSummaryChargeAmount').textContent = amount.toFixed(2);
                document.getElementById('visaSummaryNewBalance').textContent = newBalance.toFixed(2);
                document.getElementById('visaOperationSummary').style.display = 'block';
            } else {
                document.getElementById('visaOperationSummary').style.display = 'none';
            }
        }

        // وظائف شحن الفوري
        document.getElementById('fawrySelect').addEventListener('change', function() {
            const selectedOption = this.selectedOptions[0];
            if (selectedOption && selectedOption.value) {
                const fawryName = selectedOption.getAttribute('data-name');
                const balance = parseFloat(selectedOption.getAttribute('data-balance'));

                document.getElementById('fawryName').textContent = fawryName;
                document.getElementById('fawryCurrentBalance').textContent = balance.toFixed(2);
                document.getElementById('fawrySummaryCurrentBalance').textContent = balance.toFixed(2);
                document.getElementById('fawryInfo').style.display = 'block';
            } else {
                document.getElementById('fawryInfo').style.display = 'none';
                document.getElementById('fawryOperationSummary').style.display = 'none';
            }
        });

        document.getElementById('fawryChargeAmount').addEventListener('input', updateFawrySummary);

        function updateFawrySummary() {
            const amount = parseFloat(document.getElementById('fawryChargeAmount').value) || 0;
            const currentBalance = parseFloat(document.getElementById('fawryCurrentBalance').textContent) || 0;

            if (amount > 0) {
                const newBalance = currentBalance + amount;
                document.getElementById('fawrySummaryChargeAmount').textContent = amount.toFixed(2);
                document.getElementById('fawrySummaryNewBalance').textContent = newBalance.toFixed(2);
                document.getElementById('fawryOperationSummary').style.display = 'block';
            } else {
                document.getElementById('fawryOperationSummary').style.display = 'none';
            }
        }

        // معالج إرسال نموذج شحن الكريديت كارد
        document.getElementById('chargeCreditCardForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(this);
            const data = Object.fromEntries(formData);
            data.card_type = 'credit_card';

            // إرسال البيانات
            fetch('api/cards/charge.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(result => {
                if (result.success) {
                    alert('تم شحن الكريديت كارد بنجاح!');
                    const modal = bootstrap.Modal.getInstance(document.getElementById('chargeCreditCardModal'));
                    modal.hide();
                    window.location.reload();
                } else {
                    alert('حدث خطأ: ' + result.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('حدث خطأ أثناء شحن الكريديت كارد');
            });
        });

        // معالج إرسال نموذج شحن الفيزا
        document.getElementById('chargeVisaForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(this);
            const data = Object.fromEntries(formData);
            data.card_type = 'visa';

            // إرسال البيانات
            fetch('api/cards/charge.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(result => {
                if (result.success) {
                    alert('تم شحن الفيزا بنجاح!');
                    const modal = bootstrap.Modal.getInstance(document.getElementById('chargeVisaModal'));
                    modal.hide();
                    window.location.reload();
                } else {
                    alert('حدث خطأ: ' + result.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('حدث خطأ أثناء شحن الفيزا');
            });
        });

        // معالج إرسال نموذج شحن الفوري
        document.getElementById('chargeFawryForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(this);
            const data = Object.fromEntries(formData);
            data.card_type = 'fawry';

            // إرسال البيانات
            fetch('api/cards/charge.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(result => {
                if (result.success) {
                    alert('تم شحن حساب فوري بنجاح!');
                    const modal = bootstrap.Modal.getInstance(document.getElementById('chargeFawryModal'));
                    modal.hide();
                    window.location.reload();
                } else {
                    alert('حدث خطأ: ' + result.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('حدث خطأ أثناء شحن حساب فوري');
            });
        });

        // إضافة بطاقة ائتمان جديدة
        document.getElementById('addCreditCardForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = {
                name: document.getElementById('creditCardName').value,
                balance: parseFloat(document.getElementById('creditCardBalance').value),
                daily_spend: parseFloat(document.getElementById('creditCardDailySpend').value),
                remaining_balance: parseFloat(document.getElementById('creditCardRemainingBalance').value),
                total_debt: parseFloat(document.getElementById('creditCardTotalDebt').value)
            };

            fetch('api/credit_cards/add.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(formData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // إظهار رسالة نجاح
                    alert(data.message);

                    // إغلاق النافذة المنبثقة
                    const modal = bootstrap.Modal.getInstance(document.getElementById('addCreditCardModal'));
                    modal.hide();

                    // إعادة تحميل الصفحة لتحديث القوائم
                    window.location.reload();
                } else {
                    // إظهار رسالة الخطأ
                    alert('حدث خطأ: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('حدث خطأ أثناء إضافة بطاقة الائتمان');
            });
        });

        // إضافة حساب فوري جديد
        document.getElementById('addFawryAccountForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = {
                name: document.getElementById('fawryAccountName').value,
                status: document.getElementById('fawryAccountStatus').value,
                balance: parseFloat(document.getElementById('fawryAccountBalance').value)
            };

            fetch('api/fawry_accounts/add.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(formData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // إظهار رسالة نجاح
                    alert(data.message);

                    // إغلاق النافذة المنبثقة
                    const modal = bootstrap.Modal.getInstance(document.getElementById('addFawryAccountModal'));
                    modal.hide();

                    // إعادة تحميل الصفحة لتحديث القوائم
                    window.location.reload();
                } else {
                    // إظهار رسالة الخطأ
                    alert('حدث خطأ: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('حدث خطأ أثناء إضافة حساب فوري');
            });
        });

        // إضافة فيزا جديدة
        document.getElementById('addVisaForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = {
                name: document.getElementById('visaName').value,
                base_balance: parseFloat(document.getElementById('visaBaseBalance').value),
                daily_limit: parseFloat(document.getElementById('visaDailyLimit').value),
                remaining_balance: parseFloat(document.getElementById('visaRemainingBalance').value),
                total_debt: parseFloat(document.getElementById('visaTotalDebt').value)
            };

            fetch('api/visa/add.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(formData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // إظهار رسالة نجاح
                    alert(data.message);

                    // إغلاق النافذة المنبثقة
                    const modal = bootstrap.Modal.getInstance(document.getElementById('addVisaModal'));
                    modal.hide();

                    // إعادة تحميل الصفحة لتحديث القوائم
                    window.location.reload();
                } else {
                    // إظهار رسالة الخطأ
                    alert('حدث خطأ: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('حدث خطأ أثناء إضافة الفيزا');
            });
        });

        // معالج إضافة حساب جديد
        const addAccountForm = document.getElementById('addAccountForm');
        if (addAccountForm) {
            addAccountForm.addEventListener('submit', function(e) {
                e.preventDefault();

                const formData = new FormData(this);
                const data = Object.fromEntries(formData);

                // تحديد معرف الحساب المرتبط
                if (data.linked_account_type === 'credit_card') {
                    data.linked_account_id = data.credit_card_id;
                } else if (data.linked_account_type === 'visa') {
                    data.linked_account_id = data.visa_id;
                } else if (data.linked_account_type === 'fawry') {
                    data.linked_account_id = data.fawry_id;
                } else {
                    data.linked_account_id = null;
                }

                // إرسال البيانات
                fetch('api/ad_accounts/add.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(data)
                })
                .then(response => response.json())
                .then(result => {
                    if (result.success) {
                        alert('تم إضافة الحساب بنجاح!');
                        const modal = bootstrap.Modal.getInstance(document.getElementById('addAccountModal'));
                        modal.hide();
                        window.location.reload();
                    } else {
                        alert('حدث خطأ: ' + result.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('حدث خطأ أثناء إضافة الحساب');
                });
            });
        }

        // معالج تعديل حساب
        const editAccountForm = document.getElementById('editAccountForm');
        if (editAccountForm) {
            editAccountForm.addEventListener('submit', function(e) {
                e.preventDefault();

                const formData = new FormData(this);
                const data = Object.fromEntries(formData);

                // تحديد معرف الحساب المرتبط
                if (data.linked_account_type === 'credit_card') {
                    data.linked_account_id = data.credit_card_id;
                } else if (data.linked_account_type === 'visa') {
                    data.linked_account_id = data.visa_id;
                } else if (data.linked_account_type === 'fawry') {
                    data.linked_account_id = data.fawry_id;
                } else {
                    data.linked_account_id = null;
                }

                // إرسال البيانات
                fetch('api/ad_accounts/update.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(data)
                })
                .then(response => response.json())
                .then(result => {
                    if (result.success) {
                        alert('تم تحديث الحساب بنجاح!');
                        const modal = bootstrap.Modal.getInstance(document.getElementById('editAccountModal'));
                        modal.hide();
                        window.location.reload();
                    } else {
                        alert('حدث خطأ: ' + result.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('حدث خطأ أثناء تحديث الحساب');
                });
            });
        }

        // معالج أزرار التعديل
        document.querySelectorAll('.edit-account-btn').forEach(button => {
            button.addEventListener('click', function() {
                const accountId = this.getAttribute('data-id');
                const accountName = this.getAttribute('data-name');
                const accountStatus = this.getAttribute('data-status');
                const accountBalance = this.getAttribute('data-balance');
                const accountSpendingLimit = this.getAttribute('data-spending-limit');
                const accountNotes = this.getAttribute('data-notes');
                const linkedType = this.getAttribute('data-linked-type');
                const linkedId = this.getAttribute('data-linked-id');

                // تعبئة النموذج
                document.getElementById('editAccountId').value = accountId;
                document.getElementById('editAccountName').value = accountName;
                document.getElementById('editAccountStatus').value = accountStatus;
                document.getElementById('editAccountBalance').value = accountBalance;
                document.getElementById('editAccountSpendingLimit').value = accountSpendingLimit;
                document.getElementById('editAccountNotes').value = accountNotes;
                document.getElementById('editAccountLinkedType').value = linkedType;

                // إظهار الحاوية المناسبة وتعيين القيمة
                const editCreditCardContainer = document.getElementById('editCreditCardContainer');
                const editVisaContainer = document.getElementById('editVisaContainer');
                const editFawryContainer = document.getElementById('editFawryContainer');

                if (editCreditCardContainer) editCreditCardContainer.style.display = 'none';
                if (editVisaContainer) editVisaContainer.style.display = 'none';
                if (editFawryContainer) editFawryContainer.style.display = 'none';

                if (linkedType === 'credit_card' && editCreditCardContainer) {
                    editCreditCardContainer.style.display = 'block';
                    document.getElementById('editAccountCreditCard').value = linkedId;
                } else if (linkedType === 'visa' && editVisaContainer) {
                    editVisaContainer.style.display = 'block';
                    document.getElementById('editAccountVisa').value = linkedId;
                } else if (linkedType === 'fawry' && editFawryContainer) {
                    editFawryContainer.style.display = 'block';
                    document.getElementById('editAccountFawry').value = linkedId;
                }

                // إظهار النافذة
                const modal = new bootstrap.Modal(document.getElementById('editAccountModal'));
                modal.show();
            });
        });
    </script>
</body>
</html>