<?php
/**
 * صفحة تقرير حسابات الإعلانات
 */

// تضمين ملف التكوين
require_once 'config/config.php';

// تضمين اتصال قاعدة البيانات
require_once 'includes/db.php';

// تضمين الدوال المساعدة
require_once 'includes/functions.php';

// تضمين دوال المصادقة
require_once 'includes/auth.php';

// تضمين دوال الصلاحيات
require_once 'includes/permissions.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    $_SESSION['flash_message'] = 'يجب تسجيل الدخول للوصول إلى هذه الصفحة';
    $_SESSION['flash_type'] = 'danger';
    redirect(BASE_URL . 'index.php');
    exit;
}

// التحقق من صلاحيات المستخدم
if (!isAdmin()) {
    $_SESSION['flash_message'] = 'ليس لديك صلاحية للوصول إلى هذه الصفحة';
    $_SESSION['flash_type'] = 'danger';
    redirect(BASE_URL . 'dashboard.php');
    exit;
}

?>
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقرير عملاء الإعلانات - <?php echo SITE_TITLE; ?></title>

    <!-- Bootstrap RTL CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Google Fonts - Cairo -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="assets/css/ad_accounts_report.css">
</head>
<body>
    <!-- Main Container -->
    <div class="main-container">
        <!-- Header -->
        <div class="page-header">
            <div class="logo-container">
                <img src="assets/images/logo.png" alt="Logo" class="logo">
            </div>
            <h1 class="page-title">تقرير عملاء الإعلانات</h1>
            <a href="ad_clients.php" class="back-btn">
                <i class="fas fa-arrow-right"></i> العودة
            </a>
        </div>

        <!-- Search Section -->
        <div class="search-section">
            <i class="fas fa-filter filter-icon"></i>
            <div class="client-filter-container">
                <select id="clientTypeFilter" class="client-filter-dropdown">
                    <option value="">جميع العملاء</option>
                    <!-- سيتم إضافة الفئات هنا بواسطة JavaScript -->
                </select>
                <i class="fas fa-chevron-down dropdown-icon"></i>
            </div>
            <div class="search-container">
                <input type="text" id="searchInput" class="search-input" placeholder="البحث...">
                <i class="fas fa-search search-icon"></i>
            </div>
        </div>

        <!-- Table Container -->
        <div class="table-container">
            <table class="ad-accounts-table">
                <thead>
                    <tr>
                        <th>الحسابات</th>
                        <th>الصرف</th>
                        <th>الصرف بالنسبة</th>
                        <th>المدفوعات</th>
                        <th>صرف يومي</th>
                        <th>إجمالي</th>
                    </tr>
                </thead>
            </table>

            <div class="table-body-container">
                <table class="ad-accounts-table">
                    <tbody id="adAccountsReportTableBody">
                        <!-- سيتم إضافة البيانات هنا بواسطة JavaScript -->
                    </tbody>
                </table>
            </div>

            <div class="footer-summary">
                <div class="footer-item">
                    <div class="footer-label">إجمالي</div>
                    <div class="footer-value" id="totalExchange">0.00</div>
                </div>
                <div class="footer-item">
                    <div class="footer-label">الصرف</div>
                    <div class="footer-value" id="totalExchangeRaw">0.00</div>
                </div>
                <div class="footer-item">
                    <div class="footer-label">الصرف بالنسبة</div>
                    <div class="footer-value" id="totalExchangeWithPercentage">0.00</div>
                </div>
                <div class="footer-item">
                    <div class="footer-label">المدفوعات</div>
                    <div class="footer-value" id="totalPayments">0.00</div>
                </div>
                <div class="footer-item">
                    <div class="footer-label">صرف يومي</div>
                    <div class="footer-value" id="totalDailyExchange">0.00</div>
                </div>
                <div class="footer-item">
                    <div class="footer-label">مسبق الدفع</div>
                    <div class="footer-value positive-value" id="totalPrepaidSecond">0.00</div>
                </div>
                <div class="footer-item">
                    <div class="footer-label">مديونيات</div>
                    <div class="footer-value negative-value" id="totalDebts">0.00</div>
                </div>
            </div>
        </div>
    </div>

    <!-- jQuery and Bootstrap JS -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Custom JS -->
    <script src="assets/js/ad_accounts_report.js"></script>
</body>
</html>
