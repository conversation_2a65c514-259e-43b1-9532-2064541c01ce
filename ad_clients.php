<?php
// تضمين ملف التكوين
require_once 'config/config.php';

// تضمين اتصال قاعدة البيانات
require_once 'includes/db.php';

// تضمين الدوال المساعدة
require_once 'includes/functions.php';

// تضمين دوال المصادقة
require_once 'includes/auth.php';

// تضمين دوال الصلاحيات
require_once 'includes/permissions.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    $_SESSION['flash_message'] = 'يجب تسجيل الدخول للوصول إلى عملاء الإعلانات';
    $_SESSION['flash_type'] = 'danger';
    redirect(BASE_URL . 'index.php');
    exit;
}

// الحصول على بيانات المستخدم
$user_id = $_SESSION['user_id'];
$query = "SELECT * FROM users WHERE id = :id LIMIT 1";
$stmt = $db->prepare($query);
$stmt->bindParam(':id', $user_id);
$stmt->execute();
$user = $stmt->fetch(PDO::FETCH_ASSOC);
?>
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>عملاء الإعلانات - <?php echo SITE_TITLE; ?></title>

    <!-- Bootstrap RTL CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Google Fonts - Cairo -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>assets/css/styles.css">
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>assets/css/ad_clients.css">
</head>
<body>
    <!-- Main Content -->
    <div class="page-container">
        <div class="page-header">
            <a href="<?php echo BASE_URL; ?>" class="logo-link">
                <img src="<?php echo BASE_URL; ?>assets/images/logo.png" alt="<?php echo SITE_TITLE; ?>" class="page-logo">
            </a>
            <h1 class="page-title">عملاء الإعلانات</h1>
            <a href="dashboard.php" class="back-btn">
                <i class="fas fa-arrow-right"></i> العودة
            </a>
        </div>

        <!-- Content Area -->
        <div class="content-area">
            <div class="ad-clients-container">
                <div class="ad-clients-header">
                    <div class="search-container">
                        <i class="fas fa-filter filter-icon"></i>
                        <div class="client-filter-container">
                            <select id="clientTypeFilter" class="client-filter-dropdown">
                                <option value="">جميع العملاء</option>
                                <!-- سيتم إضافة الفئات هنا بواسطة JavaScript -->
                            </select>
                            <i class="fas fa-chevron-down dropdown-icon"></i>
                        </div>
                        <a href="manage_client_types.php" class="btn btn-warning manage-types-btn">
                            <i class="fas fa-cogs"></i> إدارة الفئات
                        </a>
                        <div class="search-box">
                            <input type="text" id="searchInput" class="search-input" placeholder="البحث...">
                            <i class="fas fa-search search-icon"></i>
                        </div>
                        <a href="ad_accounts_report.php" class="btn btn-info ad-accounts-report-btn">
                            <i class="fas fa-chart-bar"></i> حسابات الإعلانات
                        </a>
                        <button id="addClientBtn" class="btn btn-primary add-client-btn">
                            <i class="fas fa-user-plus"></i> إضافة عميل
                        </button>
                    </div>
                </div>

                <div class="ad-clients-content" id="adClientsContent">
                    <!-- سيتم إضافة محتوى العملاء هنا بواسطة JavaScript -->
                    <div class="loading-message">جاري تحميل البيانات...</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal إضافة عميل -->
    <div class="modal fade" id="addClientModal" tabindex="-1" aria-labelledby="addClientModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addClientModalLabel">إضافة عميل جديد</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="addClientForm">
                        <div class="mb-3">
                            <label for="clientName" class="form-label">اسم العميل</label>
                            <input type="text" class="form-control" id="clientName" name="name" required>
                        </div>
                        <div class="mb-3">
                            <label for="clientType" class="form-label">نوع العميل</label>
                            <select class="form-control" id="clientType" name="type" required>
                                <option value="">-- اختر نوع العميل --</option>
                                <!-- سيتم إضافة فئات العملاء هنا بواسطة JavaScript -->
                            </select>
                            <small class="form-text text-muted">
                                <a href="manage_client_types.php" target="_blank" class="text-primary">
                                    <i class="fas fa-cogs"></i> إدارة فئات العملاء
                                </a>
                            </small>
                        </div>
                        <div class="mb-3">
                            <label for="clientCommissionPercentage" class="form-label">نسبة العمولة (%)</label>
                            <input type="number" class="form-control" id="clientCommissionPercentage" name="commission_percentage" min="0" max="100" step="0.01" value="50.00" required>
                            <small class="form-text text-muted">النسبة المئوية التي تضاف إلى سعر الصرف (مثال: 50% تعني إضافة 50% إلى سعر الصرف)</small>
                        </div>
                        <div class="text-center">
                            <button type="submit" class="btn btn-primary">إضافة العميل</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal إضافة إعلان -->
    <div class="modal fade" id="addAdModal" tabindex="-1" aria-labelledby="addAdModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addAdModalLabel">إضافة اعلان</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="addAdForm">
                        <input type="hidden" id="adClientId" name="client_id">
                        <input type="hidden" id="adClientType" name="client_type">

                        <div class="ad-form-header">
                            <div class="ad-form-header-item">تاريخ</div>
                            <div class="ad-form-header-item">نوع</div>
                            <div class="ad-form-header-item">يومي / اجمالي</div>
                            <div class="ad-form-header-item">بوست</div>
                            <div class="ad-form-header-item">عدد ايام</div>
                            <div class="ad-form-header-item">التكلفة</div>
                            <div class="ad-form-header-item">الصرف</div>
                            <div class="ad-form-header-item">الاكونت</div>
                        </div>

                        <div class="ad-form-row">
                            <div class="ad-form-item">
                                <input type="date" class="form-control" id="adDate" name="date" required>
                            </div>
                            <div class="ad-form-item">
                                <input type="text" class="form-control" id="adType" name="type" required>
                            </div>
                            <div class="ad-form-item">
                                <select class="form-control" id="adDailyTotal" name="daily_total" required>
                                    <option value="يومي">يومي</option>
                                    <option value="اجمالي">اجمالي</option>
                                </select>
                            </div>
                            <div class="ad-form-item">
                                
                                <input type="text" class="form-control" id="adPost" name="post_type">
                            </div>
                            <div class="ad-form-item">
                                <input type="number" class="form-control" id="adDays" name="days" min="1" value="7" required>
                            </div>
                            <div class="ad-form-item">
                                <input type="number" class="form-control" id="adCost" name="cost" required>
                            </div>
                            <div class="ad-form-item">
                                <input type="number" class="form-control" id="adExchangeRate" name="exchange_rate" required>
                            </div>
                            <div class="ad-form-item">
                                <select class="form-control" id="adAccount" name="ad_account_id" required>
                                    <option value="">-- اختر الحساب --</option>
                                    <?php
                                    // جلب الحسابات الإعلانية النشطة
                                    try {
                                        $stmt = $db->prepare("SELECT id, name FROM ad_accounts WHERE status = 'نشط' ORDER BY name ASC");
                                        $stmt->execute();
                                        $adAccounts = $stmt->fetchAll(PDO::FETCH_ASSOC);

                                        foreach ($adAccounts as $account) {
                                            echo '<option value="' . $account['id'] . '">' . htmlspecialchars($account['name']) . '</option>';
                                        }
                                    } catch (PDOException $e) {
                                        // في حالة حدوث خطأ، عرض خيار افتراضي
                                        echo '<option value="1">ليلى اسامة</option>';
                                    }
                                    ?>
                                </select>
                            </div>
                        </div>

                        <hr class="ad-form-divider">

                        <div class="text-center">
                            <button type="submit" class="btn btn-primary add-ad-submit">إضافة اعلان</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal المدفوعات -->
    <div class="modal fade" id="paymentsModal" tabindex="-1" aria-labelledby="paymentsModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="paymentsModalLabel">المدفوعات</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="payments-container">
                        <div class="payments-header">
                            <div class="payment-header-item">تاريخ الدفع</div>
                            <div class="payment-header-item">طريقة الدفع</div>
                            <div class="payment-header-item">المبلغ</div>
                        </div>
                        <div class="payments-list" id="paymentsList">
                            <!-- سيتم إضافة المدفوعات هنا بواسطة JavaScript -->
                        </div>
                        <div class="payments-total">
                            <div class="payment-total-label">اجمالي المدفوعات</div>
                            <div class="payment-total-value" id="paymentsTotal">0</div>
                        </div>
                        <div class="add-payment-container">
                            <button class="add-new-payment-btn" id="addNewPaymentBtn">
                                <i class="fas fa-plus"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal إضافة دفعة -->
    <div class="modal fade" id="addPaymentModal" tabindex="-1" aria-labelledby="addPaymentModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addPaymentModalLabel">إضافة دفعة</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="addPaymentForm">
                        <input type="hidden" id="paymentClientId" name="client_id">
                        <input type="hidden" id="paymentClientType" name="client_type">

                        <div class="mb-3">
                            <label for="paymentDate" class="form-label">تاريخ الدفع</label>
                            <input type="date" class="form-control" id="paymentDate" name="date" required>
                        </div>

                        <div class="mb-3">
                            <label for="paymentMethod" class="form-label">طريقة الدفع</label>
                            <select class="form-control" id="paymentMethod" name="method" required>
                                <option value="نقدي">نقدي</option>
                                <option value="تحويل بنكي">تحويل بنكي</option>
                                <option value="فوري">فوري</option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="paymentAmount" class="form-label">المبلغ</label>
                            <input type="number" class="form-control" id="paymentAmount" name="amount" required>
                        </div>

                        <div class="mb-3">
                            <label for="paymentNotes" class="form-label">ملاحظات</label>
                            <textarea class="form-control" id="paymentNotes" name="notes" rows="3"></textarea>
                        </div>

                        <div class="text-center">
                            <button type="submit" class="btn btn-primary">إضافة الدفعة</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal تعديل النسبة -->
    <div class="modal fade" id="editPercentageModal" tabindex="-1" aria-labelledby="editPercentageModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editPercentageModalLabel">تعديل نسبة العمولة</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="editPercentageForm">
                        <input type="hidden" id="percentageClientId" name="client_id">

                        <div class="mb-3">
                            <label for="clientPercentage" class="form-label">نسبة العمولة (%)</label>
                            <input type="number" class="form-control" id="clientPercentage" name="commission_percentage" min="0" max="100" step="0.01" value="50.00" required>
                            <small class="form-text text-muted">النسبة المئوية التي تضاف إلى سعر الصرف (مثال: 50% تعني إضافة 50% إلى سعر الصرف)</small>
                        </div>

                        <div class="text-center">
                            <button type="submit" class="btn btn-primary">حفظ التعديلات</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal تقرير العميل -->
    <div class="modal fade" id="clientReportModal" tabindex="-1" aria-labelledby="clientReportModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="clientReportModalLabel">تقرير العميل</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body p-0">
                    <div class="client-report-container" id="printableArea">
                        <div class="client-report-header">
                            <div class="client-report-name" id="reportClientName">Menna Zena</div>
                        </div>

                        <div class="client-report-content">
                            <table class="client-report-table">
                                <thead>
                                    <tr>
                                        <th width="10%">تاريخ</th>
                                        <th width="10%">نوع</th>
                                        <th width="10%">تكلفة</th>
                                        <th width="20%">حالة</th>
                                        <th width="30%">الاكونت</th>
                                        <th width="10%">الصرف</th>
                                    </tr>
                                </thead>
                                <tbody id="reportAdsList">
                                    <!-- سيتم إضافة الإعلانات هنا بواسطة JavaScript -->
                                </tbody>
                            </table>
                        </div>

                        <div class="client-report-summary">
                            <table class="client-report-summary-table">
                                <tr>
                                    <td class="summary-label">توتال الصرف</td>
                                    <td class="summary-value" id="reportTotalExchange">1000</td>
                                    <td class="summary-label">توتال الصرف بالنسبة</td>
                                    <td class="summary-value" id="reportTotalExchangeWithPercentage">1000</td>
                                </tr>
                                <tr>
                                    <td class="summary-label">الصرف يومي</td>
                                    <td class="summary-value" id="reportDailyExchange">1000</td>
                                    <td class="summary-label">المدفوعات</td>
                                    <td class="summary-value" id="reportTotalPayments">1000</td>
                                </tr>
                                <tr>
                                    <td colspan="2" class="summary-label">اجمالي الحساب حاليا</td>
                                    <td colspan="2" class="summary-value" id="reportCurrentTotal">1000</td>
                                </tr>
                            </table>
                        </div>

                        <div class="client-report-actions">
                            <button class="btn btn-primary" id="printReportBtn">إرسال</button>
                        </div>

                        <div class="client-report-pagination">
                            <button class="pagination-prev"><i class="fas fa-chevron-left"></i></button>
                            <span class="pagination-info" id="paginationInfo">74 / 137</span>
                            <button class="pagination-next"><i class="fas fa-chevron-right"></i></button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- jQuery and Bootstrap JS -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Custom JS -->
    <script src="<?php echo BASE_URL; ?>assets/js/scripts.js"></script>
    <script src="<?php echo BASE_URL; ?>assets/js/ad_clients.js"></script>
</body>
</html>
