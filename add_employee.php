<?php
// تضمين ملف الإعدادات
require_once 'config/config.php';

// تضمين اتصال قاعدة البيانات
require_once 'includes/db.php';

// تضمين الدوال المساعدة
require_once 'includes/functions.php';

// تضمين دوال المصادقة
require_once 'includes/auth.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    $_SESSION['flash_message'] = 'يجب تسجيل الدخول للوصول إلى هذه الصفحة';
    $_SESSION['flash_type'] = 'danger';
    redirect(BASE_URL . 'index.php');
}

// التحقق من طريقة الطلب
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // التحقق من البيانات المطلوبة
    if (
        isset($_POST['employee_name']) && !empty($_POST['employee_name']) &&
        isset($_POST['amount']) && !empty($_POST['amount']) &&
        isset($_POST['payment_type']) && !empty($_POST['payment_type']) &&
        isset($_POST['role']) && !empty($_POST['role'])
    ) {
        try {
            // التحقق من وجود جدول المرتبات
            $checkTableQuery = "SHOW TABLES LIKE 'salaries'";
            $stmt = $db->prepare($checkTableQuery);
            $stmt->execute();
            $tableExists = $stmt->rowCount() > 0;

            if (!$tableExists) {
                // إنشاء جدول المرتبات إذا لم يكن موجودًا
                $createTableQuery = "CREATE TABLE salaries (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    employee_name VARCHAR(255) NOT NULL,
                    amount DECIMAL(10,2) NOT NULL,
                    payment_type ENUM('نقدي', 'VF') NOT NULL,
                    role VARCHAR(50) NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )";
                $db->exec($createTableQuery);

                // إنشاء جدول صفحات الموظفين إذا لم يكن موجودًا
                $createEmployeePagesTableQuery = "CREATE TABLE employee_pages (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    employee_id INT NOT NULL,
                    page_name VARCHAR(255) NOT NULL,
                    salary DECIMAL(10,2) NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (employee_id) REFERENCES salaries(id) ON DELETE CASCADE
                )";
                $db->exec($createEmployeePagesTableQuery);
            }

            // إعداد البيانات للإدخال
            $employeeName = trim($_POST['employee_name']);
            $amount = floatval($_POST['amount']);
            $paymentType = $_POST['payment_type'];
            $role = $_POST['role'];
            $isOffice = isset($_POST['is_office']) ? 1 : 0;

            // بدء المعاملة
            $db->beginTransaction();

            // إدخال البيانات في جدول المرتبات
            $insertQuery = "INSERT INTO salaries (employee_name, amount, payment_type, role, is_office) VALUES (?, ?, ?, ?, ?)";
            $stmt = $db->prepare($insertQuery);
            $stmt->execute([$employeeName, $amount, $paymentType, $role, $isOffice]);

            // الحصول على معرف الموظف المضاف
            $employeeId = $db->lastInsertId();

            // إذا كان الدور هو "مودريتور" وتم إرسال بيانات الصفحات
            if ($role === 'مودريتور' && isset($_POST['page_names']) && isset($_POST['page_salaries'])) {
                $pageNames = $_POST['page_names'];
                $pageSalaries = $_POST['page_salaries'];

                // التحقق من وجود بيانات صالحة
                for ($i = 0; $i < count($pageNames); $i++) {
                    if (!empty($pageNames[$i]) && !empty($pageSalaries[$i])) {
                        // إدخال بيانات الصفحة
                        $insertPageQuery = "INSERT INTO employee_pages (employee_id, page_name, salary) VALUES (?, ?, ?)";
                        $stmt = $db->prepare($insertPageQuery);
                        $stmt->execute([$employeeId, $pageNames[$i], floatval($pageSalaries[$i])]);
                    }
                }
            }

            // تأكيد المعاملة
            $db->commit();

            // رسالة نجاح
            $_SESSION['flash_message'] = 'تمت إضافة الموظف بنجاح';
            $_SESSION['flash_type'] = 'success';
        } catch (PDOException $e) {
            // التراجع عن المعاملة في حالة حدوث خطأ
            $db->rollBack();

            // رسالة خطأ
            $_SESSION['flash_message'] = 'حدث خطأ أثناء إضافة الموظف: ' . $e->getMessage();
            $_SESSION['flash_type'] = 'danger';
        }
    } else {
        // رسالة خطأ في حالة عدم توفر البيانات المطلوبة
        $_SESSION['flash_message'] = 'يرجى ملء جميع الحقول المطلوبة';
        $_SESSION['flash_type'] = 'danger';
    }

    // إعادة التوجيه إلى الصفحة المناسبة
    if (isset($_POST['is_office']) && $_POST['is_office'] == 1) {
        redirect(BASE_URL . 'office_salaries.php');
    } else {
        redirect(BASE_URL . 'external_salaries.php');
    }
} else {
    // إعادة التوجيه إلى صفحة المرتبات الخارجية في حالة الوصول المباشر
    redirect(BASE_URL . 'external_salaries.php');
}
