<?php
// تضمين ملف الإعدادات
require_once 'config/config.php';

// تضمين اتصال قاعدة البيانات
require_once 'includes/db.php';

// تضمين الدوال المساعدة
require_once 'includes/functions.php';

// تضمين دوال المصادقة
require_once 'includes/auth.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'يجب تسجيل الدخول للوصول إلى هذه الخدمة']);
    exit;
}

// التحقق من طريقة الطلب
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'طريقة الطلب غير صحيحة']);
    exit;
}

// التحقق من البيانات المطلوبة
if (
    !isset($_POST['employee_id']) || empty($_POST['employee_id']) ||
    !isset($_POST['page_name']) || empty($_POST['page_name']) ||
    !isset($_POST['salary']) || empty($_POST['salary'])
) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'جميع الحقول مطلوبة']);
    exit;
}

// إعداد البيانات
$employeeId = intval($_POST['employee_id']);
$pageName = trim($_POST['page_name']);
$salary = floatval($_POST['salary']);

try {
    // التحقق من وجود الموظف
    $checkEmployeeQuery = "SELECT * FROM salaries WHERE id = :id";
    $stmt = $db->prepare($checkEmployeeQuery);
    $stmt->bindParam(':id', $employeeId);
    $stmt->execute();
    
    if ($stmt->rowCount() === 0) {
        header('Content-Type: application/json');
        echo json_encode(['success' => false, 'message' => 'الموظف غير موجود']);
        exit;
    }
    
    $employee = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // التحقق من أن الموظف من نوع "مودريتور"
    if ($employee['role'] !== 'مودريتور') {
        header('Content-Type: application/json');
        echo json_encode(['success' => false, 'message' => 'يمكن إضافة صفحات فقط للموظفين من نوع مودريتور']);
        exit;
    }
    
    // إضافة الصفحة
    $insertQuery = "INSERT INTO employee_pages (employee_id, page_name, salary) VALUES (:employee_id, :page_name, :salary)";
    $stmt = $db->prepare($insertQuery);
    $stmt->bindParam(':employee_id', $employeeId);
    $stmt->bindParam(':page_name', $pageName);
    $stmt->bindParam(':salary', $salary);
    $stmt->execute();
    
    // إرجاع استجابة نجاح
    header('Content-Type: application/json');
    echo json_encode([
        'success' => true,
        'message' => 'تمت إضافة الصفحة بنجاح',
        'page' => [
            'id' => $db->lastInsertId(),
            'employee_id' => $employeeId,
            'page_name' => $pageName,
            'salary' => $salary
        ]
    ]);
    
} catch (PDOException $e) {
    // إرجاع استجابة خطأ
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'حدث خطأ أثناء إضافة الصفحة: ' . $e->getMessage()]);
}
?>
