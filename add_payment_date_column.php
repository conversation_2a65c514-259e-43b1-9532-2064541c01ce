<?php
/**
 * إضافة عمود تاريخ السداد لجدول credit_cards
 */

// تضمين ملفات الاتصال بقاعدة البيانات
require_once 'config/config.php';
require_once 'includes/db.php';

try {
    echo "<h2>إضافة عمود تاريخ السداد لجدول credit_cards</h2>";
    
    // التحقق من وجود العمود
    $stmt = $db->query("SHOW COLUMNS FROM credit_cards LIKE 'payment_due_date'");
    $columnExists = $stmt->rowCount() > 0;
    
    if (!$columnExists) {
        echo "إضافة عمود payment_due_date...<br>";
        
        // إضافة عمود تاريخ السداد
        $db->exec("ALTER TABLE credit_cards ADD COLUMN payment_due_date DATE NULL DEFAULT NULL AFTER total_debt");
        
        echo "تم إضافة عمود payment_due_date بنجاح.<br>";
        
        // إضافة تواريخ تجريبية
        echo "إضافة تواريخ سداد تجريبية...<br>";
        
        $db->exec("UPDATE credit_cards SET payment_due_date = DATE_ADD(CURDATE(), INTERVAL 5 DAY) WHERE id = 1");
        $db->exec("UPDATE credit_cards SET payment_due_date = DATE_ADD(CURDATE(), INTERVAL 2 DAY) WHERE id = 2");
        $db->exec("UPDATE credit_cards SET payment_due_date = DATE_ADD(CURDATE(), INTERVAL 10 DAY) WHERE id = 3");
        $db->exec("UPDATE credit_cards SET payment_due_date = DATE_ADD(CURDATE(), INTERVAL 1 DAY) WHERE id = 4");
        
        echo "تم إضافة تواريخ السداد التجريبية.<br>";
        
    } else {
        echo "عمود payment_due_date موجود بالفعل.<br>";
    }
    
    // عرض البيانات الحالية
    echo "<h3>البطاقات مع تواريخ السداد:</h3>";
    $stmt = $db->query("SELECT id, name, balance, payment_due_date, 
                        DATEDIFF(payment_due_date, CURDATE()) as days_until_payment 
                        FROM credit_cards ORDER BY id");
    $cards = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>ID</th><th>الاسم</th><th>الرصيد</th><th>تاريخ السداد</th><th>الأيام المتبقية</th><th>طلب سداد</th></tr>";
    
    foreach ($cards as $card) {
        $daysUntilPayment = $card['days_until_payment'];
        $paymentRequest = ($daysUntilPayment <= 3 && $daysUntilPayment >= 0) ? 'نعم' : 'لا';
        
        echo "<tr>";
        echo "<td>{$card['id']}</td>";
        echo "<td>{$card['name']}</td>";
        echo "<td>{$card['balance']}</td>";
        echo "<td>{$card['payment_due_date']}</td>";
        echo "<td>{$daysUntilPayment}</td>";
        echo "<td style='color: " . ($paymentRequest == 'نعم' ? 'red' : 'green') . ";'>{$paymentRequest}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<br><a href='credit_cards.php'>العودة إلى صفحة البطاقات</a>";
    
} catch (PDOException $e) {
    echo "خطأ: " . $e->getMessage();
}
?>
