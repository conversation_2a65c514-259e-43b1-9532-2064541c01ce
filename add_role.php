<?php
// تضمين ملف الإعدادات
require_once 'config/config.php';

// تضمين اتصال قاعدة البيانات
require_once 'includes/db.php';

// تضمين الدوال المساعدة
require_once 'includes/functions.php';

// تضمين دوال المصادقة
require_once 'includes/auth.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'يجب تسجيل الدخول للوصول إلى هذه الخدمة']);
    exit;
}

// التحقق من طريقة الطلب
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'طريقة الطلب غير صحيحة']);
    exit;
}

// التحقق من البيانات المطلوبة
if (!isset($_POST['role_name']) || empty($_POST['role_name'])) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'اسم الوظيفة مطلوب']);
    exit;
}

// إعداد البيانات
$roleName = trim($_POST['role_name']);
$roleDescription = isset($_POST['role_description']) ? trim($_POST['role_description']) : '';

try {
    // التحقق من وجود جدول الوظائف
    $checkTableQuery = "SHOW TABLES LIKE 'roles'";
    $stmt = $db->prepare($checkTableQuery);
    $stmt->execute();
    $tableExists = $stmt->rowCount() > 0;
    
    if (!$tableExists) {
        // إنشاء جدول الوظائف
        $createTableQuery = "CREATE TABLE roles (
            id INT AUTO_INCREMENT PRIMARY KEY,
            role_name VARCHAR(100) NOT NULL UNIQUE,
            role_description TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )";
        $db->exec($createTableQuery);
        
        // إضافة الوظائف الافتراضية
        $insertDefaultRolesQuery = "INSERT INTO roles (role_name) VALUES 
            ('مودريتور'), 
            ('مصمم'), 
            ('اكونتات'), 
            ('مسؤول')";
        $db->exec($insertDefaultRolesQuery);
    }
    
    // التحقق من وجود الوظيفة بالفعل
    $checkRoleQuery = "SELECT id FROM roles WHERE role_name = :role_name";
    $stmt = $db->prepare($checkRoleQuery);
    $stmt->bindParam(':role_name', $roleName);
    $stmt->execute();
    
    if ($stmt->rowCount() > 0) {
        header('Content-Type: application/json');
        echo json_encode(['success' => false, 'message' => 'الوظيفة موجودة بالفعل']);
        exit;
    }
    
    // إضافة الوظيفة الجديدة
    $insertRoleQuery = "INSERT INTO roles (role_name, role_description) VALUES (:role_name, :role_description)";
    $stmt = $db->prepare($insertRoleQuery);
    $stmt->bindParam(':role_name', $roleName);
    $stmt->bindParam(':role_description', $roleDescription);
    $stmt->execute();
    
    header('Content-Type: application/json');
    echo json_encode(['success' => true, 'message' => 'تمت إضافة الوظيفة بنجاح']);
    
} catch (PDOException $e) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'حدث خطأ أثناء إضافة الوظيفة: ' . $e->getMessage()]);
}
