<?php
// Include header
require_once '../includes/header.php';

// Check if user is logged in and is admin
if (!isLoggedIn() || !isAdmin()) {
    $_SESSION['flash_message'] = 'يجب تسجيل الدخول كمدير للوصول إلى هذه الصفحة';
    $_SESSION['flash_type'] = 'danger';
    redirect(BASE_URL . 'login.php');
}

// Process add/edit group form
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['save_group'])) {
    $group_id = isset($_POST['group_id']) ? intval($_POST['group_id']) : null;
    $name = sanitize($_POST['name']);
    $description = sanitize($_POST['description']);
    $permissions = isset($_POST['permissions']) ? $_POST['permissions'] : [];
    
    // Validate input
    $errors = [];
    
    if (empty($name)) {
        $errors[] = 'اسم المجموعة مطلوب';
    }
    
    // If no errors, save group
    if (empty($errors)) {
        if ($group_id) {
            // Update existing group
            $query = "UPDATE permission_groups SET name = :name, description = :description WHERE id = :id";
            $stmt = $db->prepare($query);
            $stmt->bindParam(':name', $name);
            $stmt->bindParam(':description', $description);
            $stmt->bindParam(':id', $group_id);
            
            if ($stmt->execute()) {
                // Delete existing permissions
                $query = "DELETE FROM group_permissions WHERE group_id = :group_id";
                $stmt = $db->prepare($query);
                $stmt->bindParam(':group_id', $group_id);
                $stmt->execute();
                
                // Add new permissions
                if (!empty($permissions)) {
                    foreach ($permissions as $permission_id) {
                        $query = "INSERT INTO group_permissions (group_id, permission_id, created_at) VALUES (:group_id, :permission_id, NOW())";
                        $stmt = $db->prepare($query);
                        $stmt->bindParam(':group_id', $group_id);
                        $stmt->bindParam(':permission_id', $permission_id);
                        $stmt->execute();
                    }
                }
                
                // Log activity
                logActivity('update_group', 'تم تحديث مجموعة الصلاحيات: ' . $name);
                
                $_SESSION['flash_message'] = 'تم تحديث المجموعة بنجاح';
                $_SESSION['flash_type'] = 'success';
                redirect(BASE_URL . 'admin/groups.php');
            } else {
                $errors[] = 'حدث خطأ أثناء تحديث المجموعة';
            }
        } else {
            // Add new group
            $query = "INSERT INTO permission_groups (name, description, created_at, created_by) VALUES (:name, :description, NOW(), :created_by)";
            $stmt = $db->prepare($query);
            $stmt->bindParam(':name', $name);
            $stmt->bindParam(':description', $description);
            $stmt->bindParam(':created_by', $_SESSION['user_id']);
            
            if ($stmt->execute()) {
                $group_id = $db->lastInsertId();
                
                // Add permissions
                if (!empty($permissions)) {
                    foreach ($permissions as $permission_id) {
                        $query = "INSERT INTO group_permissions (group_id, permission_id, created_at) VALUES (:group_id, :permission_id, NOW())";
                        $stmt = $db->prepare($query);
                        $stmt->bindParam(':group_id', $group_id);
                        $stmt->bindParam(':permission_id', $permission_id);
                        $stmt->execute();
                    }
                }
                
                // Log activity
                logActivity('add_group', 'تم إضافة مجموعة صلاحيات جديدة: ' . $name);
                
                $_SESSION['flash_message'] = 'تم إضافة المجموعة بنجاح';
                $_SESSION['flash_type'] = 'success';
                redirect(BASE_URL . 'admin/groups.php');
            } else {
                $errors[] = 'حدث خطأ أثناء إضافة المجموعة';
            }
        }
    }
}

// Process delete group
if (isset($_GET['delete']) && is_numeric($_GET['delete'])) {
    $group_id = intval($_GET['delete']);
    
    // Get group name for activity log
    $query = "SELECT name FROM permission_groups WHERE id = :id LIMIT 1";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':id', $group_id);
    $stmt->execute();
    $group_name = $stmt->fetchColumn();
    
    // Delete group
    $query = "DELETE FROM permission_groups WHERE id = :id";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':id', $group_id);
    
    if ($stmt->execute()) {
        // Log activity
        logActivity('delete_group', 'تم حذف مجموعة الصلاحيات: ' . $group_name);
        
        $_SESSION['flash_message'] = 'تم حذف المجموعة بنجاح';
        $_SESSION['flash_type'] = 'success';
    } else {
        $_SESSION['flash_message'] = 'حدث خطأ أثناء حذف المجموعة';
        $_SESSION['flash_type'] = 'danger';
    }
    
    redirect(BASE_URL . 'admin/groups.php');
}

// Get group data for edit
$edit_group = null;
$group_permissions = [];

if (isset($_GET['edit']) && is_numeric($_GET['edit'])) {
    $group_id = intval($_GET['edit']);
    
    $query = "SELECT * FROM permission_groups WHERE id = :id LIMIT 1";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':id', $group_id);
    $stmt->execute();
    
    if ($stmt->rowCount() > 0) {
        $edit_group = $stmt->fetch(PDO::FETCH_ASSOC);
        $group_permissions = getGroupPermissions($group_id);
    }
}

// Get all groups
$query = "SELECT pg.*, creator.name as created_by_name, 
          (SELECT COUNT(*) FROM group_permissions WHERE group_id = pg.id) as permission_count,
          (SELECT COUNT(*) FROM user_groups WHERE group_id = pg.id) as user_count
          FROM permission_groups pg 
          LEFT JOIN users creator ON pg.created_by = creator.id 
          ORDER BY pg.name";
$stmt = $db->prepare($query);
$stmt->execute();
$groups = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Get all permissions grouped by category
$permissions_by_category = getPermissionsByCategory();
?>

<div class="row mb-4">
    <div class="col-md-12">
        <h1>إدارة مجموعات الصلاحيات</h1>
        <p class="text-muted">إضافة وتعديل وحذف مجموعات الصلاحيات</p>
    </div>
</div>

<div class="row">
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0"><?php echo $edit_group ? 'تعديل المجموعة' : 'إضافة مجموعة جديدة'; ?></h5>
            </div>
            <div class="card-body">
                <?php if (!empty($errors)): ?>
                    <div class="alert alert-danger">
                        <ul class="mb-0">
                            <?php foreach ($errors as $error): ?>
                                <li><?php echo $error; ?></li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                <?php endif; ?>
                
                <form method="POST" action="<?php echo $_SERVER['PHP_SELF']; ?>" class="needs-validation" novalidate>
                    <?php if ($edit_group): ?>
                        <input type="hidden" name="group_id" value="<?php echo $edit_group['id']; ?>">
                    <?php endif; ?>
                    
                    <div class="mb-3">
                        <label for="name" class="form-label">اسم المجموعة</label>
                        <input type="text" class="form-control" id="name" name="name" value="<?php echo $edit_group ? $edit_group['name'] : ''; ?>" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label">وصف المجموعة</label>
                        <textarea class="form-control" id="description" name="description" rows="3"><?php echo $edit_group ? $edit_group['description'] : ''; ?></textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">الصلاحيات</label>
                        <div class="accordion" id="permissionsAccordion">
                            <?php foreach ($permissions_by_category as $category => $category_permissions): ?>
                                <div class="accordion-item">
                                    <h2 class="accordion-header" id="heading_<?php echo md5($category); ?>">
                                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse_<?php echo md5($category); ?>" aria-expanded="false" aria-controls="collapse_<?php echo md5($category); ?>">
                                            <?php echo $category; ?>
                                        </button>
                                    </h2>
                                    <div id="collapse_<?php echo md5($category); ?>" class="accordion-collapse collapse" aria-labelledby="heading_<?php echo md5($category); ?>" data-bs-parent="#permissionsAccordion">
                                        <div class="accordion-body">
                                            <?php foreach ($category_permissions as $permission): ?>
                                                <div class="form-check">
                                                    <input type="checkbox" class="form-check-input" id="permission_<?php echo $permission['id']; ?>" name="permissions[]" value="<?php echo $permission['id']; ?>" <?php echo (in_array($permission['id'], $group_permissions)) ? 'checked' : ''; ?>>
                                                    <label class="form-check-label" for="permission_<?php echo $permission['id']; ?>"><?php echo $permission['name']; ?></label>
                                                    <small class="form-text text-muted d-block"><?php echo $permission['description']; ?></small>
                                                </div>
                                            <?php endforeach; ?>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                    
                    <div class="d-grid gap-2">
                        <button type="submit" name="save_group" class="btn btn-primary"><?php echo $edit_group ? 'تحديث المجموعة' : 'إضافة المجموعة'; ?></button>
                        <?php if ($edit_group): ?>
                            <a href="<?php echo BASE_URL; ?>admin/groups.php" class="btn btn-secondary">إلغاء</a>
                        <?php endif; ?>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">قائمة المجموعات</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>اسم المجموعة</th>
                                <th>الوصف</th>
                                <th>عدد الصلاحيات</th>
                                <th>عدد المستخدمين</th>
                                <th>تاريخ الإنشاء</th>
                                <th>أنشئت بواسطة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($groups as $group): ?>
                                <tr>
                                    <td><?php echo $group['id']; ?></td>
                                    <td><?php echo $group['name']; ?></td>
                                    <td><?php echo $group['description'] ?: '-'; ?></td>
                                    <td><span class="badge bg-info"><?php echo $group['permission_count']; ?></span></td>
                                    <td><span class="badge bg-secondary"><?php echo $group['user_count']; ?></span></td>
                                    <td><?php echo formatDate($group['created_at']); ?></td>
                                    <td><?php echo $group['created_by_name'] ?: '-'; ?></td>
                                    <td>
                                        <a href="<?php echo BASE_URL; ?>admin/groups.php?edit=<?php echo $group['id']; ?>" class="btn btn-sm btn-primary">تعديل</a>
                                        <a href="<?php echo BASE_URL; ?>admin/groups.php?delete=<?php echo $group['id']; ?>" class="btn btn-sm btn-danger" onclick="return confirm('هل أنت متأكد من حذف هذه المجموعة؟')">حذف</a>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// Include footer
require_once '../includes/footer.php';
?>
