<?php
// Include header
require_once '../includes/header.php';

// Check if user is logged in and is admin
if (!isLoggedIn() || !isAdmin()) {
    $_SESSION['flash_message'] = 'يجب تسجيل الدخول كمدير للوصول إلى هذه الصفحة';
    $_SESSION['flash_type'] = 'danger';
    redirect(BASE_URL . 'login.php');
}

// Get statistics
$stats = [
    'users' => 0,
    'clients' => 0,
    'pages' => 0,
    'accounts' => 0,
    'transactions' => 0,
    'ad_campaigns' => 0
];

// Get users count
$query = "SELECT COUNT(*) FROM users";
$stmt = $db->prepare($query);
$stmt->execute();
$stats['users'] = $stmt->fetchColumn();

// Get clients count
$query = "SELECT COUNT(*) FROM clients";
$stmt = $db->prepare($query);
$stmt->execute();
$stats['clients'] = $stmt->fetchColumn();

// Get pages count
$query = "SELECT COUNT(*) FROM pages";
$stmt = $db->prepare($query);
$stmt->execute();
$stats['pages'] = $stmt->fetchColumn();

// Get accounts count
$query = "SELECT COUNT(*) FROM accounts";
$stmt = $db->prepare($query);
$stmt->execute();
$stats['accounts'] = $stmt->fetchColumn();

// Get transactions count
$query = "SELECT COUNT(*) FROM transactions";
$stmt = $db->prepare($query);
$stmt->execute();
$stats['transactions'] = $stmt->fetchColumn();

// Get ad campaigns count
$query = "SELECT COUNT(*) FROM ad_campaigns";
$stmt = $db->prepare($query);
$stmt->execute();
$stats['ad_campaigns'] = $stmt->fetchColumn();

// Get recent activities
$query = "SELECT al.*, u.name as user_name
          FROM activity_logs al
          LEFT JOIN users u ON al.user_id = u.id
          ORDER BY al.created_at DESC
          LIMIT 10";
$stmt = $db->prepare($query);
$stmt->execute();
$activities = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Get recent users
$query = "SELECT u.*, creator.name as created_by_name
          FROM users u
          LEFT JOIN users creator ON u.created_by = creator.id
          ORDER BY u.created_at DESC
          LIMIT 5";
$stmt = $db->prepare($query);
$stmt->execute();
$recent_users = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>

<div class="row mb-4">
    <div class="col-md-12">
        <h1>لوحة تحكم الإدارة</h1>
        <p class="text-muted">مرحبًا بك في لوحة تحكم الإدارة</p>
    </div>
</div>

<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">روابط سريعة</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <a href="<?php echo BASE_URL; ?>admin/users.php" class="btn btn-primary d-block">
                            <i class="fas fa-users"></i> إدارة المستخدمين
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="<?php echo BASE_URL; ?>admin/permissions.php" class="btn btn-primary d-block">
                            <i class="fas fa-user-tag"></i> إدارة الصلاحيات
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="<?php echo BASE_URL; ?>admin/clients.php" class="btn btn-primary d-block">
                            <i class="fas fa-user-tie"></i> إدارة العملاء
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="<?php echo BASE_URL; ?>admin/pages.php" class="btn btn-primary d-block">
                            <i class="fas fa-file-alt"></i> إدارة الصفحات
                        </a>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <a href="<?php echo BASE_URL; ?>admin/accounts.php" class="btn btn-primary d-block">
                            <i class="fas fa-credit-card"></i> إدارة الحسابات
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="<?php echo BASE_URL; ?>admin/transactions.php" class="btn btn-primary d-block">
                            <i class="fas fa-exchange-alt"></i> إدارة المعاملات
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="<?php echo BASE_URL; ?>admin/budgets.php" class="btn btn-primary d-block">
                            <i class="fas fa-money-bill-wave"></i> إدارة الميزانيات
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="<?php echo BASE_URL; ?>admin/ad_campaigns.php" class="btn btn-primary d-block">
                            <i class="fas fa-ad"></i> إدارة حملات الإعلانات
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">إحصائيات النظام</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-2 mb-3">
                        <div class="card bg-light">
                            <div class="card-body text-center">
                                <h3 class="display-4"><?php echo $stats['users']; ?></h3>
                                <p class="mb-0">المستخدمين</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 mb-3">
                        <div class="card bg-light">
                            <div class="card-body text-center">
                                <h3 class="display-4"><?php echo $stats['clients']; ?></h3>
                                <p class="mb-0">العملاء</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 mb-3">
                        <div class="card bg-light">
                            <div class="card-body text-center">
                                <h3 class="display-4"><?php echo $stats['pages']; ?></h3>
                                <p class="mb-0">الصفحات</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 mb-3">
                        <div class="card bg-light">
                            <div class="card-body text-center">
                                <h3 class="display-4"><?php echo $stats['accounts']; ?></h3>
                                <p class="mb-0">الحسابات</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 mb-3">
                        <div class="card bg-light">
                            <div class="card-body text-center">
                                <h3 class="display-4"><?php echo $stats['transactions']; ?></h3>
                                <p class="mb-0">المعاملات</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2 mb-3">
                        <div class="card bg-light">
                            <div class="card-body text-center">
                                <h3 class="display-4"><?php echo $stats['ad_campaigns']; ?></h3>
                                <p class="mb-0">الحملات</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">آخر النشاطات</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>المستخدم</th>
                                <th>النشاط</th>
                                <th>التاريخ</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($activities as $activity): ?>
                                <tr>
                                    <td><?php echo $activity['user_name'] ?: 'غير معروف'; ?></td>
                                    <td><?php echo $activity['description']; ?></td>
                                    <td><?php echo formatDate($activity['created_at']); ?></td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                <div class="text-center mt-3">
                    <a href="<?php echo BASE_URL; ?>admin/activities.php" class="btn btn-sm btn-primary">عرض المزيد</a>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">آخر المستخدمين المسجلين</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>الاسم</th>
                                <th>البريد الإلكتروني</th>
                                <th>الصلاحية</th>
                                <th>تاريخ التسجيل</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($recent_users as $user): ?>
                                <tr>
                                    <td><?php echo $user['name']; ?></td>
                                    <td><?php echo $user['email']; ?></td>
                                    <td>
                                        <?php if ($user['is_admin']): ?>
                                            <span class="badge bg-danger">مدير النظام</span>
                                        <?php else: ?>
                                            <span class="badge bg-primary">مستخدم</span>
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo formatDate($user['created_at']); ?></td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                <div class="text-center mt-3">
                    <a href="<?php echo BASE_URL; ?>admin/users.php" class="btn btn-sm btn-primary">عرض المزيد</a>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// Include footer
require_once '../includes/footer.php';
?>
