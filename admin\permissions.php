<?php
/**
 * صفحة إدارة الصلاحيات
 */

// تضمين ملف التكوين
require_once '../config/config.php';

// تضمين اتصال قاعدة البيانات
require_once '../includes/db.php';

// تضمين الدوال المساعدة
require_once '../includes/functions.php';

// تضمين دوال المصادقة
require_once '../includes/auth.php';

// تضمين دوال الصلاحيات
require_once '../includes/permissions.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    $_SESSION['flash_message'] = 'يجب تسجيل الدخول للوصول إلى هذه الصفحة';
    $_SESSION['flash_type'] = 'danger';
    redirect(BASE_URL . 'index.php');
    exit;
}

// التحقق من صلاحيات المستخدم
if (!isAdmin()) {
    $_SESSION['flash_message'] = 'ليس لديك صلاحية للوصول إلى هذه الصفحة';
    $_SESSION['flash_type'] = 'danger';
    redirect(BASE_URL . 'dashboard.php');
    exit;
}

// معالجة إضافة مجموعة صلاحيات جديدة
if (isset($_POST['add_group'])) {
    $name = trim($_POST['name']);
    $description = trim($_POST['description']);
    $permissions = isset($_POST['permissions']) ? $_POST['permissions'] : [];
    
    $errors = [];
    
    if (empty($name)) {
        $errors[] = 'اسم المجموعة مطلوب';
    }
    
    if (empty($errors)) {
        try {
            // إضافة المجموعة
            $query = "INSERT INTO permission_groups (name, description, created_by) VALUES (:name, :description, :created_by)";
            $stmt = $db->prepare($query);
            $stmt->bindParam(':name', $name);
            $stmt->bindParam(':description', $description);
            $stmt->bindParam(':created_by', $_SESSION['user_id']);
            $stmt->execute();
            
            $group_id = $db->lastInsertId();
            
            // إضافة الصلاحيات للمجموعة
            if (!empty($permissions)) {
                foreach ($permissions as $permission_id) {
                    $query = "INSERT INTO group_permissions (group_id, permission_id, created_by) VALUES (:group_id, :permission_id, :created_by)";
                    $stmt = $db->prepare($query);
                    $stmt->bindParam(':group_id', $group_id);
                    $stmt->bindParam(':permission_id', $permission_id);
                    $stmt->bindParam(':created_by', $_SESSION['user_id']);
                    $stmt->execute();
                }
            }
            
            // تسجيل النشاط
            logActivity('add_permission_group', 'تم إضافة مجموعة صلاحيات جديدة: ' . $name);
            
            $_SESSION['flash_message'] = 'تم إضافة مجموعة الصلاحيات بنجاح';
            $_SESSION['flash_type'] = 'success';
            redirect(BASE_URL . 'admin/permissions.php');
        } catch (PDOException $e) {
            $errors[] = 'حدث خطأ أثناء إضافة مجموعة الصلاحيات: ' . $e->getMessage();
        }
    }
}

// معالجة تعديل مجموعة صلاحيات
if (isset($_POST['edit_group'])) {
    $group_id = intval($_POST['group_id']);
    $name = trim($_POST['name']);
    $description = trim($_POST['description']);
    $permissions = isset($_POST['permissions']) ? $_POST['permissions'] : [];
    
    $errors = [];
    
    if (empty($name)) {
        $errors[] = 'اسم المجموعة مطلوب';
    }
    
    if (empty($errors)) {
        try {
            // تعديل المجموعة
            $query = "UPDATE permission_groups SET name = :name, description = :description WHERE id = :id";
            $stmt = $db->prepare($query);
            $stmt->bindParam(':name', $name);
            $stmt->bindParam(':description', $description);
            $stmt->bindParam(':id', $group_id);
            $stmt->execute();
            
            // حذف الصلاحيات الحالية للمجموعة
            $query = "DELETE FROM group_permissions WHERE group_id = :group_id";
            $stmt = $db->prepare($query);
            $stmt->bindParam(':group_id', $group_id);
            $stmt->execute();
            
            // إضافة الصلاحيات الجديدة للمجموعة
            if (!empty($permissions)) {
                foreach ($permissions as $permission_id) {
                    $query = "INSERT INTO group_permissions (group_id, permission_id, created_by) VALUES (:group_id, :permission_id, :created_by)";
                    $stmt = $db->prepare($query);
                    $stmt->bindParam(':group_id', $group_id);
                    $stmt->bindParam(':permission_id', $permission_id);
                    $stmt->bindParam(':created_by', $_SESSION['user_id']);
                    $stmt->execute();
                }
            }
            
            // تسجيل النشاط
            logActivity('edit_permission_group', 'تم تعديل مجموعة صلاحيات: ' . $name);
            
            $_SESSION['flash_message'] = 'تم تعديل مجموعة الصلاحيات بنجاح';
            $_SESSION['flash_type'] = 'success';
            redirect(BASE_URL . 'admin/permissions.php');
        } catch (PDOException $e) {
            $errors[] = 'حدث خطأ أثناء تعديل مجموعة الصلاحيات: ' . $e->getMessage();
        }
    }
}

// معالجة حذف مجموعة صلاحيات
if (isset($_GET['delete_group']) && is_numeric($_GET['delete_group'])) {
    $group_id = intval($_GET['delete_group']);
    
    try {
        // التحقق من وجود المجموعة
        $query = "SELECT name FROM permission_groups WHERE id = :id";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':id', $group_id);
        $stmt->execute();
        
        if ($stmt->rowCount() > 0) {
            $group = $stmt->fetch(PDO::FETCH_ASSOC);
            
            // حذف المجموعة
            $query = "DELETE FROM permission_groups WHERE id = :id";
            $stmt = $db->prepare($query);
            $stmt->bindParam(':id', $group_id);
            $stmt->execute();
            
            // تسجيل النشاط
            logActivity('delete_permission_group', 'تم حذف مجموعة صلاحيات: ' . $group['name']);
            
            $_SESSION['flash_message'] = 'تم حذف مجموعة الصلاحيات بنجاح';
            $_SESSION['flash_type'] = 'success';
        } else {
            $_SESSION['flash_message'] = 'مجموعة الصلاحيات غير موجودة';
            $_SESSION['flash_type'] = 'danger';
        }
    } catch (PDOException $e) {
        $_SESSION['flash_message'] = 'حدث خطأ أثناء حذف مجموعة الصلاحيات: ' . $e->getMessage();
        $_SESSION['flash_type'] = 'danger';
    }
    
    redirect(BASE_URL . 'admin/permissions.php');
}

// جلب جميع مجموعات الصلاحيات
$query = "SELECT pg.*, u.name as created_by_name 
          FROM permission_groups pg 
          LEFT JOIN users u ON pg.created_by = u.id 
          ORDER BY pg.name";
$stmt = $db->prepare($query);
$stmt->execute();
$groups = $stmt->fetchAll(PDO::FETCH_ASSOC);

// جلب جميع الصلاحيات مجمعة حسب الفئة
$permissions_by_category = getPermissionsByCategory();

// جلب صلاحيات كل مجموعة
$group_permissions = [];
foreach ($groups as $group) {
    $query = "SELECT permission_id FROM group_permissions WHERE group_id = :group_id";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':group_id', $group['id']);
    $stmt->execute();
    $permissions = $stmt->fetchAll(PDO::FETCH_COLUMN);
    $group_permissions[$group['id']] = $permissions;
}

// تحديد المجموعة المراد تعديلها
$edit_group = null;
if (isset($_GET['edit_group']) && is_numeric($_GET['edit_group'])) {
    $group_id = intval($_GET['edit_group']);
    
    $query = "SELECT * FROM permission_groups WHERE id = :id";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':id', $group_id);
    $stmt->execute();
    
    if ($stmt->rowCount() > 0) {
        $edit_group = $stmt->fetch(PDO::FETCH_ASSOC);
    }
}
?>
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الصلاحيات - <?php echo SITE_TITLE; ?></title>

    <!-- Bootstrap RTL CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Google Fonts - Cairo -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>assets/css/style.css">
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>assets/css/admin.css">
</head>
<body>
    <!-- Header -->
    <?php include '../includes/header.php'; ?>

    <!-- Main Content -->
    <div class="container mt-4">
        <div class="row">
            <div class="col-md-12">
                <h1>إدارة الصلاحيات</h1>
                <p class="text-muted">إدارة مجموعات الصلاحيات وتعيين الصلاحيات للمستخدمين</p>
            </div>
        </div>

        <?php if (!empty($errors)): ?>
            <div class="alert alert-danger">
                <ul class="mb-0">
                    <?php foreach ($errors as $error): ?>
                        <li><?php echo $error; ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>
        <?php endif; ?>

        <div class="row">
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0"><?php echo $edit_group ? 'تعديل مجموعة صلاحيات' : 'إضافة مجموعة صلاحيات جديدة'; ?></h5>
                    </div>
                    <div class="card-body">
                        <form method="post" action="">
                            <?php if ($edit_group): ?>
                                <input type="hidden" name="group_id" value="<?php echo $edit_group['id']; ?>">
                            <?php endif; ?>
                            
                            <div class="mb-3">
                                <label for="name" class="form-label">اسم المجموعة</label>
                                <input type="text" class="form-control" id="name" name="name" value="<?php echo $edit_group ? $edit_group['name'] : ''; ?>" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="description" class="form-label">وصف المجموعة</label>
                                <textarea class="form-control" id="description" name="description" rows="3"><?php echo $edit_group ? $edit_group['description'] : ''; ?></textarea>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">الصلاحيات</label>
                                
                                <?php foreach ($permissions_by_category as $category => $permissions): ?>
                                    <div class="card mb-2">
                                        <div class="card-header py-2">
                                            <h6 class="mb-0"><?php echo $category; ?></h6>
                                        </div>
                                        <div class="card-body py-2">
                                            <?php foreach ($permissions as $permission): ?>
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" name="permissions[]" value="<?php echo $permission['id']; ?>" id="permission_<?php echo $permission['id']; ?>"
                                                        <?php echo ($edit_group && in_array($permission['id'], $group_permissions[$edit_group['id']])) ? 'checked' : ''; ?>>
                                                    <label class="form-check-label" for="permission_<?php echo $permission['id']; ?>">
                                                        <?php echo $permission['name']; ?>
                                                        <small class="text-muted d-block"><?php echo $permission['description']; ?></small>
                                                    </label>
                                                </div>
                                            <?php endforeach; ?>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                            
                            <div class="d-grid">
                                <button type="submit" name="<?php echo $edit_group ? 'edit_group' : 'add_group'; ?>" class="btn btn-primary">
                                    <?php echo $edit_group ? 'تعديل المجموعة' : 'إضافة المجموعة'; ?>
                                </button>
                                
                                <?php if ($edit_group): ?>
                                    <a href="<?php echo BASE_URL; ?>admin/permissions.php" class="btn btn-secondary mt-2">إلغاء التعديل</a>
                                <?php endif; ?>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">مجموعات الصلاحيات</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>اسم المجموعة</th>
                                        <th>الوصف</th>
                                        <th>عدد الصلاحيات</th>
                                        <th>تاريخ الإنشاء</th>
                                        <th>أنشئت بواسطة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if (empty($groups)): ?>
                                        <tr>
                                            <td colspan="7" class="text-center">لا توجد مجموعات صلاحيات</td>
                                        </tr>
                                    <?php else: ?>
                                        <?php foreach ($groups as $index => $group): ?>
                                            <tr>
                                                <td><?php echo $index + 1; ?></td>
                                                <td><?php echo $group['name']; ?></td>
                                                <td><?php echo $group['description'] ?: '-'; ?></td>
                                                <td><?php echo count($group_permissions[$group['id']]); ?></td>
                                                <td><?php echo formatDate($group['created_at']); ?></td>
                                                <td><?php echo $group['created_by_name'] ?: '-'; ?></td>
                                                <td>
                                                    <a href="<?php echo BASE_URL; ?>admin/permissions.php?edit_group=<?php echo $group['id']; ?>" class="btn btn-sm btn-primary">تعديل</a>
                                                    <a href="<?php echo BASE_URL; ?>admin/permissions.php?delete_group=<?php echo $group['id']; ?>" class="btn btn-sm btn-danger" onclick="return confirm('هل أنت متأكد من حذف هذه المجموعة؟')">حذف</a>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- jQuery and Bootstrap JS -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
