<?php
// Include header
require_once '../includes/header.php';

// Check if user is logged in and is admin
if (!isLoggedIn() || !isAdmin()) {
    $_SESSION['flash_message'] = 'يجب تسجيل الدخول كمدير للوصول إلى هذه الصفحة';
    $_SESSION['flash_type'] = 'danger';
    redirect(BASE_URL . 'login.php');
}

// Process add/edit user form
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['save_user'])) {
    $user_id = isset($_POST['user_id']) ? intval($_POST['user_id']) : null;
    $name = sanitize($_POST['name']);
    $email = sanitize($_POST['email']);
    $password = $_POST['password'];
    $is_admin = isset($_POST['is_admin']) ? 1 : 0;
    $permissions = isset($_POST['permissions']) ? $_POST['permissions'] : [];
    $groups = isset($_POST['groups']) ? $_POST['groups'] : [];
    
    // Validate input
    $errors = [];
    
    if (empty($name)) {
        $errors[] = 'الاسم مطلوب';
    }
    
    if (empty($email)) {
        $errors[] = 'البريد الإلكتروني مطلوب';
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $errors[] = 'البريد الإلكتروني غير صالح';
    }
    
    // Check if email is already used by another user
    if ($user_id) {
        // Edit user
        $query = "SELECT id FROM users WHERE email = :email AND id != :id LIMIT 1";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':email', $email);
        $stmt->bindParam(':id', $user_id);
        $stmt->execute();
        
        if ($stmt->rowCount() > 0) {
            $errors[] = 'البريد الإلكتروني مستخدم بالفعل';
        }
    } else {
        // Add new user
        if (empty($password)) {
            $errors[] = 'كلمة المرور مطلوبة';
        } elseif (strlen($password) < 6) {
            $errors[] = 'كلمة المرور يجب أن تكون على الأقل 6 أحرف';
        }
        
        $query = "SELECT id FROM users WHERE email = :email LIMIT 1";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':email', $email);
        $stmt->execute();
        
        if ($stmt->rowCount() > 0) {
            $errors[] = 'البريد الإلكتروني مستخدم بالفعل';
        }
    }
    
    // If no errors, save user
    if (empty($errors)) {
        if ($user_id) {
            // Update existing user
            if (!empty($password)) {
                // Update with new password
                $hashed_password = password_hash($password, PASSWORD_DEFAULT);
                $query = "UPDATE users SET name = :name, email = :email, password = :password, is_admin = :is_admin, updated_at = NOW() WHERE id = :id";
                $stmt = $db->prepare($query);
                $stmt->bindParam(':password', $hashed_password);
            } else {
                // Update without changing password
                $query = "UPDATE users SET name = :name, email = :email, is_admin = :is_admin, updated_at = NOW() WHERE id = :id";
                $stmt = $db->prepare($query);
            }
            
            $stmt->bindParam(':name', $name);
            $stmt->bindParam(':email', $email);
            $stmt->bindParam(':is_admin', $is_admin, PDO::PARAM_BOOL);
            $stmt->bindParam(':id', $user_id);
            
            if ($stmt->execute()) {
                // Delete existing permissions
                $query = "DELETE FROM user_permissions WHERE user_id = :user_id";
                $stmt = $db->prepare($query);
                $stmt->bindParam(':user_id', $user_id);
                $stmt->execute();
                
                // Add new permissions
                if (!empty($permissions)) {
                    foreach ($permissions as $permission_id) {
                        $query = "INSERT INTO user_permissions (user_id, permission_id, created_at, created_by) VALUES (:user_id, :permission_id, NOW(), :created_by)";
                        $stmt = $db->prepare($query);
                        $stmt->bindParam(':user_id', $user_id);
                        $stmt->bindParam(':permission_id', $permission_id);
                        $stmt->bindParam(':created_by', $_SESSION['user_id']);
                        $stmt->execute();
                    }
                }
                
                // Delete existing groups
                $query = "DELETE FROM user_groups WHERE user_id = :user_id";
                $stmt = $db->prepare($query);
                $stmt->bindParam(':user_id', $user_id);
                $stmt->execute();
                
                // Add new groups
                if (!empty($groups)) {
                    foreach ($groups as $group_id) {
                        $query = "INSERT INTO user_groups (user_id, group_id, created_at, created_by) VALUES (:user_id, :group_id, NOW(), :created_by)";
                        $stmt = $db->prepare($query);
                        $stmt->bindParam(':user_id', $user_id);
                        $stmt->bindParam(':group_id', $group_id);
                        $stmt->bindParam(':created_by', $_SESSION['user_id']);
                        $stmt->execute();
                    }
                }
                
                // Log activity
                logActivity('update_user', 'تم تحديث المستخدم: ' . $name);
                
                $_SESSION['flash_message'] = 'تم تحديث المستخدم بنجاح';
                $_SESSION['flash_type'] = 'success';
                redirect(BASE_URL . 'admin/users.php');
            } else {
                $errors[] = 'حدث خطأ أثناء تحديث المستخدم';
            }
        } else {
            // Add new user
            $user_id = registerUser($name, $email, $password, $is_admin, $_SESSION['user_id']);
            
            if ($user_id) {
                // Add permissions
                if (!empty($permissions)) {
                    foreach ($permissions as $permission_id) {
                        $query = "INSERT INTO user_permissions (user_id, permission_id, created_at, created_by) VALUES (:user_id, :permission_id, NOW(), :created_by)";
                        $stmt = $db->prepare($query);
                        $stmt->bindParam(':user_id', $user_id);
                        $stmt->bindParam(':permission_id', $permission_id);
                        $stmt->bindParam(':created_by', $_SESSION['user_id']);
                        $stmt->execute();
                    }
                }
                
                // Add groups
                if (!empty($groups)) {
                    foreach ($groups as $group_id) {
                        $query = "INSERT INTO user_groups (user_id, group_id, created_at, created_by) VALUES (:user_id, :group_id, NOW(), :created_by)";
                        $stmt = $db->prepare($query);
                        $stmt->bindParam(':user_id', $user_id);
                        $stmt->bindParam(':group_id', $group_id);
                        $stmt->bindParam(':created_by', $_SESSION['user_id']);
                        $stmt->execute();
                    }
                }
                
                // Log activity
                logActivity('add_user', 'تم إضافة مستخدم جديد: ' . $name);
                
                $_SESSION['flash_message'] = 'تم إضافة المستخدم بنجاح';
                $_SESSION['flash_type'] = 'success';
                redirect(BASE_URL . 'admin/users.php');
            } else {
                $errors[] = 'حدث خطأ أثناء إضافة المستخدم';
            }
        }
    }
}

// Process delete user
if (isset($_GET['delete']) && is_numeric($_GET['delete'])) {
    $user_id = intval($_GET['delete']);
    
    // Don't allow deleting self
    if ($user_id === $_SESSION['user_id']) {
        $_SESSION['flash_message'] = 'لا يمكن حذف حسابك الشخصي';
        $_SESSION['flash_type'] = 'danger';
        redirect(BASE_URL . 'admin/users.php');
    }
    
    // Get user name for activity log
    $query = "SELECT name FROM users WHERE id = :id LIMIT 1";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':id', $user_id);
    $stmt->execute();
    $user_name = $stmt->fetchColumn();
    
    // Delete user
    $query = "DELETE FROM users WHERE id = :id";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':id', $user_id);
    
    if ($stmt->execute()) {
        // Log activity
        logActivity('delete_user', 'تم حذف المستخدم: ' . $user_name);
        
        $_SESSION['flash_message'] = 'تم حذف المستخدم بنجاح';
        $_SESSION['flash_type'] = 'success';
    } else {
        $_SESSION['flash_message'] = 'حدث خطأ أثناء حذف المستخدم';
        $_SESSION['flash_type'] = 'danger';
    }
    
    redirect(BASE_URL . 'admin/users.php');
}

// Get user data for edit
$edit_user = null;
$user_permissions = [];
$user_groups = [];

if (isset($_GET['edit']) && is_numeric($_GET['edit'])) {
    $user_id = intval($_GET['edit']);
    
    $query = "SELECT * FROM users WHERE id = :id LIMIT 1";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':id', $user_id);
    $stmt->execute();
    
    if ($stmt->rowCount() > 0) {
        $edit_user = $stmt->fetch(PDO::FETCH_ASSOC);
        $user_permissions = getUserPermissions($user_id);
        $user_groups = getUserGroups($user_id);
    }
}

// Get all users
$query = "SELECT u.*, creator.name as created_by_name 
          FROM users u 
          LEFT JOIN users creator ON u.created_by = creator.id 
          ORDER BY u.name";
$stmt = $db->prepare($query);
$stmt->execute();
$users = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Get all permissions grouped by category
$permissions_by_category = getPermissionsByCategory();

// Get all permission groups
$permission_groups = getAllPermissionGroups();
?>

<div class="row mb-4">
    <div class="col-md-12">
        <h1>إدارة المستخدمين</h1>
        <p class="text-muted">إضافة وتعديل وحذف المستخدمين وإدارة صلاحياتهم</p>
    </div>
</div>

<div class="row">
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0"><?php echo $edit_user ? 'تعديل المستخدم' : 'إضافة مستخدم جديد'; ?></h5>
            </div>
            <div class="card-body">
                <?php if (!empty($errors)): ?>
                    <div class="alert alert-danger">
                        <ul class="mb-0">
                            <?php foreach ($errors as $error): ?>
                                <li><?php echo $error; ?></li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                <?php endif; ?>
                
                <form method="POST" action="<?php echo $_SERVER['PHP_SELF']; ?>" class="needs-validation" novalidate>
                    <?php if ($edit_user): ?>
                        <input type="hidden" name="user_id" value="<?php echo $edit_user['id']; ?>">
                    <?php endif; ?>
                    
                    <div class="mb-3">
                        <label for="name" class="form-label">الاسم</label>
                        <input type="text" class="form-control" id="name" name="name" value="<?php echo $edit_user ? $edit_user['name'] : ''; ?>" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="email" class="form-label">البريد الإلكتروني</label>
                        <input type="email" class="form-control" id="email" name="email" value="<?php echo $edit_user ? $edit_user['email'] : ''; ?>" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="password" class="form-label"><?php echo $edit_user ? 'كلمة المرور (اتركها فارغة للاحتفاظ بالحالية)' : 'كلمة المرور'; ?></label>
                        <input type="password" class="form-control" id="password" name="password" <?php echo $edit_user ? '' : 'required'; ?>>
                    </div>
                    
                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="is_admin" name="is_admin" <?php echo ($edit_user && $edit_user['is_admin']) ? 'checked' : ''; ?>>
                        <label class="form-check-label" for="is_admin">مدير النظام</label>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">مجموعات الصلاحيات</label>
                        <?php foreach ($permission_groups as $group): ?>
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" id="group_<?php echo $group['id']; ?>" name="groups[]" value="<?php echo $group['id']; ?>" <?php echo (in_array($group['id'], $user_groups)) ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="group_<?php echo $group['id']; ?>"><?php echo $group['name']; ?></label>
                            </div>
                        <?php endforeach; ?>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">الصلاحيات المباشرة</label>
                        <div class="accordion" id="permissionsAccordion">
                            <?php foreach ($permissions_by_category as $category => $category_permissions): ?>
                                <div class="accordion-item">
                                    <h2 class="accordion-header" id="heading_<?php echo md5($category); ?>">
                                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse_<?php echo md5($category); ?>" aria-expanded="false" aria-controls="collapse_<?php echo md5($category); ?>">
                                            <?php echo $category; ?>
                                        </button>
                                    </h2>
                                    <div id="collapse_<?php echo md5($category); ?>" class="accordion-collapse collapse" aria-labelledby="heading_<?php echo md5($category); ?>" data-bs-parent="#permissionsAccordion">
                                        <div class="accordion-body">
                                            <?php foreach ($category_permissions as $permission): ?>
                                                <div class="form-check">
                                                    <input type="checkbox" class="form-check-input" id="permission_<?php echo $permission['id']; ?>" name="permissions[]" value="<?php echo $permission['id']; ?>" <?php echo (in_array($permission['id'], $user_permissions)) ? 'checked' : ''; ?>>
                                                    <label class="form-check-label" for="permission_<?php echo $permission['id']; ?>"><?php echo $permission['name']; ?></label>
                                                    <small class="form-text text-muted d-block"><?php echo $permission['description']; ?></small>
                                                </div>
                                            <?php endforeach; ?>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                    
                    <div class="d-grid gap-2">
                        <button type="submit" name="save_user" class="btn btn-primary"><?php echo $edit_user ? 'تحديث المستخدم' : 'إضافة المستخدم'; ?></button>
                        <?php if ($edit_user): ?>
                            <a href="<?php echo BASE_URL; ?>admin/users.php" class="btn btn-secondary">إلغاء</a>
                        <?php endif; ?>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">قائمة المستخدمين</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>الاسم</th>
                                <th>البريد الإلكتروني</th>
                                <th>الصلاحية</th>
                                <th>الحالة</th>
                                <th>تاريخ الإنشاء</th>
                                <th>أنشئ بواسطة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($users as $user): ?>
                                <tr>
                                    <td><?php echo $user['id']; ?></td>
                                    <td><?php echo $user['name']; ?></td>
                                    <td><?php echo $user['email']; ?></td>
                                    <td>
                                        <?php if ($user['is_admin']): ?>
                                            <span class="badge bg-danger">مدير النظام</span>
                                        <?php else: ?>
                                            <span class="badge bg-primary">مستخدم</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if ($user['status'] === 'active'): ?>
                                            <span class="badge bg-success">نشط</span>
                                        <?php elseif ($user['status'] === 'inactive'): ?>
                                            <span class="badge bg-warning">غير نشط</span>
                                        <?php else: ?>
                                            <span class="badge bg-danger">محظور</span>
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo formatDate($user['created_at']); ?></td>
                                    <td><?php echo $user['created_by_name'] ?: '-'; ?></td>
                                    <td>
                                        <a href="<?php echo BASE_URL; ?>admin/users.php?edit=<?php echo $user['id']; ?>" class="btn btn-sm btn-primary">تعديل</a>
                                        <?php if ($user['id'] !== $_SESSION['user_id']): ?>
                                            <a href="<?php echo BASE_URL; ?>admin/users.php?delete=<?php echo $user['id']; ?>" class="btn btn-sm btn-danger" onclick="return confirm('هل أنت متأكد من حذف هذا المستخدم؟')">حذف</a>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// Include footer
require_once '../includes/footer.php';
?>
