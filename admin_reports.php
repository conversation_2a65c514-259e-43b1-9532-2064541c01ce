<?php
// تضمين ملف التكوين
require_once 'config/config.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header('Location: index.php');
    exit;
}
?>
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقرير حسابات الإدارة - <?php echo SITE_TITLE; ?></title>

    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>assets/css/styles.css">
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>assets/css/reports.css">
</head>
<body>
    <!-- Main Content -->
    <div class="page-container">
        <div class="page-header">
            <a href="<?php echo BASE_URL; ?>" class="logo-link">
                <img src="<?php echo BASE_URL; ?>assets/images/logo.png" alt="<?php echo SITE_TITLE; ?>" class="page-logo">
            </a>
            <h1 class="page-title">تقرير حسابات الإدارة</h1>
            <a href="manage_pages.php" class="back-btn">
                <i class="fas fa-arrow-right"></i> العودة
            </a>
        </div>

        <!-- Content Area -->
        <div class="content-area">
            <div class="admin-reports-container">
                <div class="admin-reports-header">
                    <div class="admin-reports-actions">
                        <div class="search-box">
                            <input type="text" id="searchInput" class="form-control" placeholder="البحث...">
                            <i class="fas fa-search search-icon"></i>
                        </div>
                        <button id="printReportBtn" class="btn btn-primary">
                            <i class="fas fa-print"></i> طباعة التقرير
                        </button>
                        <div class="date-filter">
                            <label for="monthFilter">الشهر:</label>
                            <select id="monthFilter" class="form-select">
                                <option value="1">يناير</option>
                                <option value="2">فبراير</option>
                                <option value="3">مارس</option>
                                <option value="4">أبريل</option>
                                <option value="5">مايو</option>
                                <option value="6">يونيو</option>
                                <option value="7">يوليو</option>
                                <option value="8">أغسطس</option>
                                <option value="9">سبتمبر</option>
                                <option value="10">أكتوبر</option>
                                <option value="11">نوفمبر</option>
                                <option value="12">ديسمبر</option>
                            </select>
                        </div>
                        <div class="date-filter">
                            <label for="yearFilter">السنة:</label>
                            <select id="yearFilter" class="form-select">
                                <option value="2023">2023</option>
                                <option value="2024">2024</option>
                                <option value="2025">2025</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="admin-reports-content">
                    <div class="table-container">
                        <div class="table-header">
                            <table class="table admin-reports-table" id="adminReportsTableHeader">
                                <thead>
                                    <tr>
                                        <th>الحسابات</th>
                                        <th>إدارة الصفحة</th>
                                        <th>إعلانات</th>
                                        <th>مدفوعات</th>
                                        <th>إجمالي حساب سابق</th>
                                        <th>إجمالي حساب حالي</th>
                                        <th>الكل</th>
                                    </tr>
                                </thead>
                            </table>
                        </div>

                        <div class="table-body">
                            <table class="table admin-reports-table" id="adminReportsTable">
                                <tbody id="adminReportsTableBody">
                                    <!-- سيتم إضافة البيانات هنا بواسطة JavaScript -->
                                    <tr class="loading-row">
                                        <td colspan="7">جاري تحميل البيانات...</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <div class="table-footer">
                            <table class="table admin-reports-table" id="adminReportsTableFooter">
                                <tfoot>
                                    <tr class="totals-row">
                                        <td>الإجمالي</td>
                                        <td id="totalPageManagement">0</td>
                                        <td id="totalAds">0</td>
                                        <td id="totalPayments">0</td>
                                        <td id="totalPreviousBalance">0</td>
                                        <td id="totalCurrentBalance">0</td>
                                        <td id="grandTotal">0</td>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- jQuery and Bootstrap JS -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Custom JS -->
    <script src="<?php echo BASE_URL; ?>assets/js/scripts.js"></script>
    <script src="<?php echo BASE_URL; ?>assets/js/admin_reports.js"></script>
</body>
</html>
