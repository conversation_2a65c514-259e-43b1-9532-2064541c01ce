<?php
// Include configuration
require_once 'config/config.php';

// Include database connection
require_once 'includes/db.php';

// Include helper functions
require_once 'includes/functions.php';

// Include authentication functions
require_once 'includes/auth.php';

// Check if user is logged in
if (!isLoggedIn()) {
    $_SESSION['flash_message'] = 'يجب تسجيل الدخول للوصول إلى هذه الصفحة';
    $_SESSION['flash_type'] = 'danger';
    redirect(BASE_URL . 'index.php');
}

// جلب بيانات الإعلانات والصفحات والاستهدافات
try {
    // جلب جميع الإعلانات النشطة
 $adsQuery = "
    SELECT
        a.id,
        a.type,
        a.post,
        a.cost,
        a.status,
        COALESCE(ac.name, c.name) AS client_name,
        aa.name AS account_name
    FROM ads a
    LEFT JOIN ad_clients ac ON a.client_id = ac.id
    LEFT JOIN clients c ON a.client_id = c.id AND ac.id IS NULL
    LEFT JOIN ad_accounts aa ON a.ad_account_id = aa.id
    ORDER BY a.id DESC
";


    $adsStmt = $db->prepare($adsQuery);
    $adsStmt->execute();
    $ads = $adsStmt->fetchAll(PDO::FETCH_ASSOC);

    // التحقق من وجود جدول الاستهداف
    $tables = $db->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);

    if (!in_array('targeting', $tables)) {
        // إنشاء جدول الاستهداف إذا لم يكن موجودًا
        $db->exec("CREATE TABLE targeting (
            id INT AUTO_INCREMENT PRIMARY KEY,
            ad_id INT NOT NULL,
            location VARCHAR(255) NOT NULL,
            age VARCHAR(100),
            interests TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (ad_id) REFERENCES ads(id) ON DELETE CASCADE
        )");
    }

    // التحقق من وجود جدول نتائج الإعلانات
    if (!in_array('ad_results', $tables)) {
        // إنشاء جدول نتائج الإعلانات إذا لم يكن موجودًا
        $db->exec("CREATE TABLE ad_results (
            id INT AUTO_INCREMENT PRIMARY KEY,
            ad_id INT NOT NULL,
            reach INT NOT NULL DEFAULT 0,
            engagement INT NOT NULL DEFAULT 0,
            messages INT NOT NULL DEFAULT 0,
            date DATE NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (ad_id) REFERENCES ads(id) ON DELETE CASCADE
        )");
    }

    // جلب الاستهدافات لكل إعلان
    foreach ($ads as &$ad) {
        try {
            $targetingQuery = "SELECT t.id, t.location, t.age, t.interests
                              FROM targeting t
                              WHERE t.ad_id = :ad_id";
            $targetingStmt = $db->prepare($targetingQuery);
            $targetingStmt->bindParam(':ad_id', $ad['id']);
            $targetingStmt->execute();
            $targeting = $targetingStmt->fetchAll(PDO::FETCH_ASSOC);

            $ad['targeting'] = $targeting;

            // جلب نتائج الإعلان
            $resultsQuery = "SELECT r.id, r.reach, r.engagement, r.messages, r.date
                            FROM ad_results r
                            WHERE r.ad_id = :ad_id
                            ORDER BY r.date DESC
                            LIMIT 1";
            $resultsStmt = $db->prepare($resultsQuery);
            $resultsStmt->bindParam(':ad_id', $ad['id']);
            $resultsStmt->execute();
            $results = $resultsStmt->fetch(PDO::FETCH_ASSOC);

            $ad['results'] = $results;
        } catch (PDOException $e) {
            // تجاهل الأخطاء هنا وتعيين قيم فارغة
            $ad['targeting'] = [];
            $ad['results'] = null;
        }
    }
    unset($ad); // فك الارتباط بالمرجع

} catch (PDOException $e) {
    $error = 'خطأ في قاعدة البيانات: ' . $e->getMessage();
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الإعلانات - <?php echo SITE_TITLE; ?></title>

    <!-- Bootstrap RTL CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Google Fonts - Cairo -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap">

    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f8f9fa;
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 20px;
            background-color: white;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .logo {
            height: 40px;
        }

        .page-title {
            color: #4a56e2;
            font-size: 24px;
            font-weight: 600;
            text-align: center;
            margin: 0;
        }

        .content-container {
            max-width: 1200px;
            margin: 30px auto;
            padding: 20px;
        }

        .search-container {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
        }

        .search-box {
            position: relative;
            flex-grow: 1;
            margin-left: 20px;
        }

        .search-input {
            width: 100%;
            padding: 10px 15px 10px 40px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }

        .search-icon {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #aaa;
        }

        .add-button {
            background-color: #4a56e2;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            font-weight: 600;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .add-button:hover {
            background-color: #3a46d2;
        }

        .ads-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 30px;
        }

        .ads-table th {
            background-color: #4a56e2;
            color: white;
            padding: 12px 15px;
            text-align: center;
            font-weight: 600;
        }

        .ads-table td {
            padding: 10px 15px;
            text-align: center;
            border: 1px solid #dcf343;
            vertical-align: middle;
        }

        .ads-table tr:hover {
            background-color: #f5f5f5;
        }

        .add-targeting {
            color: #4CAF50;
            font-size: 24px;
            cursor: pointer;
            display: inline-block;
        }

        .modal-header {
            background-color: #4a56e2;
            color: white;
        }

        .modal-title {
            font-weight: 600;
        }

        .modal-footer .btn-primary {
            background-color: #4a56e2;
            border-color: #4a56e2;
        }

        .modal-footer .btn-primary:hover {
            background-color: #3a46d2;
            border-color: #3a46d2;
        }

        .results-badge {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 4px;
            margin-right: 5px;
            font-size: 12px;
            font-weight: 600;
        }

        .results-reach {
            background-color: #e3f2fd;
            color: #0d6efd;
        }

        .results-engagement {
            background-color: #d1e7dd;
            color: #198754;
        }

        .results-messages {
            background-color: #f8d7da;
            color: #dc3545;
        }

        /* تنسيقات Modal إضافة الإعلان */
        .modal-lg {
            max-width: 900px;
        }

        .modal-content {
            border-radius: 10px;
            border: none;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }

        .modal-header {
            background: linear-gradient(135deg, #4a56e2 0%, #3a46d2 100%);
            color: white;
            border-bottom: none;
            border-radius: 10px 10px 0 0;
        }

        .modal-title {
            font-weight: 600;
            width: 100%;
            text-align: center;
            margin: 0;
        }

        .btn-close {
            color: white;
            opacity: 1;
            filter: brightness(0) invert(1);
        }

        .form-label {
            font-weight: 600;
            color: #4a56e2;
            margin-bottom: 5px;
            font-size: 14px;
        }

        .form-control, .form-select {
            border: 2px solid #e6e9ff;
            border-radius: 8px;
            padding: 8px 12px;
            transition: all 0.3s ease;
            font-size: 14px;
        }

        .form-control:focus, .form-select:focus {
            border-color: #4a56e2;
            box-shadow: 0 0 0 3px rgba(74, 86, 226, 0.1);
            outline: none;
        }

        .modal-footer {
            border-top: 1px solid #e6e9ff;
            padding: 15px 20px;
            background-color: #f8f9fa;
            border-radius: 0 0 10px 10px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #4a56e2 0%, #3a46d2 100%);
            border: none;
            font-weight: 600;
            padding: 10px 25px;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #3a46d2 0%, #2a36c2 100%);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(74, 86, 226, 0.3);
        }

        .btn-secondary {
            background-color: #6c757d;
            border: none;
            font-weight: 600;
            padding: 10px 25px;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .btn-secondary:hover {
            background-color: #5a6268;
            transform: translateY(-2px);
        }

        .modal-body {
            padding: 25px;
        }

        .row .col-md-2 {
            padding-left: 8px;
            padding-right: 8px;
        }

        .row .col-md-4 {
            padding-left: 8px;
            padding-right: 8px;
        }

        /* تنسيقات Switch الربط */
        .form-check-input:checked {
            background-color: #4a56e2;
            border-color: #4a56e2;
        }

        .form-check-input:focus {
            border-color: #4a56e2;
            outline: 0;
            box-shadow: 0 0 0 0.25rem rgba(74, 86, 226, 0.25);
        }

        .form-check-label {
            color: #6c757d;
            font-weight: 500;
        }
    </style>
</head>
<body>
    <div class="header">
        <a href="<?php echo BASE_URL; ?>dashboard.php">
            <img src="<?php echo BASE_URL; ?>assets/images/logo.png" alt="<?php echo SITE_TITLE; ?>" class="logo">
        </a>
        <h1 class="page-title">الإعلانات</h1>
        <div></div>
    </div>

    <div class="content-container">
        <?php if (isset($error)): ?>
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-circle me-2"></i>
                <?php echo $error; ?>
            </div>
        <?php else: ?>
            <div class="search-container">
                <button class="add-button" data-bs-toggle="modal" data-bs-target="#addAdModal">
                    <i class="fas fa-plus me-2"></i>
                    إضافة إعلان
                </button>
                <div class="search-box">
                    <input type="text" id="searchInput" class="search-input" placeholder="البحث...">
                    <i class="fas fa-search search-icon"></i>
                </div>
            </div>

            <div class="table-responsive">
                <table class="ads-table" id="adsTable">
                    <thead>
                        <tr>
                            <th>اسم العميل</th>
                            <th>اكونت</th>
                            <th>مبلغ</th>
                            <th>الاستهداف</th>
                            <th>ملاحظات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (empty($ads)): ?>
                            <tr>
                                <td colspan="5" class="text-center">لا توجد إعلانات لعرضها</td>
                            </tr>
                        <?php else: ?>
                            <?php foreach ($ads as $ad): ?>
                                <tr data-ad-id="<?php echo $ad['id']; ?>">
                                    <td style="color: #4a56e2; font-weight: 600;"><?php echo htmlspecialchars($ad['client_name']); ?></td>
                                    <td style="color: #0d6efd;"><?php echo htmlspecialchars($ad['account_name']); ?></td>
                                    <td><?php echo number_format($ad['cost']); ?></td>
                                    <td>
                                        <?php if (empty($ad['targeting'])): ?>
                                            <span class="add-targeting" data-ad-id="<?php echo $ad['id']; ?>" data-bs-toggle="modal" data-bs-target="#targetingModal">+</span>
                                        <?php else: ?>
                                            <?php foreach ($ad['targeting'] as $target): ?>
                                                <div><?php echo htmlspecialchars($target['location']); ?></div>
                                            <?php endforeach; ?>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if (!empty($ad['results'])): ?>
                                            <span class="results-badge results-reach">وصول: <?php echo number_format($ad['results']['reach']); ?></span>
                                            <span class="results-badge results-engagement">تفاعل: <?php echo number_format($ad['results']['engagement']); ?></span>
                                            <span class="results-badge results-messages">رسائل: <?php echo number_format($ad['results']['messages']); ?></span>
                                        <?php else: ?>
                                            <button class="btn btn-sm btn-outline-primary add-results" data-ad-id="<?php echo $ad['id']; ?>" data-bs-toggle="modal" data-bs-target="#resultsModal">إضافة نتائج</button>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>

    <!-- رابط لمركز اتخاذ القرار -->
    <div class="card mt-4">
        <div class="card-body text-center">
            <h5 class="mb-3">مركز مساعدة اتخاذ القرار</h5>
            <p>يمكنك الوصول إلى مركز اتخاذ القرار المتقدم للحصول على تحليلات ورسوم بيانية مفصلة لأداء الإعلانات</p>
            <a href="decision_center.php" class="btn btn-primary">
                <i class="fas fa-chart-line me-2"></i>
                الانتقال إلى مركز اتخاذ القرار
            </a>
        </div>
    </div>

    <!-- Modal for Targeting -->
    <div class="modal fade" id="targetingModal" tabindex="-1" aria-labelledby="targetingModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="targetingModalLabel">استهداف</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="targetingForm">
                        <input type="hidden" id="adId" name="ad_id">
                        <div class="mb-3">
                            <label for="location" class="form-label">الموقع</label>
                            <input type="text" class="form-control" id="location" name="location" placeholder="الموقع">
                        </div>
                        <div class="mb-3">
                            <label for="age" class="form-label">السن</label>
                            <input type="text" class="form-control" id="age" name="age" placeholder="السن">
                        </div>
                        <div class="mb-3">
                            <label for="interests" class="form-label">الاهتمامات</label>
                            <textarea class="form-control" id="interests" name="interests" rows="3" placeholder="الاهتمامات"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" id="saveTargeting">إضافة الاستهداف</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal for Results -->
    <div class="modal fade" id="resultsModal" tabindex="-1" aria-labelledby="resultsModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="resultsModalLabel">إضافة نتائج</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="resultsForm">
                        <input type="hidden" id="resultAdId" name="ad_id">
                        <div class="mb-3">
                            <label for="reach" class="form-label">الوصول</label>
                            <input type="number" class="form-control" id="reach" name="reach" placeholder="الوصول">
                        </div>
                        <div class="mb-3">
                            <label for="engagement" class="form-label">التفاعل</label>
                            <input type="number" class="form-control" id="engagement" name="engagement" placeholder="التفاعل">
                        </div>
                        <div class="mb-3">
                            <label for="messages" class="form-label">الرسائل</label>
                            <input type="number" class="form-control" id="messages" name="messages" placeholder="الرسائل">
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" id="saveResults">حفظ النتائج</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal إضافة إعلان -->
    <div class="modal fade" id="addAdModal" tabindex="-1" aria-labelledby="addAdModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addAdModalLabel">إضافة إعلان</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="addAdForm">
                        <div class="row">
                            <!-- التاريخ -->
                            <div class="col-md-2 mb-3">
                                <label for="adDate" class="form-label">تاريخ</label>
                                <input type="date" class="form-control" id="adDate" name="date" required>
                            </div>

                            <!-- النوع -->
                            <div class="col-md-2 mb-3">
                                <label for="adType" class="form-label">نوع</label>
                                <select class="form-control" id="adType" name="type" required>
                                    <option value="جديد">جديد</option>
                                    <option value="قديم">قديم</option>
                                </select>
                            </div>

                            <!-- التكلفة -->
                            <div class="col-md-2 mb-3">
                                <label for="adCost" class="form-label">التكلفة</label>
                                <input type="number" class="form-control" id="adCost" name="cost" step="0.01" required>
                            </div>

                            <!-- عدد أيام -->
                            <div class="col-md-2 mb-3">
                                <label for="adDays" class="form-label">عدد أيام</label>
                                <input type="number" class="form-control" id="adDays" name="days" min="1" value="7" required>
                            </div>

                            <!-- بوست -->
                            <div class="col-md-2 mb-3">
                                <label for="adPost" class="form-label">بوست</label>
                                <select class="form-control" id="adPost" name="post" required>
                                    <option value="نعم">نعم</option>
                                    <option value="لا">لا</option>
                                </select>
                            </div>

                            <!-- يومي / إجمالي -->
                            <div class="col-md-2 mb-3">
                                <label for="adDailyTotal" class="form-label">يومي / إجمالي</label>
                                <select class="form-control" id="adDailyTotal" name="daily_total" required>
                                    <option value="يومي">يومي</option>
                                    <option value="إجمالي">إجمالي</option>
                                </select>
                            </div>
                        </div>

                        <div class="row">
                            <!-- العميل -->
                            <div class="col-md-4 mb-3">
                                <label for="adClient" class="form-label">العميل</label>
                                <select class="form-control" id="adClient" name="client_id" required>
                                    <option value="">-- اختر العميل --</option>
                                    <!-- سيتم إضافة العملاء هنا بواسطة JavaScript -->
                                </select>
                            </div>

                            <!-- الحساب الإعلاني -->
                            <div class="col-md-4 mb-3">
                                <div class="d-flex justify-content-between align-items-center">
                                    <label for="adAccount" class="form-label">الحساب</label>
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="linkClientAccount">
                                        <label class="form-check-label" for="linkClientAccount" style="font-size: 12px;">ربط بالعميل</label>
                                    </div>
                                </div>
                                <select class="form-control" id="adAccount" name="ad_account_id" required>
                                    <option value="">-- اختر الحساب --</option>
                                    <!-- سيتم إضافة الحسابات هنا بواسطة JavaScript -->
                                </select>
                            </div>

                            <!-- الصرف -->
                            <div class="col-md-4 mb-3">
                                <label for="adExchangeRate" class="form-label">الصرف</label>
                                <input type="number" class="form-control" id="adExchangeRate" name="exchange_rate" step="0.01" required>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" id="saveAdBtn">إضافة إعلان</button>
                </div>
            </div>
        </div>
    </div>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // دالة لتحديث البيانات بعد إضافة النتائج
        function updateResultsData() {
            // تحديث البيانات بعد إضافة النتائج
            console.log("تم تحديث البيانات بنجاح");
        }

        // متغيرات عامة
        let clientsData = [];
        let accountsData = [];
        let clientTypesData = [];

        $(document).ready(function() {
            // تهيئة الصفحة عند التحميل
            updateResultsData();

            // جلب بيانات العملاء والحسابات عند تحميل الصفحة
            loadClientsAndAccounts();

            // تعيين التاريخ الحالي
            $('#adDate').val(new Date().toISOString().split('T')[0]);

            // مستمع حدث لحفظ الإعلان
            $('#saveAdBtn').on('click', function() {
                saveNewAd();
            });

            // مستمع حدث لتغيير العميل
            $('#adClient').on('change', function() {
                if ($('#linkClientAccount').is(':checked')) {
                    filterAccountsByClient();
                }
            });

            // مستمع حدث لتفعيل/إلغاء الربط
            $('#linkClientAccount').on('change', function() {
                if ($(this).is(':checked')) {
                    // تفعيل الربط - فلترة الحسابات حسب العميل
                    filterAccountsByClient();
                } else {
                    // إلغاء الربط - عرض جميع الحسابات
                    populateAccountsDropdown();
                }
            });

            // البحث في الجدول
            $("#searchInput").on("keyup", function() {
                const value = $(this).val().toLowerCase();
                $("#adsTable tbody tr").filter(function() {
                    $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1);
                });
            });

            // فتح نافذة الاستهداف
            $(".add-targeting").on("click", function() {
                const adId = $(this).data("ad-id");
                $("#adId").val(adId);
            });

            // فتح نافذة النتائج
            $(".add-results").on("click", function() {
                const adId = $(this).data("ad-id");
                $("#resultAdId").val(adId);
            });

            // حفظ الاستهداف
            $("#saveTargeting").on("click", function() {
                const adId = $("#adId").val();
                const location = $("#location").val();
                const age = $("#age").val();
                const interests = $("#interests").val();

                // إرسال البيانات إلى الخادم
                $.ajax({
                    url: "api/targeting/add.php",
                    type: "POST",
                    data: {
                        ad_id: adId,
                        location: location,
                        age: age,
                        interests: interests
                    },
                    success: function(response) {
                        if (response.success) {
                            // تحديث الواجهة بدون إعادة تحميل الصفحة
                            const targetingCell = $(`tr[data-ad-id="${adId}"] td:nth-child(4)`);
                            targetingCell.html(`<div>${location}</div>`);

                            // إضافة تأثير بصري للتحديث
                            targetingCell.css('background-color', '#e6ffe6');
                            setTimeout(function() {
                                targetingCell.css('background-color', '');
                            }, 2000);
                        } else {
                            alert("حدث خطأ أثناء إضافة الاستهداف: " + response.message);
                        }
                    },
                    error: function() {
                        alert("حدث خطأ أثناء الاتصال بالخادم");
                    }
                });

                // إغلاق النافذة المنبثقة
                $("#targetingModal").modal("hide");
            });

            // حفظ النتائج
            $("#saveResults").on("click", function() {
                const adId = $("#resultAdId").val();
                const reach = $("#reach").val();
                const engagement = $("#engagement").val();
                const messages = $("#messages").val();

                // إرسال البيانات إلى الخادم
                $.ajax({
                    url: "api/results/add.php",
                    type: "POST",
                    data: {
                        ad_id: adId,
                        reach: reach,
                        engagement: engagement,
                        messages: messages
                    },
                    success: function(response) {
                        if (response.success) {
                            // تحديث الواجهة بدون إعادة تحميل الصفحة
                            const resultsCell = $(`tr[data-ad-id="${adId}"] td:nth-child(5)`);
                            const resultsHTML = `
                                <span class="results-badge results-reach">وصول: ${Number(reach).toLocaleString()}</span>
                                <span class="results-badge results-engagement">تفاعل: ${Number(engagement).toLocaleString()}</span>
                                <span class="results-badge results-messages">رسائل: ${Number(messages).toLocaleString()}</span>
                            `;
                            resultsCell.html(resultsHTML);

                            // إضافة تأثير بصري للتحديث
                            resultsCell.css('background-color', '#e6ffe6');
                            setTimeout(function() {
                                resultsCell.css('background-color', '');
                            }, 2000);

                            // تحديث البيانات
                            updateResultsData();
                        } else {
                            alert("حدث خطأ أثناء إضافة النتائج: " + response.message);
                        }
                    },
                    error: function() {
                        alert("حدث خطأ أثناء الاتصال بالخادم");
                    }
                });

                // إغلاق النافذة المنبثقة
                $("#resultsModal").modal("hide");
            });
        });

        /**
         * جلب بيانات العملاء والحسابات
         */
        function loadClientsAndAccounts() {
            console.log('بدء جلب بيانات العملاء والحسابات...');
            $.ajax({
                url: 'api/get_data_safe.php',
                method: 'GET',
                dataType: 'json',
                success: function(response) {
                    console.log('استجابة API:', response);
                    if (response.success) {
                        clientsData = response.clients;
                        accountsData = response.accounts;
                        clientTypesData = response.client_types;

                        console.log('عدد العملاء:', clientsData.length);
                        console.log('عدد الحسابات:', accountsData.length);

                        populateClientsDropdown();
                        populateAccountsDropdown();
                    } else {
                        console.error('خطأ في جلب البيانات: ' + response.message);
                        alert('خطأ في جلب البيانات: ' + response.message);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('حدث خطأ أثناء جلب البيانات:', error);
                    console.error('حالة الطلب:', status);
                    console.error('استجابة الخادم:', xhr.responseText);
                    alert('حدث خطأ أثناء جلب البيانات: ' + error);
                }
            });
        }

        /**
         * ملء dropdown العملاء
         */
        function populateClientsDropdown() {
            console.log('بدء ملء dropdown العملاء...');
            const clientSelect = $('#adClient');
            clientSelect.empty();
            clientSelect.append('<option value="">-- اختر العميل --</option>');

            if (clientsData && clientsData.length > 0) {
                clientsData.forEach(function(client) {
                    const optionText = `${client.name} (${client.type_display_name || client.type})`;
                    clientSelect.append(`<option value="${client.id}" data-source="${client.source}" data-original-id="${client.original_id}">${optionText}</option>`);
                });
                console.log('تم إضافة', clientsData.length, 'عميل إلى القائمة');
            } else {
                console.log('لا توجد بيانات عملاء');
                clientSelect.append('<option value="" disabled>لا توجد عملاء</option>');
            }
        }

        /**
         * ملء dropdown الحسابات
         */
        function populateAccountsDropdown() {
            console.log('بدء ملء dropdown الحسابات...');
            const accountSelect = $('#adAccount');
            accountSelect.empty();
            accountSelect.append('<option value="">-- اختر الحساب --</option>');

            if (accountsData && accountsData.length > 0) {
                accountsData.forEach(function(account) {
                    const optionText = `${account.name} (${account.client_name || 'غير محدد'} - رصيد: ${account.balance || 0})`;
                    accountSelect.append(`<option value="${account.id}" data-client-id="${account.client_id}">${optionText}</option>`);
                });
                console.log('تم إضافة', accountsData.length, 'حساب إلى القائمة');
            } else {
                console.log('لا توجد بيانات حسابات');
                accountSelect.append('<option value="" disabled>لا توجد حسابات</option>');
            }
        }

        /**
         * فلترة الحسابات حسب العميل المحدد
         */
        function filterAccountsByClient() {
            console.log('بدء فلترة الحسابات...');
            const selectedClientId = $('#adClient').val();
            const accountSelect = $('#adAccount');

            console.log('العميل المحدد:', selectedClientId);

            accountSelect.empty();
            accountSelect.append('<option value="">-- اختر الحساب --</option>');

            if (selectedClientId) {
                // استخراج معرف العميل الأصلي
                const selectedOption = $('#adClient option:selected');
                const originalClientId = selectedOption.data('original-id');
                const clientSource = selectedOption.data('source');

                console.log('معرف العميل الأصلي:', originalClientId);
                console.log('مصدر العميل:', clientSource);
                console.log('جميع الحسابات:', accountsData);

                // فلترة الحسابات حسب العميل
                const filteredAccounts = accountsData.filter(function(account) {
                    console.log(`مقارنة: حساب ${account.name} - client_id: ${account.client_id} مع ${originalClientId}`);
                    return account.client_id == originalClientId;
                });

                console.log('الحسابات المفلترة:', filteredAccounts);

                if (filteredAccounts.length > 0) {
                    filteredAccounts.forEach(function(account) {
                        const optionText = `${account.name} (رصيد: ${account.balance || 0})`;
                        accountSelect.append(`<option value="${account.id}">${optionText}</option>`);
                    });
                } else {
                    // إذا لم توجد حسابات مرتبطة، اعرض جميع الحسابات
                    console.log('لا توجد حسابات مرتبطة، عرض جميع الحسابات');
                    accountsData.forEach(function(account) {
                        const optionText = `${account.name} (${account.client_name || 'غير محدد'})`;
                        accountSelect.append(`<option value="${account.id}">${optionText}</option>`);
                    });
                }
            } else {
                // إذا لم يتم اختيار عميل، اعرض جميع الحسابات
                console.log('لم يتم اختيار عميل، عرض جميع الحسابات');
                accountsData.forEach(function(account) {
                    const optionText = `${account.name} (${account.client_name || 'غير محدد'})`;
                    accountSelect.append(`<option value="${account.id}">${optionText}</option>`);
                });
            }
        }

        /**
         * حفظ إعلان جديد
         */
        function saveNewAd() {
            const selectedClient = $('#adClient option:selected');
            const clientId = selectedClient.data('original-id');
            const clientSource = selectedClient.data('source');

            console.log('بيانات العميل المحدد:');
            console.log('معرف العميل:', clientId);
            console.log('مصدر العميل:', clientSource);
            console.log('قيمة العميل:', $('#adClient').val());

            const formData = {
                date: $('#adDate').val(),
                type: $('#adType').val(),
                cost: $('#adCost').val(),
                days: $('#adDays').val(),
                post: $('#adPost').val(),
                daily_total: $('#adDailyTotal').val(),
                client_id: clientId,
                client_source: clientSource,
                ad_account_id: $('#adAccount').val(),
                exchange_rate: $('#adExchangeRate').val()
            };

            console.log('بيانات النموذج:', formData);

            // التحقق من البيانات
            if (!formData.date || !formData.type || !formData.cost || !formData.days ||
                !formData.post || !formData.daily_total || !formData.client_id ||
                !formData.ad_account_id || !formData.exchange_rate) {
                alert('يرجى ملء جميع الحقول المطلوبة');
                return;
            }

            // إرسال البيانات
            $.ajax({
                url: 'api/ads/add_basic.php',
                method: 'POST',
                contentType: 'application/json',
                data: JSON.stringify(formData),
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        alert('تم إضافة الإعلان بنجاح');
                        $('#addAdModal').modal('hide');
                        $('#addAdForm')[0].reset();
                        location.reload(); // إعادة تحميل الصفحة لعرض الإعلان الجديد
                    } else {
                        alert('خطأ في إضافة الإعلان: ' + response.message);
                    }
                },
                error: function() {
                    alert('حدث خطأ أثناء إضافة الإعلان');
                }
            });
        }
    </script>
</body>
</html>
