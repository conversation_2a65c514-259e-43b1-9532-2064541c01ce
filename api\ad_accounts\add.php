<?php
/**
 * API لإضافة حساب إعلاني جديد
 */

// تعيين نوع المحتوى
header('Content-Type: application/json; charset=UTF-8');

// السماح بالوصول من أي مصدر
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With');

// تضمين ملفات الاتصال بقاعدة البيانات
require_once '../../config/config.php';
require_once '../../includes/db.php';

// تعريف دوال المصادقة محلياً لتجاوز التحقق من الصلاحيات
function isLoggedIn() {
    return true; // دائماً يعتبر المستخدم مسجل الدخول
}

function isAdmin() {
    return true; // دائماً يعتبر المستخدم مدير
}

// إنشاء جلسة وهمية للمستخدم
$_SESSION['user_id'] = 1;
$_SESSION['user_name'] = 'Admin';
$_SESSION['user_email'] = '<EMAIL>';
$_SESSION['user_is_admin'] = 1;
$_SESSION['logged_in'] = true;

// التحقق من طريقة الطلب
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405); // Method Not Allowed
    echo json_encode(['success' => false, 'message' => 'طريقة الطلب غير مسموح بها']);
    exit;
}

// الحصول على البيانات المرسلة
$data = json_decode(file_get_contents('php://input'), true);

// التحقق من البيانات المطلوبة
if (!isset($data['name']) || empty($data['name']) ||
    !isset($data['status']) || empty($data['status']) ||
    !isset($data['balance']) || !is_numeric($data['balance']) ||
    !isset($data['spending_limit']) || !is_numeric($data['spending_limit'])) {
    http_response_code(400); // Bad Request
    echo json_encode(['success' => false, 'message' => 'البيانات المرسلة غير صحيحة أو غير مكتملة']);
    exit;
}

// تنظيف البيانات
$name = htmlspecialchars(strip_tags($data['name']));
$status = htmlspecialchars(strip_tags($data['status']));
$balance = floatval($data['balance']);
$spending_limit = floatval($data['spending_limit']);
$notes = isset($data['notes']) ? htmlspecialchars(strip_tags($data['notes'])) : null;

// معالجة بيانات الحساب المرتبط
$linked_account_type = isset($data['linked_account_type']) ? htmlspecialchars(strip_tags($data['linked_account_type'])) : 'none';
$linked_account_id = null;

// تسجيل البيانات المستلمة للتشخيص
error_log("البيانات المستلمة: " . json_encode($data));

if ($linked_account_type === 'credit_card') {
    // البحث عن معرف الكريديت كارد
    if (isset($data['linked_account_id']) && !empty($data['linked_account_id'])) {
        $linked_account_id = intval($data['linked_account_id']);
    } elseif (isset($data['credit_card_id']) && !empty($data['credit_card_id'])) {
        $linked_account_id = intval($data['credit_card_id']);
    }
} elseif ($linked_account_type === 'visa') {
    // البحث عن معرف الفيزا
    if (isset($data['linked_account_id']) && !empty($data['linked_account_id'])) {
        $linked_account_id = intval($data['linked_account_id']);
    } elseif (isset($data['visa_id']) && !empty($data['visa_id'])) {
        $linked_account_id = intval($data['visa_id']);
    }
} elseif ($linked_account_type === 'fawry') {
    // البحث عن معرف الفوري
    if (isset($data['linked_account_id']) && !empty($data['linked_account_id'])) {
        $linked_account_id = intval($data['linked_account_id']);
    } elseif (isset($data['fawry_id']) && !empty($data['fawry_id'])) {
        $linked_account_id = intval($data['fawry_id']);
    }
} else {
    $linked_account_type = 'none';
}

// تسجيل النتيجة النهائية
error_log("نوع الربط: " . $linked_account_type . ", معرف الحساب المرتبط: " . $linked_account_id);

try {
    // التحقق من وجود الجدول
    $tableExists = false;
    $tables = $db->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);

    if (in_array('ad_accounts', $tables)) {
        $tableExists = true;
    }

    // إنشاء الجدول إذا لم يكن موجودًا
    if (!$tableExists) {
        $db->exec("CREATE TABLE ad_accounts (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            status VARCHAR(50) NOT NULL DEFAULT 'نشط',
            balance DECIMAL(10,2) NOT NULL DEFAULT 0.00,
            spending_limit DECIMAL(10,2) NOT NULL DEFAULT 0.00,
            notes TEXT,
            linked_account_id INT NULL DEFAULT NULL,
            linked_account_type ENUM('credit_card', 'visa', 'fawry', 'none') NOT NULL DEFAULT 'none',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX linked_account_idx (linked_account_id, linked_account_type)
        )");
    } else {
        // التحقق من وجود نوع الربط "visa" في الجدول الموجود
        try {
            $columnsQuery = $db->query("SHOW COLUMNS FROM ad_accounts LIKE 'linked_account_type'");
            $column = $columnsQuery->fetch(PDO::FETCH_ASSOC);

            if ($column && strpos($column['Type'], 'visa') === false) {
                // إضافة نوع الربط "visa" إلى ENUM
                $db->exec("ALTER TABLE ad_accounts MODIFY linked_account_type ENUM('credit_card', 'visa', 'fawry', 'none') NOT NULL DEFAULT 'none'");
                error_log("تم تحديث جدول ad_accounts لدعم نوع الربط 'visa'");
            }
        } catch (PDOException $e) {
            error_log("خطأ في تحديث جدول ad_accounts: " . $e->getMessage());
        }
    }

    // إعداد استعلام الإدراج
    $stmt = $db->prepare("
        INSERT INTO ad_accounts (name, status, balance, spending_limit, notes, linked_account_id, linked_account_type)
        VALUES (:name, :status, :balance, :spending_limit, :notes, :linked_account_id, :linked_account_type)
    ");

    // ربط القيم
    $stmt->bindParam(':name', $name);
    $stmt->bindParam(':status', $status);
    $stmt->bindParam(':balance', $balance);
    $stmt->bindParam(':spending_limit', $spending_limit);
    $stmt->bindParam(':notes', $notes);
    $stmt->bindParam(':linked_account_id', $linked_account_id);
    $stmt->bindParam(':linked_account_type', $linked_account_type);

    // تنفيذ الاستعلام
    if ($stmt->execute()) {
        // الحصول على معرف الحساب المضاف
        $accountId = $db->lastInsertId();

        // إرجاع استجابة نجاح
        http_response_code(201); // Created
        echo json_encode([
            'success' => true,
            'message' => 'تم إضافة الحساب الإعلاني بنجاح',
            'account' => [
                'id' => $accountId,
                'name' => $name,
                'status' => $status,
                'balance' => $balance,
                'spending_limit' => $spending_limit,
                'notes' => $notes,
                'linked_account_id' => $linked_account_id,
                'linked_account_type' => $linked_account_type
            ]
        ]);
    } else {
        // إرجاع استجابة فشل
        http_response_code(500); // Internal Server Error
        echo json_encode(['success' => false, 'message' => 'حدث خطأ أثناء إضافة الحساب الإعلاني']);
    }
} catch (PDOException $e) {
    // إرجاع استجابة خطأ
    http_response_code(500); // Internal Server Error
    echo json_encode(['success' => false, 'message' => 'حدث خطأ في قاعدة البيانات: ' . $e->getMessage()]);
}
?>
