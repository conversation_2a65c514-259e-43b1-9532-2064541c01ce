<?php
/**
 * API لشحن الحسابات الإعلانية
 */

// إعداد الاستجابة JSON
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// التحقق من طريقة الطلب
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'طريقة الطلب غير مسموحة']);
    exit;
}

// تضمين ملفات الاتصال بقاعدة البيانات
require_once '../../config/database.php';

try {
    // إنشاء اتصال بقاعدة البيانات
    $database = new Database();
    $db = $database->getConnection();

    // قراءة البيانات المرسلة
    $input = json_decode(file_get_contents('php://input'), true);

    // التحقق من صحة البيانات
    if (!isset($input['account_id']) || !isset($input['amount']) || !isset($input['source'])) {
        echo json_encode(['success' => false, 'message' => 'بيانات غير مكتملة']);
        exit;
    }

    $accountId = intval($input['account_id']);
    $amount = floatval($input['amount']);
    $source = $input['source'];
    $notes = $input['notes'] ?? '';

    // التحقق من صحة المبلغ
    if ($amount <= 0) {
        echo json_encode(['success' => false, 'message' => 'مبلغ الشحن يجب أن يكون أكبر من صفر']);
        exit;
    }

    // التحقق من وجود الحساب الإعلاني
    $stmt = $db->prepare("SELECT * FROM ad_accounts WHERE id = ?");
    $stmt->execute([$accountId]);
    $account = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$account) {
        echo json_encode(['success' => false, 'message' => 'الحساب الإعلاني غير موجود']);
        exit;
    }

    // بدء المعاملة
    $db->beginTransaction();

    try {
        $sourceId = null;
        $sourceTable = '';
        $sourceBalanceField = '';

        // تحديد مصدر الشحن والتحقق من الرصيد
        switch ($source) {
            case 'credit_card':
                if (!isset($input['credit_card_id'])) {
                    throw new Exception('يرجى اختيار بطاقة الائتمان');
                }
                $sourceId = intval($input['credit_card_id']);
                $sourceTable = 'credit_cards';
                $sourceBalanceField = 'balance';
                break;

            case 'visa':
                if (!isset($input['visa_id'])) {
                    throw new Exception('يرجى اختيار بطاقة الفيزا');
                }
                $sourceId = intval($input['visa_id']);
                $sourceTable = 'visa_cards';
                $sourceBalanceField = 'base_balance';
                break;

            case 'fawry':
                if (!isset($input['fawry_id'])) {
                    throw new Exception('يرجى اختيار حساب فوري');
                }
                $sourceId = intval($input['fawry_id']);
                $sourceTable = 'fawry_accounts';
                $sourceBalanceField = 'balance';
                break;

            case 'cash':
                // الشحن النقدي لا يحتاج تحقق من الرصيد
                break;

            default:
                throw new Exception('مصدر الشحن غير صحيح');
        }

        // التحقق من رصيد المصدر (إذا لم يكن نقدي)
        if ($source !== 'cash' && $sourceId) {
            $stmt = $db->prepare("SELECT $sourceBalanceField FROM $sourceTable WHERE id = ?");
            $stmt->execute([$sourceId]);
            $sourceData = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$sourceData) {
                throw new Exception('مصدر الشحن غير موجود');
            }

            $sourceBalance = floatval($sourceData[$sourceBalanceField]);
            if ($amount > $sourceBalance) {
                throw new Exception('الرصيد المتاح في المصدر غير كافي لهذه العملية');
            }

            // خصم المبلغ من المصدر
            $stmt = $db->prepare("UPDATE $sourceTable SET $sourceBalanceField = $sourceBalanceField - ? WHERE id = ?");
            $stmt->execute([$amount, $sourceId]);
        }

        // إضافة المبلغ إلى الحساب الإعلاني
        $stmt = $db->prepare("UPDATE ad_accounts SET balance = balance + ? WHERE id = ?");
        $stmt->execute([$amount, $accountId]);

        // تسجيل عملية الشحن في جدول المعاملات
        $stmt = $db->prepare("
            INSERT INTO account_transactions 
            (account_id, transaction_type, amount, source_type, source_id, notes, created_at) 
            VALUES (?, 'charge', ?, ?, ?, ?, NOW())
        ");
        $stmt->execute([
            $accountId,
            $amount,
            $source,
            $sourceId,
            $notes
        ]);

        // تأكيد المعاملة
        $db->commit();

        // إرسال استجابة النجاح
        echo json_encode([
            'success' => true,
            'message' => 'تم شحن الحساب بنجاح',
            'data' => [
                'account_id' => $accountId,
                'amount' => $amount,
                'source' => $source,
                'new_balance' => $account['balance'] + $amount
            ]
        ]);

    } catch (Exception $e) {
        // إلغاء المعاملة في حالة الخطأ
        $db->rollBack();
        throw $e;
    }

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
} catch (PDOException $e) {
    echo json_encode([
        'success' => false,
        'message' => 'حدث خطأ في قاعدة البيانات: ' . $e->getMessage()
    ]);
}
?>
