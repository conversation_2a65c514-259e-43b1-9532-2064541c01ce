<?php
/**
 * API لحذف حساب إعلاني
 */

// تعيين نوع المحتوى
header('Content-Type: application/json; charset=UTF-8');

// السماح بالوصول من أي مصدر
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With');

// تضمين ملفات الاتصال بقاعدة البيانات
require_once '../../config/config.php';
require_once '../../includes/db.php';

// تعريف دوال المصادقة محلياً لتجاوز التحقق من الصلاحيات
function isLoggedIn() {
    return true; // دائماً يعتبر المستخدم مسجل الدخول
}

function isAdmin() {
    return true; // دائماً يعتبر المستخدم مدير
}

// إنشاء جلسة وهمية للمستخدم
$_SESSION['user_id'] = 1;
$_SESSION['user_name'] = 'Admin';
$_SESSION['user_email'] = '<EMAIL>';
$_SESSION['user_is_admin'] = 1;
$_SESSION['logged_in'] = true;

// التحقق من طريقة الطلب
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405); // Method Not Allowed
    echo json_encode(['success' => false, 'message' => 'طريقة الطلب غير مسموح بها']);
    exit;
}

// الحصول على البيانات المرسلة
$data = json_decode(file_get_contents('php://input'), true);

// التحقق من البيانات المطلوبة
if (!isset($data['id']) || !is_numeric($data['id'])) {
    http_response_code(400); // Bad Request
    echo json_encode(['success' => false, 'message' => 'البيانات المرسلة غير صحيحة أو غير مكتملة']);
    exit;
}

// تنظيف البيانات
$id = intval($data['id']);

try {
    // التحقق من وجود الحساب
    $stmt = $db->prepare("SELECT id FROM ad_accounts WHERE id = :id");
    $stmt->bindParam(':id', $id);
    $stmt->execute();

    if ($stmt->rowCount() === 0) {
        http_response_code(404); // Not Found
        echo json_encode(['success' => false, 'message' => 'الحساب الإعلاني غير موجود']);
        exit;
    }

    // بدء المعاملة
    $db->beginTransaction();

    // تحديث الإعلانات المرتبطة بالحساب
    $stmt = $db->prepare("UPDATE ads SET ad_account_id = NULL WHERE ad_account_id = :id");
    $stmt->bindParam(':id', $id);
    $stmt->execute();

    // حذف الحساب
    $stmt = $db->prepare("DELETE FROM ad_accounts WHERE id = :id");
    $stmt->bindParam(':id', $id);

    // تنفيذ الاستعلام
    if ($stmt->execute()) {
        // تأكيد المعاملة
        $db->commit();

        // إرجاع استجابة نجاح
        http_response_code(200); // OK
        echo json_encode([
            'success' => true,
            'message' => 'تم حذف الحساب الإعلاني بنجاح'
        ]);
    } else {
        // التراجع عن المعاملة
        $db->rollBack();

        // إرجاع استجابة فشل
        http_response_code(500); // Internal Server Error
        echo json_encode(['success' => false, 'message' => 'حدث خطأ أثناء حذف الحساب الإعلاني']);
    }
} catch (PDOException $e) {
    // التراجع عن المعاملة
    if ($db->inTransaction()) {
        $db->rollBack();
    }

    // إرجاع استجابة خطأ
    http_response_code(500); // Internal Server Error
    echo json_encode(['success' => false, 'message' => 'حدث خطأ في قاعدة البيانات: ' . $e->getMessage()]);
}
?>
