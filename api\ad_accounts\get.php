<?php
/**
 * API لجلب الحسابات الإعلانية
 */

// تعيين نوع المحتوى
header('Content-Type: application/json; charset=UTF-8');

// السماح بالوصول من أي مصدر
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With');

// تضمين ملفات الاتصال بقاعدة البيانات
require_once '../../config/config.php';
require_once '../../includes/db.php';

// تعريف دوال المصادقة محلياً لتجاوز التحقق من الصلاحيات
function isLoggedIn() {
    return true; // دائماً يعتبر المستخدم مسجل الدخول
}

function isAdmin() {
    return true; // دائماً يعتبر المستخدم مدير
}

// إنشاء جلسة وهمية للمستخدم
$_SESSION['user_id'] = 1;
$_SESSION['user_name'] = 'Admin';
$_SESSION['user_email'] = '<EMAIL>';
$_SESSION['user_is_admin'] = 1;
$_SESSION['logged_in'] = true;

// التحقق من طريقة الطلب
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405); // Method Not Allowed
    echo json_encode(['success' => false, 'message' => 'طريقة الطلب غير مسموح بها']);
    exit;
}

try {
    // التحقق من وجود الجدول
    $tableExists = false;
    $tables = $db->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);

    if (in_array('ad_accounts', $tables)) {
        $tableExists = true;
    }

    // إنشاء الجدول إذا لم يكن موجودًا
    if (!$tableExists) {
        $db->exec("CREATE TABLE ad_accounts (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            status VARCHAR(50) NOT NULL DEFAULT 'نشط',
            balance DECIMAL(10,2) NOT NULL DEFAULT 0.00,
            spending_limit DECIMAL(10,2) NOT NULL DEFAULT 0.00,
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )");

        // إضافة بيانات تجريبية
        $db->exec("INSERT INTO ad_accounts (name, status, balance, spending_limit, notes) VALUES
            ('ليلى اسامة', 'نشط', 5000.00, 10000.00, 'حساب إعلاني رئيسي'),
            ('محمد أحمد', 'نشط', 3000.00, 8000.00, 'حساب إعلاني ثانوي'),
            ('سارة محمود', 'متوقف', 1000.00, 5000.00, 'تم إيقاف الحساب مؤقتًا')
        ");
    }

    // التحقق من وجود معرف محدد
    if (isset($_GET['id']) && is_numeric($_GET['id'])) {
        $id = intval($_GET['id']);

        // جلب حساب محدد
        $stmt = $db->prepare("SELECT * FROM ad_accounts WHERE id = :id");
        $stmt->bindParam(':id', $id);
        $stmt->execute();

        if ($stmt->rowCount() > 0) {
            $account = $stmt->fetch(PDO::FETCH_ASSOC);

            // جلب إحصائيات الإعلانات
            $stmt = $db->prepare("
                SELECT COUNT(*) as ad_count, SUM(cost) as total_cost
                FROM ads
                WHERE ad_account_id = :id
            ");
            $stmt->bindParam(':id', $id);
            $stmt->execute();
            $adStats = $stmt->fetch(PDO::FETCH_ASSOC);

            // جلب عدد العملاء
            $stmt = $db->prepare("
                SELECT COUNT(DISTINCT client_id) as client_count
                FROM ads
                WHERE ad_account_id = :id
            ");
            $stmt->bindParam(':id', $id);
            $stmt->execute();
            $clientCount = $stmt->fetch(PDO::FETCH_ASSOC)['client_count'];

            // إضافة الإحصائيات إلى الحساب
            $account['ad_count'] = $adStats['ad_count'] ?? 0;
            $account['total_cost'] = $adStats['total_cost'] ?? 0;
            $account['client_count'] = $clientCount ?? 0;

            // إرجاع استجابة نجاح
            http_response_code(200); // OK
            echo json_encode([
                'success' => true,
                'account' => $account
            ]);
        } else {
            // إرجاع استجابة فشل
            http_response_code(404); // Not Found
            echo json_encode(['success' => false, 'message' => 'الحساب الإعلاني غير موجود']);
        }
    } else {
        // جلب جميع الحسابات
        $stmt = $db->query("SELECT * FROM ad_accounts ORDER BY name ASC");
        $accounts = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // جلب إحصائيات الإعلانات
        $stmt = $db->query("
            SELECT ad_account_id, COUNT(*) as ad_count, SUM(cost) as total_cost
            FROM ads
            WHERE ad_account_id IS NOT NULL
            GROUP BY ad_account_id
        ");
        $adStats = [];
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            $adStats[$row['ad_account_id']] = [
                'ad_count' => $row['ad_count'],
                'total_cost' => $row['total_cost']
            ];
        }

        // جلب عدد العملاء
        $stmt = $db->query("
            SELECT a.ad_account_id, COUNT(DISTINCT a.client_id) as client_count
            FROM ads a
            WHERE a.ad_account_id IS NOT NULL
            GROUP BY a.ad_account_id
        ");
        $clientStats = [];
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            $clientStats[$row['ad_account_id']] = $row['client_count'];
        }

        // إضافة الإحصائيات إلى كل حساب
        foreach ($accounts as &$account) {
            $account['ad_count'] = isset($adStats[$account['id']]) ? $adStats[$account['id']]['ad_count'] : 0;
            $account['total_cost'] = isset($adStats[$account['id']]) ? $adStats[$account['id']]['total_cost'] : 0;
            $account['client_count'] = isset($clientStats[$account['id']]) ? $clientStats[$account['id']] : 0;
        }

        // إرجاع استجابة نجاح
        http_response_code(200); // OK
        echo json_encode([
            'success' => true,
            'accounts' => $accounts
        ]);
    }
} catch (PDOException $e) {
    // إرجاع استجابة خطأ
    http_response_code(500); // Internal Server Error
    echo json_encode(['success' => false, 'message' => 'حدث خطأ في قاعدة البيانات: ' . $e->getMessage()]);
}
?>
