<?php
/**
 * API لجلب جميع الحسابات الإعلانية
 */

// إعداد الاستجابة JSON
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type');

// التحقق من طريقة الطلب
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'طريقة الطلب غير مسموحة']);
    exit;
}

// تضمين ملفات الاتصال بقاعدة البيانات
require_once '../../config/database.php';

try {
    // إنشاء اتصال بقاعدة البيانات
    $database = new Database();
    $db = $database->getConnection();

    // جلب جميع الحسابات الإعلانية
    $stmt = $db->prepare("
        SELECT 
            id,
            name,
            status,
            balance,
            spending_limit,
            linked_account_type,
            linked_account_id,
            created_at
        FROM ad_accounts 
        WHERE status = 'نشط'
        ORDER BY name ASC
    ");
    
    $stmt->execute();
    $accounts = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // إذا لم توجد حسابات، أضف بعض الحسابات التجريبية
    if (empty($accounts)) {
        // إنشاء حسابات تجريبية
        $testAccounts = [
            ['name' => 'حساب فيسبوك الرئيسي', 'status' => 'نشط'],
            ['name' => 'حساب انستجرام التجاري', 'status' => 'نشط'],
            ['name' => 'حساب جوجل ادز', 'status' => 'نشط'],
            ['name' => 'حساب تيك توك', 'status' => 'نشط'],
            ['name' => 'حساب سناب شات', 'status' => 'نشط']
        ];

        foreach ($testAccounts as $account) {
            try {
                $stmt = $db->prepare("
                    INSERT INTO ad_accounts (name, status, balance, spending_limit, linked_account_type) 
                    VALUES (?, ?, 0, 0, 'none')
                ");
                $stmt->execute([$account['name'], $account['status']]);
            } catch (PDOException $e) {
                // في حالة فشل الإضافة، تجاهل الخطأ واستمر
                continue;
            }
        }

        // جلب الحسابات مرة أخرى بعد الإضافة
        $stmt = $db->prepare("
            SELECT 
                id,
                name,
                status,
                balance,
                spending_limit,
                linked_account_type,
                linked_account_id,
                created_at
            FROM ad_accounts 
            WHERE status = 'نشط'
            ORDER BY name ASC
        ");
        
        $stmt->execute();
        $accounts = $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    // إرجاع البيانات
    echo json_encode([
        'success' => true,
        'message' => 'تم جلب الحسابات بنجاح',
        'accounts' => $accounts,
        'count' => count($accounts)
    ]);

} catch (PDOException $e) {
    echo json_encode([
        'success' => false,
        'message' => 'حدث خطأ في قاعدة البيانات: ' . $e->getMessage(),
        'accounts' => []
    ]);
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'حدث خطأ: ' . $e->getMessage(),
        'accounts' => []
    ]);
}
?>
