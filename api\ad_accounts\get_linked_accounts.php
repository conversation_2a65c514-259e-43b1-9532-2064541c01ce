<?php
/**
 * API لجلب الحسابات الإعلانية المرتبطة ببطاقة ائتمان
 */

// تضمين ملفات الإعداد والاتصال بقاعدة البيانات
require_once '../../config/config.php';
require_once '../../includes/db.php';

// التحقق من طريقة الطلب
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode([
        'success' => false,
        'message' => 'طريقة الطلب غير مسموح بها'
    ]);
    exit;
}

// التحقق من وجود معرف البطاقة
if (!isset($_GET['card_id']) || empty($_GET['card_id'])) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => 'معرف البطاقة مطلوب'
    ]);
    exit;
}

// تنظيف البيانات
$cardId = intval($_GET['card_id']);
$cardType = isset($_GET['card_type']) ? $_GET['card_type'] : 'credit_card';

// تعيين نوع المحتوى
header('Content-Type: application/json');

try {
    // جلب الحسابات الإعلانية المرتبطة بالبطاقة
    $stmt = $db->prepare("
        SELECT * FROM ad_accounts
        WHERE linked_account_type = :card_type
        AND linked_account_id = :card_id
        ORDER BY name ASC
    ");
    $stmt->bindParam(':card_id', $cardId);
    $stmt->bindParam(':card_type', $cardType);
    $stmt->execute();
    $accounts = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // إرجاع البيانات
    echo json_encode([
        'success' => true,
        'accounts' => $accounts,
        'card_id' => $cardId,
        'card_type' => $cardType
    ]);
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'حدث خطأ أثناء جلب الحسابات: ' . $e->getMessage()
    ]);
}
?>
