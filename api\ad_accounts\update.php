<?php
/**
 * API لتحديث حساب إعلاني
 */

// تعيين نوع المحتوى
header('Content-Type: application/json; charset=UTF-8');

// السماح بالوصول من أي مصدر
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With');

// تضمين ملفات الاتصال بقاعدة البيانات
require_once '../../config/config.php';
require_once '../../includes/db.php';

// تعريف دوال المصادقة محلياً لتجاوز التحقق من الصلاحيات
function isLoggedIn() {
    return true; // دائماً يعتبر المستخدم مسجل الدخول
}

function isAdmin() {
    return true; // دائماً يعتبر المستخدم مدير
}

// إنشاء جلسة وهمية للمستخدم
$_SESSION['user_id'] = 1;
$_SESSION['user_name'] = 'Admin';
$_SESSION['user_email'] = '<EMAIL>';
$_SESSION['user_is_admin'] = 1;
$_SESSION['logged_in'] = true;

// التحقق من طريقة الطلب
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405); // Method Not Allowed
    echo json_encode(['success' => false, 'message' => 'طريقة الطلب غير مسموح بها']);
    exit;
}

// الحصول على البيانات المرسلة
$data = json_decode(file_get_contents('php://input'), true);

// التحقق من البيانات المطلوبة
if (!isset($data['id']) || !is_numeric($data['id']) ||
    !isset($data['name']) || empty($data['name']) ||
    !isset($data['status']) || empty($data['status']) ||
    !isset($data['balance']) || !is_numeric($data['balance']) ||
    !isset($data['spending_limit']) || !is_numeric($data['spending_limit'])) {
    http_response_code(400); // Bad Request
    echo json_encode(['success' => false, 'message' => 'البيانات المرسلة غير صحيحة أو غير مكتملة']);
    exit;
}

// تنظيف البيانات
$id = intval($data['id']);
$name = htmlspecialchars(strip_tags($data['name']));
$status = htmlspecialchars(strip_tags($data['status']));
$balance = floatval($data['balance']);
$spending_limit = floatval($data['spending_limit']);
$notes = isset($data['notes']) ? htmlspecialchars(strip_tags($data['notes'])) : null;

// معالجة بيانات الحساب المرتبط
$linked_account_type = isset($data['linked_account_type']) ? htmlspecialchars(strip_tags($data['linked_account_type'])) : 'none';
$linked_account_id = null;

if ($linked_account_type === 'credit_card' && isset($data['linked_account_id']) && !empty($data['linked_account_id'])) {
    $linked_account_id = intval($data['linked_account_id']);
} elseif ($linked_account_type === 'fawry' && isset($data['linked_account_id']) && !empty($data['linked_account_id'])) {
    $linked_account_id = intval($data['linked_account_id']);
} else {
    $linked_account_type = 'none';
}

try {
    // التحقق من وجود الحساب
    $stmt = $db->prepare("SELECT id FROM ad_accounts WHERE id = :id");
    $stmt->bindParam(':id', $id);
    $stmt->execute();

    if ($stmt->rowCount() === 0) {
        http_response_code(404); // Not Found
        echo json_encode(['success' => false, 'message' => 'الحساب الإعلاني غير موجود']);
        exit;
    }

    // إعداد استعلام التحديث
    $stmt = $db->prepare("
        UPDATE ad_accounts
        SET name = :name,
            status = :status,
            balance = :balance,
            spending_limit = :spending_limit,
            notes = :notes,
            linked_account_id = :linked_account_id,
            linked_account_type = :linked_account_type
        WHERE id = :id
    ");

    // ربط القيم
    $stmt->bindParam(':id', $id);
    $stmt->bindParam(':name', $name);
    $stmt->bindParam(':status', $status);
    $stmt->bindParam(':balance', $balance);
    $stmt->bindParam(':spending_limit', $spending_limit);
    $stmt->bindParam(':notes', $notes);
    $stmt->bindParam(':linked_account_id', $linked_account_id);
    $stmt->bindParam(':linked_account_type', $linked_account_type);

    // تنفيذ الاستعلام
    if ($stmt->execute()) {
        // إرجاع استجابة نجاح
        http_response_code(200); // OK
        echo json_encode([
            'success' => true, 
            'message' => 'تم تحديث الحساب الإعلاني بنجاح',
            'account' => [
                'id' => $id,
                'name' => $name,
                'status' => $status,
                'balance' => $balance,
                'spending_limit' => $spending_limit,
                'notes' => $notes,
                'linked_account_id' => $linked_account_id,
                'linked_account_type' => $linked_account_type
            ]
        ]);
    } else {
        // إرجاع استجابة فشل
        http_response_code(500); // Internal Server Error
        echo json_encode(['success' => false, 'message' => 'حدث خطأ أثناء تحديث الحساب الإعلاني']);
    }
} catch (PDOException $e) {
    // إرجاع استجابة خطأ
    http_response_code(500); // Internal Server Error
    echo json_encode(['success' => false, 'message' => 'حدث خطأ في قاعدة البيانات: ' . $e->getMessage()]);
}
?>
