<?php
/**
 * API لجلب بيانات تقرير حسابات الإعلانات
 */

// تعيين نوع المحتوى إلى JSON
header('Content-Type: application/json');

// تضمين ملف التكوين
require_once '../config/config.php';

// تضمين اتصال قاعدة البيانات
require_once '../includes/db.php';

// تضمين الدوال المساعدة
require_once '../includes/functions.php';

// تضمين دوال المصادقة
require_once '../includes/auth.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    echo json_encode([
        'success' => false,
        'message' => 'غير مصرح لك بالوصول إلى هذه البيانات'
    ]);
    exit;
}

// جلب بيانات تقرير حسابات الإعلانات
try {
    // التحقق من وجود جدول ad_clients
    $tableExists = false;
    $tables = $db->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);

    if (in_array('ad_clients', $tables)) {
        $tableExists = true;
    }

    // إنشاء الجدول إذا لم يكن موجودًا
    if (!$tableExists) {
        $db->exec("CREATE TABLE ad_clients (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            type VARCHAR(50) NOT NULL,
            status VARCHAR(50) DEFAULT 'نشط',
            commission_percentage DECIMAL(5,2) DEFAULT 50.00,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )");

        // إضافة بيانات تجريبية للعملاء
        $db->exec("INSERT INTO ad_clients (name, type, commission_percentage) VALUES
            ('Dr M7med Elraf3y', 'New Delta', 50.00),
            ('Dr M7med Elraf3y', 'New Delta', 50.00),
            ('Dr M7med Elraf3y', 'New Delta', 50.00),
            ('Dr M7med Elraf3y', 'New Delta', 50.00),
            ('Dr M7med Elraf3y', 'New Delta', 50.00),
            ('Dr M7med Elraf3y', 'New Delta', 50.00),
            ('Dr M7med Elraf3y', 'New Delta', 50.00),
            ('Dr M7med Elraf3y', 'New Delta', 50.00),
            ('Dr M7med Elraf3y', 'New Delta', 50.00),
            ('Dr M7med Elraf3y', 'New Delta', 50.00),
            ('Dr M7med Elraf3y', 'New Delta', 50.00),
            ('Dr M7med Elraf3y', 'New Delta', 50.00)
        ");

        // إضافة بيانات تجريبية للمدفوعات
        $db->exec("CREATE TABLE IF NOT EXISTS payments (
            id INT AUTO_INCREMENT PRIMARY KEY,
            client_id INT NOT NULL,
            client_type VARCHAR(50) NOT NULL,
            date DATE NOT NULL,
            amount DECIMAL(10,2) NOT NULL,
            payment_method VARCHAR(50) NOT NULL DEFAULT 'نقدي',
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )");

        // إضافة مدفوعات تجريبية
        $db->exec("INSERT INTO payments (client_id, client_type, date, amount, payment_method, notes) VALUES
            (1, 'New Delta', '2023-08-25', 500.00, 'نقدي', 'دفعة أولى'),
            (2, 'New Delta', '2023-08-25', 300.00, 'نقدي', 'دفعة أولى'),
            (3, 'New Delta', '2023-08-25', 200.00, 'نقدي', 'دفعة أولى')
        ");

        // إضافة بيانات تجريبية للإعلانات
        $db->exec("CREATE TABLE IF NOT EXISTS ads (
            id INT AUTO_INCREMENT PRIMARY KEY,
            client_id INT NOT NULL,
            client_type VARCHAR(50) NOT NULL,
            date DATE NOT NULL,
            type VARCHAR(50) NOT NULL,
            cost DECIMAL(10,2) NOT NULL,
            status VARCHAR(50) NOT NULL,
            exchange_rate DECIMAL(10,2) NOT NULL,
            days INT NOT NULL DEFAULT 1,
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )");

        // إضافة إعلانات تجريبية
        $db->exec("INSERT INTO ads (client_id, client_type, date, type, cost, status, exchange_rate, days, notes) VALUES
            (1, 'New Delta', '2023-08-25', 'جديد', 1000.00, 'يومي مستمر', 910.00, 1, 'ليل نهاره'),
            (1, 'New Delta', '2023-08-26', 'جديد', 7000.00, 'نشط 7 أيام', 910.00, 7, 'ليل نهاره'),
            (2, 'New Delta', '2023-08-25', 'جديد', 30000.00, 'نشط 30 أيام', 910.00, 30, 'ليل نهاره'),
            (2, 'New Delta', '2023-08-26', 'جديد', 1500.00, 'يومي مستمر', 910.00, 1, 'ليل نهاره'),
            (3, 'New Delta', '2023-08-25', 'جديد', 2000.00, 'يومي مستمر', 910.00, 1, 'ليل نهاره'),
            (3, 'New Delta', '2023-08-26', 'جديد', 15000.00, 'نشط 15 أيام', 910.00, 15, 'ليل نهاره')
        ");
    }

    // استعلام لجلب بيانات العملاء مع الإعلانات والمدفوعات
    $query = "SELECT * FROM ad_clients ORDER BY name ASC";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $clients = $stmt->fetchAll(PDO::FETCH_ASSOC);

    $accounts = [];

    foreach ($clients as $client) {
        // جلب الإعلانات لكل عميل
        $query = "SELECT * FROM ads WHERE client_id = :client_id ORDER BY date DESC";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':client_id', $client['id']);
        $stmt->execute();
        $ads = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // حساب الإجماليات
        $total_exchange = 0;
        $total_exchange_with_percentage = 0;
        $daily_exchange = 0;

        foreach ($ads as $ad) {
            $total_exchange += floatval($ad['exchange_rate']);

            // حساب الصرف بالنسبة (إضافة نسبة العمولة إلى سعر الصرف)
            $commissionPercentage = isset($client['commission_percentage']) ? floatval($client['commission_percentage']) : 50.00;
            $commissionMultiplier = 1 + ($commissionPercentage / 100);
            $exchangeRateWithPercentage = floatval($ad['exchange_rate']) * $commissionMultiplier;
            $total_exchange_with_percentage += $exchangeRateWithPercentage;

            // حساب الصرف اليومي
            // استخدام حقل days مباشرة من جدول ads
            $days = isset($ad['days']) ? intval($ad['days']) : 1;

            // حساب الصرف اليومي = التكلفة / عدد الأيام
            if ($days > 0) {
                $daily_exchange += floatval($ad['cost']) / $days;
            }
        }

        // جلب المدفوعات لكل عميل - نحاول أولاً من جدول ad_payments
        $query = "SELECT SUM(amount) as total_payments FROM ad_payments WHERE client_id = :client_id";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':client_id', $client['id']);
        $stmt->execute();
        $payments = $stmt->fetch(PDO::FETCH_ASSOC);
        $total_payments = $payments['total_payments'] ? floatval($payments['total_payments']) : 0;

        // إذا لم نجد مدفوعات، نحاول من جدول payments
        if ($total_payments == 0) {
            $query = "SELECT SUM(amount) as total_payments FROM payments WHERE client_id = :client_id AND client_type = :client_type";
            $stmt = $db->prepare($query);
            $stmt->bindParam(':client_id', $client['id']);
            $stmt->bindParam(':client_type', $client['type']);
            $stmt->execute();
            $payments = $stmt->fetch(PDO::FETCH_ASSOC);
            $total_payments = $payments['total_payments'] ? floatval($payments['total_payments']) : 0;
        }

        // إعداد بيانات الحساب
        $account = [
            'id' => $client['id'],
            'name' => $client['name'],
            'type' => $client['type'],
            'total_exchange' => $total_exchange,
            'total_exchange_with_percentage' => $total_exchange_with_percentage,
            'payments' => $total_payments,
            'daily_exchange' => $daily_exchange,
            'current_total' => $total_exchange_with_percentage - $total_payments
        ];

        $accounts[] = $account;
    }

    // إرجاع البيانات كـ JSON
    echo json_encode([
        'success' => true,
        'accounts' => $accounts
    ]);

} catch (PDOException $e) {
    // إرجاع رسالة الخطأ
    echo json_encode([
        'success' => false,
        'message' => 'حدث خطأ أثناء جلب البيانات: ' . $e->getMessage()
    ]);
}
