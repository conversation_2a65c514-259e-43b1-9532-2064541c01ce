<?php
// تضمين ملف التكوين
require_once '../config/config.php';

// تضمين اتصال قاعدة البيانات
require_once '../includes/db.php';

// تضمين الدوال المساعدة
require_once '../includes/functions.php';

// تضمين دوال المصادقة
require_once '../includes/auth.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    header('Content-Type: application/json');
    echo json_encode([
        'success' => false,
        'message' => 'يجب تسجيل الدخول للوصول إلى هذه الواجهة'
    ]);
    exit;
}

// التعامل مع طلبات POST
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // الحصول على البيانات المرسلة
    $data = json_decode(file_get_contents('php://input'), true);

    if (!$data) {
        header('Content-Type: application/json');
        echo json_encode([
            'success' => false,
            'message' => 'بيانات غير صالحة'
        ]);
        exit;
    }

    // التحقق من الإجراء المطلوب
    if (!isset($data['action'])) {
        header('Content-Type: application/json');
        echo json_encode([
            'success' => false,
            'message' => 'الإجراء غير محدد'
        ]);
        exit;
    }

    // التعامل مع إجراء إضافة عميل
    if ($data['action'] === 'add_client') {
        addClient($data['client']);
    }

    // التعامل مع إجراء إضافة إعلان
    if ($data['action'] === 'add_ad') {
        addAd($data['ad']);
    }

    // التعامل مع إجراء تحديث حقل الإعلان
    if ($data['action'] === 'update_ad_field') {
        updateAdField($data['ad_id'], $data['field'], $data['value']);
    }
}
// التعامل مع طلبات GET
else {
    // جلب بيانات عملاء الإعلانات
    getAdClients();
}

/**
 * جلب بيانات عملاء الإعلانات
 */
function getAdClients()
{
    global $db;

    try {
        // جلب العملاء
        $query = "SELECT * FROM ad_clients ORDER BY type, name";
        $stmt = $db->prepare($query);
        $stmt->execute();
        $clients = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // جلب الإعلانات لكل عميل
        foreach ($clients as &$client) {
            $query = "SELECT * FROM ads WHERE client_id = :client_id ORDER BY date DESC";
            $stmt = $db->prepare($query);
            $stmt->bindParam(':client_id', $client['id']);
            $stmt->execute();
            $client['ads'] = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // حساب الإجماليات
            $client['total_cost'] = 0;
            $client['total_exchange'] = 0;
            $client['daily_exchange'] = 0;
            $client['total_exchange_with_percentage'] = 0;
            $activeAdsCount = 0;
            $n = 0;
            // حساب الصرف اليومي بالطريقة الصحيحة
            foreach ($client['ads'] as &$ad) {
                // حساب الصرف اليومي = التكلفة / عدد الأيام
                $days = isset($ad['days']) && intval($ad['days']) > 0 ? intval($ad['days']) : 1;
                $ad['daily_cost'] = floatval($ad['cost']) / $days;
                if ($client['ads']) {
                    // جلب اسم الحساب الاعلاني 
                    $query = "SELECT id, name, status, balance, spending_limit FROM ad_accounts WHERE id = :id";
                    $stmt = $db->prepare($query);
                    $stmt->bindParam(':id', $ad['ad_account_id']);
                    $stmt->execute();
                    $account = $stmt->fetch(PDO::FETCH_ASSOC);
                    $client['ads'][$n]['account'] = $account['name'];
                }
                $n  += 1;
                // حساب الإجماليات فقط للإعلانات النشطة
                if (strpos($ad['status'], 'نشط') !== false || strpos($ad['status'], 'يومي') !== false) {
                    $client['total_cost'] += floatval($ad['cost']);
                    $client['total_exchange'] += floatval($ad['exchange_rate']);
                    $client['daily_exchange'] += $ad['daily_cost'];

                    // حساب الصرف بالنسبة
                    $commissionPercentage = isset($client['commission_percentage']) ? floatval($client['commission_percentage']) : 50.00;
                    $commissionMultiplier = 1 + ($commissionPercentage / 100);
                    $exchangeRateWithPercentage = floatval($ad['exchange_rate']) * $commissionMultiplier;
                    $client['total_exchange_with_percentage'] += $exchangeRateWithPercentage;

                    $activeAdsCount++;
                }
            }

            // تم حساب الإجماليات بالفعل في الحلقة السابقة

            // تقريب الصرف اليومي إلى رقمين عشريين
            $client['daily_exchange'] = round($client['daily_exchange'], 2);
        }

        // إرجاع البيانات
        header('Content-Type: application/json');
        echo json_encode([
            'success' => true,
            'clients' => $clients
        ]);
    } catch (PDOException $e) {
        // إرجاع رسالة الخطأ
        header('Content-Type: application/json');
        echo json_encode([
            'success' => false,
            'message' => 'خطأ في قاعدة البيانات: ' . $e->getMessage()
        ]);
    }
}

/**
 * إضافة عميل جديد
 * @param array $client بيانات العميل
 */
function addClient($client)
{
    global $db;

    try {
        // التحقق من وجود العميل
        $query = "SELECT COUNT(*) FROM ad_clients WHERE name = :name AND type = :type";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':name', $client['name']);
        $stmt->bindParam(':type', $client['type']);
        $stmt->execute();

        if ($stmt->fetchColumn() > 0) {
            header('Content-Type: application/json');
            echo json_encode([
                'success' => false,
                'message' => 'العميل موجود بالفعل'
            ]);
            return;
        }

        // إضافة العميل
        $query = "INSERT INTO ad_clients (name, type, commission_percentage) VALUES (:name, :type, :commission_percentage)";
        $stmt = $db->prepare($query);

        // تحضير المتغيرات
        $name = $client['name'];
        $type = $client['type'];
        $commissionPercentage = isset($client['commission_percentage']) ? $client['commission_percentage'] : 50.00;

        // ربط المتغيرات
        $stmt->bindParam(':name', $name);
        $stmt->bindParam(':type', $type);
        $stmt->bindParam(':commission_percentage', $commissionPercentage);
        $stmt->execute();

        // إرجاع النجاح
        header('Content-Type: application/json');
        echo json_encode([
            'success' => true,
            'message' => 'تمت إضافة العميل بنجاح',
            'client_id' => $db->lastInsertId()
        ]);
    } catch (PDOException $e) {
        // إرجاع رسالة الخطأ
        header('Content-Type: application/json');
        echo json_encode([
            'success' => false,
            'message' => 'خطأ في قاعدة البيانات: ' . $e->getMessage()
        ]);
    }
}

/**
 * إضافة إعلان جديد
 * @param array $ad بيانات الإعلان
 */
function addAd($ad)
{
    global $db;

    try {
        // التحقق من وجود العميل في جدول ad_clients
        $query = "SELECT id FROM ad_clients WHERE id = :id";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':id', $ad['client_id']);
        $stmt->execute();

        if ($stmt->rowCount() === 0) {
            // إذا لم يكن العميل موجودًا، نقوم بإنشائه
            $query = "INSERT INTO ad_clients (id, name, type) VALUES (:id, :name, :type)";
            $stmt = $db->prepare($query);
            $stmt->bindParam(':id', $ad['client_id']);
            $stmt->bindParam(':name', $ad['client_type']);
            $stmt->bindParam(':type', $ad['client_type']);
            $stmt->execute();
        }

        // التحقق من وجود الحساب الإعلاني
        if (isset($ad['ad_account_id']) && !empty($ad['ad_account_id'])) {
            $query = "SELECT id, name, status, balance, spending_limit FROM ad_accounts WHERE id = :id";
            $stmt = $db->prepare($query);
            $stmt->bindParam(':id', $ad['ad_account_id']);
            $stmt->execute();
            $account = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$account) {
                header('Content-Type: application/json');
                echo json_encode([
                    'success' => false,
                    'message' => 'الحساب الإعلاني غير موجود'
                ]);
                return;
            }

            if ($account['status'] !== 'نشط') {
                header('Content-Type: application/json');
                echo json_encode([
                    'success' => false,
                    'message' => 'الحساب الإعلاني غير نشط'
                ]);
                return;
            }

            // التحقق من حد الصرف
            if ($account['balance'] + $ad['cost'] > $account['spending_limit']) {
                header('Content-Type: application/json');
                echo json_encode([
                    'success' => false,
                    'message' => 'تجاوز حد الصرف للحساب الإعلاني'
                ]);
                return;
            }
        }

        // استخدام سعر الصرف المرسل
        $exchangeRate = $ad['exchange_rate'];

        // بدء المعاملة
        $db->beginTransaction();

        // التحقق من وجود إعلان مشابه تم إضافته خلال الدقيقة الماضية
        $oneMinuteAgo = date('Y-m-d H:i:s', strtotime('-1 minute'));
        $query = "SELECT id FROM ads
                  WHERE client_id = :client_id
                  AND client_type = :client_type
                  AND date = :date
                  AND type = :type
                  AND cost = :cost
                  AND status = :status
                  AND created_at > :one_minute_ago
                  LIMIT 1";

        $stmt = $db->prepare($query);
        $stmt->bindParam(':client_id', $ad['client_id']);
        $stmt->bindParam(':client_type', $ad['client_type']);
        $stmt->bindParam(':date', $ad['date']);
        $stmt->bindParam(':type', $ad['type']);
        $stmt->bindParam(':cost', $ad['cost']);
        $stmt->bindParam(':status', $ad['status']);
        $stmt->bindParam(':one_minute_ago', $oneMinuteAgo);
        $stmt->execute();

        if ($stmt->rowCount() > 0) {
            $db->commit(); // تأكيد المعاملة
            header('Content-Type: application/json');
            echo json_encode([
                'success' => true,
                'message' => 'تمت إضافة الإعلان بنجاح',
                'ad_id' => $stmt->fetch(PDO::FETCH_COLUMN)
            ]);
            return;
        }

        // إضافة الإعلان
        $query = "INSERT INTO ads (client_id, client_type, ad_account_id, date, days, type, cost, status, exchange_rate, post)
                  VALUES (:client_id, :client_type, :ad_account_id, :date, :days, :type, :cost, :status, :exchange_rate, :post)";

        $stmt = $db->prepare($query);
        $stmt->bindParam(':client_id', $ad['client_id']);
        $stmt->bindParam(':client_type', $ad['client_type']);
        $stmt->bindParam(':ad_account_id', $ad['ad_account_id']);
        $stmt->bindParam(':date', $ad['date']);
        $stmt->bindParam(':days', $ad['days']);
        $stmt->bindParam(':type', $ad['type']);
        $stmt->bindParam(':cost', $ad['cost']);
        $stmt->bindParam(':status', $ad['status']);
        $stmt->bindParam(':exchange_rate', $exchangeRate);
        $stmt->bindParam(':post', $ad['post']);
        $stmt->execute();

        // تحديث رصيد الحساب الإعلاني
        if (isset($ad['ad_account_id']) && !empty($ad['ad_account_id'])) {
            $query = "UPDATE ad_accounts SET balance = balance + :cost WHERE id = :id";
            $stmt = $db->prepare($query);
            $stmt->bindParam(':cost', $ad['cost']);
            $stmt->bindParam(':id', $ad['ad_account_id']);
            $stmt->execute();
        }

        // تأكيد المعاملة
        $db->commit();

        // إرجاع النجاح
        header('Content-Type: application/json');
        echo json_encode([
            'success' => true,
            'message' => 'تمت إضافة الإعلان بنجاح',
            'ad_id' => $db->lastInsertId()
        ]);
    } catch (PDOException $e) {
        if ($db->inTransaction()) {
            $db->rollBack();
        }

        header('Content-Type: application/json');
        echo json_encode([
            'success' => false,
            'message' => 'خطأ في قاعدة البيانات: ' . $e->getMessage()
        ]);
    }
}

/**
 * تحديث حقل الإعلان
 * @param int $adId معرف الإعلان
 * @param string $field اسم الحقل
 * @param mixed $value القيمة الجديدة
 */
function updateAdField($adId, $field, $value)
{
    global $db;

    try {
        // التحقق من وجود الإعلان
        $query = "SELECT * FROM ads WHERE id = :id";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':id', $adId);
        $stmt->execute();

        if ($stmt->rowCount() === 0) {
            header('Content-Type: application/json');
            echo json_encode([
                'success' => false,
                'message' => 'الإعلان غير موجود'
            ]);
            return;
        }

        $ad = $stmt->fetch(PDO::FETCH_ASSOC);

        // التحقق من صحة الحقل
        $allowedFields = ['cost', 'status', 'exchange_rate'];
        if (!in_array($field, $allowedFields)) {
            header('Content-Type: application/json');
            echo json_encode([
                'success' => false,
                'message' => 'الحقل غير صالح'
            ]);
            return;
        }

        // تحديث الحقل
        $query = "UPDATE ads SET $field = :value";

        // إذا كان الحقل هو الحالة، نحتاج إلى تحديث عدد الأيام
        if ($field === 'status') {
            $days = 1; // القيمة الافتراضية

            // استخراج عدد الأيام من الحالة (مثل "نشط 7 أيام")
            if (preg_match('/نشط\s+(\d+)\s+أيام/', $value, $matches)) {
                $days = intval($matches[1]);
            }

            $query .= ", days = :days";
        }

        $query .= " WHERE id = :id";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':value', $value);
        $stmt->bindParam(':id', $adId);

        if ($field === 'status') {
            $stmt->bindParam(':days', $days);
        }

        $stmt->execute();

        // إرجاع النجاح
        header('Content-Type: application/json');
        echo json_encode([
            'success' => true,
            'message' => 'تم تحديث الإعلان بنجاح'
        ]);
    } catch (PDOException $e) {
        // إرجاع رسالة الخطأ
        header('Content-Type: application/json');
        echo json_encode([
            'success' => false,
            'message' => 'خطأ في قاعدة البيانات: ' . $e->getMessage()
        ]);
    }
}
