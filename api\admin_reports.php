<?php
// تضمين ملف الاتصال بقاعدة البيانات
require_once '../config/database.php';

// التحقق من وجود الشهر والسنة
$month = isset($_GET['month']) ? intval($_GET['month']) : date('m');
$year = isset($_GET['year']) ? intval($_GET['year']) : date('Y');

try {
    // إنشاء اتصال بقاعدة البيانات
    $database = new Database();
    $db = $database->getConnection();

    // الحصول على بيانات العملاء
    $query = "SELECT id, name, page_management, ads, ads_ratio, payments, previous_debt, previous_balance, end_month_balance 
              FROM clients 
              ORDER BY name ASC";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $clients = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // إعداد مصفوفة للنتائج
    $result = [];

    foreach ($clients as $client) {
        // حساب إجمالي حساب العميل
        $total = $client['previous_balance'] + $client['page_management'] + $client['ads_ratio'] - $client['payments'];

        // إضافة العميل إلى النتائج
        $result[] = [
            'id' => $client['id'],
            'name' => $client['name'],
            'page_management' => $client['page_management'],
            'ads' => $client['ads'],
            'ads_ratio' => $client['ads_ratio'],
            'payments' => $client['payments'],
            'previous_debt' => $client['previous_debt'],
            'previous_balance' => $client['previous_balance'],
            'end_month_balance' => $client['end_month_balance'],
            'total' => $total
        ];
    }

    // إرجاع البيانات بتنسيق JSON
    header('Content-Type: application/json');
    echo json_encode([
        'success' => true,
        'month' => $month,
        'year' => $year,
        'clients' => $result
    ]);
} catch (PDOException $e) {
    // إرجاع رسالة الخطأ
    header('Content-Type: application/json');
    echo json_encode([
        'success' => false,
        'message' => 'خطأ في قاعدة البيانات: ' . $e->getMessage()
    ]);
}
