<?php
// تعيين نوع المحتوى
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// تضمين ملف الاتصال بقاعدة البيانات
try {
    require_once '../../includes/db.php';
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'فشل الاتصال بقاعدة البيانات']);
    exit;
}

// التحقق من طريقة الطلب
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'طريقة الطلب غير مدعومة']);
    exit;
}

try {
    // قراءة البيانات من الطلب
    $input = json_decode(file_get_contents('php://input'), true);

    if (!$input) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'بيانات غير صالحة']);
        exit;
    }

    // التحقق من الحقول المطلوبة
    $requiredFields = ['date', 'type', 'cost', 'days', 'post', 'daily_total', 'client_id', 'ad_account_id', 'exchange_rate'];
    foreach ($requiredFields as $field) {
        if (!isset($input[$field]) || $input[$field] === '') {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => "الحقل {$field} مطلوب"]);
            exit;
        }
    }

    // تنظيف البيانات
    $date = trim($input['date']);
    $type = trim($input['type']);
    $cost = floatval($input['cost']);
    $days = intval($input['days']);
    $post = trim($input['post']);
    $dailyTotal = trim($input['daily_total']);
    $clientId = intval($input['client_id']);
    $clientSource = isset($input['client_source']) ? trim($input['client_source']) : 'clients';
    $adAccountId = intval($input['ad_account_id']);
    $exchangeRate = floatval($input['exchange_rate']);

    // تسجيل البيانات المستلمة للتصحيح
    error_log("بيانات الإعلان المستلمة: " . json_encode($input));

    // التحقق من صحة البيانات
    if ($cost <= 0) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'التكلفة يجب أن تكون أكبر من صفر']);
        exit;
    }

    if ($days <= 0) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'عدد الأيام يجب أن يكون أكبر من صفر']);
        exit;
    }

    if ($exchangeRate <= 0) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'سعر الصرف يجب أن يكون أكبر من صفر']);
        exit;
    }

    // التحقق من وجود العميل حسب المصدر المحدد
    $client = null;

    if ($clientSource === 'ad_clients') {
        // البحث في جدول ad_clients
        try {
            $adClientQuery = "SELECT id, name, 'ad_clients' as source FROM ad_clients WHERE id = :client_id";
            $adClientStmt = $db->prepare($adClientQuery);
            $adClientStmt->bindParam(':client_id', $clientId);
            $adClientStmt->execute();
            $client = $adClientStmt->fetch(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            error_log("خطأ في البحث في جدول ad_clients: " . $e->getMessage());
        }
    } else {
        // البحث في جدول clients (افتراضي)
        try {
            $clientQuery = "SELECT id, name, 'clients' as source FROM clients WHERE id = :client_id";
            $clientStmt = $db->prepare($clientQuery);
            $clientStmt->bindParam(':client_id', $clientId);
            $clientStmt->execute();
            $client = $clientStmt->fetch(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            error_log("خطأ في البحث في جدول clients: " . $e->getMessage());
        }
    }

    // إذا لم يوجد العميل في الجدول المحدد، ابحث في الجدول الآخر
    if (!$client) {
        if ($clientSource === 'ad_clients') {
            // ابحث في جدول clients
            try {
                $clientQuery = "SELECT id, name, 'clients' as source FROM clients WHERE id = :client_id";
                $clientStmt = $db->prepare($clientQuery);
                $clientStmt->bindParam(':client_id', $clientId);
                $clientStmt->execute();
                $client = $clientStmt->fetch(PDO::FETCH_ASSOC);
            } catch (PDOException $e) {
                error_log("خطأ في البحث الاحتياطي في جدول clients: " . $e->getMessage());
            }
        } else {
            // ابحث في جدول ad_clients
            try {
                $adClientQuery = "SELECT id, name, 'ad_clients' as source FROM ad_clients WHERE id = :client_id";
                $adClientStmt = $db->prepare($adClientQuery);
                $adClientStmt->bindParam(':client_id', $clientId);
                $adClientStmt->execute();
                $client = $adClientStmt->fetch(PDO::FETCH_ASSOC);
            } catch (PDOException $e) {
                error_log("خطأ في البحث الاحتياطي في جدول ad_clients: " . $e->getMessage());
            }
        }
    }

    // إذا لم يوجد العميل في أي من الجدولين، أنشئ عميل افتراضي
    if (!$client) {
        $client = [
            'id' => $clientId,
            'name' => 'عميل افتراضي ' . $clientId,
            'source' => $clientSource
        ];
        error_log("لم يتم العثور على العميل، استخدام عميل افتراضي: " . json_encode($client));
    }

    // التحقق من وجود الحساب الإعلاني
    $account = null;
    try {
        $accountQuery = "SELECT id, name FROM ad_accounts WHERE id = :ad_account_id";
        $accountStmt = $db->prepare($accountQuery);
        $accountStmt->bindParam(':ad_account_id', $adAccountId);
        $accountStmt->execute();
        $account = $accountStmt->fetch(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        error_log("خطأ في البحث عن الحساب الإعلاني: " . $e->getMessage());
    }

    // إذا لم يوجد الحساب، أنشئ حساب افتراضي
    if (!$account) {
        $account = [
            'id' => $adAccountId,
            'name' => 'حساب افتراضي ' . $adAccountId
        ];
        error_log("لم يتم العثور على الحساب الإعلاني، استخدام حساب افتراضي: " . json_encode($account));
    }

    // حساب التكلفة اليومية
    $dailyCost = $dailyTotal === 'يومي' ? $cost : ($cost / $days);

    // تسجيل البيانات النهائية قبل الإدراج
    error_log("البيانات النهائية للإدراج:");
    error_log("العميل: " . json_encode($client));
    error_log("الحساب: " . json_encode($account));
    error_log("التكلفة اليومية: " . $dailyCost);

    // إدراج الإعلان الجديد
    $insertQuery = "INSERT INTO ads (
        date,
        type,
        cost,
        days,
        post,
        daily_total,
        client_id,
        ad_account_id,
        exchange_rate,
        daily_cost,
        status,
        account,
        created_at
    ) VALUES (
        :date,
        :type,
        :cost,
        :days,
        :post,
        :daily_total,
        :client_id,
        :ad_account_id,
        :exchange_rate,
        :daily_cost,
        'مستمر',
        :account,
        NOW()
    )";

    $insertStmt = $db->prepare($insertQuery);
    $insertStmt->bindParam(':date', $date);
    $insertStmt->bindParam(':type', $type);
    $insertStmt->bindParam(':cost', $cost);
    $insertStmt->bindParam(':days', $days);
    $insertStmt->bindParam(':post', $post);
    $insertStmt->bindParam(':daily_total', $dailyTotal);
    $insertStmt->bindParam(':client_id', $clientId);
    $insertStmt->bindParam(':ad_account_id', $adAccountId);
    $insertStmt->bindParam(':exchange_rate', $exchangeRate);
    $insertStmt->bindParam(':daily_cost', $dailyCost);
    $insertStmt->bindParam(':account', $account['name']);

    $insertStmt->execute();

    $newAdId = $db->lastInsertId();

    // جلب بيانات الإعلان المضاف للإرجاع
    $adQuery = "SELECT a.*, c.name as client_name, aa.name as account_name
                FROM ads a
                LEFT JOIN clients c ON a.client_id = c.id
                LEFT JOIN ad_accounts aa ON a.ad_account_id = aa.id
                WHERE a.id = :ad_id";
    $adStmt = $db->prepare($adQuery);
    $adStmt->bindParam(':ad_id', $newAdId);
    $adStmt->execute();
    $newAd = $adStmt->fetch(PDO::FETCH_ASSOC);

    echo json_encode([
        'success' => true,
        'message' => 'تم إضافة الإعلان بنجاح',
        'ad' => $newAd
    ]);

} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'خطأ في قاعدة البيانات: ' . $e->getMessage()]);
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'خطأ في الخادم: ' . $e->getMessage()]);
}
?>
