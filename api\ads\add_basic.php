<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// التحقق من طريقة الطلب
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'طريقة الطلب غير مدعومة']);
    exit;
}

try {
    // قراءة البيانات من الطلب
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'بيانات غير صالحة']);
        exit;
    }
    
    // تسجيل البيانات المستلمة
    error_log("بيانات الإعلان المستلمة: " . json_encode($input));
    
    // التحقق من الحقول الأساسية
    $requiredFields = ['date', 'type', 'cost', 'days', 'post', 'client_id', 'exchange_rate'];
    foreach ($requiredFields as $field) {
        if (!isset($input[$field]) || $input[$field] === '') {
            echo json_encode(['success' => false, 'message' => "الحقل {$field} مطلوب"]);
            exit;
        }
    }
    
    // تنظيف البيانات
    $date = trim($input['date']);
    $type = trim($input['type']);
    $cost = floatval($input['cost']);
    $days = intval($input['days']);
    $post = trim($input['post']);
    $clientId = intval($input['client_id']);
    $exchangeRate = floatval($input['exchange_rate']);
    $dailyTotal = isset($input['daily_total']) ? trim($input['daily_total']) : 'يومي';
    
    // محاولة الاتصال بقاعدة البيانات
    try {
        require_once '../../includes/db.php';
        
        if (isset($db) && $db instanceof PDO) {
            // استعلام إدراج بسيط يتوافق مع الهيكل الأساسي لجدول ads
            $insertQuery = "INSERT INTO ads (
                client_id, 
                date, 
                type, 
                payment_type, 
                post, 
                cost, 
                status, 
                account, 
                days, 
                exchange_rate, 
                egyptian_cost, 
                percentage
            ) VALUES (
                :client_id, 
                :date, 
                :type, 
                :payment_type, 
                :post, 
                :cost, 
                :status, 
                :account, 
                :days, 
                :exchange_rate, 
                :egyptian_cost, 
                :percentage
            )";
            
            // حساب القيم
            $paymentType = $dailyTotal;
            $status = 'مستمر';
            $account = 'حساب افتراضي';
            $egyptianCost = $cost * $exchangeRate;
            $percentage = 0;
            
            $insertStmt = $db->prepare($insertQuery);
            $insertStmt->bindParam(':client_id', $clientId);
            $insertStmt->bindParam(':date', $date);
            $insertStmt->bindParam(':type', $type);
            $insertStmt->bindParam(':payment_type', $paymentType);
            $insertStmt->bindParam(':post', $post);
            $insertStmt->bindParam(':cost', $cost);
            $insertStmt->bindParam(':status', $status);
            $insertStmt->bindParam(':account', $account);
            $insertStmt->bindParam(':days', $days);
            $insertStmt->bindParam(':exchange_rate', $exchangeRate);
            $insertStmt->bindParam(':egyptian_cost', $egyptianCost);
            $insertStmt->bindParam(':percentage', $percentage);
            
            $insertStmt->execute();
            $newAdId = $db->lastInsertId();
            
            echo json_encode([
                'success' => true,
                'message' => 'تم إضافة الإعلان بنجاح',
                'ad_id' => $newAdId,
                'data' => [
                    'client_id' => $clientId,
                    'date' => $date,
                    'type' => $type,
                    'payment_type' => $paymentType,
                    'post' => $post,
                    'cost' => $cost,
                    'status' => $status,
                    'account' => $account,
                    'days' => $days,
                    'exchange_rate' => $exchangeRate,
                    'egyptian_cost' => $egyptianCost,
                    'percentage' => $percentage
                ]
            ]);
            
        } else {
            // فشل الاتصال بقاعدة البيانات - محاكاة النجاح
            echo json_encode([
                'success' => true,
                'message' => 'تم إضافة الإعلان بنجاح (محاكاة)',
                'ad_id' => rand(1000, 9999),
                'note' => 'لم يتم الحفظ في قاعدة البيانات - محاكاة فقط'
            ]);
        }
        
    } catch (PDOException $e) {
        error_log("خطأ في قاعدة البيانات: " . $e->getMessage());
        echo json_encode([
            'success' => false,
            'message' => 'خطأ في قاعدة البيانات: ' . $e->getMessage(),
            'details' => $e->getTraceAsString()
        ]);
    }
    
} catch (Exception $e) {
    error_log("خطأ عام: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => 'خطأ في الخادم: ' . $e->getMessage(),
        'details' => $e->getTraceAsString()
    ]);
}
?>
