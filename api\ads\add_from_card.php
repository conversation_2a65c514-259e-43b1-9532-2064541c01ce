<?php
/**
 * API لإضافة إعلان جديد من بطاقة ائتمان
 */

// تضمين ملفات الإعداد والاتصال بقاعدة البيانات
require_once '../../config/config.php';
require_once '../../includes/db.php';

// التحقق من طريقة الطلب
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode([
        'success' => false,
        'message' => 'طريقة الطلب غير مسموح بها'
    ]);
    exit;
}

// قراءة البيانات المرسلة
$data = json_decode(file_get_contents('php://input'), true);

// التحقق من البيانات المطلوبة
$requiredFields = ['date', 'type', 'payment_type', 'post', 'days', 'cost', 'spend', 'page', 'card_id'];
foreach ($requiredFields as $field) {
    if (!isset($data[$field]) || (empty($data[$field]) && $data[$field] !== '0')) {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'message' => "الحقل {$field} مطلوب"
        ]);
        exit;
    }
}

// تنظيف البيانات
$date = htmlspecialchars(strip_tags($data['date']));
$type = htmlspecialchars(strip_tags($data['type']));
$paymentType = htmlspecialchars(strip_tags($data['payment_type']));
$post = htmlspecialchars(strip_tags($data['post']));
$days = intval($data['days']);
$cost = floatval($data['cost']);
$spend = floatval($data['spend']);
$page = htmlspecialchars(strip_tags($data['page']));
$cardId = intval($data['card_id']);
$adAccountId = isset($data['ad_account_id']) && !empty($data['ad_account_id']) ? intval($data['ad_account_id']) : null;

try {
    // بدء المعاملة
    $db->beginTransaction();

    // التحقق من وجود البطاقة
    $stmt = $db->prepare("SELECT * FROM credit_cards WHERE id = :id");
    $stmt->bindParam(':id', $cardId);
    $stmt->execute();
    $card = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$card) {
        throw new Exception('بطاقة الائتمان غير موجودة');
    }

    // التحقق من وجود الحساب الإعلاني إذا تم تحديده
    if ($adAccountId) {
        $stmt = $db->prepare("
            SELECT * FROM ad_accounts
            WHERE id = :id AND linked_account_type = 'credit_card' AND linked_account_id = :card_id
        ");
        $stmt->bindParam(':id', $adAccountId);
        $stmt->bindParam(':card_id', $cardId);
        $stmt->execute();
        $adAccount = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$adAccount) {
            throw new Exception('الحساب الإعلاني غير موجود أو غير مرتبط بهذه البطاقة');
        }
    }

    // إنشاء عميل افتراضي إذا لم يكن موجود
    $defaultClientId = 1;
    $stmt = $db->prepare("SELECT id FROM clients LIMIT 1");
    $stmt->execute();
    $client = $stmt->fetch(PDO::FETCH_ASSOC);
    if ($client) {
        $defaultClientId = $client['id'];
    } else {
        // إنشاء عميل افتراضي
        $stmt = $db->prepare("INSERT INTO clients (name, email, phone) VALUES ('عميل افتراضي', '<EMAIL>', '*********')");
        $stmt->execute();
        $defaultClientId = $db->lastInsertId();
    }

    // إضافة الإعلان
    $stmt = $db->prepare("
        INSERT INTO ads (
            client_id, date, type, payment_type, post, days, cost, egyptian_cost, account,
            ad_account_id, status, exchange_rate, created_at
        ) VALUES (
            :client_id, :date, :type, :payment_type, :post, :days, :cost, :egyptian_cost, :account,
            :ad_account_id, 'نشط', 1.0, NOW()
        )
    ");

    $stmt->bindParam(':client_id', $defaultClientId);
    $stmt->bindParam(':date', $date);
    $stmt->bindParam(':type', $type);
    $stmt->bindParam(':payment_type', $paymentType);
    $stmt->bindParam(':post', $post);
    $stmt->bindParam(':days', $days);
    $stmt->bindParam(':cost', $cost);
    $stmt->bindParam(':egyptian_cost', $spend);
    $stmt->bindParam(':account', $page);
    $stmt->bindParam(':ad_account_id', $adAccountId);

    $stmt->execute();
    $adId = $db->lastInsertId();

    // تحديث رصيد البطاقة
    $newBalance = $card['balance'] - $cost;
    $stmt = $db->prepare("UPDATE credit_cards SET balance = :balance WHERE id = :id");
    $stmt->bindParam(':balance', $newBalance);
    $stmt->bindParam(':id', $cardId);
    $stmt->execute();

    // تحديث رصيد الحساب الإعلاني إذا تم تحديده
    if ($adAccountId) {
        $newAccountBalance = $adAccount['balance'] - $cost;
        $stmt = $db->prepare("UPDATE ad_accounts SET balance = :balance WHERE id = :id");
        $stmt->bindParam(':balance', $newAccountBalance);
        $stmt->bindParam(':id', $adAccountId);
        $stmt->execute();
    }

    // إنهاء المعاملة
    $db->commit();

    // إرجاع البيانات
    echo json_encode([
        'success' => true,
        'message' => 'تم إضافة الإعلان بنجاح',
        'ad' => [
            'id' => $adId,
            'date' => $date,
            'type' => $type,
            'payment_type' => $paymentType,
            'post' => $post,
            'days' => $days,
            'cost' => $cost,
            'spend' => $spend,
            'page' => $page,
            'card_id' => $cardId,
            'ad_account_id' => $adAccountId
        ]
    ]);
} catch (Exception $e) {
    // التراجع عن المعاملة في حالة حدوث خطأ
    $db->rollBack();

    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'حدث خطأ أثناء إضافة الإعلان: ' . $e->getMessage()
    ]);
}
?>
