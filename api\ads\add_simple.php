<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// التحقق من طريقة الطلب
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'طريقة الطلب غير مدعومة']);
    exit;
}

try {
    // قراءة البيانات من الطلب
    $input = json_decode(file_get_contents('php://input'), true);

    if (!$input) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'بيانات غير صالحة']);
        exit;
    }

    // تسجيل البيانات المستلمة
    error_log("بيانات الإعلان المستلمة: " . json_encode($input));

    // التحقق من الحقول الأساسية
    $requiredFields = ['date', 'type', 'cost', 'days', 'post', 'daily_total', 'client_id', 'ad_account_id', 'exchange_rate'];
    foreach ($requiredFields as $field) {
        if (!isset($input[$field]) || $input[$field] === '') {
            echo json_encode(['success' => false, 'message' => "الحقل {$field} مطلوب"]);
            exit;
        }
    }

    // تنظيف البيانات
    $date = trim($input['date']);
    $type = trim($input['type']);
    $cost = floatval($input['cost']);
    $days = intval($input['days']);
    $post = trim($input['post']);
    $dailyTotal = trim($input['daily_total']);
    $clientId = intval($input['client_id']);
    $clientSource = isset($input['client_source']) ? trim($input['client_source']) : 'clients';
    $adAccountId = intval($input['ad_account_id']);
    $exchangeRate = floatval($input['exchange_rate']);

    // حساب التكلفة اليومية
    $dailyCost = $dailyTotal === 'يومي' ? $cost : ($cost / $days);

    // محاولة الاتصال بقاعدة البيانات
    try {
        require_once '../../includes/db.php';

        if (isset($db) && $db instanceof PDO) {
            // التحقق من هيكل جدول ads أولاً
            $columnsQuery = $db->query("SHOW COLUMNS FROM ads");
            $columns = $columnsQuery->fetchAll(PDO::FETCH_COLUMN);

            // تحديد الأعمدة المتاحة
            $hasPaymentType = in_array('payment_type', $columns);
            $hasAccount = in_array('account', $columns);
            $hasStatus = in_array('status', $columns);
            $hasAdAccountId = in_array('ad_account_id', $columns);

            // إنشاء استعلام الإدراج حسب الأعمدة المتاحة
            if ($hasPaymentType) {
                // استخدام هيكل الجدول الأصلي
                $insertQuery = "INSERT INTO ads (
                    client_id, date, type, payment_type, post, cost,
                    status, days, exchange_rate
                ) VALUES (
                    :client_id, :date, :type, :payment_type, :post, :cost,
                    :status, :days, :exchange_rate
                )";
            } else {
                // استخدام هيكل مبسط
                $insertQuery = "INSERT INTO ads (
                    client_id, date, type, post, cost, days, exchange_rate
                ) VALUES (
                    :client_id, :date, :type, :post, :cost, :days, :exchange_rate
                )";
            }

            $insertStmt = $db->prepare($insertQuery);
            $insertStmt->bindParam(':client_id', $clientId);
            $insertStmt->bindParam(':date', $date);
            $insertStmt->bindParam(':type', $type);
            $insertStmt->bindParam(':post', $post);
            $insertStmt->bindParam(':cost', $cost);
            $insertStmt->bindParam(':days', $days);
            $insertStmt->bindParam(':exchange_rate', $exchangeRate);

            // إضافة parameters إضافية حسب هيكل الجدول
            if ($hasPaymentType) {
                $paymentType = $dailyTotal; // استخدام daily_total كـ payment_type
                $insertStmt->bindParam(':payment_type', $paymentType);
                $insertStmt->bindParam(':status', $status = 'مستمر');
            }

            $insertStmt->execute();
            $newAdId = $db->lastInsertId();

            echo json_encode([
                'success' => true,
                'message' => 'تم إضافة الإعلان بنجاح',
                'ad_id' => $newAdId,
                'data' => [
                    'date' => $date,
                    'type' => $type,
                    'cost' => $cost,
                    'days' => $days,
                    'post' => $post,
                    'daily_total' => $dailyTotal,
                    'client_id' => $clientId,
                    'client_source' => $clientSource,
                    'ad_account_id' => $adAccountId,
                    'exchange_rate' => $exchangeRate,
                    'daily_cost' => $dailyCost
                ]
            ]);

        } else {
            // فشل الاتصال بقاعدة البيانات - محاكاة النجاح
            echo json_encode([
                'success' => true,
                'message' => 'تم إضافة الإعلان بنجاح (محاكاة)',
                'ad_id' => rand(1000, 9999),
                'data' => [
                    'date' => $date,
                    'type' => $type,
                    'cost' => $cost,
                    'days' => $days,
                    'post' => $post,
                    'daily_total' => $dailyTotal,
                    'client_id' => $clientId,
                    'client_source' => $clientSource,
                    'ad_account_id' => $adAccountId,
                    'exchange_rate' => $exchangeRate,
                    'daily_cost' => $dailyCost
                ],
                'note' => 'لم يتم الحفظ في قاعدة البيانات - محاكاة فقط'
            ]);
        }

    } catch (PDOException $e) {
        error_log("خطأ في قاعدة البيانات: " . $e->getMessage());
        echo json_encode([
            'success' => false,
            'message' => 'خطأ في قاعدة البيانات: ' . $e->getMessage()
        ]);
    }

} catch (Exception $e) {
    error_log("خطأ عام: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => 'خطأ في الخادم: ' . $e->getMessage()
    ]);
}
?>
