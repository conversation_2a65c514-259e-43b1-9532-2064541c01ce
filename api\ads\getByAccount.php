<?php
// تضمين ملف الاتصال بقاعدة البيانات
require_once '../../config/database.php';

// إنشاء اتصال بقاعدة البيانات
$database = new Database();
$db = $database->getConnection();

// التحقق من وجود اتصال بقاعدة البيانات
if (!isset($db)) {
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'خطأ في الاتصال بقاعدة البيانات']);
    exit;
}

// تهيئة الاستجابة كـ JSON
header('Content-Type: application/json');

/**
 * جلب الإعلانات حسب الحساب الإعلاني
 */
function getAdsByAccount() {
    global $db;

    $accountId = isset($_GET['accountId']) ? $_GET['accountId'] : null;

    if (!$accountId) {
        echo json_encode(['success' => false, 'message' => 'معرف الحساب الإعلاني مطلوب']);
        return;
    }

    try {
        // التحقق من وجود جدول الإعلانات
        try {
            $tables = $db->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);

            if (!in_array('ads', $tables)) {
                echo json_encode(['success' => false, 'message' => 'جدول الإعلانات غير موجود']);
                return;
            }
        } catch (PDOException $e) {
            // في حالة حدوث خطأ في استعلام SHOW TABLES، نستمر في التنفيذ
            // قد يكون هذا بسبب اختلاف في إعدادات قاعدة البيانات
        }

        // التحقق من وجود العميل رقم 25 في جدول clients
        try {
            $checkClient = $db->prepare("SELECT id, name FROM clients WHERE id = 25");
            $checkClient->execute();
            $clientInfo = $checkClient->fetch(PDO::FETCH_ASSOC);

            if ($clientInfo) {
                $debugInfo = "العميل رقم 25 موجود في جدول clients: " . $clientInfo['name'];
            } else {
                $debugInfo = "العميل رقم 25 غير موجود في جدول clients";
            }
        } catch (PDOException $e) {
            $debugInfo = "خطأ في التحقق من العميل رقم 25: " . $e->getMessage();
        }

        // استخدام JOIN بدلاً من CASE للحصول على اسم العميل
        $query = "SELECT a.*,
                 COALESCE(ac.name, c.name, 'غير محدد') as client_name,
                 a.client_id,
                 a.client_type
                 FROM ads a
                 LEFT JOIN ad_clients ac ON a.client_id = ac.id AND a.client_type = 'ad_client'
                 LEFT JOIN clients c ON a.client_id = c.id AND a.client_type = 'client'
                 WHERE a.ad_account_id = :accountId
                 ORDER BY a.date DESC";

        $stmt = $db->prepare($query);
        $stmt->bindParam(':accountId', $accountId, PDO::PARAM_INT);
        $stmt->execute();
        $ads = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // إضافة معلومات تصحيح الأخطاء إلى الاستجابة
        echo json_encode([
            'success' => true,
            'ads' => $ads,
            'debug' => $debugInfo,
            'client_check' => $clientInfo
        ]);
    } catch (PDOException $e) {
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
    }
}

// تنفيذ الدالة
getAdsByAccount();
