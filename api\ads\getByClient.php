<?php
// تضمين ملف الاتصال بقاعدة البيانات
require_once '../../config/database.php';

// إنشاء اتصال بقاعدة البيانات
$database = new Database();
$db = $database->getConnection();

// التحقق من وجود اتصال بقاعدة البيانات
if (!isset($db)) {
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'خطأ في الاتصال بقاعدة البيانات']);
    exit;
}

// تهيئة الاستجابة كـ JSON
header('Content-Type: application/json');

/**
 * جلب الإعلانات حسب العميل
 */
function getAdsByClient() {
    global $db;

    $clientId = isset($_GET['clientId']) ? $_GET['clientId'] : null;

    if (!$clientId) {
        echo json_encode(['success' => false, 'message' => 'معرف العميل مطلوب']);
        return;
    }

    try {
        // التحقق من وجود جدول الإعلانات
        try {
            $tables = $db->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);

            if (!in_array('ads', $tables)) {
                echo json_encode(['success' => false, 'message' => 'جدول الإعلانات غير موجود']);
                return;
            }
        } catch (PDOException $e) {
            // في حالة حدوث خطأ في استعلام SHOW TABLES، نستمر في التنفيذ
            // قد يكون هذا بسبب اختلاف في إعدادات قاعدة البيانات
        }

        // البحث في جدول الإعلانات
        $query = "SELECT * FROM ads WHERE client_id = :clientId ORDER BY date DESC";

        $stmt = $db->prepare($query);
        $stmt->bindParam(':clientId', $clientId, PDO::PARAM_INT);
        $stmt->execute();
        $ads = $stmt->fetchAll(PDO::FETCH_ASSOC);

        echo json_encode(['success' => true, 'ads' => $ads]);
    } catch (PDOException $e) {
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
    }
}

// تنفيذ الدالة
getAdsByClient();
