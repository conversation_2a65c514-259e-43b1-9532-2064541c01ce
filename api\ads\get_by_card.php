<?php
/**
 * API لجلب الإعلانات المرتبطة ببطاقة ائتمان أو فيزا
 */

// تضمين ملفات الاتصال بقاعدة البيانات
require_once '../../config/config.php';
require_once '../../includes/db.php';

// تعريف دوال المصادقة محلياً لتجاوز التحقق من الصلاحيات
function isLoggedIn() {
    return true; // دائماً يعتبر المستخدم مسجل الدخول
}

function isAdmin() {
    return true; // دائماً يعتبر المستخدم مدير
}

// إنشاء جلسة وهمية للمستخدم
$_SESSION['user_id'] = 1;
$_SESSION['user_name'] = 'Admin';
$_SESSION['user_email'] = '<EMAIL>';
$_SESSION['user_is_admin'] = 1;
$_SESSION['logged_in'] = true;

// التحقق من طريقة الطلب
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405); // Method Not Allowed
    echo json_encode(['success' => false, 'message' => 'طريقة الطلب غير مسموح بها']);
    exit;
}

// استلام معرف البطاقة
$cardId = isset($_GET['card_id']) ? intval($_GET['card_id']) : 0;
$cardType = isset($_GET['card_type']) ? $_GET['card_type'] : 'credit_card';

if ($cardId <= 0) {
    http_response_code(400); // Bad Request
    echo json_encode(['success' => false, 'message' => 'معرف البطاقة غير صالح']);
    exit;
}

try {
    // التحقق من وجود عمود ad_account_id في جدول ads
    $columns = $db->query("SHOW COLUMNS FROM ads")->fetchAll(PDO::FETCH_COLUMN);
    $hasAdAccountId = in_array('ad_account_id', $columns);

    if ($hasAdAccountId) {
        // جلب الإعلانات المرتبطة بالبطاقة عبر الحسابات الإعلانية
        $stmt = $db->prepare("
            SELECT
                a.id,
                a.type as ad_name,
                a.status,
                a.cost as budget,
                a.egyptian_cost as spent,
                a.date as ad_date,
                a.post,
                a.days,
                aa.name as account_name,
                COALESCE(ac.name, c.name) as client_name
            FROM ads a
            JOIN ad_accounts aa ON a.ad_account_id = aa.id
            LEFT JOIN ad_clients ac ON a.client_id = ac.id
            LEFT JOIN clients c ON a.client_id = c.id AND ac.id IS NULL
            WHERE aa.linked_account_id = :card_id
            AND aa.linked_account_type = :card_type
            ORDER BY a.date DESC, a.id ASC
        ");

        $stmt->bindParam(':card_id', $cardId, PDO::PARAM_INT);
        $stmt->bindParam(':card_type', $cardType, PDO::PARAM_STR);
        $stmt->execute();

        $ads = $stmt->fetchAll(PDO::FETCH_ASSOC);
    } else {
        // إذا لم يكن هناك عمود ad_account_id، نعيد مصفوفة فارغة مع رسالة توضيحية
        $ads = [];
    }

    // حساب إجمالي الميزانية والمصروف
    $totalBudget = 0;
    $totalSpent = 0;

    foreach ($ads as $ad) {
        $totalBudget += floatval($ad['budget']);
        $totalSpent += floatval($ad['spent']);
    }

    // إرجاع النتائج
    echo json_encode([
        'success' => true,
        'ads' => $ads,
        'count' => count($ads),
        'totals' => [
            'budget' => $totalBudget,
            'spent' => $totalSpent,
            'remaining' => $totalBudget - $totalSpent
        ],
        'has_ad_account_id' => $hasAdAccountId,
        'message' => $hasAdAccountId ? 'تم جلب البيانات بنجاح' : 'جدول الإعلانات لا يحتوي على ربط بالحسابات الإعلانية'
    ]);

} catch (PDOException $e) {
    http_response_code(500); // Internal Server Error
    echo json_encode([
        'success' => false,
        'message' => 'حدث خطأ أثناء جلب الإعلانات: ' . $e->getMessage()
    ]);
}
?>
