<?php
/**
 * API للإعلانات
 */

// تضمين ملفات الإعدادات
require_once '../../config/config.php';
require_once '../../config/database.php';

// إنشاء اتصال بقاعدة البيانات
$database = new Database();
$db = $database->getConnection();

// التحقق من وجود اتصال بقاعدة البيانات
if (!isset($db)) {
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'خطأ في الاتصال بقاعدة البيانات']);
    exit;
}

// تحديد نوع الطلب
$action = isset($_GET['action']) ? $_GET['action'] : '';

// معالجة الطلبات
switch ($action) {
    case 'get':
        getAds();
        break;
    case 'getByClient':
        getAdsByClient();
        break;
    case 'add':
        addAd();
        break;
    case 'delete':
        deleteAd();
        break;
    case 'update_field':
        updateAdField();
        break;
    case 'update_all_percentage':
        updateAllPercentage();
        break;
    case 'get_percentage':
        getPercentage();
        break;
    case 'get_ad':
        getAd();
        break;
    case 'update':
        updateAd();
        break;
    default:
        // طلب غير صالح
        header('Content-Type: application/json');
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'طلب غير صالح']);
        break;
}

/**
 * جلب الإعلانات
 */
function getAds() {
    global $db;

    header('Content-Type: application/json');

    try {
        // التحقق من وجود معرف العميل
        if (!isset($_GET['client_id'])) {
            throw new Exception('معرف العميل مطلوب');
        }

        $client_id = (int)$_GET['client_id'];

        // التحقق من وجود العميل
        $query = "SELECT id, name FROM clients WHERE id = :id";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':id', $client_id);
        $stmt->execute();

        $client = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$client) {
            throw new Exception('العميل غير موجود');
        }

        // التحقق من وجود جدول الإعلانات
        $tables = $db->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);

        if (!in_array('ads', $tables)) {
            // إنشاء جدول الإعلانات إذا لم يكن موجودًا
            $db->exec("CREATE TABLE ads (
                id INT AUTO_INCREMENT PRIMARY KEY,
                client_id INT NOT NULL,
                date DATE NOT NULL,
                type VARCHAR(50) NOT NULL,
                payment_type VARCHAR(50) NOT NULL,
                post VARCHAR(255) NOT NULL,
                cost DECIMAL(10,2) NOT NULL,
                status VARCHAR(50) NOT NULL,
                account VARCHAR(255) NOT NULL,
                days INT NOT NULL DEFAULT 1,
                exchange_rate DECIMAL(10,2) NOT NULL,
                egyptian_cost DECIMAL(10,2) NOT NULL,
                percentage DECIMAL(5,2) DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                KEY client_id (client_id),
                CONSTRAINT ads_ibfk_1 FOREIGN KEY (client_id) REFERENCES clients (id) ON DELETE CASCADE
            )");

            // إضافة بيانات تجريبية

        }

        // جلب الإعلانات
        $query = "SELECT * FROM ads WHERE client_id = :client_id ORDER BY date DESC, id DESC";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':client_id', $client_id);
        $stmt->execute();

        $ads = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // حساب المجموع
        $totalSpend = 0;
        $totalSpendRatio = 0;

        foreach ($ads as &$ad) {
            // التأكد من وجود قيمة للنسبة
            if (!isset($ad['percentage'])) {
                $ad['percentage'] = 0;
            }

            // حساب الصرف بالمصري
            $egyptianCost = $ad['exchange_rate'];
            $ad['egyptian_cost'] = $egyptianCost;

            // حساب الصرف بالنسبة
            $spendRatio = $egyptianCost + ($egyptianCost * $ad['percentage'] / 100);

            // إضافة إلى المجموع
            $totalSpend += $egyptianCost; // تغيير من $ad['cost'] إلى $egyptianCost
            $totalSpendRatio += $spendRatio;
        }

        $response = [
            'success' => true,
            'client' => $client,
            'ads' => $ads,
            'total_spend' => $totalSpend,
            'total_spend_ratio' => $totalSpendRatio
        ];

        echo json_encode($response);
    } catch (Exception $e) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
    }
}

/**
 * إضافة إعلان جديد
 */
function addAd() {
    global $db;

    header('Content-Type: application/json');

    try {
        // قراءة البيانات من الطلب
        $json = file_get_contents('php://input');
        $data = json_decode($json, true);

        if (!$data || !isset($data['client_id']) || !isset($data['date']) || !isset($data['type']) ||
            !isset($data['payment_type']) || !isset($data['post']) || !isset($data['cost']) ||
            !isset($data['status']) || !isset($data['days']) ||
            !isset($data['exchange_rate']) || !isset($data['egyptian_cost'])) {
            throw new Exception('بيانات غير صالحة');
        }

        // إذا لم يتم تحديد ad_account_id، نبحث عن حساب إعلاني صالح
        if (!isset($data['ad_account_id']) || empty($data['ad_account_id'])) {
            // التحقق من وجود حسابات إعلانية
            $query = "SELECT id FROM ad_accounts WHERE status = 'نشط' LIMIT 1";
            $stmt = $db->prepare($query);
            $stmt->execute();
            $account = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($account) {
                $data['ad_account_id'] = $account['id']; // استخدام أول حساب نشط
            } else {
                // إذا لم يكن هناك حسابات نشطة، نجعل ad_account_id قيمة NULL
                $data['ad_account_id'] = null;
            }
        }

        $client_id = (int)$data['client_id'];
        $date = $data['date'];
        $type = $data['type'];
        $payment_type = $data['payment_type'];
        $post = $data['post'];
        $cost = (float)$data['cost'];
        $status = $data['status'];
        $ad_account_id = (int)$data['ad_account_id'];
        $days = (int)$data['days'];
        $exchange_rate = (float)$data['exchange_rate'];
        $egyptian_cost = (float)$data['egyptian_cost'];
        $percentage = isset($data['percentage']) ? (float)$data['percentage'] : 0;

        // التحقق من وجود العميل في جدول clients أو ad_clients
        $query = "SELECT id FROM clients WHERE id = :id
                  UNION
                  SELECT id FROM ad_clients WHERE id = :id";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':id', $client_id);
        $stmt->execute();

        if ($stmt->rowCount() === 0) {
            throw new Exception('العميل غير موجود');
        }

        // إدراج الإعلان في قاعدة البيانات
        if ($ad_account_id === null) {
            // إذا كان ad_account_id هو NULL، نستخدم استعلام بدون هذا الحقل
            $query = "INSERT INTO ads (client_id, date, type, payment_type, post, cost, status, days, exchange_rate, egyptian_cost, percentage)
                      VALUES (:client_id, :date, :type, :payment_type, :post, :cost, :status, :days, :exchange_rate, :egyptian_cost, :percentage)";
            $stmt = $db->prepare($query);
            $stmt->bindParam(':client_id', $client_id);
            $stmt->bindParam(':date', $date);
            $stmt->bindParam(':type', $type);
            $stmt->bindParam(':payment_type', $payment_type);
            $stmt->bindParam(':post', $post);
            $stmt->bindParam(':cost', $cost);
            $stmt->bindParam(':status', $status);
            $stmt->bindParam(':days', $days);
            $stmt->bindParam(':exchange_rate', $exchange_rate);
            $stmt->bindParam(':egyptian_cost', $egyptian_cost);
            $stmt->bindParam(':percentage', $percentage);
        } else {
            // إذا كان ad_account_id ليس NULL، نستخدم الاستعلام العادي
            $query = "INSERT INTO ads (client_id, date, type, payment_type, post, cost, status, ad_account_id, days, exchange_rate, egyptian_cost, percentage)
                      VALUES (:client_id, :date, :type, :payment_type, :post, :cost, :status, :ad_account_id, :days, :exchange_rate, :egyptian_cost, :percentage)";
            $stmt = $db->prepare($query);
            $stmt->bindParam(':client_id', $client_id);
            $stmt->bindParam(':date', $date);
            $stmt->bindParam(':type', $type);
            $stmt->bindParam(':payment_type', $payment_type);
            $stmt->bindParam(':post', $post);
            $stmt->bindParam(':cost', $cost);
            $stmt->bindParam(':status', $status);
            $stmt->bindParam(':ad_account_id', $ad_account_id);
            $stmt->bindParam(':days', $days);
            $stmt->bindParam(':exchange_rate', $exchange_rate);
            $stmt->bindParam(':egyptian_cost', $egyptian_cost);
            $stmt->bindParam(':percentage', $percentage);
        }
        $stmt->execute();

        // الحصول على معرف الإعلان المدرج
        $ad_id = $db->lastInsertId();

        // ملاحظة: الصرف بالمصري هو القيمة المدخلة مباشرة في حقل exchange_rate

        // حساب الصرف بالنسبة
        $exchange_rate_with_percentage = $exchange_rate + ($exchange_rate * $percentage / 100);

        // تحديث إجمالي الإعلانات للعميل
        $query = "UPDATE clients SET ads = ads + :exchange_rate_with_percentage WHERE id = :id";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':exchange_rate_with_percentage', $exchange_rate_with_percentage);
        $stmt->bindParam(':id', $client_id);
        $stmt->execute();

        // الحصول على قيمة الإعلانات الجديدة للعميل
        $clientData = null;
        try {
            $query = "SELECT c.ads, c.end_month_balance,
                     (SELECT SUM(exchange_rate + (exchange_rate * percentage / 100))
                      FROM ads
                      WHERE client_id = c.id) as ads_ratio
                     FROM clients c
                     WHERE c.id = :id";
            $stmt = $db->prepare($query);
            $stmt->bindParam(':id', $client_id);
            $stmt->execute();
            $clientData = $stmt->fetch(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            // إذا كان العميل من جدول ad_clients وليس من جدول clients
            // نتجاهل الخطأ ونستمر بدون بيانات العميل
        }

        // حساب إجمالي الصرف بالنسبة
        $query = "SELECT
                    SUM(exchange_rate) as total_spend,
                    SUM(exchange_rate + (exchange_rate * percentage / 100)) as total_spend_ratio
                  FROM ads
                  WHERE client_id = :client_id";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':client_id', $client_id);
        $stmt->execute();

        $totals = $stmt->fetch(PDO::FETCH_ASSOC);

        // تحديث حقل ads_ratio في جدول العملاء
        $query = "UPDATE clients SET ads_ratio = :ads_ratio WHERE id = :id";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':ads_ratio', $totals['total_spend_ratio']);
        $stmt->bindParam(':id', $client_id);
        $stmt->execute();

        // إعداد الاستجابة
        $response = [
            'success' => true,
            'message' => 'تمت إضافة الإعلان بنجاح',
            'ad' => [
                'id' => $ad_id,
                'client_id' => $client_id,
                'date' => $date,
                'type' => $type,
                'payment_type' => $payment_type,
                'post' => $post,
                'cost' => $cost,
                'status' => $status,
                'ad_account_id' => $ad_account_id,
                'days' => $days,
                'exchange_rate' => $exchange_rate,
                'egyptian_cost' => $egyptian_cost,
                'percentage' => $percentage
            ],
            'client_data' => [
                'client_id' => $client_id,
                'ads' => $clientData ? $clientData['ads'] : null,
                'ads_ratio' => $clientData ? $clientData['ads_ratio'] : null,
                'end_month_balance' => $clientData ? $clientData['end_month_balance'] : null
            ],
            'total_spend' => $totals['total_spend'],
            'total_spend_ratio' => $totals['total_spend_ratio']
        ];

        echo json_encode($response);
    } catch (Exception $e) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
    }
}

/**
 * حذف إعلان
 */
function deleteAd() {
    global $db;

    header('Content-Type: application/json');

    try {
        // التحقق من وجود معرف الإعلان
        if (!isset($_POST['id'])) {
            throw new Exception('معرف الإعلان مطلوب');
        }

        $ad_id = (int)$_POST['id'];

        // الحصول على بيانات الإعلان قبل الحذف
        $query = "SELECT client_id, exchange_rate, percentage FROM ads WHERE id = :id";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':id', $ad_id);
        $stmt->execute();

        $ad = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$ad) {
            throw new Exception('الإعلان غير موجود');
        }

        // حذف الإعلان من قاعدة البيانات
        $query = "DELETE FROM ads WHERE id = :id";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':id', $ad_id);
        $stmt->execute();

        // حساب الصرف بالنسبة
        $exchange_rate_with_percentage = $ad['exchange_rate'] + ($ad['exchange_rate'] * $ad['percentage'] / 100);

        // تحديث إجمالي الإعلانات للعميل
        $query = "UPDATE clients SET ads = ads - :exchange_rate_with_percentage WHERE id = :id";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':exchange_rate_with_percentage', $exchange_rate_with_percentage);
        $stmt->bindParam(':id', $ad['client_id']);
        $stmt->execute();

        // حساب إجمالي الصرف بالنسبة
        $query = "SELECT
                    SUM(exchange_rate) as total_spend,
                    SUM(exchange_rate + (exchange_rate * percentage / 100)) as total_spend_ratio
                  FROM ads
                  WHERE client_id = :client_id";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':client_id', $ad['client_id']);
        $stmt->execute();

        $totals = $stmt->fetch(PDO::FETCH_ASSOC);

        // تحديث حقل ads_ratio في جدول العملاء
        $query = "UPDATE clients SET ads_ratio = :ads_ratio WHERE id = :id";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':ads_ratio', $totals['total_spend_ratio'] ?? 0);
        $stmt->bindParam(':id', $ad['client_id']);
        $stmt->execute();

        $response = [
            'success' => true,
            'message' => 'تم حذف الإعلان بنجاح'
        ];

        echo json_encode($response);
    } catch (Exception $e) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
    }
}

/**
 * تحديث النسبة لجميع الإعلانات
 */
function updateAllPercentage() {
    global $db;

    header('Content-Type: application/json');

    try {
        // قراءة البيانات من الطلب
        $json = file_get_contents('php://input');
        $data = json_decode($json, true);

        if (!$data || !isset($data['client_id']) || !isset($data['percentage'])) {
            throw new Exception('بيانات غير صالحة');
        }

        $client_id = (int)$data['client_id'];
        $percentage = (float)$data['percentage'];

        // التحقق من وجود العميل
        $query = "SELECT id FROM clients WHERE id = :id";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':id', $client_id);
        $stmt->execute();

        if ($stmt->rowCount() === 0) {
            throw new Exception('العميل غير موجود');
        }

        // تحديث النسبة لجميع إعلانات العميل
        $query = "UPDATE ads SET percentage = :percentage WHERE client_id = :client_id";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':percentage', $percentage);
        $stmt->bindParam(':client_id', $client_id);
        $stmt->execute();

        // حساب إجمالي الصرف بالنسبة
        $query = "SELECT
                    SUM(exchange_rate) as total_spend,
                    SUM(exchange_rate + (exchange_rate * percentage / 100)) as total_spend_ratio
                  FROM ads
                  WHERE client_id = :client_id";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':client_id', $client_id);
        $stmt->execute();

        $totals = $stmt->fetch(PDO::FETCH_ASSOC);

        // تحديث حقل ads_ratio في جدول العملاء
        $query = "UPDATE clients SET ads_ratio = :ads_ratio WHERE id = :id";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':ads_ratio', $totals['total_spend_ratio']);
        $stmt->bindParam(':id', $client_id);
        $stmt->execute();

        // إعداد الاستجابة
        $response = [
            'success' => true,
            'message' => 'تم تحديث النسبة بنجاح',
            'total_spend' => $totals['total_spend'],
            'total_spend_ratio' => $totals['total_spend_ratio'],
            'client_id' => $client_id
        ];

        echo json_encode($response);
    } catch (Exception $e) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
    }
}

/**
 * تحديث حقل في إعلان
 */
function updateAdField() {
    global $db;

    header('Content-Type: application/json');

    try {
        // قراءة البيانات من الطلب
        $json = file_get_contents('php://input');
        $data = json_decode($json, true);

        if (!$data || !isset($data['id']) || !isset($data['field']) || !isset($data['value'])) {
            throw new Exception('بيانات غير صالحة');
        }

        $ad_id = (int)$data['id'];
        $field = $data['field'];
        $value = $data['value'];

        // التحقق من صحة الحقل
        $allowedFields = ['cost', 'status', 'exchange_rate', 'percentage'];
        if (!in_array($field, $allowedFields)) {
            throw new Exception('الحقل غير مسموح به');
        }

        // الحصول على بيانات الإعلان قبل التحديث
        $query = "SELECT client_id, cost, exchange_rate, percentage FROM ads WHERE id = :id";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':id', $ad_id);
        $stmt->execute();

        $ad = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$ad) {
            throw new Exception('الإعلان غير موجود');
        }

        // تحديث الحقل في قاعدة البيانات
        $query = "UPDATE ads SET $field = :value";

        // إذا كان الحقل هو سعر الصرف، نحتاج إلى تحديث الصرف بالمصري
        if ($field === 'exchange_rate') {
            // الصرف بالمصري هو القيمة المدخلة مباشرة
            $egyptian_cost = (float)$value;
            $query .= ", egyptian_cost = :egyptian_cost";
        }

        $query .= " WHERE id = :id";

        $stmt = $db->prepare($query);
        $stmt->bindParam(':value', $value);
        $stmt->bindParam(':id', $ad_id);

        if ($field === 'exchange_rate') {
            $stmt->bindParam(':egyptian_cost', $egyptian_cost);
        }

        $stmt->execute();

        // إذا كان الحقل هو سعر الصرف أو النسبة، نحتاج إلى تحديث إجمالي الإعلانات للعميل
        if ($field === 'exchange_rate' || $field === 'percentage') {
            // حساب الصرف بالنسبة القديم
            $old_exchange_rate_with_percentage = $ad['exchange_rate'] + ($ad['exchange_rate'] * $ad['percentage'] / 100);

            // حساب الصرف بالنسبة الجديد
            $new_percentage = $field === 'percentage' ? (float)$value : $ad['percentage'];
            $new_exchange_rate = $field === 'exchange_rate' ? (float)$value : $ad['exchange_rate'];
            $new_exchange_rate_with_percentage = $new_exchange_rate + ($new_exchange_rate * $new_percentage / 100);

            // حساب الفرق بين القيمة القديمة والجديدة
            $diff = $new_exchange_rate_with_percentage - $old_exchange_rate_with_percentage;

            // تحديث إجمالي الإعلانات للعميل
            $query = "UPDATE clients SET ads = ads + :diff WHERE id = :id";
            $stmt = $db->prepare($query);
            $stmt->bindParam(':diff', $diff);
            $stmt->bindParam(':id', $ad['client_id']);
            $stmt->execute();
        }

        // الحصول على إجمالي الإعلانات للعميل
        $query = "SELECT
                    SUM(exchange_rate) as total_spend,
                    SUM(exchange_rate + (exchange_rate * percentage / 100)) as total_spend_ratio
                  FROM ads
                  WHERE client_id = :client_id";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':client_id', $ad['client_id']);
        $stmt->execute();

        $totals = $stmt->fetch(PDO::FETCH_ASSOC);

        // تحديث حقل ads_ratio في جدول العملاء
        $query = "UPDATE clients SET ads_ratio = :ads_ratio WHERE id = :id";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':ads_ratio', $totals['total_spend_ratio']);
        $stmt->bindParam(':id', $ad['client_id']);
        $stmt->execute();

        // الحصول على بيانات العميل المحدثة
        $query = "SELECT ads, end_month_balance FROM clients WHERE id = :id";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':id', $ad['client_id']);
        $stmt->execute();
        $clientData = $stmt->fetch(PDO::FETCH_ASSOC);

        // إعداد الاستجابة
        $response = [
            'success' => true,
            'message' => 'تم تحديث الحقل بنجاح',
            'total_spend' => $totals['total_spend'],
            'total_spend_ratio' => $totals['total_spend_ratio'],
            'client_data' => [
                'client_id' => $ad['client_id'],
                'ads' => $clientData['ads'],
                'end_month_balance' => $clientData['end_month_balance']
            ]
        ];

        echo json_encode($response);
    } catch (Exception $e) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
    }
}

/**
 * جلب النسبة الحالية للعميل
 */
function getPercentage() {
    global $db;

    header('Content-Type: application/json');

    try {
        // التحقق من وجود معرف العميل
        if (!isset($_GET['client_id'])) {
            throw new Exception('معرف العميل مطلوب');
        }

        $client_id = (int)$_GET['client_id'];

        // التحقق من وجود العميل
        $query = "SELECT id FROM clients WHERE id = :id";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':id', $client_id);
        $stmt->execute();

        if ($stmt->rowCount() === 0) {
            throw new Exception('العميل غير موجود');
        }

        // الحصول على النسبة الحالية
        $query = "SELECT percentage FROM ads WHERE client_id = :client_id ORDER BY id DESC LIMIT 1";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':client_id', $client_id);
        $stmt->execute();

        $percentage = 0;
        if ($stmt->rowCount() > 0) {
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            $percentage = $result['percentage'];
        }

        // إعداد الاستجابة
        $response = [
            'success' => true,
            'percentage' => $percentage,
            'client_id' => $client_id
        ];

        echo json_encode($response);
    } catch (Exception $e) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
    }
}

/**
 * جلب تفاصيل إعلان محدد
 */
function getAd() {
    global $db;

    header('Content-Type: application/json');

    try {
        // التحقق من وجود معرف الإعلان
        if (!isset($_GET['id'])) {
            throw new Exception('معرف الإعلان مطلوب');
        }

        $ad_id = (int)$_GET['id'];

        // جلب تفاصيل الإعلان
        $query = "SELECT * FROM ads WHERE id = :id";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':id', $ad_id);
        $stmt->execute();

        $ad = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$ad) {
            throw new Exception('الإعلان غير موجود');
        }

        // إعداد الاستجابة
        $response = [
            'success' => true,
            'ad' => $ad
        ];

        echo json_encode($response);
    } catch (Exception $e) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
    }
}

/**
 * تحديث إعلان
 */
function updateAd() {
    global $db;

    header('Content-Type: application/json');

    try {
        // قراءة البيانات من الطلب
        $json = file_get_contents('php://input');
        $data = json_decode($json, true);

        if (!$data || !isset($data['id']) || !isset($data['client_id']) || !isset($data['date']) || !isset($data['type']) ||
            !isset($data['payment_type']) || !isset($data['post']) || !isset($data['cost']) ||
            !isset($data['status']) || !isset($data['days']) ||
            !isset($data['exchange_rate']) || !isset($data['ad_account_id'])) {
            throw new Exception('بيانات غير صالحة');
        }

        $ad_id = (int)$data['id'];
        $client_id = (int)$data['client_id'];
        $date = $data['date'];
        $type = $data['type'];
        $payment_type = $data['payment_type'];
        $post = $data['post'];
        $cost = (float)$data['cost'];
        $status = $data['status'];
        $ad_account_id = (int)$data['ad_account_id'];
        $days = (int)$data['days'];
        $exchange_rate = (float)$data['exchange_rate'];
        $egyptian_cost = isset($data['egyptian_cost']) ? (float)$data['egyptian_cost'] : $exchange_rate;
        $percentage = isset($data['percentage']) ? (float)$data['percentage'] : 0;

        // الحصول على بيانات الإعلان قبل التحديث
        $query = "SELECT client_id, exchange_rate, percentage FROM ads WHERE id = :id";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':id', $ad_id);
        $stmt->execute();

        $old_ad = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$old_ad) {
            throw new Exception('الإعلان غير موجود');
        }

        // تحديث الإعلان في قاعدة البيانات
        $query = "UPDATE ads SET
                    client_id = :client_id,
                    date = :date,
                    type = :type,
                    payment_type = :payment_type,
                    post = :post,
                    cost = :cost,
                    status = :status,
                    ad_account_id = :ad_account_id,
                    days = :days,
                    exchange_rate = :exchange_rate,
                    egyptian_cost = :egyptian_cost,
                    percentage = :percentage
                  WHERE id = :id";

        $stmt = $db->prepare($query);
        $stmt->bindParam(':id', $ad_id);
        $stmt->bindParam(':client_id', $client_id);
        $stmt->bindParam(':date', $date);
        $stmt->bindParam(':type', $type);
        $stmt->bindParam(':payment_type', $payment_type);
        $stmt->bindParam(':post', $post);
        $stmt->bindParam(':cost', $cost);
        $stmt->bindParam(':status', $status);
        $stmt->bindParam(':ad_account_id', $ad_account_id);
        $stmt->bindParam(':days', $days);
        $stmt->bindParam(':exchange_rate', $exchange_rate);
        $stmt->bindParam(':egyptian_cost', $egyptian_cost);
        $stmt->bindParam(':percentage', $percentage);
        $stmt->execute();

        // حساب الصرف بالنسبة القديم
        $old_exchange_rate_with_percentage = $old_ad['exchange_rate'] + ($old_ad['exchange_rate'] * $old_ad['percentage'] / 100);

        // حساب الصرف بالنسبة الجديد
        $new_exchange_rate_with_percentage = $exchange_rate + ($exchange_rate * $percentage / 100);

        // حساب الفرق بين القيمة القديمة والجديدة
        $diff = $new_exchange_rate_with_percentage - $old_exchange_rate_with_percentage;

        // تحديث إجمالي الإعلانات للعميل
        $query = "UPDATE clients SET ads = ads + :diff WHERE id = :id";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':diff', $diff);
        $stmt->bindParam(':id', $client_id);
        $stmt->execute();

        // حساب إجمالي الصرف بالنسبة
        $query = "SELECT
                    SUM(exchange_rate) as total_spend,
                    SUM(exchange_rate + (exchange_rate * percentage / 100)) as total_spend_ratio
                  FROM ads
                  WHERE client_id = :client_id";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':client_id', $client_id);
        $stmt->execute();

        $totals = $stmt->fetch(PDO::FETCH_ASSOC);

        // تحديث حقل ads_ratio في جدول العملاء
        $query = "UPDATE clients SET ads_ratio = :ads_ratio WHERE id = :id";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':ads_ratio', $totals['total_spend_ratio']);
        $stmt->bindParam(':id', $client_id);
        $stmt->execute();

        // الحصول على بيانات العميل المحدثة
        $query = "SELECT ads, end_month_balance FROM clients WHERE id = :id";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':id', $client_id);
        $stmt->execute();
        $clientData = $stmt->fetch(PDO::FETCH_ASSOC);

        // إعداد الاستجابة
        $response = [
            'success' => true,
            'message' => 'تم تحديث الإعلان بنجاح',
            'total_spend' => $totals['total_spend'],
            'total_spend_ratio' => $totals['total_spend_ratio'],
            'client_data' => [
                'client_id' => $client_id,
                'ads' => $clientData['ads'],
                'end_month_balance' => $clientData['end_month_balance']
            ]
        ];

        echo json_encode($response);
    } catch (Exception $e) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
    }
}