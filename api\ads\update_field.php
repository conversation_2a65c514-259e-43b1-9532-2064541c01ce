<?php
/**
 * API لتحديث حقول الإعلانات
 */

// إعداد الاستجابة JSON
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// التحقق من طريقة الطلب
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'طريقة الطلب غير مسموحة']);
    exit;
}

// تضمين ملفات الاتصال بقاعدة البيانات
require_once '../../config/database.php';

try {
    // إنشاء اتصال بقاعدة البيانات
    $database = new Database();
    $db = $database->getConnection();

    // التحقق من البيانات المرسلة
    if (!isset($_POST['action']) || $_POST['action'] !== 'update_field') {
        echo json_encode(['success' => false, 'message' => 'إجراء غير صحيح']);
        exit;
    }

    if (!isset($_POST['ad_id']) || !isset($_POST['field']) || !isset($_POST['value'])) {
        echo json_encode(['success' => false, 'message' => 'بيانات غير مكتملة']);
        exit;
    }

    $adId = $_POST['ad_id'];
    $field = $_POST['field'];
    $value = $_POST['value'];

    // التحقق من صحة اسم الحقل
    $allowedFields = ['ad_post', 'ad_cost', 'ad_spent', 'ad_days', 'ad_type'];
    if (!in_array($field, $allowedFields)) {
        echo json_encode(['success' => false, 'message' => 'حقل غير مسموح بتعديله']);
        exit;
    }

    // تحويل اسم الحقل إلى اسم العمود في قاعدة البيانات
    $columnMap = [
        'ad_post' => 'post',
        'ad_cost' => 'cost',
        'ad_spent' => 'egyptian_cost',
        'ad_days' => 'days',
        'ad_type' => 'type'
    ];

    $column = $columnMap[$field];

    // التحقق من صحة القيمة حسب نوع الحقل
    if (in_array($field, ['ad_cost', 'ad_spent'])) {
        if (!is_numeric($value) || $value < 0) {
            echo json_encode(['success' => false, 'message' => 'يجب أن تكون القيمة رقماً موجباً']);
            exit;
        }
        $value = floatval($value);
    } elseif ($field === 'ad_days') {
        if (!is_numeric($value) || $value < 1) {
            echo json_encode(['success' => false, 'message' => 'يجب أن يكون عدد الأيام رقماً موجباً أكبر من صفر']);
            exit;
        }
        $value = intval($value);
    } else {
        // حقول نصية
        $value = trim($value);
        if (empty($value)) {
            echo json_encode(['success' => false, 'message' => 'لا يمكن ترك الحقل فارغاً']);
            exit;
        }
    }

    // إذا كان معرف الإعلان "new"، فهذا يعني إنشاء إعلان جديد
    if ($adId === 'new') {
        // في هذه الحالة، نحتاج لمعلومات إضافية لإنشاء الإعلان
        echo json_encode(['success' => false, 'message' => 'لا يمكن تحديث إعلان غير موجود. يرجى إنشاء الإعلان أولاً.']);
        exit;
    }

    // التحقق من وجود الإعلان
    $stmt = $db->prepare("SELECT id FROM ads WHERE id = ?");
    $stmt->execute([$adId]);
    $ad = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$ad) {
        echo json_encode(['success' => false, 'message' => 'الإعلان غير موجود']);
        exit;
    }

    // تحديث الحقل
    $sql = "UPDATE ads SET $column = ?, updated_at = NOW() WHERE id = ?";
    $stmt = $db->prepare($sql);
    $result = $stmt->execute([$value, $adId]);

    if ($result) {
        // جلب البيانات المحدثة
        $stmt = $db->prepare("
            SELECT id, post, cost, egyptian_cost, days, type, 
                   (cost / GREATEST(days, 1)) as daily_spend,
                   (cost - egyptian_cost) as remaining_spend
            FROM ads 
            WHERE id = ?
        ");
        $stmt->execute([$adId]);
        $updatedAd = $stmt->fetch(PDO::FETCH_ASSOC);

        echo json_encode([
            'success' => true,
            'message' => 'تم التحديث بنجاح',
            'data' => [
                'ad_id' => $adId,
                'field' => $field,
                'value' => $value,
                'updated_ad' => $updatedAd
            ]
        ]);
    } else {
        echo json_encode(['success' => false, 'message' => 'فشل في تحديث البيانات']);
    }

} catch (PDOException $e) {
    echo json_encode([
        'success' => false,
        'message' => 'حدث خطأ في قاعدة البيانات: ' . $e->getMessage()
    ]);
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'حدث خطأ: ' . $e->getMessage()
    ]);
}
?>
