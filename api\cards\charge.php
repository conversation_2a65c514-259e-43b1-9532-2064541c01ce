<?php
/**
 * API لشحن الكريديت كارد والفيزا والفوري
 */

// إعداد الاستجابة JSON
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// التحقق من طريقة الطلب
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'طريقة الطلب غير مسموحة']);
    exit;
}

// تضمين ملفات الاتصال بقاعدة البيانات
require_once '../../config/database.php';

try {
    // إنشاء اتصال بقاعدة البيانات
    $database = new Database();
    $db = $database->getConnection();

    // قراءة البيانات المرسلة
    $input = json_decode(file_get_contents('php://input'), true);

    // التحقق من صحة البيانات
    if (!isset($input['card_type']) || !isset($input['amount']) || !isset($input['source'])) {
        echo json_encode(['success' => false, 'message' => 'بيانات غير مكتملة']);
        exit;
    }

    $cardType = $input['card_type'];
    $amount = floatval($input['amount']);
    $source = $input['source'];
    $notes = $input['notes'] ?? '';

    // التحقق من صحة المبلغ
    if ($amount <= 0) {
        echo json_encode(['success' => false, 'message' => 'مبلغ الشحن يجب أن يكون أكبر من صفر']);
        exit;
    }

    // تحديد الجدول والحقول حسب نوع البطاقة
    $targetTable = '';
    $targetIdField = '';
    $targetBalanceField = '';
    $targetId = 0;

    switch ($cardType) {
        case 'credit_card':
            if (!isset($input['credit_card_id'])) {
                echo json_encode(['success' => false, 'message' => 'يرجى اختيار كريديت كارد']);
                exit;
            }
            $targetTable = 'credit_cards';
            $targetIdField = 'credit_card_id';
            $targetBalanceField = 'balance';
            $targetId = intval($input['credit_card_id']);
            break;

        case 'visa':
            if (!isset($input['visa_id'])) {
                echo json_encode(['success' => false, 'message' => 'يرجى اختيار فيزا']);
                exit;
            }
            $targetTable = 'visa_cards';
            $targetIdField = 'visa_id';
            $targetBalanceField = 'base_balance';
            $targetId = intval($input['visa_id']);
            break;

        case 'fawry':
            if (!isset($input['fawry_id'])) {
                echo json_encode(['success' => false, 'message' => 'يرجى اختيار حساب فوري']);
                exit;
            }
            $targetTable = 'fawry_accounts';
            $targetIdField = 'fawry_id';
            $targetBalanceField = 'balance';
            $targetId = intval($input['fawry_id']);
            break;

        default:
            echo json_encode(['success' => false, 'message' => 'نوع البطاقة غير صحيح']);
            exit;
    }

    // التحقق من وجود البطاقة/الحساب
    $stmt = $db->prepare("SELECT * FROM $targetTable WHERE id = ?");
    $stmt->execute([$targetId]);
    $targetCard = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$targetCard) {
        echo json_encode(['success' => false, 'message' => 'البطاقة/الحساب غير موجود']);
        exit;
    }

    // بدء المعاملة
    $db->beginTransaction();

    try {
        $sourceId = null;
        $sourceTable = '';
        $sourceBalanceField = '';

        // التحقق من مصدر الشحن وخصم المبلغ إذا لزم الأمر
        if ($source === 'fawry' && isset($input['fawry_id']) && $input['fawry_id']) {
            $sourceId = intval($input['fawry_id']);
            $sourceTable = 'fawry_accounts';
            $sourceBalanceField = 'balance';

            // التحقق من رصيد المصدر
            $stmt = $db->prepare("SELECT $sourceBalanceField FROM $sourceTable WHERE id = ?");
            $stmt->execute([$sourceId]);
            $sourceData = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$sourceData) {
                throw new Exception('مصدر الشحن غير موجود');
            }

            $sourceBalance = floatval($sourceData[$sourceBalanceField]);
            if ($amount > $sourceBalance) {
                throw new Exception('الرصيد المتاح في المصدر غير كافي لهذه العملية');
            }

            // خصم المبلغ من المصدر
            $stmt = $db->prepare("UPDATE $sourceTable SET $sourceBalanceField = $sourceBalanceField - ? WHERE id = ?");
            $stmt->execute([$amount, $sourceId]);
        }

        // إضافة المبلغ إلى البطاقة/الحساب المستهدف
        $stmt = $db->prepare("UPDATE $targetTable SET $targetBalanceField = $targetBalanceField + ? WHERE id = ?");
        $stmt->execute([$amount, $targetId]);

        // تسجيل عملية الشحن في جدول المعاملات
        $stmt = $db->prepare("
            INSERT INTO card_transactions 
            (card_type, card_id, transaction_type, amount, source_type, source_id, notes, created_at) 
            VALUES (?, ?, 'charge', ?, ?, ?, ?, NOW())
        ");
        $stmt->execute([
            $cardType,
            $targetId,
            $amount,
            $source,
            $sourceId,
            $notes
        ]);

        // تأكيد المعاملة
        $db->commit();

        // إرسال استجابة النجاح
        echo json_encode([
            'success' => true,
            'message' => 'تم الشحن بنجاح',
            'data' => [
                'card_type' => $cardType,
                'card_id' => $targetId,
                'amount' => $amount,
                'source' => $source,
                'new_balance' => $targetCard[$targetBalanceField] + $amount
            ]
        ]);

    } catch (Exception $e) {
        // إلغاء المعاملة في حالة الخطأ
        $db->rollBack();
        throw $e;
    }

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
} catch (PDOException $e) {
    echo json_encode([
        'success' => false,
        'message' => 'حدث خطأ في قاعدة البيانات: ' . $e->getMessage()
    ]);
}
?>
