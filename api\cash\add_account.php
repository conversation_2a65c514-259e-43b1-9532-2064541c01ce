<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// التعامل مع طلبات OPTIONS
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// تضمين ملف الاتصال بقاعدة البيانات
require_once '../../config/database.php';

try {
    // إنشاء اتصال بقاعدة البيانات
    $database = new Database();
    $db = $database->getConnection();
    
    if (!$db) {
        throw new Exception('فشل الاتصال بقاعدة البيانات');
    }

    // التحقق من طريقة الطلب
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('طريقة الطلب غير مدعومة');
    }

    // قراءة البيانات المرسلة
    $input = file_get_contents('php://input');
    $data = json_decode($input, true);

    if (!$data) {
        throw new Exception('بيانات غير صحيحة');
    }

    // التحقق من وجود الحقول المطلوبة
    if (!isset($data['account_name']) || empty(trim($data['account_name']))) {
        throw new Exception('اسم الحساب مطلوب');
    }

    $accountName = trim($data['account_name']);
    $balance = isset($data['balance']) ? floatval($data['balance']) : 0.00;

    // التحقق من عدم وجود حساب بنفس الاسم
    $stmt = $db->prepare("SELECT COUNT(*) FROM cash_accounts WHERE account_name = ?");
    $stmt->execute([$accountName]);
    $exists = $stmt->fetchColumn();

    if ($exists > 0) {
        throw new Exception('يوجد حساب بهذا الاسم بالفعل');
    }

    // إدراج الحساب الجديد
    $stmt = $db->prepare("
        INSERT INTO cash_accounts (account_name, balance, account_type) 
        VALUES (?, ?, 'personal')
    ");
    $stmt->execute([$accountName, $balance]);

    $newAccountId = $db->lastInsertId();

    // إرسال استجابة نجح
    echo json_encode([
        'success' => true,
        'message' => 'تم إضافة الحساب بنجاح',
        'account_id' => $newAccountId,
        'account_name' => $accountName,
        'balance' => $balance
    ], JSON_UNESCAPED_UNICODE);

} catch (Exception $e) {
    // إرسال استجابة خطأ
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}
?>
