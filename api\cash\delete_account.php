<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// التعامل مع طلبات OPTIONS
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// تضمين ملف الاتصال بقاعدة البيانات
require_once '../../config/database.php';

try {
    // إنشاء اتصال بقاعدة البيانات
    $database = new Database();
    $db = $database->getConnection();
    
    if (!$db) {
        throw new Exception('فشل الاتصال بقاعدة البيانات');
    }

    // التحقق من طريقة الطلب
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('طريقة الطلب غير مدعومة');
    }

    // قراءة البيانات المرسلة
    $input = file_get_contents('php://input');
    $data = json_decode($input, true);

    if (!$data || !isset($data['id'])) {
        throw new Exception('معرف الحساب مطلوب');
    }

    $accountId = intval($data['id']);

    if ($accountId <= 0) {
        throw new Exception('معرف الحساب غير صحيح');
    }

    // التحقق من وجود الحساب
    $stmt = $db->prepare("SELECT account_name FROM cash_accounts WHERE id = ?");
    $stmt->execute([$accountId]);
    $account = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$account) {
        throw new Exception('الحساب غير موجود');
    }

    // حذف الحساب
    $stmt = $db->prepare("DELETE FROM cash_accounts WHERE id = ?");
    $stmt->execute([$accountId]);

    // إرسال استجابة نجح
    echo json_encode([
        'success' => true,
        'message' => 'تم حذف الحساب بنجاح',
        'deleted_account' => $account['account_name']
    ], JSON_UNESCAPED_UNICODE);

} catch (Exception $e) {
    // إرسال استجابة خطأ
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}
?>
