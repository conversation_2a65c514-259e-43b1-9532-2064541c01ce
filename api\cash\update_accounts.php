<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// التعامل مع طلبات OPTIONS
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// تضمين ملف الاتصال بقاعدة البيانات
require_once '../../config/database.php';

try {
    // إنشاء اتصال بقاعدة البيانات
    $database = new Database();
    $db = $database->getConnection();
    
    if (!$db) {
        throw new Exception('فشل الاتصال بقاعدة البيانات');
    }

    // التحقق من طريقة الطلب
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('طريقة الطلب غير مدعومة');
    }

    // قراءة البيانات المرسلة
    $input = file_get_contents('php://input');
    $data = json_decode($input, true);

    if (!$data || !isset($data['accounts']) || !is_array($data['accounts'])) {
        throw new Exception('بيانات غير صحيحة');
    }

    $accounts = $data['accounts'];
    $updatedCount = 0;

    // بدء المعاملة
    $db->beginTransaction();

    try {
        // تحديث كل حساب
        $stmt = $db->prepare("UPDATE cash_accounts SET balance = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?");
        
        foreach ($accounts as $account) {
            if (!isset($account['id']) || !isset($account['balance'])) {
                continue;
            }
            
            $accountId = intval($account['id']);
            $balance = floatval($account['balance']);
            
            $stmt->execute([$balance, $accountId]);
            $updatedCount++;
        }

        // تأكيد المعاملة
        $db->commit();

        // إرسال استجابة نجح
        echo json_encode([
            'success' => true,
            'message' => 'تم تحديث الحسابات بنجاح',
            'updated_count' => $updatedCount
        ], JSON_UNESCAPED_UNICODE);

    } catch (Exception $e) {
        // إلغاء المعاملة في حالة الخطأ
        $db->rollback();
        throw $e;
    }

} catch (Exception $e) {
    // إرسال استجابة خطأ
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}
?>
