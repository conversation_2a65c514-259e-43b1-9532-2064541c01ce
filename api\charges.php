<?php
// Include configuration
require_once '../config/config.php';

// Include database connection
require_once '../includes/db.php';

// Include helper functions
require_once '../includes/functions.php';

// Include authentication functions
require_once '../includes/auth.php';

// Check if user is logged in
if (!isLoggedIn()) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'يجب تسجيل الدخول للوصول إلى هذه الخدمة']);
    exit;
}

// Set header to JSON
header('Content-Type: application/json');

// Handle different actions
$action = $_POST['action'] ?? $_GET['action'] ?? '';

switch ($action) {
    case 'getAll':
        // Get all charges
        getAllCharges();
        break;
    case 'getByAccount':
        // Get charges by account ID
        $accountId = $_GET['accountId'] ?? 0;
        getChargesByAccount($accountId);
        break;
    case 'addCharge':
        // Add new charge
        addCharge();
        break;
    case 'updateStatus':
        // Update charge status
        updateChargeStatus();
        break;
    default:
        // Invalid action
        echo json_encode(['success' => false, 'message' => 'إجراء غير صالح']);
        break;
}

/**
 * Get all charges
 */
function getAllCharges() {
    global $db;

    try {
        // Check if the table exists
        $tableExists = false;
        $tables = $db->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);

        if (in_array('fawry_charges', $tables)) {
            $tableExists = true;
        }

        // Create table if it doesn't exist
        if (!$tableExists) {
            createChargesTable();
        }

        // Get all charges with account names
        $stmt = $db->prepare("
            SELECT fc.*, aa.name as account_name
            FROM fawry_charges fc
            LEFT JOIN ad_accounts aa ON fc.account_id = aa.id
            ORDER BY fc.date DESC
        ");
        $stmt->execute();
        $charges = $stmt->fetchAll(PDO::FETCH_ASSOC);

        echo json_encode(['success' => true, 'charges' => $charges]);
    } catch (PDOException $e) {
        echo json_encode(['success' => false, 'message' => 'خطأ في قاعدة البيانات: ' . $e->getMessage()]);
    }
}

/**
 * Get charges by account ID
 *
 * @param int $accountId Account ID
 */
function getChargesByAccount($accountId) {
    global $db;

    if (!$accountId) {
        echo json_encode(['success' => false, 'message' => 'معرف الحساب مطلوب']);
        return;
    }

    try {
        // Check if the table exists
        $tableExists = false;
        $tables = $db->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);

        if (in_array('fawry_charges', $tables)) {
            $tableExists = true;
        }

        // Create table if it doesn't exist
        if (!$tableExists) {
            createChargesTable();
        }

        // Get charges for the specified account
        $stmt = $db->prepare("
            SELECT fc.*, aa.name as account_name
            FROM fawry_charges fc
            LEFT JOIN ad_accounts aa ON fc.account_id = aa.id
            WHERE fc.account_id = :account_id
            ORDER BY fc.date DESC
        ");
        $stmt->bindParam(':account_id', $accountId, PDO::PARAM_INT);
        $stmt->execute();
        $charges = $stmt->fetchAll(PDO::FETCH_ASSOC);

        echo json_encode(['success' => true, 'charges' => $charges]);
    } catch (PDOException $e) {
        echo json_encode(['success' => false, 'message' => 'خطأ في قاعدة البيانات: ' . $e->getMessage()]);
    }
}

/**
 * Add new charge
 */
function addCharge() {
    global $db;

    // Get form data
    $accountId = $_POST['chargeAccount'] ?? 0;
    $amount = $_POST['chargeAmount'] ?? 0;
    $code = $_POST['chargeCode'] ?? '';
    $status = $_POST['chargeStatus'] ?? 'لم يتم';
    $date = date('Y-m-d'); // Current date

    // Validate data
    if (!$accountId) {
        echo json_encode(['success' => false, 'message' => 'يجب اختيار الحساب']);
        return;
    }

    if (!$amount || $amount <= 0) {
        echo json_encode(['success' => false, 'message' => 'يجب إدخال مبلغ صحيح']);
        return;
    }

    try {
        // Start transaction
        $db->beginTransaction();

        // Check if the table exists
        $tableExists = false;
        $tables = $db->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);

        if (in_array('fawry_charges', $tables)) {
            $tableExists = true;
        }

        // Create table if it doesn't exist
        if (!$tableExists) {
            createChargesTable();
        }

        // Insert new charge
        $stmt = $db->prepare("
            INSERT INTO fawry_charges (account_id, amount, code, date, status)
            VALUES (:account_id, :amount, :code, :date, :status)
        ");
        $stmt->bindParam(':account_id', $accountId, PDO::PARAM_INT);
        $stmt->bindParam(':amount', $amount, PDO::PARAM_STR);
        $stmt->bindParam(':code', $code, PDO::PARAM_STR);
        $stmt->bindParam(':date', $date, PDO::PARAM_STR);
        $stmt->bindParam(':status', $status, PDO::PARAM_STR);
        $stmt->execute();

        // Get the inserted charge ID
        $chargeId = $db->lastInsertId();

        // Update account balance if status is "تم الشحن"
        if ($status === 'تم الشحن') {
            // Get current account balance
            $stmtAccount = $db->prepare("SELECT balance FROM ad_accounts WHERE id = :account_id");
            $stmtAccount->bindParam(':account_id', $accountId, PDO::PARAM_INT);
            $stmtAccount->execute();
            $currentBalance = $stmtAccount->fetchColumn();

            // Calculate new balance
            $newBalance = $currentBalance + $amount;

            // Update account balance
            $stmtUpdate = $db->prepare("UPDATE ad_accounts SET balance = :balance WHERE id = :account_id");
            $stmtUpdate->bindParam(':balance', $newBalance, PDO::PARAM_STR);
            $stmtUpdate->bindParam(':account_id', $accountId, PDO::PARAM_INT);
            $stmtUpdate->execute();
        }

        // Commit transaction
        $db->commit();

        echo json_encode(['success' => true, 'message' => 'تمت إضافة الشحنة بنجاح', 'chargeId' => $chargeId]);
    } catch (PDOException $e) {
        // Rollback transaction on error
        $db->rollBack();
        echo json_encode(['success' => false, 'message' => 'خطأ في قاعدة البيانات: ' . $e->getMessage()]);
    }
}

/**
 * Update charge status
 */
function updateChargeStatus() {
    global $db;

    // Get form data
    $chargeId = $_POST['id'] ?? 0;
    $status = $_POST['status'] ?? '';

    // Validate data
    if (!$chargeId) {
        echo json_encode(['success' => false, 'message' => 'معرف الشحنة مطلوب']);
        return;
    }

    if (!in_array($status, ['لم يتم', 'في انتظار الشحن', 'تم الشحن'])) {
        echo json_encode(['success' => false, 'message' => 'حالة غير صالحة']);
        return;
    }

    try {
        // Start transaction
        $db->beginTransaction();

        // Get charge details
        $stmtCharge = $db->prepare("
            SELECT account_id, amount, status as current_status
            FROM fawry_charges
            WHERE id = :id
        ");
        $stmtCharge->bindParam(':id', $chargeId, PDO::PARAM_INT);
        $stmtCharge->execute();
        $charge = $stmtCharge->fetch(PDO::FETCH_ASSOC);

        if (!$charge) {
            echo json_encode(['success' => false, 'message' => 'الشحنة غير موجودة']);
            return;
        }

        // Update charge status
        $stmt = $db->prepare("
            UPDATE fawry_charges
            SET status = :status
            WHERE id = :id
        ");
        $stmt->bindParam(':id', $chargeId, PDO::PARAM_INT);
        $stmt->bindParam(':status', $status, PDO::PARAM_STR);
        $stmt->execute();

        // Update account balance if status changed to or from "تم الشحن"
        if ($charge['current_status'] !== $status) {
            $accountId = $charge['account_id'];
            $amount = $charge['amount'];

            // Get current account balance
            $stmtAccount = $db->prepare("SELECT balance FROM ad_accounts WHERE id = :account_id");
            $stmtAccount->bindParam(':account_id', $accountId, PDO::PARAM_INT);
            $stmtAccount->execute();
            $currentBalance = $stmtAccount->fetchColumn();

            // Calculate new balance
            $newBalance = $currentBalance;

            // If new status is "تم الشحن", add amount to balance
            if ($status === 'تم الشحن') {
                $newBalance += $amount;
            }

            // If old status was "تم الشحن", subtract amount from balance
            if ($charge['current_status'] === 'تم الشحن') {
                $newBalance -= $amount;
            }

            // Update account balance
            $stmtUpdate = $db->prepare("UPDATE ad_accounts SET balance = :balance WHERE id = :account_id");
            $stmtUpdate->bindParam(':balance', $newBalance, PDO::PARAM_STR);
            $stmtUpdate->bindParam(':account_id', $accountId, PDO::PARAM_INT);
            $stmtUpdate->execute();
        }

        // Commit transaction
        $db->commit();

        echo json_encode(['success' => true, 'message' => 'تم تحديث حالة الشحنة بنجاح']);
    } catch (PDOException $e) {
        // Rollback transaction on error
        $db->rollBack();
        echo json_encode(['success' => false, 'message' => 'خطأ في قاعدة البيانات: ' . $e->getMessage()]);
    }
}

/**
 * Create charges table
 */
function createChargesTable() {
    global $db;

    $db->exec("CREATE TABLE fawry_charges (
        id INT AUTO_INCREMENT PRIMARY KEY,
        account_id INT NOT NULL,
        amount DECIMAL(10,3) NOT NULL,
        code VARCHAR(50),
        date DATE NOT NULL,
        status VARCHAR(50) NOT NULL DEFAULT 'لم يتم',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (account_id) REFERENCES ad_accounts(id) ON DELETE CASCADE
    )");

    // Add sample data
    $db->exec("INSERT INTO fawry_charges (account_id, amount, code, date, status) VALUES
        (1, 2.000, '3076', '2024-08-25', 'لم يتم'),
        (2, 2.000, '3076', '2024-08-25', 'في انتظار الشحن'),
        (3, 2.000, '3076', '2024-08-25', 'تم الشحن'),
        (1, 2.000, '3076', '2024-08-24', 'لم يتم'),
        (2, 2.000, '3076', '2024-08-24', 'في انتظار الشحن'),
        (3, 2.000, '3076', '2024-08-24', 'تم الشحن')
    ");
}
