<?php
// تضمين ملف الاتصال بقاعدة البيانات
require_once '../includes/db.php';

// تعيين نوع المحتوى
header('Content-Type: application/json');

// التحقق من طريقة الطلب
$method = $_SERVER['REQUEST_METHOD'];

try {
    switch ($method) {
        case 'GET':
            handleGetRequest();
            break;
        case 'POST':
            handlePostRequest();
            break;
        case 'PUT':
            handlePutRequest();
            break;
        case 'DELETE':
            handleDeleteRequest();
            break;
        default:
            http_response_code(405);
            echo json_encode(['success' => false, 'message' => 'طريقة الطلب غير مدعومة']);
            break;
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'خطأ في الخادم: ' . $e->getMessage()]);
}

/**
 * التعامل مع طلبات GET - جلب فئات العملاء
 */
function handleGetRequest() {
    global $db;
    
    try {
        $query = "SELECT * FROM client_types WHERE is_active = 1 ORDER BY sort_order ASC, name ASC";
        $stmt = $db->prepare($query);
        $stmt->execute();
        $clientTypes = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo json_encode([
            'success' => true,
            'client_types' => $clientTypes
        ]);
    } catch (PDOException $e) {
        throw new Exception('خطأ في جلب فئات العملاء: ' . $e->getMessage());
    }
}

/**
 * التعامل مع طلبات POST - إضافة فئة جديدة
 */
function handlePostRequest() {
    global $db;
    
    // قراءة البيانات من الطلب
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input || !isset($input['name']) || !isset($input['display_name'])) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'بيانات غير صالحة']);
        return;
    }
    
    $name = trim($input['name']);
    $displayName = trim($input['display_name']);
    $description = isset($input['description']) ? trim($input['description']) : '';
    $color = isset($input['color']) ? trim($input['color']) : '#4a56e2';
    $sortOrder = isset($input['sort_order']) ? (int)$input['sort_order'] : 0;
    
    if (empty($name) || empty($displayName)) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'اسم الفئة واسم العرض مطلوبان']);
        return;
    }
    
    try {
        // التحقق من عدم وجود فئة بنفس الاسم
        $checkQuery = "SELECT id FROM client_types WHERE name = :name";
        $checkStmt = $db->prepare($checkQuery);
        $checkStmt->bindParam(':name', $name);
        $checkStmt->execute();
        
        if ($checkStmt->rowCount() > 0) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'فئة بهذا الاسم موجودة بالفعل']);
            return;
        }
        
        // إدراج الفئة الجديدة
        $insertQuery = "INSERT INTO client_types (name, display_name, description, color, sort_order) 
                       VALUES (:name, :display_name, :description, :color, :sort_order)";
        $insertStmt = $db->prepare($insertQuery);
        $insertStmt->bindParam(':name', $name);
        $insertStmt->bindParam(':display_name', $displayName);
        $insertStmt->bindParam(':description', $description);
        $insertStmt->bindParam(':color', $color);
        $insertStmt->bindParam(':sort_order', $sortOrder);
        $insertStmt->execute();
        
        $newId = $db->lastInsertId();
        
        echo json_encode([
            'success' => true,
            'message' => 'تم إضافة الفئة بنجاح',
            'client_type_id' => $newId
        ]);
    } catch (PDOException $e) {
        throw new Exception('خطأ في إضافة الفئة: ' . $e->getMessage());
    }
}

/**
 * التعامل مع طلبات PUT - تحديث فئة موجودة
 */
function handlePutRequest() {
    global $db;
    
    // قراءة البيانات من الطلب
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input || !isset($input['id'])) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'معرف الفئة مطلوب']);
        return;
    }
    
    $id = (int)$input['id'];
    $name = isset($input['name']) ? trim($input['name']) : null;
    $displayName = isset($input['display_name']) ? trim($input['display_name']) : null;
    $description = isset($input['description']) ? trim($input['description']) : null;
    $color = isset($input['color']) ? trim($input['color']) : null;
    $sortOrder = isset($input['sort_order']) ? (int)$input['sort_order'] : null;
    $isActive = isset($input['is_active']) ? (int)$input['is_active'] : null;
    
    try {
        // التحقق من وجود الفئة
        $checkQuery = "SELECT id FROM client_types WHERE id = :id";
        $checkStmt = $db->prepare($checkQuery);
        $checkStmt->bindParam(':id', $id);
        $checkStmt->execute();
        
        if ($checkStmt->rowCount() === 0) {
            http_response_code(404);
            echo json_encode(['success' => false, 'message' => 'الفئة غير موجودة']);
            return;
        }
        
        // بناء استعلام التحديث
        $updateFields = [];
        $params = [':id' => $id];
        
        if ($name !== null) {
            $updateFields[] = "name = :name";
            $params[':name'] = $name;
        }
        if ($displayName !== null) {
            $updateFields[] = "display_name = :display_name";
            $params[':display_name'] = $displayName;
        }
        if ($description !== null) {
            $updateFields[] = "description = :description";
            $params[':description'] = $description;
        }
        if ($color !== null) {
            $updateFields[] = "color = :color";
            $params[':color'] = $color;
        }
        if ($sortOrder !== null) {
            $updateFields[] = "sort_order = :sort_order";
            $params[':sort_order'] = $sortOrder;
        }
        if ($isActive !== null) {
            $updateFields[] = "is_active = :is_active";
            $params[':is_active'] = $isActive;
        }
        
        if (empty($updateFields)) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'لا توجد بيانات للتحديث']);
            return;
        }
        
        $updateQuery = "UPDATE client_types SET " . implode(', ', $updateFields) . " WHERE id = :id";
        $updateStmt = $db->prepare($updateQuery);
        $updateStmt->execute($params);
        
        echo json_encode([
            'success' => true,
            'message' => 'تم تحديث الفئة بنجاح'
        ]);
    } catch (PDOException $e) {
        throw new Exception('خطأ في تحديث الفئة: ' . $e->getMessage());
    }
}

/**
 * التعامل مع طلبات DELETE - حذف فئة
 */
function handleDeleteRequest() {
    global $db;
    
    if (!isset($_GET['id'])) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'معرف الفئة مطلوب']);
        return;
    }
    
    $id = (int)$_GET['id'];
    
    try {
        // التحقق من عدم وجود عملاء مرتبطين بهذه الفئة
        $checkClientsQuery = "SELECT COUNT(*) as count FROM clients WHERE client_type_id = :id";
        $checkClientsStmt = $db->prepare($checkClientsQuery);
        $checkClientsStmt->bindParam(':id', $id);
        $checkClientsStmt->execute();
        $clientsCount = $checkClientsStmt->fetch(PDO::FETCH_ASSOC)['count'];
        
        if ($clientsCount > 0) {
            http_response_code(400);
            echo json_encode([
                'success' => false, 
                'message' => "لا يمكن حذف هذه الفئة لأنها مرتبطة بـ {$clientsCount} عميل. يرجى نقل العملاء إلى فئة أخرى أولاً."
            ]);
            return;
        }
        
        // حذف الفئة
        $deleteQuery = "DELETE FROM client_types WHERE id = :id";
        $deleteStmt = $db->prepare($deleteQuery);
        $deleteStmt->bindParam(':id', $id);
        $deleteStmt->execute();
        
        if ($deleteStmt->rowCount() === 0) {
            http_response_code(404);
            echo json_encode(['success' => false, 'message' => 'الفئة غير موجودة']);
            return;
        }
        
        echo json_encode([
            'success' => true,
            'message' => 'تم حذف الفئة بنجاح'
        ]);
    } catch (PDOException $e) {
        throw new Exception('خطأ في حذف الفئة: ' . $e->getMessage());
    }
}
?>
