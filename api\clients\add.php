<?php
/**
 * API لإضافة عميل جديد
 */

// Include configuration
require_once '../../config/config.php';

// Include database connection
require_once '../../includes/db.php';

// Include helper functions
require_once '../../includes/functions.php';

// Set content type to JSON
header('Content-Type: application/json');

// Check if request method is POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405); // Method Not Allowed
    echo json_encode(['success' => false, 'message' => 'طريقة الطلب غير مسموح بها']);
    exit;
}

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    http_response_code(401); // Unauthorized
    echo json_encode(['success' => false, 'message' => 'يجب تسجيل الدخول للوصول إلى هذه الخدمة']);
    exit;
}

try {
    // Validate input
    if (!isset($_POST['name']) || !isset($_POST['previous_debt']) || !isset($_POST['payment_date'])) {
        throw new Exception('البيانات المطلوبة غير مكتملة');
    }
    
    $name = trim($_POST['name']);
    $previous_debt = (float)$_POST['previous_debt'];
    $payment_date = $_POST['payment_date'];
    
    if (empty($name)) {
        throw new Exception('اسم العميل مطلوب');
    }
    
    if ($previous_debt < 0) {
        throw new Exception('المديونية السابقة يجب أن تكون قيمة موجبة');
    }
    
    // Validate date format
    if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $payment_date)) {
        throw new Exception('تنسيق التاريخ غير صحيح (يجب أن يكون YYYY-MM-DD)');
    }
    
    // Check if clients table exists
    $tableExists = false;
    $tables = $db->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
    
    if (in_array('clients', $tables)) {
        $tableExists = true;
    }
    
    // Create clients table if it doesn't exist
    if (!$tableExists) {
        $db->exec("CREATE TABLE clients (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            page_management DECIMAL(10,2) DEFAULT 0,
            ads DECIMAL(10,2) DEFAULT 0,
            payments DECIMAL(10,2) DEFAULT 0,
            previous_balance DECIMAL(10,2) DEFAULT 0,
            end_month_balance DECIMAL(10,2) DEFAULT 0,
            payment_date DATE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )");
    }
    
    // Insert into database
    $query = "INSERT INTO clients (name, page_management, ads, payments, previous_balance, end_month_balance, payment_date) 
              VALUES (:name, 0, 0, 0, :previous_debt, :previous_debt, :payment_date)";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':name', $name);
    $stmt->bindParam(':previous_debt', $previous_debt);
    $stmt->bindParam(':payment_date', $payment_date);
    $stmt->execute();
    
    // Get the inserted ID
    $client_id = $db->lastInsertId();
    
    // Return success response
    echo json_encode([
        'success' => true,
        'message' => 'تمت إضافة العميل بنجاح',
        'client' => [
            'id' => $client_id,
            'name' => $name,
            'page_management' => 0,
            'ads' => 0,
            'payments' => 0,
            'previous_balance' => $previous_debt,
            'end_month_balance' => $previous_debt,
            'payment_date' => $payment_date
        ]
    ]);
    
} catch (Exception $e) {
    http_response_code(400); // Bad Request
    echo json_encode(['success' => false, 'message' => $e->getMessage()]);
}
