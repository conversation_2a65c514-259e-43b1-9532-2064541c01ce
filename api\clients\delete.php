<?php
/**
 * API لحذف عميل
 */

// Include configuration
require_once '../../config/config.php';

// Include database connection
require_once '../../includes/db.php';

// Include helper functions
require_once '../../includes/functions.php';

// Set content type to JSON
header('Content-Type: application/json');

// Check if request method is POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405); // Method Not Allowed
    echo json_encode(['success' => false, 'message' => 'طريقة الطلب غير مسموح بها']);
    exit;
}

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    http_response_code(401); // Unauthorized
    echo json_encode(['success' => false, 'message' => 'يجب تسجيل الدخول للوصول إلى هذه الخدمة']);
    exit;
}

try {
    // Validate input
    if (!isset($_POST['id'])) {
        throw new Exception('معرف العميل مطلوب');
    }
    
    $client_id = (int)$_POST['id'];
    
    if ($client_id <= 0) {
        throw new Exception('معرف العميل غير صالح');
    }
    
    // Check if clients table exists
    $tableExists = false;
    $tables = $db->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
    
    if (!in_array('clients', $tables)) {
        throw new Exception('جدول العملاء غير موجود');
    }
    
    // Delete from database
    $query = "DELETE FROM clients WHERE id = :id";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':id', $client_id);
    $stmt->execute();
    
    if ($stmt->rowCount() === 0) {
        throw new Exception('العميل غير موجود');
    }
    
    // Return success response
    echo json_encode([
        'success' => true,
        'message' => 'تم حذف العميل بنجاح'
    ]);
    
} catch (Exception $e) {
    http_response_code(400); // Bad Request
    echo json_encode(['success' => false, 'message' => $e->getMessage()]);
}
