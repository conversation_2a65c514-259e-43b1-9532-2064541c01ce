<?php
/**
 * API لجلب العملاء
 */

// Include configuration
require_once '../../config/config.php';

// Include database connection
require_once '../../includes/db.php';

// Include helper functions
require_once '../../includes/functions.php';

// Set content type to JSON
header('Content-Type: application/json');

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    http_response_code(401); // Unauthorized
    echo json_encode(['success' => false, 'message' => 'يجب تسجيل الدخول للوصول إلى هذه الخدمة']);
    exit;
}

try {
    // Check if clients table exists
    $tableExists = false;
    $tables = $db->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
    
    if (in_array('clients', $tables)) {
        $tableExists = true;
    }
    
    // Create clients table if it doesn't exist
    if (!$tableExists) {
        $db->exec("CREATE TABLE clients (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            page_management DECIMAL(10,2) DEFAULT 0,
            ads DECIMAL(10,2) DEFAULT 0,
            payments DECIMAL(10,2) DEFAULT 0,
            previous_balance DECIMAL(10,2) DEFAULT 0,
            end_month_balance DECIMAL(10,2) DEFAULT 0,
            payment_date DATE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )");
        
        // Insert sample data
        $db->exec("INSERT INTO clients (name, page_management, ads, payments, previous_balance, end_month_balance, payment_date) VALUES
            ('Dr M7med El4af3y', 2500, 6900, 21700, 6900, 43405, '2023-12-15'),
            ('Nihal Anbar', 3200, 5500, 18900, 7800, 35400, '2023-12-20')
        ");
    }
    
    // Get clients from database
    $query = "SELECT * FROM clients ORDER BY id DESC";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $clients = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Return success response
    echo json_encode([
        'success' => true,
        'clients' => $clients
    ]);
    
} catch (Exception $e) {
    http_response_code(500); // Internal Server Error
    echo json_encode(['success' => false, 'message' => $e->getMessage()]);
}
