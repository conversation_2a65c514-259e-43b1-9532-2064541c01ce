<?php
/**
 * API لجلب تفاصيل العميل وإعلاناته
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// تضمين ملفات الاتصال بقاعدة البيانات
try {
    require_once '../../config/database.php';

    // إنشاء اتصال مباشر بقاعدة البيانات
    $database = new Database();
    $db = $database->getConnection();

    if (!$db) {
        throw new Exception('فشل في الاتصال بقاعدة البيانات');
    }
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'خطأ في تحميل ملفات قاعدة البيانات: ' . $e->getMessage(),
        'error' => true
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

try {
    // تسجيل المعاملات المستلمة للتشخيص
    error_log("API Debug - Received parameters: " . print_r($_GET, true));

    // التحقق من المعاملات المطلوبة
    if (!isset($_GET['client_name']) || !isset($_GET['card_id'])) {
        throw new Exception('معاملات مفقودة: client_name و card_id مطلوبان');
    }

    $clientName = trim($_GET['client_name']);
    $cardId = intval($_GET['card_id']);

    // تسجيل القيم بعد المعالجة
    error_log("API Debug - Processed values: clientName='$clientName', cardId=$cardId");

    // تنظيف اسم العميل من الأرقام في النهاية
    $clientName = preg_replace('/\s+\d+$/', '', $clientName);

    if (empty($clientName)) {
        throw new Exception("اسم العميل مطلوب: clientName='$clientName'");
    }

    // إذا كان cardId = 0، نبحث في جميع الحسابات الإعلانية
    if ($cardId <= 0) {
        error_log("API Debug - cardId is 0, searching all ad accounts for client: $clientName");
    }

    // الخطوة 1: جلب الحسابات الإعلانية
    if ($cardId > 0) {
        // البحث بمعرف البطاقة المحدد
        $stmt = $db->prepare("
            SELECT id FROM ad_accounts
            WHERE linked_account_type = 'credit_card'
            AND linked_account_id = :card_id
        ");
        $stmt->bindParam(':card_id', $cardId);
        $stmt->execute();
        $accountIds = $stmt->fetchAll(PDO::FETCH_COLUMN);
    } else {
        // البحث في جميع الحسابات الإعلانية
        $stmt = $db->prepare("SELECT id FROM ad_accounts");
        $stmt->execute();
        $accountIds = $stmt->fetchAll(PDO::FETCH_COLUMN);
    }

    if (empty($accountIds)) {
        echo json_encode([
            'success' => true,
            'ads' => [],
            'totals' => [
                'total_daily_spend' => 0,
                'total_remaining_spend' => 0,
                'total_payment_request' => 0,
                'total_due' => 0,
                'total_debt' => 0
            ],
            'message' => 'لا توجد حسابات إعلانية'
        ]);
        exit;
    }

    // الخطوة 2: جلب إعلانات العميل من الحسابات المرتبطة بالبطاقة
    $accountIdsStr = implode(',', $accountIds);

    $stmt = $db->prepare("
        SELECT a.*,
               aa.name as account_name,
               aa.name as client_name,
               a.date as start_date,
               DATE_ADD(a.date, INTERVAL a.days DAY) as end_date,
               a.type as ad_type,
               a.post as ad_post,
               a.cost as ad_cost,
               a.egyptian_cost as ad_spent,
               a.days as ad_days,
               a.status as ad_status,
               aa.name as ad_page
        FROM ads a
        JOIN ad_accounts aa ON a.ad_account_id = aa.id
        WHERE a.ad_account_id IN ($accountIdsStr)
        ORDER BY a.date DESC
    ");

    $stmt->execute();
    $allAds = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // فلترة الإعلانات حسب اسم العميل في PHP
    $ads = [];
    foreach ($allAds as $ad) {
        $adClientName = $ad['client_name'] ?? $ad['account_name'] ?? '';
        if (stripos($adClientName, $clientName) !== false) {
            $ads[] = $ad;
        }
    }

    // الخطوة 3: حساب الإجماليات
    $totals = [
        'total_daily_spend' => 0,
        'total_remaining_spend' => 0,
        'total_payment_request' => 0,
        'total_due' => 0,
        'total_debt' => 0
    ];

    foreach ($ads as &$ad) {
        $cost = floatval($ad['ad_cost']);
        $spent = floatval($ad['ad_spent']);
        $days = intval($ad['ad_days']);

        // حساب القيم لكل إعلان
        $dailySpend = $days > 0 ? $cost / $days : 0;
        $remainingSpend = $cost - $spent;

        // إضافة القيم المحسوبة للإعلان
        $ad['daily_spend'] = $dailySpend;
        $ad['remaining_spend'] = $remainingSpend;
        $ad['cost'] = $cost;
        $ad['spent'] = $spent;
        $ad['days'] = $days;
        $ad['post'] = $ad['ad_post'];
        $ad['type'] = $ad['ad_type'];
        $ad['date'] = $ad['start_date'];

        // تجميع الإجماليات
        $totals['total_daily_spend'] += $dailySpend;
        $totals['total_remaining_spend'] += $remainingSpend;
    }

    // إرسال النتيجة
    echo json_encode([
        'success' => true,
        'ads' => $ads,
        'totals' => $totals,
        'client_name' => $clientName,
        'card_id' => $cardId,
        'ads_count' => count($ads)
    ], JSON_UNESCAPED_UNICODE);

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage(),
        'error' => true
    ], JSON_UNESCAPED_UNICODE);
} catch (PDOException $e) {
    echo json_encode([
        'success' => false,
        'message' => 'خطأ في قاعدة البيانات: ' . $e->getMessage(),
        'error' => true
    ], JSON_UNESCAPED_UNICODE);
}
?>
