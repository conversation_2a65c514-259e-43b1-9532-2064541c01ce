<?php
/**
 * API للعملاء
 */

// تضمين ملفات الإعدادات
require_once '../../config/config.php';
require_once '../../includes/db.php';
require_once '../../includes/functions.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    header('Content-Type: application/json');
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'غير مصرح لك بالوصول']);
    exit;
}

// تحديد نوع الطلب
$action = isset($_GET['action']) ? $_GET['action'] : '';

// معالجة الطلبات
switch ($action) {
    case 'get':
        getClients();
        break;
    case 'add':
        addClient();
        break;
    case 'delete':
        deleteClient();
        break;
    default:
        // طلب غير صالح
        header('Content-Type: application/json');
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'طلب غير صالح']);
        break;
}

/**
 * جلب العملاء
 */
function getClients() {
    global $db;
    
    header('Content-Type: application/json');
    
    try {
        // جلب جميع العملاء
        $query = "SELECT * FROM clients ORDER BY id DESC";
        $stmt = $db->prepare($query);
        $stmt->execute();
        
        $clients = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $response = [
            'success' => true,
            'clients' => $clients
        ];
        
        echo json_encode($response);
    } catch (Exception $e) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
    }
}

/**
 * إضافة عميل جديد
 */
function addClient() {
    global $db;
    
    header('Content-Type: application/json');
    
    try {
        // التحقق من البيانات المطلوبة
        if (!isset($_POST['name']) || !isset($_POST['previous_debt']) || !isset($_POST['payment_day'])) {
            throw new Exception('البيانات غير مكتملة');
        }
        
        // تنظيف وتحقق من البيانات
        $name = trim($_POST['name']);
        $previous_debt = (float)$_POST['previous_debt'];
        $payment_day = (int)$_POST['payment_day'];
        
        if (empty($name)) {
            throw new Exception('اسم العميل مطلوب');
        }
        
        if ($previous_debt < 0) {
            throw new Exception('المديونية السابقة يجب أن تكون قيمة موجبة');
        }
        
        if ($payment_day < 1 || $payment_day > 31) {
            throw new Exception('يوم الدفع يجب أن يكون بين 1 و 31');
        }
        
        // إدراج العميل في قاعدة البيانات
        $query = "INSERT INTO clients (name, page_management, ads, payments, previous_balance, end_month_balance, payment_day) 
                  VALUES (:name, 0, 0, 0, :previous_debt, :previous_debt, :payment_day)";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':name', $name);
        $stmt->bindParam(':previous_debt', $previous_debt);
        $stmt->bindParam(':payment_day', $payment_day);
        $stmt->execute();
        
        // الحصول على معرف العميل المدرج
        $client_id = $db->lastInsertId();
        
        // إعداد الاستجابة
        $response = [
            'success' => true,
            'message' => 'تمت إضافة العميل بنجاح',
            'client' => [
                'id' => $client_id,
                'name' => $name,
                'page_management' => 0,
                'ads' => 0,
                'payments' => 0,
                'previous_balance' => $previous_debt,
                'end_month_balance' => $previous_debt,
                'payment_day' => $payment_day
            ]
        ];
        
        echo json_encode($response);
    } catch (Exception $e) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
    }
}

/**
 * حذف عميل
 */
function deleteClient() {
    global $db;
    
    header('Content-Type: application/json');
    
    try {
        // التحقق من وجود معرف العميل
        if (!isset($_POST['id'])) {
            throw new Exception('معرف العميل مطلوب');
        }
        
        $client_id = (int)$_POST['id'];
        
        // حذف العميل من قاعدة البيانات
        $query = "DELETE FROM clients WHERE id = :id";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':id', $client_id);
        $stmt->execute();
        
        if ($stmt->rowCount() === 0) {
            throw new Exception('العميل غير موجود');
        }
        
        $response = [
            'success' => true,
            'message' => 'تم حذف العميل بنجاح'
        ];
        
        echo json_encode($response);
    } catch (Exception $e) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
    }
}
