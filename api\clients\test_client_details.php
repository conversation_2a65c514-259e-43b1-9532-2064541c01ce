<?php
/**
 * API اختبار لجلب تفاصيل العميل
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// اختبار المعاملات
$clientName = isset($_GET['client_name']) ? trim($_GET['client_name']) : '';
$cardId = isset($_GET['card_id']) ? intval($_GET['card_id']) : 0;

// إرجاع بيانات تجريبية للاختبار
echo json_encode([
    'success' => true,
    'debug' => [
        'received_client_name' => $_GET['client_name'] ?? 'not set',
        'received_card_id' => $_GET['card_id'] ?? 'not set',
        'processed_client_name' => $clientName,
        'processed_card_id' => $cardId,
        'validation' => [
            'client_name_empty' => empty($clientName),
            'card_id_zero_or_less' => $cardId <= 0
        ]
    ],
    'ads' => [
        [
            'date' => '2024-01-15',
            'end_date' => '2024-01-25',
            'page' => 'منة فوزي',
            'post' => 'الطفل',
            'type' => 'تفاعل',
            'cost' => 1000.00,
            'spent' => 500.00,
            'days' => 10
        ],
        [
            'date' => '2024-01-20',
            'end_date' => '2024-01-30',
            'page' => 'سارة أحمد',
            'post' => 'المرأة',
            'type' => 'رسايل',
            'cost' => 800.00,
            'spent' => 300.00,
            'days' => 10
        ]
    ],
    'totals' => [
        'total_daily_spend' => 180.00,
        'total_remaining_spend' => 1000.00,
        'total_payment_request' => 0.00,
        'total_due' => 0.00,
        'total_debt' => 0.00
    ],
    'client_name' => $clientName,
    'card_id' => $cardId,
    'ads_count' => 2
], JSON_UNESCAPED_UNICODE);
?>
