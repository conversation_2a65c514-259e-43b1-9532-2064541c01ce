<?php
/**
 * API لإضافة معاملة كريديت كارد جديدة
 */

// تعيين نوع المحتوى
header('Content-Type: application/json; charset=UTF-8');

// السماح بالوصول من أي مصدر
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With');

// تضمين ملفات الاتصال بقاعدة البيانات
require_once '../../config/config.php';
require_once '../../includes/db.php';
require_once '../../includes/functions.php';
require_once '../../includes/auth.php';
require_once '../../includes/permissions.php';

// التحقق من طريقة الطلب
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405); // Method Not Allowed
    echo json_encode(['success' => false, 'message' => 'طريقة الطلب غير مسموح بها']);
    exit;
}

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    http_response_code(401); // Unauthorized
    echo json_encode(['success' => false, 'message' => 'يجب تسجيل الدخول']);
    exit;
}

// التحقق من البيانات المطلوبة
if (!isset($_POST['account_id']) || !isset($_POST['date']) || !isset($_POST['amount']) || !isset($_POST['days']) || !isset($_POST['description'])) {
    http_response_code(400); // Bad Request
    echo json_encode(['success' => false, 'message' => 'البيانات المطلوبة غير مكتملة']);
    exit;
}

// جمع البيانات من الطلب
$accountId = $_POST['account_id'];
$date = $_POST['date'];
$amount = floatval($_POST['amount']);
$days = intval($_POST['days']);
$description = $_POST['description'];
$post = isset($_POST['post']) ? $_POST['post'] : null;
$dailyAmount = isset($_POST['daily_amount']) ? floatval($_POST['daily_amount']) : ($amount / $days);

// التحقق من صحة البيانات
if ($amount <= 0) {
    http_response_code(400); // Bad Request
    echo json_encode(['success' => false, 'message' => 'المبلغ يجب أن يكون أكبر من صفر']);
    exit;
}

if ($days <= 0) {
    http_response_code(400); // Bad Request
    echo json_encode(['success' => false, 'message' => 'عدد الأيام يجب أن يكون أكبر من صفر']);
    exit;
}

try {
    // التحقق من وجود الجدول
    $tableExists = false;
    $tables = $db->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);

    if (in_array('credit_card_transactions', $tables)) {
        $tableExists = true;
    }

    // إنشاء الجدول إذا لم يكن موجودًا
    if (!$tableExists) {
        $db->exec("CREATE TABLE credit_card_transactions (
            id INT AUTO_INCREMENT PRIMARY KEY,
            account_id INT NOT NULL,
            date DATE NOT NULL,
            amount DECIMAL(10,2) NOT NULL,
            daily_amount DECIMAL(10,2) NOT NULL,
            days INT NOT NULL DEFAULT 21,
            description VARCHAR(255) NOT NULL,
            post VARCHAR(255) DEFAULT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (account_id) REFERENCES credit_card_accounts(id) ON DELETE CASCADE
        )");
    }

    // إعداد استعلام الإدراج
    $stmt = $db->prepare("
        INSERT INTO credit_card_transactions (account_id, date, amount, daily_amount, days, description, post)
        VALUES (:account_id, :date, :amount, :daily_amount, :days, :description, :post)
    ");

    // ربط البيانات
    $stmt->bindParam(':account_id', $accountId);
    $stmt->bindParam(':date', $date);
    $stmt->bindParam(':amount', $amount);
    $stmt->bindParam(':daily_amount', $dailyAmount);
    $stmt->bindParam(':days', $days);
    $stmt->bindParam(':description', $description);
    $stmt->bindParam(':post', $post);

    // تنفيذ الاستعلام
    if ($stmt->execute()) {
        // إرجاع استجابة نجاح
        http_response_code(201); // Created
        echo json_encode([
            'success' => true,
            'message' => 'تمت إضافة المعاملة بنجاح',
            'transaction_id' => $db->lastInsertId()
        ]);
    } else {
        // إرجاع استجابة فشل
        http_response_code(500); // Internal Server Error
        echo json_encode(['success' => false, 'message' => 'حدث خطأ أثناء إضافة المعاملة']);
    }
} catch (PDOException $e) {
    // إرجاع استجابة فشل مع رسالة الخطأ
    http_response_code(500); // Internal Server Error
    echo json_encode(['success' => false, 'message' => 'حدث خطأ في قاعدة البيانات: ' . $e->getMessage()]);
}
