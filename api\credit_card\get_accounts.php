<?php
/**
 * API لجلب حسابات كريديت كارد
 */

// تعيين نوع المحتوى
header('Content-Type: application/json; charset=UTF-8');

// السماح بالوصول من أي مصدر
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With');

// تضمين ملفات الاتصال بقاعدة البيانات
require_once '../../config/config.php';
require_once '../../includes/db.php';
require_once '../../includes/functions.php';
require_once '../../includes/auth.php';
require_once '../../includes/permissions.php';

// التحقق من طريقة الطلب
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405); // Method Not Allowed
    echo json_encode(['success' => false, 'message' => 'طريقة الطلب غير مسموح بها']);
    exit;
}

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    http_response_code(401); // Unauthorized
    echo json_encode(['success' => false, 'message' => 'يجب تسجيل الدخول']);
    exit;
}

try {
    // التحقق من وجود الجدول
    $tableExists = false;
    $tables = $db->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);

    if (in_array('credit_card_accounts', $tables)) {
        $tableExists = true;
    }

    // إنشاء الجدول إذا لم يكن موجودًا
    if (!$tableExists) {
        $db->exec("CREATE TABLE credit_card_accounts (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            status VARCHAR(50) NOT NULL DEFAULT 'نشط',
            balance DECIMAL(10,2) NOT NULL DEFAULT 0.00,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )");

        // إضافة بيانات تجريبية
        $db->exec("INSERT INTO credit_card_accounts (name, status, balance) VALUES
            ('هيثم خليفة', 'نشط', 445.00)
        ");
    }

    // جلب الحسابات
    $stmt = $db->prepare("SELECT * FROM credit_card_accounts ORDER BY name ASC");
    $stmt->execute();
    $accounts = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // جلب المعاملات لكل حساب
    foreach ($accounts as &$account) {
        $stmt = $db->prepare("
            SELECT * FROM credit_card_transactions 
            WHERE account_id = :account_id 
            ORDER BY date DESC
        ");
        $stmt->bindParam(':account_id', $account['id']);
        $stmt->execute();
        $transactions = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // حساب الإجماليات
        $totalAmount = 0;
        $totalDaily = 0;

        foreach ($transactions as $transaction) {
            $totalAmount += $transaction['amount'];
            $totalDaily += $transaction['daily_amount'];
        }

        $account['transactions'] = $transactions;
        $account['total_amount'] = $totalAmount;
        $account['total_daily'] = $totalDaily;
        $account['remaining_balance'] = $account['balance'] - $totalAmount;
    }

    // إرجاع استجابة نجاح
    http_response_code(200); // OK
    echo json_encode([
        'success' => true,
        'accounts' => $accounts
    ]);
} catch (PDOException $e) {
    // إرجاع استجابة فشل مع رسالة الخطأ
    http_response_code(500); // Internal Server Error
    echo json_encode(['success' => false, 'message' => 'حدث خطأ في قاعدة البيانات: ' . $e->getMessage()]);
}
