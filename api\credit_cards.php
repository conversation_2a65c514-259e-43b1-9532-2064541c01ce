<?php
// تضمين ملف الاتصال بقاعدة البيانات
require_once '../config/database.php';

// التحقق من الطلب
header('Content-Type: application/json');

// إنشاء اتصال بقاعدة البيانات
$database = new Database();
$db = $database->getConnection();

// التحقق من وجود اتصال بقاعدة البيانات
if (!isset($db)) {
    echo json_encode(['success' => false, 'message' => 'خطأ في الاتصال بقاعدة البيانات']);
    exit;
}

// التحقق من نوع الطلب
$action = isset($_POST['action']) ? $_POST['action'] : (isset($_GET['action']) ? $_GET['action'] : '');

switch ($action) {
    case 'getAll':
        // جلب جميع بطاقات الائتمان
        getCreditCards();
        break;
    case 'get':
        // جلب بطاقة ائتمان محددة
        $id = isset($_GET['id']) ? intval($_GET['id']) : 0;
        getCreditCard($id);
        break;
    case 'add':
        // إضافة بطاقة ائتمان جديدة
        addCreditCard();
        break;
    case 'update':
        // تحديث بطاقة ائتمان
        updateCreditCard();
        break;
    case 'delete':
        // حذف بطاقة ائتمان
        $id = isset($_POST['id']) ? intval($_POST['id']) : 0;
        deleteCreditCard($id);
        break;
    case 'addTransaction':
        // إضافة معاملة جديدة
        addTransaction();
        break;
    default:
        echo json_encode(['success' => false, 'message' => 'إجراء غير صالح']);
        break;
}

// دالة لجلب جميع بطاقات الائتمان
function getCreditCards() {
    global $db;
    
    try {
        $query = "SELECT * FROM credit_cards ORDER BY name";
        $stmt = $db->prepare($query);
        $stmt->execute();
        
        $creditCards = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo json_encode(['success' => true, 'data' => $creditCards]);
    } catch (PDOException $e) {
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
    }
}

// دالة لجلب بطاقة ائتمان محددة
function getCreditCard($id) {
    global $db;
    
    if ($id <= 0) {
        echo json_encode(['success' => false, 'message' => 'معرف غير صالح']);
        return;
    }
    
    try {
        $query = "SELECT * FROM credit_cards WHERE id = :id LIMIT 1";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':id', $id);
        $stmt->execute();
        
        $creditCard = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($creditCard) {
            echo json_encode(['success' => true, 'data' => $creditCard]);
        } else {
            echo json_encode(['success' => false, 'message' => 'بطاقة الائتمان غير موجودة']);
        }
    } catch (PDOException $e) {
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
    }
}

// دالة لإضافة بطاقة ائتمان جديدة
function addCreditCard() {
    global $db;
    
    // التحقق من البيانات المطلوبة
    $name = isset($_POST['name']) ? trim($_POST['name']) : '';
    $balance = isset($_POST['balance']) ? floatval($_POST['balance']) : 0;
    
    if (empty($name)) {
        echo json_encode(['success' => false, 'message' => 'اسم البطاقة مطلوب']);
        return;
    }
    
    try {
        $query = "INSERT INTO credit_cards (name, balance, daily_spend, remaining_balance, total_debt) 
                  VALUES (:name, :balance, 0, 0, 0)";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':name', $name);
        $stmt->bindParam(':balance', $balance);
        
        if ($stmt->execute()) {
            $id = $db->lastInsertId();
            echo json_encode(['success' => true, 'message' => 'تمت إضافة بطاقة الائتمان بنجاح', 'id' => $id]);
        } else {
            echo json_encode(['success' => false, 'message' => 'فشل في إضافة بطاقة الائتمان']);
        }
    } catch (PDOException $e) {
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
    }
}

// دالة لتحديث بطاقة ائتمان
function updateCreditCard() {
    global $db;
    
    // التحقق من البيانات المطلوبة
    $id = isset($_POST['id']) ? intval($_POST['id']) : 0;
    $name = isset($_POST['name']) ? trim($_POST['name']) : '';
    $balance = isset($_POST['balance']) ? floatval($_POST['balance']) : 0;
    
    if ($id <= 0) {
        echo json_encode(['success' => false, 'message' => 'معرف غير صالح']);
        return;
    }
    
    if (empty($name)) {
        echo json_encode(['success' => false, 'message' => 'اسم البطاقة مطلوب']);
        return;
    }
    
    try {
        $query = "UPDATE credit_cards SET name = :name, balance = :balance WHERE id = :id";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':id', $id);
        $stmt->bindParam(':name', $name);
        $stmt->bindParam(':balance', $balance);
        
        if ($stmt->execute()) {
            echo json_encode(['success' => true, 'message' => 'تم تحديث بطاقة الائتمان بنجاح']);
        } else {
            echo json_encode(['success' => false, 'message' => 'فشل في تحديث بطاقة الائتمان']);
        }
    } catch (PDOException $e) {
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
    }
}

// دالة لحذف بطاقة ائتمان
function deleteCreditCard($id) {
    global $db;
    
    if ($id <= 0) {
        echo json_encode(['success' => false, 'message' => 'معرف غير صالح']);
        return;
    }
    
    try {
        $query = "DELETE FROM credit_cards WHERE id = :id";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':id', $id);
        
        if ($stmt->execute()) {
            echo json_encode(['success' => true, 'message' => 'تم حذف بطاقة الائتمان بنجاح']);
        } else {
            echo json_encode(['success' => false, 'message' => 'فشل في حذف بطاقة الائتمان']);
        }
    } catch (PDOException $e) {
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
    }
}

// دالة لإضافة معاملة جديدة
function addTransaction() {
    global $db;
    
    // التحقق من البيانات المطلوبة
    $cardId = isset($_POST['cardId']) ? intval($_POST['cardId']) : 0;
    $page = isset($_POST['transactionPage']) ? trim($_POST['transactionPage']) : '';
    $post = isset($_POST['transactionPost']) ? trim($_POST['transactionPost']) : '';
    $amount = isset($_POST['transactionAmount']) ? floatval($_POST['transactionAmount']) : 0;
    $days = isset($_POST['transactionDays']) ? intval($_POST['transactionDays']) : 1;
    
    if ($cardId <= 0) {
        echo json_encode(['success' => false, 'message' => 'معرف البطاقة غير صالح']);
        return;
    }
    
    if (empty($page)) {
        echo json_encode(['success' => false, 'message' => 'اسم الصفحة مطلوب']);
        return;
    }
    
    if ($amount <= 0) {
        echo json_encode(['success' => false, 'message' => 'المبلغ يجب أن يكون أكبر من صفر']);
        return;
    }
    
    try {
        // إضافة المعاملة إلى جدول المعاملات
        $query = "INSERT INTO credit_card_transactions (card_id, page, post, amount, days, date) 
                  VALUES (:card_id, :page, :post, :amount, :days, NOW())";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':card_id', $cardId);
        $stmt->bindParam(':page', $page);
        $stmt->bindParam(':post', $post);
        $stmt->bindParam(':amount', $amount);
        $stmt->bindParam(':days', $days);
        
        if ($stmt->execute()) {
            // تحديث بيانات البطاقة
            $daily = $amount / $days;
            
            $query = "UPDATE credit_cards SET 
                      daily_spend = daily_spend + :daily,
                      remaining_balance = remaining_balance + :amount,
                      total_debt = total_debt + :amount
                      WHERE id = :card_id";
            $stmt = $db->prepare($query);
            $stmt->bindParam(':card_id', $cardId);
            $stmt->bindParam(':daily', $daily);
            $stmt->bindParam(':amount', $amount);
            
            if ($stmt->execute()) {
                echo json_encode(['success' => true, 'message' => 'تمت إضافة المعاملة بنجاح']);
            } else {
                echo json_encode(['success' => false, 'message' => 'تمت إضافة المعاملة ولكن فشل تحديث البطاقة']);
            }
        } else {
            echo json_encode(['success' => false, 'message' => 'فشل في إضافة المعاملة']);
        }
    } catch (PDOException $e) {
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
    }
}
?>
