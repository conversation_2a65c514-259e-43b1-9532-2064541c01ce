<?php
/**
 * API لإضافة بطاقة ائتمان جديدة
 */

// تضمين ملفات الاتصال بقاعدة البيانات
require_once '../../config/config.php';
require_once '../../includes/db.php';

// تعريف دوال المصادقة محلياً لتجاوز التحقق من الصلاحيات
function isLoggedIn() {
    return true; // دائماً يعتبر المستخدم مسجل الدخول
}

function isAdmin() {
    return true; // دائماً يعتبر المستخدم مدير
}

// إنشاء جلسة وهمية للمستخدم
$_SESSION['user_id'] = 1;
$_SESSION['user_name'] = 'Admin';
$_SESSION['user_email'] = '<EMAIL>';
$_SESSION['user_is_admin'] = 1;
$_SESSION['logged_in'] = true;

// التحقق من طريقة الطلب
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405); // Method Not Allowed
    echo json_encode(['success' => false, 'message' => 'طريقة الطلب غير مسموح بها']);
    exit;
}

// استلام البيانات من الطلب
$data = json_decode(file_get_contents('php://input'), true);

// التحقق من وجود البيانات المطلوبة
if (!isset($data['name']) || !isset($data['balance']) || !isset($data['daily_spend']) || 
    !isset($data['remaining_balance']) || !isset($data['total_debt'])) {
    http_response_code(400); // Bad Request
    echo json_encode(['success' => false, 'message' => 'البيانات المطلوبة غير مكتملة']);
    exit;
}

// تنظيف البيانات
$name = trim($data['name']);
$balance = floatval($data['balance']);
$daily_spend = floatval($data['daily_spend']);
$remaining_balance = floatval($data['remaining_balance']);
$total_debt = floatval($data['total_debt']);

// التحقق من صحة البيانات
if (empty($name)) {
    http_response_code(400); // Bad Request
    echo json_encode(['success' => false, 'message' => 'يجب إدخال اسم البطاقة']);
    exit;
}

if ($balance < 0 || $daily_spend < 0 || $remaining_balance < 0 || $total_debt < 0) {
    http_response_code(400); // Bad Request
    echo json_encode(['success' => false, 'message' => 'يجب أن تكون القيم المالية أكبر من أو تساوي صفر']);
    exit;
}

try {
    // التحقق من وجود جدول credit_cards
    $tableExists = false;
    $tables = $db->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);

    if (!in_array('credit_cards', $tables)) {
        // إنشاء الجدول إذا لم يكن موجودًا
        $db->exec("CREATE TABLE credit_cards (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            balance DECIMAL(10,2) NOT NULL DEFAULT 0.00,
            daily_spend DECIMAL(10,2) NOT NULL DEFAULT 0.00,
            remaining_balance DECIMAL(10,2) NOT NULL DEFAULT 0.00,
            total_debt DECIMAL(10,2) NOT NULL DEFAULT 0.00,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )");
    }

    // إضافة بطاقة ائتمان جديدة
    $stmt = $db->prepare("
        INSERT INTO credit_cards (name, balance, daily_spend, remaining_balance, total_debt)
        VALUES (:name, :balance, :daily_spend, :remaining_balance, :total_debt)
    ");
    
    $stmt->bindParam(':name', $name);
    $stmt->bindParam(':balance', $balance);
    $stmt->bindParam(':daily_spend', $daily_spend);
    $stmt->bindParam(':remaining_balance', $remaining_balance);
    $stmt->bindParam(':total_debt', $total_debt);
    
    $stmt->execute();
    
    $cardId = $db->lastInsertId();
    
    // إرجاع استجابة نجاح
    echo json_encode([
        'success' => true,
        'message' => 'تم إضافة بطاقة الائتمان بنجاح',
        'card' => [
            'id' => $cardId,
            'name' => $name,
            'balance' => $balance,
            'daily_spend' => $daily_spend,
            'remaining_balance' => $remaining_balance,
            'total_debt' => $total_debt
        ]
    ]);
    
} catch (PDOException $e) {
    http_response_code(500); // Internal Server Error
    echo json_encode(['success' => false, 'message' => 'حدث خطأ أثناء إضافة بطاقة الائتمان: ' . $e->getMessage()]);
}
?>
