<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// التحقق من طريقة الطلب
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'طريقة الطلب غير مدعومة']);
    exit;
}

try {
    // قراءة البيانات من الطلب
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'بيانات غير صالحة']);
        exit;
    }
    
    // التحقق من الحقول المطلوبة
    $accountId = null;
    $cardId = null;

    if (isset($input['account_id'])) {
        $accountId = intval($input['account_id']);
    } elseif (isset($input['card_id'])) {
        $cardId = intval($input['card_id']);
    } else {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'معرف الحساب أو البطاقة مطلوب']);
        exit;
    }

    if (!isset($input['field']) || !isset($input['value'])) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'بيانات ناقصة']);
        exit;
    }

    $field = trim($input['field']);
    $value = floatval($input['value']);

    // التحقق من صحة الحقل
    $allowedFields = ['manual_due_amount', 'manual_debt_amount'];
    if (!in_array($field, $allowedFields)) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'حقل غير مسموح']);
        exit;
    }

    // تسجيل العملية
    error_log("تحديث مبلغ: account_id=$accountId, card_id=$cardId, field=$field, value=$value");

    // الاتصال بقاعدة البيانات
    require_once '../../config/database.php';
    $database = new Database();
    $db = $database->getConnection();

    if (!$db) {
        throw new Exception('فشل الاتصال بقاعدة البيانات');
    }

    if ($accountId) {
        // التعامل مع الحساب الإعلاني
        // إضافة الحقول إلى جدول ad_accounts إذا لم تكن موجودة
        try {
            $stmt = $db->prepare("SHOW COLUMNS FROM ad_accounts");
            $stmt->execute();
            $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);

            if (!in_array('manual_due_amount', $columns)) {
                $db->exec("ALTER TABLE ad_accounts ADD COLUMN manual_due_amount DECIMAL(10,2) DEFAULT NULL COMMENT 'المستحق اليدوي'");
            }

            if (!in_array('manual_debt_amount', $columns)) {
                $db->exec("ALTER TABLE ad_accounts ADD COLUMN manual_debt_amount DECIMAL(10,2) DEFAULT NULL COMMENT 'المديونية اليدوية'");
            }
        } catch (PDOException $e) {
            error_log("خطأ في إضافة الحقول: " . $e->getMessage());
        }

        // التحقق من وجود الحساب
        $checkStmt = $db->prepare("SELECT id FROM ad_accounts WHERE id = :account_id");
        $checkStmt->bindParam(':account_id', $accountId);
        $checkStmt->execute();

        if (!$checkStmt->fetch()) {
            http_response_code(404);
            echo json_encode(['success' => false, 'message' => 'الحساب الإعلاني غير موجود']);
            exit;
        }

        // تحديث المبلغ
        $updateQuery = "UPDATE ad_accounts SET $field = :value WHERE id = :account_id";
        $updateStmt = $db->prepare($updateQuery);
        $updateStmt->bindParam(':value', $value);
        $updateStmt->bindParam(':account_id', $accountId);

        if ($updateStmt->execute()) {
            echo json_encode([
                'success' => true,
                'message' => 'تم تحديث المبلغ بنجاح',
                'data' => [
                    'account_id' => $accountId,
                    'field' => $field,
                    'value' => $value
                ]
            ]);
        } else {
            throw new Exception('فشل في تحديث المبلغ');
        }
    } else {
        // التعامل مع الكريديت كارد (الكود القديم)
        $checkStmt = $db->prepare("SELECT id FROM credit_cards WHERE id = :card_id");
        $checkStmt->bindParam(':card_id', $cardId);
        $checkStmt->execute();

        if (!$checkStmt->fetch()) {
            http_response_code(404);
            echo json_encode(['success' => false, 'message' => 'البطاقة غير موجودة']);
            exit;
        }

        // تحديث المبلغ
        $updateQuery = "UPDATE credit_cards SET $field = :value WHERE id = :card_id";
        $updateStmt = $db->prepare($updateQuery);
        $updateStmt->bindParam(':value', $value);
        $updateStmt->bindParam(':card_id', $cardId);

        if ($updateStmt->execute()) {
            echo json_encode([
                'success' => true,
                'message' => 'تم تحديث المبلغ بنجاح',
                'data' => [
                    'card_id' => $cardId,
                    'field' => $field,
                    'value' => $value
                ]
            ]);
        } else {
            throw new Exception('فشل في تحديث المبلغ');
        }
    }
    
} catch (Exception $e) {
    error_log("خطأ في تحديث المبلغ: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'حدث خطأ في الخادم: ' . $e->getMessage()
    ]);
}
?>
