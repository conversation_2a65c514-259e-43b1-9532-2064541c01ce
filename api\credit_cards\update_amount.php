<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// التحقق من طريقة الطلب
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'طريقة الطلب غير مدعومة']);
    exit;
}

try {
    // قراءة البيانات من الطلب
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'بيانات غير صالحة']);
        exit;
    }
    
    // التحقق من الحقول المطلوبة
    if (!isset($input['card_id']) || !isset($input['field']) || !isset($input['value'])) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'بيانات ناقصة']);
        exit;
    }
    
    $cardId = intval($input['card_id']);
    $field = trim($input['field']);
    $value = floatval($input['value']);
    
    // التحقق من صحة الحقل
    $allowedFields = ['manual_due_amount', 'manual_debt_amount'];
    if (!in_array($field, $allowedFields)) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'حقل غير مسموح']);
        exit;
    }
    
    // تسجيل العملية
    error_log("تحديث مبلغ البطاقة: card_id=$cardId, field=$field, value=$value");
    
    // الاتصال بقاعدة البيانات
    require_once '../../config/database.php';
    $database = new Database();
    $db = $database->getConnection();
    
    if (!$db) {
        throw new Exception('فشل الاتصال بقاعدة البيانات');
    }
    
    // التحقق من وجود البطاقة
    $checkStmt = $db->prepare("SELECT id FROM credit_cards WHERE id = :card_id");
    $checkStmt->bindParam(':card_id', $cardId);
    $checkStmt->execute();
    
    if (!$checkStmt->fetch()) {
        http_response_code(404);
        echo json_encode(['success' => false, 'message' => 'البطاقة غير موجودة']);
        exit;
    }
    
    // تحديث المبلغ
    $updateQuery = "UPDATE credit_cards SET $field = :value WHERE id = :card_id";
    $updateStmt = $db->prepare($updateQuery);
    $updateStmt->bindParam(':value', $value);
    $updateStmt->bindParam(':card_id', $cardId);
    
    if ($updateStmt->execute()) {
        echo json_encode([
            'success' => true,
            'message' => 'تم تحديث المبلغ بنجاح',
            'data' => [
                'card_id' => $cardId,
                'field' => $field,
                'value' => $value
            ]
        ]);
    } else {
        throw new Exception('فشل في تحديث المبلغ');
    }
    
} catch (Exception $e) {
    error_log("خطأ في تحديث المبلغ: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'حدث خطأ في الخادم: ' . $e->getMessage()
    ]);
}
?>
