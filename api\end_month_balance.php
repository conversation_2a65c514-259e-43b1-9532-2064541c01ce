<?php
// تضمين ملف الاتصال بقاعدة البيانات
require_once '../config/database.php';

// التحقق من وجود معرف العميل
if (!isset($_GET['client_id']) || empty($_GET['client_id'])) {
    echo json_encode([
        'success' => false,
        'message' => 'معرف العميل مطلوب'
    ]);
    exit;
}

// الحصول على معرف العميل
$client_id = $_GET['client_id'];

try {
    // إنشاء اتصال بقاعدة البيانات
    $database = new Database();
    $db = $database->getConnection();

    // الحصول على بيانات العميل
    $client_query = "SELECT id, name, previous_debt, previous_balance, end_month_balance FROM clients WHERE id = ?";
    $client_stmt = $db->prepare($client_query);
    $client_stmt->execute([$client_id]);
    $client = $client_stmt->fetch(PDO::FETCH_ASSOC);

    if (!$client) {
        echo json_encode([
            'success' => false,
            'message' => 'العميل غير موجود'
        ]);
        exit;
    }

    // الحصول على خدمات العميل من جدول page_services
    $services_query = "SELECT id, name, price FROM page_services WHERE client_id = ?";
    $services_stmt = $db->prepare($services_query);
    $services_stmt->execute([$client_id]);
    $services = $services_stmt->fetchAll(PDO::FETCH_ASSOC);

    // حساب إجمالي الخدمات
    $services_total = 0;
    foreach ($services as $service) {
        $services_total += $service['price'];
    }

    // الحصول على إعلانات العميل من جدول ads
    $current_month = date('m');
    $ads_query = "SELECT id, date, type, post, cost, exchange_rate, exchange_rate_with_percentage, status FROM ads WHERE client_id = ? AND MONTH(date) = ?";
    $ads_stmt = $db->prepare($ads_query);
    $ads_stmt->execute([$client_id, $current_month]);
    $ads = $ads_stmt->fetchAll(PDO::FETCH_ASSOC);

    // حساب إجمالي الإعلانات
    $ads_total = 0;
    $ads_exchange_total = 0;
    foreach ($ads as $ad) {
        $ads_total += $ad['cost'];
        $ads_exchange_total += $ad['exchange_rate_with_percentage'];
    }

    // الحصول على مدفوعات العميل من جدول payments
    $current_month = date('m');
    $payments_query = "SELECT id, date, amount, payment_method, notes FROM payments WHERE client_id = ? AND MONTH(date) = ?";
    $payments_stmt = $db->prepare($payments_query);
    $payments_stmt->execute([$client_id, $current_month]);
    $payments = $payments_stmt->fetchAll(PDO::FETCH_ASSOC);

    // حساب إجمالي المدفوعات
    $payments_total = 0;
    foreach ($payments as $payment) {
        $payments_total += $payment['amount'];
    }

    // حساب إجمالي حساب العميل نهاية الشهر
    $total_balance = $client['previous_balance'] + $services_total + $ads_exchange_total - $payments_total;

    // إعداد البيانات للإرجاع
    $response = [
        'success' => true,
        'client' => [
            'id' => $client['id'],
            'name' => $client['name']
        ],
        'previous_debt' => (float)$client['previous_debt'],
        'previous_balance' => (float)$client['previous_balance'],
        'end_month_balance' => (float)$client['end_month_balance'],
        'services' => $services,
        'services_total' => $services_total,
        'ads' => $ads,
        'ads_total' => $ads_total,
        'ads_exchange_total' => $ads_exchange_total,
        'payments' => $payments,
        'payments_total' => $payments_total,
        'total_balance' => $total_balance
    ];

    // إرجاع البيانات بتنسيق JSON
    header('Content-Type: application/json');
    echo json_encode($response);
} catch (PDOException $e) {
    echo json_encode([
        'success' => false,
        'message' => 'خطأ في قاعدة البيانات: ' . $e->getMessage()
    ]);
}
