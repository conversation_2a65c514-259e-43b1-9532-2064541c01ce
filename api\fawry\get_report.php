<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// التعامل مع طلبات OPTIONS
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// تضمين ملف الاتصال بقاعدة البيانات
require_once '../../config/database.php';

try {
    // إنشاء اتصال بقاعدة البيانات
    $database = new Database();
    $db = $database->getConnection();
    
    if (!$db) {
        throw new Exception('فشل الاتصال بقاعدة البيانات');
    }

    // إنشاء الجدول إذا لم يكن موجوداً
    $db->exec("
        CREATE TABLE IF NOT EXISTS fawry_report (
            id INT AUTO_INCREMENT PRIMARY KEY,
            basic_balance DECIMAL(10,2) NOT NULL DEFAULT 14034.00,
            cash_out DECIMAL(10,2) NOT NULL DEFAULT 0.00,
            total_balance DECIMAL(10,2) NOT NULL DEFAULT 14034.00,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )
    ");

    // جلب البيانات
    $stmt = $db->prepare("SELECT * FROM fawry_report ORDER BY id DESC LIMIT 1");
    $stmt->execute();
    $fawryData = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$fawryData) {
        // إدراج بيانات افتراضية إذا لم توجد
        $stmt = $db->prepare("
            INSERT INTO fawry_report (basic_balance, cash_out, total_balance) 
            VALUES (14034.00, 0.00, 14034.00)
        ");
        $stmt->execute();
        
        $fawryData = [
            'basic_balance' => 14034.00,
            'cash_out' => 0.00,
            'total_balance' => 14034.00
        ];
    }

    // إرسال البيانات
    echo json_encode([
        'success' => true,
        'data' => [
            'basic_balance' => floatval($fawryData['basic_balance']),
            'cash_out' => floatval($fawryData['cash_out']),
            'total_balance' => floatval($fawryData['total_balance'])
        ]
    ], JSON_UNESCAPED_UNICODE);

} catch (Exception $e) {
    // إرسال استجابة خطأ
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage(),
        'data' => [
            'basic_balance' => 14034.00,
            'cash_out' => 0.00,
            'total_balance' => 14034.00
        ]
    ], JSON_UNESCAPED_UNICODE);
}
?>
