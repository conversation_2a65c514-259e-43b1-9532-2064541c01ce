<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// التعامل مع طلبات OPTIONS
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// تضمين ملف الاتصال بقاعدة البيانات
require_once '../../config/database.php';

try {
    // إنشاء اتصال بقاعدة البيانات
    $database = new Database();
    $db = $database->getConnection();
    
    if (!$db) {
        throw new Exception('فشل الاتصال بقاعدة البيانات');
    }

    // التحقق من طريقة الطلب
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('طريقة الطلب غير مدعومة');
    }

    // قراءة البيانات المرسلة
    $input = file_get_contents('php://input');
    $data = json_decode($input, true);

    if (!$data) {
        throw new Exception('بيانات غير صحيحة');
    }

    // التحقق من وجود الحقول المطلوبة
    if (!isset($data['basic_balance']) || !isset($data['cash_out']) || !isset($data['total_balance'])) {
        throw new Exception('بيانات ناقصة');
    }

    $basicBalance = floatval($data['basic_balance']);
    $cashOut = floatval($data['cash_out']);
    $totalBalance = floatval($data['total_balance']);

    // إنشاء الجدول إذا لم يكن موجوداً
    $db->exec("
        CREATE TABLE IF NOT EXISTS fawry_report (
            id INT AUTO_INCREMENT PRIMARY KEY,
            basic_balance DECIMAL(10,2) NOT NULL DEFAULT 14034.00,
            cash_out DECIMAL(10,2) NOT NULL DEFAULT 0.00,
            total_balance DECIMAL(10,2) NOT NULL DEFAULT 14034.00,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )
    ");

    // التحقق من وجود بيانات
    $stmt = $db->prepare("SELECT COUNT(*) FROM fawry_report");
    $stmt->execute();
    $count = $stmt->fetchColumn();

    if ($count > 0) {
        // تحديث البيانات الموجودة
        $stmt = $db->prepare("
            UPDATE fawry_report 
            SET basic_balance = ?, cash_out = ?, total_balance = ?, updated_at = CURRENT_TIMESTAMP 
            ORDER BY id DESC 
            LIMIT 1
        ");
        $stmt->execute([$basicBalance, $cashOut, $totalBalance]);
    } else {
        // إدراج بيانات جديدة
        $stmt = $db->prepare("
            INSERT INTO fawry_report (basic_balance, cash_out, total_balance) 
            VALUES (?, ?, ?)
        ");
        $stmt->execute([$basicBalance, $cashOut, $totalBalance]);
    }

    // إرسال استجابة نجح
    echo json_encode([
        'success' => true,
        'message' => 'تم حفظ البيانات بنجاح',
        'data' => [
            'basic_balance' => $basicBalance,
            'cash_out' => $cashOut,
            'total_balance' => $totalBalance
        ]
    ], JSON_UNESCAPED_UNICODE);

} catch (Exception $e) {
    // إرسال استجابة خطأ
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}
?>
