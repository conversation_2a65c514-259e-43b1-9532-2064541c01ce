<?php
/**
 * API لإضافة حساب فوري جديد
 */

// تضمين ملفات الاتصال بقاعدة البيانات
require_once '../../config/config.php';
require_once '../../includes/db.php';

// تعريف دوال المصادقة محلياً لتجاوز التحقق من الصلاحيات
function isLoggedIn() {
    return true; // دائماً يعتبر المستخدم مسجل الدخول
}

function isAdmin() {
    return true; // دائماً يعتبر المستخدم مدير
}

// إنشاء جلسة وهمية للمستخدم
$_SESSION['user_id'] = 1;
$_SESSION['user_name'] = 'Admin';
$_SESSION['user_email'] = '<EMAIL>';
$_SESSION['user_is_admin'] = 1;
$_SESSION['logged_in'] = true;

// التحقق من طريقة الطلب
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405); // Method Not Allowed
    echo json_encode(['success' => false, 'message' => 'طريقة الطلب غير مسموح بها']);
    exit;
}

// استلام البيانات من الطلب
$data = json_decode(file_get_contents('php://input'), true);

// التحقق من وجود البيانات المطلوبة
if (!isset($data['name']) || !isset($data['status']) || !isset($data['balance'])) {
    http_response_code(400); // Bad Request
    echo json_encode(['success' => false, 'message' => 'البيانات المطلوبة غير مكتملة']);
    exit;
}

// تنظيف البيانات
$name = trim($data['name']);
$status = trim($data['status']);
$balance = floatval($data['balance']);

// التحقق من صحة البيانات
if (empty($name)) {
    http_response_code(400); // Bad Request
    echo json_encode(['success' => false, 'message' => 'يجب إدخال اسم الحساب']);
    exit;
}

if ($balance < 0) {
    http_response_code(400); // Bad Request
    echo json_encode(['success' => false, 'message' => 'يجب أن يكون الرصيد أكبر من أو يساوي صفر']);
    exit;
}

if (!in_array($status, ['نشط', 'متوقف'])) {
    http_response_code(400); // Bad Request
    echo json_encode(['success' => false, 'message' => 'حالة الحساب غير صالحة']);
    exit;
}

try {
    // التحقق من وجود جدول fawry_accounts
    $tableExists = false;
    $tables = $db->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);

    if (!in_array('fawry_accounts', $tables)) {
        // إنشاء الجدول إذا لم يكن موجودًا
        $db->exec("CREATE TABLE fawry_accounts (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            status VARCHAR(50) NOT NULL DEFAULT 'نشط',
            balance DECIMAL(10,2) NOT NULL DEFAULT 0.00,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )");
    }

    // إضافة حساب فوري جديد
    $stmt = $db->prepare("
        INSERT INTO fawry_accounts (name, status, balance)
        VALUES (:name, :status, :balance)
    ");
    
    $stmt->bindParam(':name', $name);
    $stmt->bindParam(':status', $status);
    $stmt->bindParam(':balance', $balance);
    
    $stmt->execute();
    
    $accountId = $db->lastInsertId();
    
    // إرجاع استجابة نجاح
    echo json_encode([
        'success' => true,
        'message' => 'تم إضافة حساب فوري بنجاح',
        'account' => [
            'id' => $accountId,
            'name' => $name,
            'status' => $status,
            'balance' => $balance
        ]
    ]);
    
} catch (PDOException $e) {
    http_response_code(500); // Internal Server Error
    echo json_encode(['success' => false, 'message' => 'حدث خطأ أثناء إضافة حساب فوري: ' . $e->getMessage()]);
}
?>
