<?php
// تعيين نوع المحتوى
header('Content-Type: application/json');

// التحقق من طريقة الطلب
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'طريقة الطلب غير مدعومة']);
    exit;
}

try {
    // تضمين ملف الاتصال بقاعدة البيانات
    require_once '../includes/db.php';

    // التحقق من الاتصال بقاعدة البيانات
    if (!isset($db)) {
        throw new Exception('فشل الاتصال بقاعدة البيانات');
    }

    // جلب العملاء من جدول clients
    $clients = [];
    try {
        $clientsQuery = "SELECT id, name, COALESCE(type, 'عميل عادي') as type FROM clients ORDER BY name ASC";
        $clientsStmt = $db->prepare($clientsQuery);
        $clientsStmt->execute();
        $clients = $clientsStmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        // إذا فشل جلب العملاء، نتجاهل الخطأ ونستمر
        $clients = [];
    }

    // جلب عملاء الإعلانات من جدول ad_clients
    $adClients = [];
    try {
        $adClientsQuery = "SELECT id, name, COALESCE(type, 'عميل إعلانات') as type FROM ad_clients ORDER BY name ASC";
        $adClientsStmt = $db->prepare($adClientsQuery);
        $adClientsStmt->execute();
        $adClients = $adClientsStmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        // إذا فشل جلب عملاء الإعلانات، نتجاهل الخطأ ونستمر
        $adClients = [];
    }

    // دمج العملاء من الجدولين
    $allClients = [];

    // إضافة العملاء من جدول clients
    foreach ($clients as $client) {
        $allClients[] = [
            'id' => 'client_' . $client['id'],
            'name' => $client['name'],
            'type' => $client['type'],
            'type_display_name' => $client['type'],
            'type_color' => '#4a56e2',
            'source' => 'clients',
            'original_id' => $client['id']
        ];
    }

    // إضافة عملاء الإعلانات من جدول ad_clients
    foreach ($adClients as $adClient) {
        $allClients[] = [
            'id' => 'ad_client_' . $adClient['id'],
            'name' => $adClient['name'],
            'type' => $adClient['type'],
            'type_display_name' => $adClient['type'],
            'type_color' => '#4a56e2',
            'source' => 'ad_clients',
            'original_id' => $adClient['id']
        ];
    }

    // ترتيب العملاء حسب الاسم
    usort($allClients, function($a, $b) {
        return strcmp($a['name'], $b['name']);
    });

    // جلب الحسابات الإعلانية
    $accounts = [];
    try {
        $accountsQuery = "SELECT aa.id, aa.name, COALESCE(aa.type, 'حساب إعلاني') as type,
                                 COALESCE(aa.balance, 0) as balance, COALESCE(aa.status, 'نشط') as status,
                                 c.name as client_name, c.id as client_id
                          FROM ad_accounts aa
                          LEFT JOIN clients c ON aa.client_id = c.id
                          ORDER BY aa.name ASC";
        $accountsStmt = $db->prepare($accountsQuery);
        $accountsStmt->execute();
        $accounts = $accountsStmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        // إذا فشل جلب الحسابات، نتجاهل الخطأ ونستمر
        $accounts = [];
    }

    // جلب فئات العملاء (مبسط)
    $clientTypes = [
        ['id' => 1, 'name' => 'client_a', 'display_name' => 'Client A', 'color' => '#4a56e2'],
        ['id' => 2, 'name' => 'client_b', 'display_name' => 'Client B', 'color' => '#28a745'],
        ['id' => 3, 'name' => 'vip', 'display_name' => 'VIP', 'color' => '#ffc107']
    ];

    echo json_encode([
        'success' => true,
        'clients' => $allClients,
        'accounts' => $accounts,
        'client_types' => $clientTypes
    ]);

} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'خطأ في قاعدة البيانات: ' . $e->getMessage(),
        'error_details' => $e->getTraceAsString()
    ]);
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'خطأ في الخادم: ' . $e->getMessage(),
        'error_details' => $e->getTraceAsString()
    ]);
}
?>
