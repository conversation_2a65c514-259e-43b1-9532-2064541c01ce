<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type');

// التحقق من طريقة الطلب
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'طريقة الطلب غير مدعومة']);
    exit;
}

// بيانات افتراضية في حالة فشل قاعدة البيانات
$defaultClients = [
    // عملاء من جدول clients
    [
        'id' => 'client_1',
        'name' => 'عميل عادي 1',
        'type' => 'Client A',
        'type_display_name' => 'Client A',
        'type_color' => '#4a56e2',
        'source' => 'clients',
        'original_id' => 1
    ],
    [
        'id' => 'client_2',
        'name' => 'عميل عادي 2',
        'type' => 'Client B',
        'type_display_name' => 'Client B',
        'type_color' => '#28a745',
        'source' => 'clients',
        'original_id' => 2
    ],
    // عملاء من جدول ad_clients
    [
        'id' => 'ad_client_1',
        'name' => 'عميل إعلانات 1',
        'type' => 'VIP',
        'type_display_name' => 'VIP',
        'type_color' => '#ffc107',
        'source' => 'ad_clients',
        'original_id' => 1
    ],
    [
        'id' => 'ad_client_2',
        'name' => 'عميل إعلانات 2',
        'type' => 'Premium',
        'type_display_name' => 'Premium',
        'type_color' => '#dc3545',
        'source' => 'ad_clients',
        'original_id' => 2
    ]
];

$defaultAccounts = [
    [
        'id' => 1,
        'name' => 'حساب فيسبوك 1',
        'type' => 'Facebook',
        'balance' => 1000,
        'status' => 'نشط',
        'client_name' => 'عميل عادي 1',
        'client_id' => 1
    ],
    [
        'id' => 2,
        'name' => 'حساب جوجل 1',
        'type' => 'Google',
        'balance' => 2000,
        'status' => 'نشط',
        'client_name' => 'عميل عادي 2',
        'client_id' => 2
    ],
    [
        'id' => 3,
        'name' => 'حساب انستجرام 1',
        'type' => 'Instagram',
        'balance' => 1500,
        'status' => 'نشط',
        'client_name' => 'عميل إعلانات 1',
        'client_id' => 1
    ],
    [
        'id' => 4,
        'name' => 'حساب تيك توك 1',
        'type' => 'TikTok',
        'balance' => 800,
        'status' => 'نشط',
        'client_name' => 'عميل إعلانات 2',
        'client_id' => 2
    ]
];

$defaultClientTypes = [
    ['id' => 1, 'name' => 'client_a', 'display_name' => 'Client A', 'color' => '#4a56e2'],
    ['id' => 2, 'name' => 'client_b', 'display_name' => 'Client B', 'color' => '#28a745'],
    ['id' => 3, 'name' => 'vip', 'display_name' => 'VIP', 'color' => '#ffc107']
];

try {
    // محاولة الاتصال بقاعدة البيانات
    if (file_exists('../includes/db.php')) {
        require_once '../includes/db.php';

        if (isset($db) && $db instanceof PDO) {
            // جلب العملاء من قاعدة البيانات
            $allClients = [];

            // جلب العملاء من جدول clients
            try {
                $clientsQuery = "SELECT id, name, COALESCE(type, 'عميل عادي') as type FROM clients ORDER BY name ASC";
                $clientsStmt = $db->prepare($clientsQuery);
                $clientsStmt->execute();
                $clients = $clientsStmt->fetchAll(PDO::FETCH_ASSOC);

                foreach ($clients as $client) {
                    $allClients[] = [
                        'id' => 'client_' . $client['id'],
                        'name' => $client['name'],
                        'type' => $client['type'],
                        'type_display_name' => $client['type'],
                        'type_color' => '#4a56e2',
                        'source' => 'clients',
                        'original_id' => $client['id']
                    ];
                }
            } catch (Exception $e) {
                // في حالة عدم وجود جدول clients، أضف بيانات تجريبية
                $allClients[] = [
                    'id' => 'client_1',
                    'name' => 'عميل من جدول clients',
                    'type' => 'عميل عادي',
                    'type_display_name' => 'عميل عادي',
                    'type_color' => '#4a56e2',
                    'source' => 'clients',
                    'original_id' => 1
                ];
            }

            // جلب عملاء الإعلانات من جدول ad_clients
            try {
                $adClientsQuery = "SELECT id, name, COALESCE(type, 'عميل إعلانات') as type FROM ad_clients ORDER BY name ASC";
                $adClientsStmt = $db->prepare($adClientsQuery);
                $adClientsStmt->execute();
                $adClients = $adClientsStmt->fetchAll(PDO::FETCH_ASSOC);

                foreach ($adClients as $adClient) {
                    $allClients[] = [
                        'id' => 'ad_client_' . $adClient['id'],
                        'name' => $adClient['name'],
                        'type' => $adClient['type'],
                        'type_display_name' => $adClient['type'],
                        'type_color' => '#28a745',
                        'source' => 'ad_clients',
                        'original_id' => $adClient['id']
                    ];
                }
            } catch (Exception $e) {
                // في حالة عدم وجود جدول ad_clients، أضف بيانات تجريبية
                $allClients[] = [
                    'id' => 'ad_client_1',
                    'name' => 'عميل من جدول ad_clients',
                    'type' => 'عميل إعلانات',
                    'type_display_name' => 'عميل إعلانات',
                    'type_color' => '#28a745',
                    'source' => 'ad_clients',
                    'original_id' => 1
                ];
            }

            // جلب الحسابات الإعلانية
            $accounts = [];
            try {
                $accountsQuery = "SELECT aa.id, aa.name, COALESCE(aa.type, 'حساب إعلاني') as type,
                                         COALESCE(aa.balance, 0) as balance, COALESCE(aa.status, 'نشط') as status,
                                         c.name as client_name, c.id as client_id
                                  FROM ad_accounts aa
                                  LEFT JOIN clients c ON aa.client_id = c.id
                                  ORDER BY aa.name ASC LIMIT 50";
                $accountsStmt = $db->prepare($accountsQuery);
                $accountsStmt->execute();
                $accounts = $accountsStmt->fetchAll(PDO::FETCH_ASSOC);
            } catch (Exception $e) {
                $accounts = $defaultAccounts;
            }

            // إذا لم نحصل على أي عملاء، استخدم البيانات الافتراضية
            if (empty($allClients)) {
                $allClients = $defaultClients;
            }

            // إذا لم نحصل على أي حسابات، استخدم البيانات الافتراضية
            if (empty($accounts)) {
                $accounts = $defaultAccounts;
            }

            echo json_encode([
                'success' => true,
                'clients' => $allClients,
                'accounts' => $accounts,
                'client_types' => $defaultClientTypes,
                'source' => 'database'
            ]);

        } else {
            // فشل الاتصال بقاعدة البيانات، استخدم البيانات الافتراضية
            echo json_encode([
                'success' => true,
                'clients' => $defaultClients,
                'accounts' => $defaultAccounts,
                'client_types' => $defaultClientTypes,
                'source' => 'default_no_db'
            ]);
        }
    } else {
        // ملف قاعدة البيانات غير موجود، استخدم البيانات الافتراضية
        echo json_encode([
            'success' => true,
            'clients' => $defaultClients,
            'accounts' => $defaultAccounts,
            'client_types' => $defaultClientTypes,
            'source' => 'default_no_file'
        ]);
    }

} catch (Exception $e) {
    // في حالة أي خطأ، استخدم البيانات الافتراضية
    echo json_encode([
        'success' => true,
        'clients' => $defaultClients,
        'accounts' => $defaultAccounts,
        'client_types' => $defaultClientTypes,
        'source' => 'default_error',
        'error_message' => $e->getMessage()
    ]);
}
?>
