<?php
// تضمين ملف الاتصال بقاعدة البيانات
require_once '../config/database.php';

// التحقق من وجود معرف العميل
if (!isset($_GET['client_id']) || empty($_GET['client_id'])) {
    echo json_encode([
        'status' => 'error',
        'message' => 'معرف العميل مطلوب'
    ]);
    exit;
}

// الحصول على معرف العميل
$client_id = $_GET['client_id'];

try {
    // إنشاء اتصال بقاعدة البيانات
    $database = new Database();
    $db = $database->getConnection();

    // الحصول على بيانات العميل
    $client_query = "SELECT name, previous_balance FROM clients WHERE id = ?";
    $client_stmt = $db->prepare($client_query);
    $client_stmt->execute([$client_id]);
    $client = $client_stmt->fetch(PDO::FETCH_ASSOC);

    if (!$client) {
        echo json_encode([
            'status' => 'error',
            'message' => 'العميل غير موجود'
        ]);
        exit;
    }

    // الحصول على الشهر الحالي والشهر السابق
    $current_month = date('n'); // الشهر الحالي (1-12)
    $previous_month = $current_month - 1;
    if ($previous_month == 0) {
        $previous_month = 12;
    }

    // تحويل الأرقام إلى أسماء الأشهر بالعربية
    $months_ar = [
        1 => 'يناير',
        2 => 'فبراير',
        3 => 'مارس',
        4 => 'أبريل',
        5 => 'مايو',
        6 => 'يونيو',
        7 => 'يوليو',
        8 => 'أغسطس',
        9 => 'سبتمبر',
        10 => 'أكتوبر',
        11 => 'نوفمبر',
        12 => 'ديسمبر'
    ];

    $current_month_name = $months_ar[$current_month];
    $previous_month_name = $months_ar[$previous_month];

    // الحصول على خدمات العميل من جدول page_services
    $services_query = "SELECT name as title, price as amount FROM page_services WHERE client_id = ?";
    $services_stmt = $db->prepare($services_query);
    $services_stmt->execute([$client_id]);
    $services = $services_stmt->fetchAll(PDO::FETCH_ASSOC);

    // الحصول على إعلانات العميل من جدول ads
    $ads_query = "SELECT
                    CONCAT(DAY(date), '-', MONTH(date)) as title,
                    cost as amount,
                    status
                  FROM ads
                  WHERE client_id = ? AND MONTH(date) = ?";
    $ads_stmt = $db->prepare($ads_query);
    $ads_stmt->execute([$client_id, $current_month]);
    $ads = $ads_stmt->fetchAll(PDO::FETCH_ASSOC);

    // حساب إجمالي الإعلانات
    $ads_total = 0;
    foreach ($ads as $ad) {
        $ads_total += $ad['amount'];
    }

    // الحصول على معاد الدفع للعميل
    $payment_date_query = "SELECT payment_day FROM clients WHERE id = ?";
    $payment_date_stmt = $db->prepare($payment_date_query);
    $payment_date_stmt->execute([$client_id]);
    $payment_date_result = $payment_date_stmt->fetch(PDO::FETCH_ASSOC);

    // تحديد معاد الدفع (اليوم من الشهر)
    $payment_day = 1; // القيمة الافتراضية هي اليوم الأول من الشهر
    if ($payment_date_result && !empty($payment_date_result['payment_day'])) {
        $payment_day = (int)$payment_date_result['payment_day'];
    }

    // تحديد تاريخ بداية ونهاية الفترة
    $current_year = date('Y');
    $current_month_start = date('Y-m-d', strtotime("$current_year-$current_month-$payment_day"));

    // إذا كان معاد الدفع أكبر من اليوم الحالي، فإن الفترة تبدأ من الشهر السابق
    $today = date('Y-m-d');
    if ($current_month_start > $today) {
        $previous_month_start = date('Y-m-d', strtotime("$current_month_start -1 month"));
        $period_start = $previous_month_start;
    } else {
        $period_start = $current_month_start;
    }

    $period_end = date('Y-m-d', strtotime("$period_start +1 month -1 day"));

    // الحصول على مدفوعات العميل خلال الفترة من جدول payments
    $payments_query = "SELECT
                        CONCAT(DAY(date), '-', MONTH(date)) as date,
                        amount,
                        payment_method
                      FROM payments
                      WHERE client_id = ? AND date BETWEEN ? AND ?
                      ORDER BY date DESC";
    $payments_stmt = $db->prepare($payments_query);
    $payments_stmt->execute([$client_id, $period_start, $period_end]);
    $payments = $payments_stmt->fetchAll(PDO::FETCH_ASSOC);

    // حساب إجمالي المدفوعات
    $payments_total = 0;
    foreach ($payments as $payment) {
        $payments_total += $payment['amount'];
    }

    // حساب إجمالي حسابات الشهر الحالي
    $services_total = 0;
    foreach ($services as $service) {
        $services_total += $service['amount'];
    }
    $current_month_total = $services_total + $ads_total;

    // حساب إجمالي حساب العميل حتى الشهر الحالي
    $total_month_balance = $client['previous_balance'] + $current_month_total - $payments_total;

    // إعداد البيانات للإرجاع
    $response = [
        'status' => 'success',
        'data' => [
            'client' => [
                'id' => $client_id,
                'name' => $client['name']
            ],
            'previous_balance' => (float)$client['previous_balance'],
            'previous_month' => "شهر {$previous_month}",
            'current_month' => "شهر {$current_month}",
            'services' => $services,
            'ads' => $ads,
            'ads_total' => $ads_total,
            'payments' => $payments,
            'payments_total' => $payments_total,
            'current_month_total' => $current_month_total,
            'total_month_balance' => $total_month_balance
        ]
    ];

    // إرجاع البيانات بتنسيق JSON
    header('Content-Type: application/json');
    echo json_encode($response);
} catch (PDOException $e) {
    echo json_encode([
        'status' => 'error',
        'message' => 'خطأ في قاعدة البيانات: ' . $e->getMessage()
    ]);
}
