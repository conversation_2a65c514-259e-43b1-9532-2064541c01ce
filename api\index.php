<?php
/**
 * API Entry Point
 */

// Set headers
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS");
header("Access-Control-Max-Age: 3600");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

// Handle preflight OPTIONS request
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Include configuration
require_once __DIR__ . '/../config/config.php';

// Include database connection
require_once __DIR__ . '/../includes/db.php';

// Include helper functions
require_once __DIR__ . '/../includes/functions.php';

// Include authentication functions
require_once __DIR__ . '/../includes/auth.php';

// Get request method
$method = $_SERVER['REQUEST_METHOD'];

// Get request URI
$request_uri = $_SERVER['REQUEST_URI'];

// Parse URI to get endpoint and parameters
$uri_parts = parse_url($request_uri);
$path = $uri_parts['path'];

// Remove base path from URI
$base_path = '/bb/api/';
$endpoint = str_replace($base_path, '', $path);

// Parse query parameters
$query_params = [];
if (isset($uri_parts['query'])) {
    parse_str($uri_parts['query'], $query_params);
}

// Get request body for POST, PUT requests
$request_body = file_get_contents('php://input');
$request_data = json_decode($request_body, true);

// Initialize response array
$response = [
    'success' => false,
    'message' => '',
    'data' => null
];

// Check if endpoint is specified
if (empty($endpoint)) {
    $response['message'] = 'API endpoint not specified';
    echo json_encode($response);
    exit;
}

// Route request to appropriate endpoint
switch ($endpoint) {
    case 'auth/login':
        if ($method === 'POST') {
            // Handle login request
            if (isset($request_data['email']) && isset($request_data['password'])) {
                $email = sanitize($request_data['email']);
                $password = $request_data['password'];
                
                $user = authenticateUser($email, $password);
                
                if ($user) {
                    $token = generateJWTToken($user);
                    
                    $response['success'] = true;
                    $response['message'] = 'تم تسجيل الدخول بنجاح';
                    $response['data'] = [
                        'user' => $user,
                        'token' => $token
                    ];
                } else {
                    $response['message'] = 'البريد الإلكتروني أو كلمة المرور غير صحيحة';
                }
            } else {
                $response['message'] = 'البريد الإلكتروني وكلمة المرور مطلوبان';
            }
        } else {
            $response['message'] = 'Method not allowed';
        }
        break;
    
    case 'auth/register':
        if ($method === 'POST') {
            // Handle register request
            if (isset($request_data['name']) && isset($request_data['email']) && isset($request_data['password'])) {
                $name = sanitize($request_data['name']);
                $email = sanitize($request_data['email']);
                $password = $request_data['password'];
                
                $user_id = registerUser($name, $email, $password);
                
                if ($user_id) {
                    // Get user data
                    $query = "SELECT * FROM users WHERE id = :id LIMIT 1";
                    $stmt = $db->prepare($query);
                    $stmt->bindParam(':id', $user_id);
                    $stmt->execute();
                    $user = $stmt->fetch(PDO::FETCH_ASSOC);
                    
                    // Remove password from user data
                    unset($user['password']);
                    
                    $token = generateJWTToken($user);
                    
                    $response['success'] = true;
                    $response['message'] = 'تم إنشاء الحساب بنجاح';
                    $response['data'] = [
                        'user' => $user,
                        'token' => $token
                    ];
                } else {
                    $response['message'] = 'البريد الإلكتروني مستخدم بالفعل';
                }
            } else {
                $response['message'] = 'الاسم والبريد الإلكتروني وكلمة المرور مطلوبة';
            }
        } else {
            $response['message'] = 'Method not allowed';
        }
        break;
    
    case 'users/profile':
        // Check if user is authenticated
        $headers = getallheaders();
        $token = isset($headers['Authorization']) ? str_replace('Bearer ', '', $headers['Authorization']) : null;
        
        if ($token) {
            $user = validateJWTToken($token);
            
            if ($user) {
                if ($method === 'GET') {
                    // Get user profile
                    $query = "SELECT id, name, email, phone, role, created_at, updated_at FROM users WHERE id = :id LIMIT 1";
                    $stmt = $db->prepare($query);
                    $stmt->bindParam(':id', $user['id']);
                    $stmt->execute();
                    $profile = $stmt->fetch(PDO::FETCH_ASSOC);
                    
                    $response['success'] = true;
                    $response['message'] = 'تم جلب بيانات الملف الشخصي بنجاح';
                    $response['data'] = $profile;
                } elseif ($method === 'PUT') {
                    // Update user profile
                    if (isset($request_data['name']) && isset($request_data['email'])) {
                        $name = sanitize($request_data['name']);
                        $email = sanitize($request_data['email']);
                        $phone = isset($request_data['phone']) ? sanitize($request_data['phone']) : null;
                        
                        // Check if email is already used by another user
                        if ($email !== $user['email']) {
                            $query = "SELECT id FROM users WHERE email = :email AND id != :id LIMIT 1";
                            $stmt = $db->prepare($query);
                            $stmt->bindParam(':email', $email);
                            $stmt->bindParam(':id', $user['id']);
                            $stmt->execute();
                            
                            if ($stmt->rowCount() > 0) {
                                $response['message'] = 'البريد الإلكتروني مستخدم بالفعل';
                                break;
                            }
                        }
                        
                        $query = "UPDATE users SET name = :name, email = :email, phone = :phone, updated_at = NOW() WHERE id = :id";
                        $stmt = $db->prepare($query);
                        $stmt->bindParam(':name', $name);
                        $stmt->bindParam(':email', $email);
                        $stmt->bindParam(':phone', $phone);
                        $stmt->bindParam(':id', $user['id']);
                        
                        if ($stmt->execute()) {
                            $response['success'] = true;
                            $response['message'] = 'تم تحديث الملف الشخصي بنجاح';
                        } else {
                            $response['message'] = 'حدث خطأ أثناء تحديث الملف الشخصي';
                        }
                    } else {
                        $response['message'] = 'الاسم والبريد الإلكتروني مطلوبان';
                    }
                } else {
                    $response['message'] = 'Method not allowed';
                }
            } else {
                $response['message'] = 'Invalid token';
            }
        } else {
            $response['message'] = 'Unauthorized';
        }
        break;
    
    default:
        $response['message'] = 'Endpoint not found';
        break;
}

// Return response as JSON
echo json_encode($response);
?>
