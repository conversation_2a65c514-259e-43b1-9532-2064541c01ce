<?php
/**
 * API لخدمات إدارة الصفحة
 */

// تضمين ملفات الإعدادات
require_once '../../config/config.php';
require_once '../../includes/db.php';
require_once '../../includes/functions.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    header('Content-Type: application/json');
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'غير مصرح لك بالوصول']);
    exit;
}

// تحديد نوع الطلب
$action = isset($_GET['action']) ? $_GET['action'] : '';

// معالجة الطلبات
switch ($action) {
    case 'get':
        getPageServices();
        break;
    case 'save':
        savePageServices();
        break;
    default:
        // طلب غير صالح
        header('Content-Type: application/json');
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'طلب غير صالح']);
        break;
}

/**
 * جلب خدمات إدارة الصفحة
 */
function getPageServices() {
    global $db;
    
    header('Content-Type: application/json');
    
    try {
        // التحقق من وجود معرف العميل
        if (!isset($_GET['client_id'])) {
            throw new Exception('معرف العميل مطلوب');
        }
        
        $client_id = (int)$_GET['client_id'];
        
        // التحقق من وجود العميل
        $query = "SELECT id, name FROM clients WHERE id = :id";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':id', $client_id);
        $stmt->execute();
        
        $client = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$client) {
            throw new Exception('العميل غير موجود');
        }
        
        // جلب خدمات إدارة الصفحة
        $query = "SELECT id, name, price FROM page_services WHERE client_id = :client_id ORDER BY id ASC";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':client_id', $client_id);
        $stmt->execute();
        
        $services = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // حساب المجموع
        $total = 0;
        foreach ($services as $service) {
            $total += (float)$service['price'];
        }
        
        $response = [
            'success' => true,
            'client' => $client,
            'services' => $services,
            'total' => $total
        ];
        
        echo json_encode($response);
    } catch (Exception $e) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
    }
}

/**
 * حفظ خدمات إدارة الصفحة
 */
function savePageServices() {
    global $db;
    
    header('Content-Type: application/json');
    
    try {
        // قراءة البيانات من الطلب
        $json = file_get_contents('php://input');
        $data = json_decode($json, true);
        
        if (!$data || !isset($data['client_id']) || !isset($data['services']) || !isset($data['total'])) {
            throw new Exception('بيانات غير صالحة');
        }
        
        $client_id = (int)$data['client_id'];
        $services = $data['services'];
        $total = (float)$data['total'];
        
        // التحقق من وجود العميل
        $query = "SELECT id FROM clients WHERE id = :id";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':id', $client_id);
        $stmt->execute();
        
        if ($stmt->rowCount() === 0) {
            throw new Exception('العميل غير موجود');
        }
        
        // بدء المعاملة
        $db->beginTransaction();
        
        try {
            // تحديث إجمالي إدارة الصفحة للعميل
            $query = "UPDATE clients SET page_management = :total WHERE id = :id";
            $stmt = $db->prepare($query);
            $stmt->bindParam(':total', $total);
            $stmt->bindParam(':id', $client_id);
            $stmt->execute();
            
            // حذف الخدمات الحالية للعميل
            $query = "DELETE FROM page_services WHERE client_id = :client_id";
            $stmt = $db->prepare($query);
            $stmt->bindParam(':client_id', $client_id);
            $stmt->execute();
            
            // إضافة الخدمات الجديدة
            if (!empty($services)) {
                $query = "INSERT INTO page_services (client_id, name, price) VALUES (:client_id, :name, :price)";
                $stmt = $db->prepare($query);
                
                foreach ($services as $service) {
                    $name = $service['name'];
                    $price = $service['price'];
                    
                    $stmt->bindParam(':client_id', $client_id);
                    $stmt->bindParam(':name', $name);
                    $stmt->bindParam(':price', $price);
                    $stmt->execute();
                }
            }
            
            // تأكيد المعاملة
            $db->commit();
            
            $response = [
                'success' => true,
                'message' => 'تم حفظ خدمات إدارة الصفحة بنجاح'
            ];
            
            echo json_encode($response);
        } catch (Exception $e) {
            // التراجع عن المعاملة في حالة حدوث خطأ
            $db->rollBack();
            throw $e;
        }
    } catch (Exception $e) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
    }
}
