<?php
// تضمين ملفات الإعدادات والاتصال بقاعدة البيانات
require_once '../config/config.php';
require_once '../includes/db.php';
require_once '../includes/functions.php';

// التحقق من طريقة الطلب
$method = $_SERVER['REQUEST_METHOD'];

// الحصول على البيانات المرسلة
$data = json_decode(file_get_contents('php://input'), true);

// التحقق من نوع الإجراء
if ($method === 'POST') {
    // إضافة دفعة جديدة
    if (isset($data['action']) && $data['action'] === 'add_payment') {
        addPayment($data['payment']);
    }
} elseif ($method === 'GET') {
    // جلب مدفوعات العميل
    if (isset($_GET['action']) && $_GET['action'] === 'get_client_payments') {
        getClientPayments($_GET['client_id'], $_GET['client_type']);
    }
    // جلب إجمالي مدفوعات العميل
    elseif (isset($_GET['action']) && $_GET['action'] === 'get_client_payments_total') {
        getClientPaymentsTotal($_GET['client_id'], $_GET['client_type']);
    }
}

/**
 * تسجيل الأخطاء في ملف
 *
 * @param string $message رسالة الخطأ
 * @param mixed $data البيانات المرتبطة بالخطأ
 */
function logError($message, $data = null) {
    $logFile = __DIR__ . '/../logs/payment_errors.log';
    $logDir = dirname($logFile);

    // إنشاء مجلد السجلات إذا لم يكن موجودًا
    if (!file_exists($logDir)) {
        mkdir($logDir, 0777, true);
    }

    // تنسيق رسالة الخطأ
    $logMessage = '[' . date('Y-m-d H:i:s') . '] ' . $message . "\n";
    if ($data !== null) {
        $logMessage .= 'Data: ' . print_r($data, true) . "\n";
    }
    $logMessage .= "--------------------\n";

    // كتابة رسالة الخطأ في ملف السجل
    file_put_contents($logFile, $logMessage, FILE_APPEND);
}

/**
 * إضافة دفعة جديدة
 *
 * @param array $payment بيانات الدفعة
 */
function addPayment($payment) {
    global $db;

    // تسجيل بيانات الدفعة
    logError('بيانات الدفعة المرسلة:', $payment);

    // التحقق من وجود جميع الحقول المطلوبة
    $requiredFields = ['client_id', 'client_type', 'date', 'amount', 'method'];
    foreach ($requiredFields as $field) {
        if (!isset($payment[$field]) || empty($payment[$field])) {
            logError('حقل مفقود في بيانات الدفعة:', [
                'field' => $field,
                'payment' => $payment
            ]);

            header('Content-Type: application/json');
            echo json_encode([
                'success' => false,
                'message' => 'حقل مفقود في بيانات الدفعة: ' . $field
            ]);
            return;
        }
    }

    try {
        // بدء المعاملة
        $db->beginTransaction();

        // إضافة الدفعة
        $query = "INSERT INTO payments (client_id, client_type, date, amount, payment_method, notes)
                  VALUES (:client_id, :client_type, :date, :amount, :payment_method, :notes)";
        $stmt = $db->prepare($query);

        // تحضير المتغيرات
        $clientId = $payment['client_id'];
        $clientType = $payment['client_type'];
        $date = $payment['date'];
        $amount = $payment['amount'];
        $method = $payment['method'];
        $notes = isset($payment['notes']) ? $payment['notes'] : '';

        // ربط المتغيرات
        $stmt->bindParam(':client_id', $clientId);
        $stmt->bindParam(':client_type', $clientType);
        $stmt->bindParam(':date', $date);
        $stmt->bindParam(':amount', $amount);
        $stmt->bindParam(':payment_method', $method);
        $stmt->bindParam(':notes', $notes);

        // تسجيل الاستعلام والقيم
        logError('استعلام إضافة الدفعة:', [
            'query' => $query,
            'values' => [
                'client_id' => $payment['client_id'],
                'client_type' => $payment['client_type'],
                'date' => $payment['date'],
                'amount' => $payment['amount'],
                'payment_method' => $payment['method'],
                'notes' => $payment['notes'] ?? ''
            ]
        ]);

        $stmt->execute();

        // الحصول على معرف الدفعة
        $paymentId = $db->lastInsertId();

        // تأكيد المعاملة
        $db->commit();

        // إرجاع رسالة نجاح
        header('Content-Type: application/json');
        echo json_encode([
            'success' => true,
            'message' => 'تمت إضافة الدفعة بنجاح',
            'payment_id' => $paymentId
        ]);
    } catch (PDOException $e) {
        // التراجع عن المعاملة في حالة حدوث خطأ
        $db->rollBack();

        // تسجيل الخطأ
        logError('خطأ في قاعدة البيانات أثناء إضافة الدفعة:', [
            'error' => $e->getMessage(),
            'trace' => $e->getTraceAsString(),
            'payment' => $payment
        ]);

        // إرجاع رسالة خطأ
        header('Content-Type: application/json');
        echo json_encode([
            'success' => false,
            'message' => 'حدث خطأ أثناء إضافة الدفعة: ' . $e->getMessage()
        ]);
    }
}

/**
 * جلب مدفوعات العميل
 *
 * @param int $clientId معرف العميل
 * @param string $clientType نوع العميل
 */
function getClientPayments($clientId, $clientType) {
    global $db;

    try {
        // جلب مدفوعات العميل
        $query = "SELECT id, date, amount, payment_method as method, notes
                  FROM payments
                  WHERE client_id = :client_id AND client_type = :client_type
                  ORDER BY date DESC";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':client_id', $clientId);
        $stmt->bindParam(':client_type', $clientType);
        $stmt->execute();

        $payments = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // حساب إجمالي المدفوعات
        $total = 0;
        foreach ($payments as $payment) {
            $total += floatval($payment['amount']);
        }

        // إرجاع البيانات
        header('Content-Type: application/json');
        echo json_encode([
            'success' => true,
            'payments' => $payments,
            'total' => $total
        ]);
    } catch (PDOException $e) {
        // إرجاع رسالة خطأ
        header('Content-Type: application/json');
        echo json_encode([
            'success' => false,
            'message' => 'حدث خطأ أثناء جلب مدفوعات العميل: ' . $e->getMessage()
        ]);
    }
}

/**
 * جلب إجمالي مدفوعات العميل
 *
 * @param int $clientId معرف العميل
 * @param string $clientType نوع العميل
 */
function getClientPaymentsTotal($clientId, $clientType) {
    global $db;

    try {
        // حساب إجمالي مدفوعات العميل
        $query = "SELECT SUM(amount) as total
                  FROM payments
                  WHERE client_id = :client_id AND client_type = :client_type";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':client_id', $clientId);
        $stmt->bindParam(':client_type', $clientType);
        $stmt->execute();

        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        $total = $result['total'] ?? 0;

        // إرجاع البيانات
        header('Content-Type: application/json');
        echo json_encode([
            'success' => true,
            'total' => $total
        ]);
    } catch (PDOException $e) {
        // إرجاع رسالة خطأ
        header('Content-Type: application/json');
        echo json_encode([
            'success' => false,
            'message' => 'حدث خطأ أثناء حساب إجمالي مدفوعات العميل: ' . $e->getMessage()
        ]);
    }
}
