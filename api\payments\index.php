<?php
/**
 * API للتعامل مع المدفوعات
 */

// تضمين ملف الإعدادات
require_once '../../config/config.php';
require_once '../../config/database.php';

// إنشاء اتصال بقاعدة البيانات
$database = new Database();
$db = $database->getConnection();

// التحقق من وجود اتصال بقاعدة البيانات
if (!isset($db)) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'خطأ في الاتصال بقاعدة البيانات']);
    exit;
}

// التحقق من وجود معلمة الإجراء
if (!isset($_GET['action'])) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'لم يتم تحديد الإجراء']);
    exit;
}

// تنفيذ الإجراء المطلوب
$action = $_GET['action'];

switch ($action) {
    case 'get_payments':
        getPayments();
        break;
    case 'add_payment':
        addPayment();
        break;
    case 'pay_previous_month':
        payPreviousMonth();
        break;
    default:
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'إجراء غير صالح']);
        exit;
}

/**
 * الحصول على مدفوعات العميل
 */
function getPayments() {
    global $db;

    // التحقق من وجود معرف العميل
    if (!isset($_GET['client_id'])) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'لم يتم تحديد معرف العميل']);
        exit;
    }

    $client_id = $_GET['client_id'];

    try {
        // الحصول على معلومات العميل
        $query = "SELECT * FROM clients WHERE id = :client_id";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':client_id', $client_id);
        $stmt->execute();

        $client = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$client) {
            http_response_code(404);
            echo json_encode(['success' => false, 'message' => 'العميل غير موجود']);
            exit;
        }

        // الحصول على مدفوعات العميل
        $query = "SELECT * FROM payments WHERE client_id = :client_id ORDER BY date DESC";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':client_id', $client_id);
        $stmt->execute();

        $payments = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // حساب إجمالي المدفوعات
        $total_payments = 0;
        foreach ($payments as $payment) {
            $total_payments += $payment['amount'];
        }

        // إرجاع البيانات
        echo json_encode([
            'success' => true,
            'client' => $client,
            'payments' => $payments,
            'total_payments' => $total_payments
        ]);
    } catch (PDOException $e) {
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'خطأ في قاعدة البيانات: ' . $e->getMessage()]);
        exit;
    }
}

/**
 * إضافة دفعة جديدة
 */
function addPayment() {
    global $db;

    // التحقق من وجود البيانات المطلوبة
    if (!isset($_POST['client_id']) || !isset($_POST['date']) || !isset($_POST['amount']) || !isset($_POST['payment_method'])) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'البيانات غير مكتملة']);
        exit;
    }

    $client_id = $_POST['client_id'];
    $date = $_POST['date'];
    $amount = (float)$_POST['amount'];
    $payment_method = $_POST['payment_method'];

    // التحقق من صحة البيانات
    if (empty($date) || $amount <= 0 || empty($payment_method)) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'البيانات غير صحيحة']);
        exit;
    }

    try {
        // التحقق من وجود العميل
        $query = "SELECT * FROM clients WHERE id = :client_id";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':client_id', $client_id);
        $stmt->execute();

        $client = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$client) {
            http_response_code(404);
            echo json_encode(['success' => false, 'message' => 'العميل غير موجود']);
            exit;
        }

        // إضافة الدفعة
        $query = "INSERT INTO payments (client_id, date, amount, payment_method) VALUES (:client_id, :date, :amount, :payment_method)";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':client_id', $client_id);
        $stmt->bindParam(':date', $date);
        $stmt->bindParam(':amount', $amount);
        $stmt->bindParam(':payment_method', $payment_method);
        $stmt->execute();

        // تحديث إجمالي المدفوعات للعميل
        $query = "UPDATE clients SET payments = payments + :amount WHERE id = :client_id";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':amount', $amount);
        $stmt->bindParam(':client_id', $client_id);
        $stmt->execute();

        // تحديث إجمالي حساب العميل نهاية الشهر
        $query = "UPDATE clients SET end_month_balance = end_month_balance - :amount WHERE id = :client_id";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':amount', $amount);
        $stmt->bindParam(':client_id', $client_id);
        $stmt->execute();

        // الحصول على بيانات العميل المحدثة
        $query = "SELECT * FROM clients WHERE id = :client_id";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':client_id', $client_id);
        $stmt->execute();

        $updated_client = $stmt->fetch(PDO::FETCH_ASSOC);

        // إرجاع البيانات
        echo json_encode([
            'success' => true,
            'message' => 'تمت إضافة الدفعة بنجاح',
            'payment_id' => $db->lastInsertId(),
            'client_data' => $updated_client
        ]);
    } catch (PDOException $e) {
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'خطأ في قاعدة البيانات: ' . $e->getMessage()]);
        exit;
    }
}

/**
 * دفع المتبقي من الشهر السابق
 */
function payPreviousMonth() {
    global $db;

    // التحقق من وجود معرف العميل
    if (!isset($_POST['client_id'])) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'معرف العميل مطلوب']);
        exit;
    }

    $client_id = $_POST['client_id'];

    try {
        // الحصول على بيانات العميل
        $query = "SELECT * FROM clients WHERE id = :client_id";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':client_id', $client_id);
        $stmt->execute();

        $client = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$client) {
            http_response_code(404);
            echo json_encode(['success' => false, 'message' => 'العميل غير موجود']);
            exit;
        }

        // التحقق من وجود متبقي من الشهر السابق
        $previous_debt = isset($client['previous_debt']) ? (float)$client['previous_debt'] : 0;

        if ($previous_debt <= 0) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => 'لا يوجد متبقي من الشهر السابق']);
            exit;
        }

        // تحديد تاريخ الشهر السابق
        $lastMonth = date('Y-m-d', strtotime('first day of last month'));

        // إضافة دفعة بقيمة المتبقي من الشهر السابق
        $query = "INSERT INTO payments (client_id, date, amount, payment_method, notes) VALUES (:client_id, :date, :amount, :payment_method, :notes)";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':client_id', $client_id);
        $stmt->bindParam(':date', $lastMonth);
        $stmt->bindParam(':amount', $previous_debt);
        $payment_method = 'نقدي';
        $stmt->bindParam(':payment_method', $payment_method);
        $notes = 'دفع المتبقي من الشهر السابق';
        $stmt->bindParam(':notes', $notes);
        $stmt->execute();

        // تحديث إجمالي المدفوعات للعميل
        $query = "UPDATE clients SET payments = payments + :amount WHERE id = :client_id";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':amount', $previous_debt);
        $stmt->bindParam(':client_id', $client_id);
        $stmt->execute();

        // تحديث إجمالي حساب العميل نهاية الشهر
        $query = "UPDATE clients SET end_month_balance = end_month_balance - :amount WHERE id = :client_id";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':amount', $previous_debt);
        $stmt->bindParam(':client_id', $client_id);
        $stmt->execute();

        // تصفير المتبقي من الشهر السابق
        $query = "UPDATE clients SET previous_debt = 0, previous_balance = previous_balance - :amount WHERE id = :client_id";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':amount', $previous_debt);
        $stmt->bindParam(':client_id', $client_id);
        $stmt->execute();

        // الحصول على بيانات العميل المحدثة
        $query = "SELECT * FROM clients WHERE id = :client_id";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':client_id', $client_id);
        $stmt->execute();

        $updated_client = $stmt->fetch(PDO::FETCH_ASSOC);

        // إرجاع البيانات
        echo json_encode([
            'success' => true,
            'message' => 'تم دفع المتبقي من الشهر السابق بنجاح',
            'payment_id' => $db->lastInsertId(),
            'client_data' => $updated_client,
            'amount_paid' => $previous_debt
        ]);
    } catch (PDOException $e) {
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'خطأ في قاعدة البيانات: ' . $e->getMessage()]);
        exit;
    }
}