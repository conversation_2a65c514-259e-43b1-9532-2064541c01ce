<?php
// Include configuration
require_once '../../config/config.php';

// Include database connection
require_once '../../includes/db.php';

// Include helper functions
require_once '../../includes/functions.php';

// Include authentication functions
require_once '../../includes/auth.php';

// Check if user is logged in
if (!isLoggedIn()) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'يجب تسجيل الدخول للوصول إلى هذه الصفحة']);
    exit;
}

// Check if request method is POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'طريقة الطلب غير صحيحة']);
    exit;
}

// Get POST data
$ad_id = isset($_POST['ad_id']) ? intval($_POST['ad_id']) : 0;
$reach = isset($_POST['reach']) ? intval($_POST['reach']) : 0;
$engagement = isset($_POST['engagement']) ? intval($_POST['engagement']) : 0;
$messages = isset($_POST['messages']) ? intval($_POST['messages']) : 0;

// Validate data
if ($ad_id <= 0) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'معرف الإعلان غير صحيح']);
    exit;
}

try {
    // Check if ad_results table exists
    $tables = $db->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
    
    if (!in_array('ad_results', $tables)) {
        // Create ad_results table if it doesn't exist
        $db->exec("CREATE TABLE ad_results (
            id INT AUTO_INCREMENT PRIMARY KEY,
            ad_id INT NOT NULL,
            reach INT NOT NULL DEFAULT 0,
            engagement INT NOT NULL DEFAULT 0,
            messages INT NOT NULL DEFAULT 0,
            date DATE NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (ad_id) REFERENCES ads(id) ON DELETE CASCADE
        )");
    }

    // Check if ad exists
    $stmt = $db->prepare("SELECT id FROM ads WHERE id = :ad_id");
    $stmt->bindParam(':ad_id', $ad_id);
    $stmt->execute();
    
    if ($stmt->rowCount() === 0) {
        header('Content-Type: application/json');
        echo json_encode(['success' => false, 'message' => 'الإعلان غير موجود']);
        exit;
    }

    // Get current date
    $today = date('Y-m-d');

    // Insert results data
    $stmt = $db->prepare("INSERT INTO ad_results (ad_id, reach, engagement, messages, date) VALUES (:ad_id, :reach, :engagement, :messages, :date)");
    $stmt->bindParam(':ad_id', $ad_id);
    $stmt->bindParam(':reach', $reach);
    $stmt->bindParam(':engagement', $engagement);
    $stmt->bindParam(':messages', $messages);
    $stmt->bindParam(':date', $today);
    $stmt->execute();

    header('Content-Type: application/json');
    echo json_encode(['success' => true, 'message' => 'تم إضافة النتائج بنجاح']);

} catch (PDOException $e) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'حدث خطأ أثناء إضافة النتائج: ' . $e->getMessage()]);
}
