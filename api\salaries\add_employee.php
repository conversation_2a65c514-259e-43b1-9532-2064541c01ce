<?php
// تضمين ملف الإعدادات
require_once 'config/config.php';

// تضمين اتصال قاعدة البيانات
require_once 'includes/db.php';

// تضمين الدوال المساعدة
require_once 'includes/functions.php';

// تضمين دوال المصادقة
require_once 'includes/auth.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    $_SESSION['flash_message'] = 'يجب تسجيل الدخول للوصول إلى هذه الصفحة';
    $_SESSION['flash_type'] = 'danger';
    redirect(BASE_URL . 'index.php');
}

// التحقق من طريقة الطلب
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // التحقق من البيانات المطلوبة
    if (
        isset($_POST['employee_name']) && !empty($_POST['employee_name']) &&
        isset($_POST['amount']) && !empty($_POST['amount']) &&
        isset($_POST['payment_type']) && !empty($_POST['payment_type']) &&
        isset($_POST['role']) && !empty($_POST['role'])
    ) {
        try {
            // التحقق من وجود جدول المرتبات
            $checkTableQuery = "SHOW TABLES LIKE 'salaries'";
            $stmt = $db->prepare($checkTableQuery);
            $stmt->execute();
            $tableExists = $stmt->rowCount() > 0;
            
            if (!$tableExists) {
                // إنشاء جدول المرتبات إذا لم يكن موجودًا
                $createTableQuery = "CREATE TABLE salaries (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    employee_name VARCHAR(255) NOT NULL,
                    amount DECIMAL(10,2) NOT NULL,
                    payment_type ENUM('نقدي', 'VF') NOT NULL,
                    role VARCHAR(50) NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )";
                $db->exec($createTableQuery);
            }
            
            // إعداد البيانات للإدخال
            $employeeName = trim($_POST['employee_name']);
            $amount = floatval($_POST['amount']);
            $paymentType = $_POST['payment_type'];
            $role = $_POST['role'];
            
            // إدخال البيانات في قاعدة البيانات
            $insertQuery = "INSERT INTO salaries (employee_name, amount, payment_type, role) VALUES (?, ?, ?, ?)";
            $stmt = $db->prepare($insertQuery);
            $stmt->execute([$employeeName, $amount, $paymentType, $role]);
            
            // رسالة نجاح
            $_SESSION['flash_message'] = 'تمت إضافة الموظف بنجاح';
            $_SESSION['flash_type'] = 'success';
        } catch (PDOException $e) {
            // رسالة خطأ
            $_SESSION['flash_message'] = 'حدث خطأ أثناء إضافة الموظف: ' . $e->getMessage();
            $_SESSION['flash_type'] = 'danger';
        }
    } else {
        // رسالة خطأ في حالة عدم توفر البيانات المطلوبة 
        $_SESSION['flash_message'] = 'يرجى ملء جميع الحقول المطلوبة';
        $_SESSION['flash_type'] = 'danger';
    }
    
    // إعادة التوجيه إلى صفحة المرتبات
    redirect(BASE_URL . 'salaries.php');
} else {
    // إعادة التوجيه إلى صفحة المرتبات في حالة الوصول المباشر
    redirect(BASE_URL . 'salaries.php');
}
?>
