<?php
header('Content-Type: application/json');

// بيانات تجريبية للعملاء
$clients = [
    [
        'id' => 'client_1',
        'name' => 'عميل تجريبي 1',
        'type' => 'Client A',
        'type_display_name' => 'Client A',
        'type_color' => '#4a56e2',
        'source' => 'clients',
        'original_id' => 1
    ],
    [
        'id' => 'client_2',
        'name' => 'عميل تجريبي 2',
        'type' => 'Client B',
        'type_display_name' => 'Client B',
        'type_color' => '#28a745',
        'source' => 'clients',
        'original_id' => 2
    ],
    [
        'id' => 'ad_client_1',
        'name' => 'عميل إعلانات 1',
        'type' => 'VIP',
        'type_display_name' => 'VIP',
        'type_color' => '#ffc107',
        'source' => 'ad_clients',
        'original_id' => 1
    ]
];

// بيانات تجريبية للحسابات
$accounts = [
    [
        'id' => 1,
        'name' => 'حساب فيسبوك 1',
        'type' => 'Facebook',
        'balance' => 1000,
        'status' => 'نشط',
        'client_name' => 'عميل تجريبي 1',
        'client_id' => 1
    ],
    [
        'id' => 2,
        'name' => 'حساب جوجل 1',
        'type' => 'Google',
        'balance' => 2000,
        'status' => 'نشط',
        'client_name' => 'عميل تجريبي 2',
        'client_id' => 2
    ],
    [
        'id' => 3,
        'name' => 'حساب انستجرام 1',
        'type' => 'Instagram',
        'balance' => 1500,
        'status' => 'نشط',
        'client_name' => 'عميل إعلانات 1',
        'client_id' => 1
    ]
];

// فئات العملاء
$clientTypes = [
    ['id' => 1, 'name' => 'client_a', 'display_name' => 'Client A', 'color' => '#4a56e2'],
    ['id' => 2, 'name' => 'client_b', 'display_name' => 'Client B', 'color' => '#28a745'],
    ['id' => 3, 'name' => 'vip', 'display_name' => 'VIP', 'color' => '#ffc107']
];

echo json_encode([
    'success' => true,
    'clients' => $clients,
    'accounts' => $accounts,
    'client_types' => $clientTypes
]);
?>
