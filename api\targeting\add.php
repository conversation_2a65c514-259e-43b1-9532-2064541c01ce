<?php
// Include configuration
require_once '../../config/config.php';

// Include database connection
require_once '../../includes/db.php';

// Include helper functions
require_once '../../includes/functions.php';

// Include authentication functions
require_once '../../includes/auth.php';

// Check if user is logged in
if (!isLoggedIn()) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'يجب تسجيل الدخول للوصول إلى هذه الصفحة']);
    exit;
}

// Check if request method is POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'طريقة الطلب غير صحيحة']);
    exit;
}

// Get POST data
$ad_id = isset($_POST['ad_id']) ? intval($_POST['ad_id']) : 0;
$location = isset($_POST['location']) ? trim($_POST['location']) : '';
$age = isset($_POST['age']) ? trim($_POST['age']) : '';
$interests = isset($_POST['interests']) ? trim($_POST['interests']) : '';

// Validate data
if ($ad_id <= 0) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'معرف الإعلان غير صحيح']);
    exit;
}

if (empty($location)) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'يجب إدخال الموقع']);
    exit;
}

try {
    // Check if targeting table exists
    $tables = $db->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
    
    if (!in_array('targeting', $tables)) {
        // Create targeting table if it doesn't exist
        $db->exec("CREATE TABLE targeting (
            id INT AUTO_INCREMENT PRIMARY KEY,
            ad_id INT NOT NULL,
            location VARCHAR(255) NOT NULL,
            age VARCHAR(100),
            interests TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (ad_id) REFERENCES ads(id) ON DELETE CASCADE
        )");
    }

    // Check if ad exists
    $stmt = $db->prepare("SELECT id FROM ads WHERE id = :ad_id");
    $stmt->bindParam(':ad_id', $ad_id);
    $stmt->execute();
    
    if ($stmt->rowCount() === 0) {
        header('Content-Type: application/json');
        echo json_encode(['success' => false, 'message' => 'الإعلان غير موجود']);
        exit;
    }

    // Insert targeting data
    $stmt = $db->prepare("INSERT INTO targeting (ad_id, location, age, interests) VALUES (:ad_id, :location, :age, :interests)");
    $stmt->bindParam(':ad_id', $ad_id);
    $stmt->bindParam(':location', $location);
    $stmt->bindParam(':age', $age);
    $stmt->bindParam(':interests', $interests);
    $stmt->execute();

    header('Content-Type: application/json');
    echo json_encode(['success' => true, 'message' => 'تم إضافة الاستهداف بنجاح']);

} catch (PDOException $e) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'حدث خطأ أثناء إضافة الاستهداف: ' . $e->getMessage()]);
}
