<?php
// تضمين ملف الاتصال بقاعدة البيانات
require_once '../includes/db.php';

// تعيين نوع المحتوى
header('Content-Type: application/json');

try {
    // اختبار الاتصال بقاعدة البيانات
    $testQuery = "SELECT 1 as test";
    $testStmt = $db->prepare($testQuery);
    $testStmt->execute();
    $testResult = $testStmt->fetch(PDO::FETCH_ASSOC);
    
    // جلب العملاء من جدول clients
    $clientsQuery = "SELECT id, name, type FROM clients LIMIT 10";
    $clientsStmt = $db->prepare($clientsQuery);
    $clientsStmt->execute();
    $clients = $clientsStmt->fetchAll(PDO::FETCH_ASSOC);
    
    // جلب عملاء الإعلانات من جدول ad_clients
    $adClientsQuery = "SELECT id, name, type FROM ad_clients LIMIT 10";
    $adClientsStmt = $db->prepare($adClientsQuery);
    $adClientsStmt->execute();
    $adClients = $adClientsStmt->fetchAll(PDO::FETCH_ASSOC);
    
    // جلب الحسابات الإعلانية
    $accountsQuery = "SELECT id, name, type FROM ad_accounts LIMIT 10";
    $accountsStmt = $db->prepare($accountsQuery);
    $accountsStmt->execute();
    $accounts = $accountsStmt->fetchAll(PDO::FETCH_ASSOC);
    
    // جلب فئات العملاء
    $clientTypesQuery = "SELECT id, name, display_name FROM client_types LIMIT 10";
    $clientTypesStmt = $db->prepare($clientTypesQuery);
    $clientTypesStmt->execute();
    $clientTypes = $clientTypesStmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo json_encode([
        'success' => true,
        'database_connection' => $testResult,
        'clients_count' => count($clients),
        'clients' => $clients,
        'ad_clients_count' => count($adClients),
        'ad_clients' => $adClients,
        'accounts_count' => count($accounts),
        'accounts' => $accounts,
        'client_types_count' => count($clientTypes),
        'client_types' => $clientTypes
    ]);
    
} catch (PDOException $e) {
    echo json_encode([
        'success' => false,
        'error_type' => 'PDOException',
        'message' => $e->getMessage(),
        'code' => $e->getCode(),
        'file' => $e->getFile(),
        'line' => $e->getLine()
    ]);
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error_type' => 'Exception',
        'message' => $e->getMessage(),
        'code' => $e->getCode(),
        'file' => $e->getFile(),
        'line' => $e->getLine()
    ]);
}
?>
