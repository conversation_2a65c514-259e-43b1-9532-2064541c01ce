<?php
// تضمين ملف التكوين
require_once '../config/config.php';

// تضمين اتصال قاعدة البيانات
require_once '../includes/db.php';

// تضمين الدوال المساعدة
require_once '../includes/functions.php';

// تضمين دوال المصادقة
require_once '../includes/auth.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    header('Content-Type: application/json');
    echo json_encode([
        'success' => false,
        'message' => 'يجب تسجيل الدخول للوصول إلى هذه الواجهة'
    ]);
    exit;
}

// التحقق من طريقة الطلب
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    header('Content-Type: application/json');
    echo json_encode([
        'success' => false,
        'message' => 'طريقة الطلب غير صحيحة'
    ]);
    exit;
}

// الحصول على البيانات المرسلة
$data = json_decode(file_get_contents('php://input'), true);

if (!$data) {
    header('Content-Type: application/json');
    echo json_encode([
        'success' => false,
        'message' => 'بيانات غير صالحة'
    ]);
    exit;
}

// التحقق من وجود البيانات المطلوبة
if (!isset($data['client_id']) || !isset($data['commission_percentage'])) {
    header('Content-Type: application/json');
    echo json_encode([
        'success' => false,
        'message' => 'البيانات المطلوبة غير مكتملة'
    ]);
    exit;
}

// تحديث نسبة العمولة
try {
    // تحضير المتغيرات
    $clientId = $data['client_id'];
    $commissionPercentage = $data['commission_percentage'];
    
    // التحقق من وجود العميل
    $query = "SELECT id FROM ad_clients WHERE id = :id";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':id', $clientId);
    $stmt->execute();
    
    if ($stmt->rowCount() === 0) {
        header('Content-Type: application/json');
        echo json_encode([
            'success' => false,
            'message' => 'العميل غير موجود'
        ]);
        exit;
    }
    
    // تحديث نسبة العمولة
    $query = "UPDATE ad_clients SET commission_percentage = :commission_percentage WHERE id = :id";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':commission_percentage', $commissionPercentage);
    $stmt->bindParam(':id', $clientId);
    $stmt->execute();
    
    // إرجاع النجاح
    header('Content-Type: application/json');
    echo json_encode([
        'success' => true,
        'message' => 'تم تحديث نسبة العمولة بنجاح'
    ]);
} catch (PDOException $e) {
    // إرجاع رسالة الخطأ
    header('Content-Type: application/json');
    echo json_encode([
        'success' => false,
        'message' => 'خطأ في قاعدة البيانات: ' . $e->getMessage()
    ]);
}
?>
