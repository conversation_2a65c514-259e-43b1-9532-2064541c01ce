<?php
/**
 * API لإضافة معاملة متوقعة في الجدول المخصص
 */

// إعداد الاستجابة JSON
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// التحقق من طريقة الطلب
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'طريقة الطلب غير مسموحة']);
    exit;
}

// تضمين ملفات الاتصال بقاعدة البيانات
require_once '../../config/database.php';

try {
    // إنشاء اتصال بقاعدة البيانات
    $database = new Database();
    $db = $database->getConnection();

    // قراءة البيانات المرسلة
    $data = json_decode(file_get_contents('php://input'), true);

    // التحقق من البيانات المطلوبة
    if (!isset($data['card_id']) || !isset($data['payment_date']) || 
        !isset($data['account_name']) || !isset($data['expected_amount'])) {
        echo json_encode(['success' => false, 'message' => 'بيانات غير مكتملة']);
        exit;
    }

    $cardId = intval($data['card_id']);
    $paymentDate = htmlspecialchars(strip_tags($data['payment_date']));
    $accountName = htmlspecialchars(strip_tags($data['account_name']));
    $expectedAmount = floatval($data['expected_amount']);
    $description = isset($data['description']) ? htmlspecialchars(strip_tags($data['description'])) : 'معاملة فيزا متوقعة';

    // التحقق من صحة البيانات
    if ($cardId <= 0) {
        echo json_encode(['success' => false, 'message' => 'معرف البطاقة غير صحيح']);
        exit;
    }

    if (empty($paymentDate)) {
        echo json_encode(['success' => false, 'message' => 'تاريخ الدفع مطلوب']);
        exit;
    }

    if (empty($accountName)) {
        echo json_encode(['success' => false, 'message' => 'اسم الحساب مطلوب']);
        exit;
    }

    if ($expectedAmount <= 0) {
        echo json_encode(['success' => false, 'message' => 'المبلغ المتوقع يجب أن يكون أكبر من صفر']);
        exit;
    }

    // بدء المعاملة
    $db->beginTransaction();

    // التحقق من وجود البطاقة
    $stmt = $db->prepare("SELECT * FROM visa_cards WHERE id = ?");
    $stmt->execute([$cardId]);
    $card = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$card) {
        $db->rollBack();
        echo json_encode(['success' => false, 'message' => 'بطاقة الفيزا غير موجودة']);
        exit;
    }

    // إضافة المعاملة المتوقعة
    $stmt = $db->prepare("
        INSERT INTO visa_expected_transactions 
        (visa_card_id, payment_date, account_name, expected_amount, description, status) 
        VALUES (?, ?, ?, ?, ?, 'نشط')
    ");
    
    $stmt->execute([
        $cardId,
        $paymentDate,
        $accountName,
        $expectedAmount,
        $description
    ]);
    
    $transactionId = $db->lastInsertId();

    // تحديث رصيد البطاقة (خصم المبلغ المتوقع من الرصيد المتبقي)
    $newRemainingBalance = $card['remaining_balance'] - $expectedAmount;
    $stmt = $db->prepare("UPDATE visa_cards SET remaining_balance = ? WHERE id = ?");
    $stmt->execute([$newRemainingBalance, $cardId]);

    // إنهاء المعاملة
    $db->commit();

    // إرجاع البيانات
    echo json_encode([
        'success' => true,
        'message' => 'تم إضافة المعاملة المتوقعة بنجاح',
        'transaction' => [
            'id' => $transactionId,
            'visa_card_id' => $cardId,
            'payment_date' => $paymentDate,
            'account_name' => $accountName,
            'expected_amount' => $expectedAmount,
            'description' => $description,
            'new_remaining_balance' => $newRemainingBalance
        ]
    ]);

} catch (PDOException $e) {
    // التراجع عن المعاملة في حالة حدوث خطأ
    if ($db->inTransaction()) {
        $db->rollBack();
    }
    
    echo json_encode([
        'success' => false,
        'message' => 'حدث خطأ في قاعدة البيانات: ' . $e->getMessage()
    ]);
} catch (Exception $e) {
    // التراجع عن المعاملة في حالة حدوث خطأ
    if ($db->inTransaction()) {
        $db->rollBack();
    }
    
    echo json_encode([
        'success' => false,
        'message' => 'حدث خطأ: ' . $e->getMessage()
    ]);
}
?>
