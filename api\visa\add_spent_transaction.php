<?php
/**
 * API لإضافة مبلغ مصروف في الجدول المخصص
 */

// إعداد الاستجابة JSON
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// التحقق من طريقة الطلب
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'طريقة الطلب غير مسموحة']);
    exit;
}

// تضمين ملفات الاتصال بقاعدة البيانات
require_once '../../config/database.php';

try {
    // إنشاء اتصال بقاعدة البيانات
    $database = new Database();
    $db = $database->getConnection();

    // قراءة البيانات المرسلة
    $data = json_decode(file_get_contents('php://input'), true);

    // التحقق من البيانات المطلوبة
    if (!isset($data['card_id']) || !isset($data['spent_date']) ||
        !isset($data['account_name']) || !isset($data['spent_amount'])) {
        echo json_encode(['success' => false, 'message' => 'بيانات غير مكتملة']);
        exit;
    }

    $cardId = intval($data['card_id']);
    $spentDate = htmlspecialchars(strip_tags($data['spent_date']));
    $accountName = htmlspecialchars(strip_tags($data['account_name']));
    $spentAmount = floatval($data['spent_amount']);
    $description = isset($data['description']) ? htmlspecialchars(strip_tags($data['description'])) : 'مبلغ مصروف من الفيزا';
    $transactionType = isset($data['transaction_type']) ? htmlspecialchars(strip_tags($data['transaction_type'])) : 'صرف عادي';
    $isInternational = isset($data['is_international']) ? (bool)$data['is_international'] : ($transactionType == 'صرف دولي');

    // التحقق من صحة البيانات
    if ($cardId <= 0) {
        echo json_encode(['success' => false, 'message' => 'معرف البطاقة غير صحيح']);
        exit;
    }

    if (empty($spentDate)) {
        echo json_encode(['success' => false, 'message' => 'تاريخ الصرف مطلوب']);
        exit;
    }

    if (empty($accountName)) {
        echo json_encode(['success' => false, 'message' => 'اسم الحساب مطلوب']);
        exit;
    }

    if ($spentAmount <= 0) {
        echo json_encode(['success' => false, 'message' => 'المبلغ المصروف يجب أن يكون أكبر من صفر']);
        exit;
    }

    // التحقق من نوع المعاملة
    $allowedTypes = ['صرف عادي', 'صرف دولي', 'رسوم', 'أخرى'];
    if (!in_array($transactionType, $allowedTypes)) {
        $transactionType = 'صرف عادي';
    }

    // بدء المعاملة
    $db->beginTransaction();

    // التحقق من وجود البطاقة
    $stmt = $db->prepare("SELECT * FROM visa_cards WHERE id = ?");
    $stmt->execute([$cardId]);
    $card = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$card) {
        $db->rollBack();
        echo json_encode(['success' => false, 'message' => 'بطاقة الفيزا غير موجودة']);
        exit;
    }

    // البحث عن معاملة موجودة لتحديث المبلغ المصروف أو إنشاء واحدة جديدة
    $stmt = $db->prepare("
        SELECT * FROM visa_spent_transactions
        WHERE visa_card_id = ? AND spent_date = ? AND account_name = ?
        ORDER BY id DESC LIMIT 1
    ");
    $stmt->execute([$cardId, $spentDate, $accountName]);
    $existingTransaction = $stmt->fetch(PDO::FETCH_ASSOC);

    if ($existingTransaction) {
        // تحديث المعاملة الموجودة
        $newSpentAmount = $existingTransaction['spent_amount'] + $spentAmount;
        $stmt = $db->prepare("
            UPDATE visa_spent_transactions
            SET spent_amount = ?, description = ?, updated_at = NOW()
            WHERE id = ?
        ");
        $stmt->execute([$newSpentAmount, $description, $existingTransaction['id']]);
        $transactionId = $existingTransaction['id'];
        $finalSpentAmount = $newSpentAmount;
    } else {
        // إضافة معاملة جديدة
        $stmt = $db->prepare("
            INSERT INTO visa_spent_transactions
            (visa_card_id, spent_date, account_name, spent_amount, description, transaction_type, is_international, status)
            VALUES (?, ?, ?, ?, ?, ?, ?, 'نشط')
        ");

        $stmt->execute([
            $cardId,
            $spentDate,
            $accountName,
            $spentAmount,
            $description,
            $transactionType,
            $isInternational
        ]);

        $transactionId = $db->lastInsertId();
        $finalSpentAmount = $spentAmount;
    }

    // تحديث إجمالي المديونية في البطاقة
    $newTotalDebt = $card['total_debt'] + $spentAmount;
    $stmt = $db->prepare("UPDATE visa_cards SET total_debt = ? WHERE id = ?");
    $stmt->execute([$newTotalDebt, $cardId]);

    // إذا كانت المعاملة دولية، تحقق من الحد الدولي
    $alertMessage = null;
    if ($isInternational) {
        try {
            // استدعاء إجراء التحقق من التنبيهات
            $stmt = $db->prepare("CALL check_international_limits(?)");
            $stmt->execute([$cardId]);

            // جلب آخر تنبيه للبطاقة
            $stmt = $db->prepare("
                SELECT * FROM visa_international_alerts
                WHERE visa_card_id = ?
                ORDER BY created_at DESC
                LIMIT 1
            ");
            $stmt->execute([$cardId]);
            $latestAlert = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($latestAlert && $latestAlert['percentage_used'] >= 50) {
                $alertMessage = $latestAlert['message'];
            }
        } catch (PDOException $e) {
            // في حالة فشل التحقق من التنبيهات، نكمل العملية
            error_log("فشل في التحقق من التنبيهات الدولية: " . $e->getMessage());
        }
    }

    // إنهاء المعاملة
    $db->commit();

    // إرجاع البيانات
    $response = [
        'success' => true,
        'message' => 'تم إضافة المبلغ المصروف بنجاح',
        'transaction' => [
            'id' => $transactionId,
            'visa_card_id' => $cardId,
            'spent_date' => $spentDate,
            'account_name' => $accountName,
            'spent_amount' => $finalSpentAmount,
            'description' => $description,
            'transaction_type' => $transactionType,
            'is_international' => $isInternational,
            'new_total_debt' => $newTotalDebt,
            'is_updated' => isset($existingTransaction)
        ]
    ];

    // إضافة تنبيه الحد الدولي إذا وجد
    if ($alertMessage) {
        $response['international_alert'] = $alertMessage;
        $response['message'] .= ' - ' . $alertMessage;
    }

    echo json_encode($response);

} catch (PDOException $e) {
    // التراجع عن المعاملة في حالة حدوث خطأ
    if ($db->inTransaction()) {
        $db->rollBack();
    }

    echo json_encode([
        'success' => false,
        'message' => 'حدث خطأ في قاعدة البيانات: ' . $e->getMessage()
    ]);
} catch (Exception $e) {
    // التراجع عن المعاملة في حالة حدوث خطأ
    if ($db->inTransaction()) {
        $db->rollBack();
    }

    echo json_encode([
        'success' => false,
        'message' => 'حدث خطأ: ' . $e->getMessage()
    ]);
}
?>
