<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// التعامل مع طلبات OPTIONS
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// تضمين ملف الاتصال بقاعدة البيانات
require_once '../../config/database.php';

try {
    // إنشاء اتصال بقاعدة البيانات
    $database = new Database();
    $db = $database->getConnection();
    
    if (!$db) {
        throw new Exception('فشل الاتصال بقاعدة البيانات');
    }

    // التحقق من طريقة الطلب
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('طريقة الطلب غير مدعومة');
    }

    // قراءة البيانات المرسلة
    $input = file_get_contents('php://input');
    $data = json_decode($input, true);

    if (!$data) {
        throw new Exception('بيانات غير صحيحة');
    }

    // التحقق من وجود الحقول المطلوبة
    if (!isset($data['phone_number']) || empty(trim($data['phone_number']))) {
        throw new Exception('رقم الهاتف مطلوب');
    }

    $phoneNumber = trim($data['phone_number']);
    $balance = isset($data['balance']) ? floatval($data['balance']) : 0.00;

    // إدراج الحساب الجديد
    $stmt = $db->prepare("
        INSERT INTO vodafone_cash (phone_number, balance, account_type) 
        VALUES (?, ?, 'personal')
    ");
    $stmt->execute([$phoneNumber, $balance]);

    $newAccountId = $db->lastInsertId();

    // إرسال استجابة نجح
    echo json_encode([
        'success' => true,
        'message' => 'تم إضافة الحساب بنجاح',
        'account_id' => $newAccountId,
        'phone_number' => $phoneNumber,
        'balance' => $balance
    ], JSON_UNESCAPED_UNICODE);

} catch (Exception $e) {
    // إرسال استجابة خطأ
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}
?>
