<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// التعامل مع طلبات OPTIONS
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// تضمين ملف الاتصال بقاعدة البيانات
require_once '../../config/database.php';

try {
    // إنشاء اتصال بقاعدة البيانات
    $database = new Database();
    $db = $database->getConnection();
    
    if (!$db) {
        throw new Exception('فشل الاتصال بقاعدة البيانات');
    }

    // إنشاء الجدول إذا لم يكن موجوداً
    $db->exec("
        CREATE TABLE IF NOT EXISTS vodafone_cash (
            id INT AUTO_INCREMENT PRIMARY KEY,
            phone_number VARCHAR(20) NOT NULL,
            balance DECIMAL(10,2) NOT NULL DEFAULT 0.00,
            account_type ENUM('main', 'personal') DEFAULT 'personal',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )
    ");

    // التحقق من وجود بيانات
    $stmt = $db->prepare("SELECT COUNT(*) FROM vodafone_cash");
    $stmt->execute();
    $count = $stmt->fetchColumn();

    if ($count == 0) {
        // إدراج بيانات افتراضية
        $stmt = $db->prepare("
            INSERT INTO vodafone_cash (phone_number, balance, account_type) VALUES
            ('***********', 16640.00, 'personal'),
            ('***********', 16640.00, 'personal'),
            ('***********', 16640.00, 'personal'),
            ('***********', 16640.00, 'personal'),
            ('***********', 16640.00, 'personal'),
            ('***********', 16640.00, 'personal'),
            ('***********', 16640.00, 'personal')
        ");
        $stmt->execute();
    }

    // جلب جميع الحسابات
    $stmt = $db->prepare("SELECT * FROM vodafone_cash ORDER BY phone_number");
    $stmt->execute();
    $accounts = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // حساب الإجمالي
    $totalBalance = 0;
    foreach ($accounts as $account) {
        $totalBalance += floatval($account['balance']);
    }

    // إرسال البيانات
    echo json_encode([
        'success' => true,
        'accounts' => $accounts,
        'total_balance' => $totalBalance
    ], JSON_UNESCAPED_UNICODE);

} catch (Exception $e) {
    // إرسال استجابة خطأ
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage(),
        'accounts' => [],
        'total_balance' => 0
    ], JSON_UNESCAPED_UNICODE);
}
?>
