/**
 * تنسيقات صفحة إدارة الحسابات الإعلانية
 */

/* تنسيقات عامة */
body {
    font-family: 'Cairo', sans-serif;
    background-color: #f8f9fa;
}

.card {
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

.card-header {
    background-color: #4a56e2;
    color: white;
    border-radius: 10px 10px 0 0 !important;
    padding: 15px 20px;
}

.card-body {
    padding: 20px;
}

/* تنسيقات البحث */
.search-container {
    margin-bottom: 20px;
}

.search-container .input-group {
    width: 100%;
}

.search-container .input-group-text {
    background-color: #4a56e2;
    color: white;
    border: none;
}

.search-container .form-control {
    border-color: #4a56e2;
}

.search-container .form-control:focus {
    box-shadow: 0 0 0 0.25rem rgba(74, 86, 226, 0.25);
}

/* تنسيقات الجدول */
.table {
    width: 100%;
    margin-bottom: 0;
}

.table th {
    background-color: #f0f0f0;
    color: #333;
    font-weight: 600;
    text-align: center;
    vertical-align: middle;
    padding: 12px;
}

.table td {
    text-align: center;
    vertical-align: middle;
    padding: 12px;
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(74, 86, 226, 0.05);
}

.table-hover tbody tr:hover {
    background-color: rgba(74, 86, 226, 0.1);
}

/* تنسيقات الأزرار */
.btn-primary {
    background-color: #4a56e2;
    border-color: #4a56e2;
}

.btn-primary:hover, .btn-primary:focus {
    background-color: #3a46d2;
    border-color: #3a46d2;
}

.btn-danger {
    background-color: #dc3545;
    border-color: #dc3545;
}

.btn-danger:hover, .btn-danger:focus {
    background-color: #bb2d3b;
    border-color: #bb2d3b;
}

.btn-group .btn {
    margin-right: 5px;
}

.btn-group .btn:last-child {
    margin-right: 0;
}

/* تنسيقات الشارات */
.badge {
    padding: 6px 10px;
    font-size: 12px;
    font-weight: 500;
    border-radius: 4px;
}

.bg-success {
    background-color: #28a745 !important;
}

.bg-danger {
    background-color: #dc3545 !important;
}

/* تنسيقات النوافذ المنبثقة */
.modal-header {
    background-color: #4a56e2;
    color: white;
    border-bottom: none;
    border-radius: 10px 10px 0 0;
}

.modal-content {
    border: none;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.modal-footer {
    border-top: none;
    padding: 15px 20px;
}

.btn-close {
    color: white;
    opacity: 1;
}

/* تنسيقات النماذج */
.form-label {
    font-weight: 600;
    margin-bottom: 8px;
}

.form-control {
    padding: 10px 12px;
    border-radius: 5px;
    border: 1px solid #ced4da;
}

.form-control:focus {
    border-color: #4a56e2;
    box-shadow: 0 0 0 0.25rem rgba(74, 86, 226, 0.25);
}

/* تنسيقات للشاشات الصغيرة */
@media (max-width: 768px) {
    .card-header {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .card-header button {
        margin-top: 10px;
        width: 100%;
    }
    
    .table-responsive {
        overflow-x: auto;
    }
    
    .table th, .table td {
        padding: 8px;
        font-size: 14px;
    }
    
    .btn-group .btn {
        padding: 4px 8px;
    }
}
