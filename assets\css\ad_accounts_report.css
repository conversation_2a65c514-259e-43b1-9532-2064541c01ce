/**
 * تنسيقات صفحة تقرير حسابات الإعلانات
 */

/* تنسيقات عامة */
body {
    font-family: 'Cairo', sans-serif;
    background-color: white;
    margin: 0;
    padding: 0;
}

.container-fluid {
    padding: 0;
}

.main-container {
    width: 100%;
    max-width: 100%;
    padding: 0;
    margin: 0;
}

/* تنسيقات الهيدر */
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 20px;
    background-color: white;
    border-bottom: 1px solid #eee;
}

.logo-container {
    display: flex;
    align-items: center;
}

.logo {
    height: 50px;
    margin-right: 20px;
}

.page-title {
    color: #4a56e2;
    font-size: 24px;
    font-weight: 600;
    text-align: center;
    margin: 0;
}

.back-btn {
    background-color: transparent;
    color: #4a56e2;
    border: none;
    padding: 8px 15px;
    font-size: 16px;
    cursor: pointer;
    display: flex;
    align-items: center;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
}

.back-btn:hover {
    color: #3a46d2;
    text-decoration: underline;
}

.back-btn i {
    margin-left: 8px;
    font-size: 20px;
}

/* تنسيقات البحث */
.search-section {
    display: flex;
    align-items: center;
    padding: 10px 20px;
    background-color: white;
    border-bottom: 1px solid #eee;
}

.filter-icon {
    color: #4a56e2;
    font-size: 24px;
    margin-right: 15px;
}

.client-filter-container {
    position: relative;
    margin-right: 15px;
}

.client-filter-dropdown {
    background-color: white;
    border: 2px solid #4a56e2;
    border-radius: 8px;
    padding: 8px 35px 8px 15px;
    font-size: 16px;
    color: #4a56e2;
    font-weight: 600;
    cursor: pointer;
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    min-width: 150px;
    outline: none;
    transition: all 0.3s ease;
}

.client-filter-dropdown:hover {
    background-color: #f8f9ff;
    border-color: #3a46d2;
}

.client-filter-dropdown:focus {
    border-color: #3a46d2;
    box-shadow: 0 0 0 3px rgba(74, 86, 226, 0.1);
}

.dropdown-icon {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: #4a56e2;
    font-size: 12px;
    pointer-events: none;
}

.search-container {
    flex-grow: 1;
    position: relative;
}

.search-input {
    width: 100%;
    padding: 8px 15px;
    border: 1px solid #ddd;
    border-radius: 20px;
    font-size: 14px;
}

.search-icon {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: #999;
}

/* تنسيقات الجدول */
.table-container {
    width: 100%;
    height: calc(100vh - 150px); /* ارتفاع ثابت مع مراعاة الهيدر والبحث */
    overflow: hidden;
    position: relative;
    border: 1px solid #dcf343;
    display: flex;
    flex-direction: column;
}

.ad-accounts-table {
    width: 100%;
    border-collapse: collapse;
    border-spacing: 0;
    table-layout: fixed;
}

/* تنسيق الهيدر */
.ad-accounts-table th {
    background-color: #4a56e2;
    color: white;
    text-align: center;
    padding: 12px;
    font-weight: 600;
    border: 1px solid #dcf343;
}

/* تنسيق الجزء المتحرك (tbody) */
.table-body-container {
    flex: 1;
    overflow-y: auto;
    border-top: none;
    border-bottom: none;
}

.table-body-container .ad-accounts-table {
    border-top: none;
    border-bottom: none;
}

.ad-accounts-table td {
    padding: 10px;
    text-align: center;
    border: 1px solid #dcf343;
    color: #4a56e2;
}

.ad-accounts-table tr:nth-child(even) td {
    background-color: rgba(220, 243, 67, 0.05);
}

/* تنسيق الفوتر */
.footer-summary {
    display: flex;
    justify-content: space-between;
    margin-top: 20px;
    border-top: 2px solid #dcf343;
    padding-top: 10px;
    direction: rtl;
    background-color: white;
}

.footer-item {
    flex: 1;
    text-align: center;
    border-left: 1px solid #dcf343;
    padding: 10px 5px;
}

.footer-item:last-child {
    border-left: none;
}

.footer-label {
    color: #4a56e2;
    font-weight: 700;
    font-size: 14px;
    margin-bottom: 5px;
}

.footer-value {
    color: #4a56e2;
    font-weight: 700;
    font-size: 16px;
}

.footer-value.negative-value {
    color: red;
}

.footer-value.positive-value {
    color: green;
}

.account-name {
    color: #4a56e2;
    font-weight: 600;
}

.amount {
    font-weight: 600;
}

.amount.positive {
    color: green;
    font-weight: bold;
}

.amount.negative {
    color: red;
    font-weight: bold;
}

/* تنسيقات الإجماليات */
.totals-row {
    background-color: #f8f9fa;
}

.totals-row td {
    font-weight: 700;
    padding: 15px 10px;
    border-top: 2px solid #dcf343;
}

.totals-label {
    color: #4a56e2;
    text-align: center;
}

.totals-value {
    color: #4a56e2;
}

.totals-value.positive {
    color: green;
}

.totals-value.negative {
    color: red;
}

.grand-total-label {
    color: #4a56e2;
    font-weight: 700;
    text-align: right;
}
