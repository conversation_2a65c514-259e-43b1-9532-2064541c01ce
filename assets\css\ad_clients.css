/* CSS لصفحة عملاء الإعلانات */

/* تنسيقات الهيدر */
body {
    background-color: white;
    margin: 0;
    padding: 0;
    font-family: 'Cairo', sans-serif;
    overflow-x: hidden;
}

.page-container {
    width: 100%;
    margin: 0;
    padding: 0 15px;
}

.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 0;
    border-bottom: 1px solid #eee;
    margin-bottom: 20px;
}

.logo-link {
    display: block;
    transition: all 0.3s ease;
}

.logo-link:hover {
    transform: scale(1.05);
}

.page-logo {
    max-width: 80px;
    height: auto;
    cursor: pointer;
}

.page-title {
    color: #4a56e2;
    font-size: 24px;
    font-weight: 600;
    text-align: center;
    flex-grow: 1;
}

.back-btn {
    background-color: transparent;
    color: #4a56e2;
    border: none;
    padding: 8px 15px;
    font-size: 16px;
    cursor: pointer;
    display: flex;
    align-items: center;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
}

.back-btn:hover {
    color: #3a46d2;
    text-decoration: underline;
}

.back-btn i {
    margin-left: 8px;
    font-size: 20px;
}

.content-area {
    width: 100%;
    padding: 0;
}

/* تنسيقات عملاء الإعلانات */
.ad-clients-container {
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    padding: 20px;
    margin-bottom: 30px;
}

.ad-clients-header {
    margin-bottom: 20px;
}

.search-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 15px;
}

.filter-icon {
    color: #4a56e2;
    font-size: 24px;
    margin-right: 15px;
}

.client-filter-container {
    position: relative;
    margin-right: 15px;
}

.client-filter-dropdown {
    background-color: white;
    border: 2px solid #4a56e2;
    border-radius: 8px;
    padding: 8px 35px 8px 15px;
    font-size: 16px;
    color: #4a56e2;
    font-weight: 600;
    cursor: pointer;
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    min-width: 150px;
    outline: none;
    transition: all 0.3s ease;
}

.client-filter-dropdown:hover {
    background-color: #f8f9ff;
    border-color: #3a46d2;
}

.client-filter-dropdown:focus {
    border-color: #3a46d2;
    box-shadow: 0 0 0 3px rgba(74, 86, 226, 0.1);
}

.dropdown-icon {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: #4a56e2;
    font-size: 12px;
    pointer-events: none;
}

.search-box {
    position: relative;
    width: 250px;
}

.search-input {
    width: 100%;
    padding: 10px 35px 10px 15px;
    border: 1px solid #ddd;
    border-radius: 20px;
    font-size: 14px;
}

.search-icon {
    position: absolute;
    left: 10px;
    top: 50%;
    transform: translateY(-50%);
    color: #4a56e2;
}

.add-client-btn {
    background-color: #4a56e2;
    color: white;
    border: none;
    border-radius: 20px;
    padding: 8px 20px;
    font-size: 14px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.add-client-btn:hover {
    background-color: #3a46d2;
    transform: translateY(-2px);
}

.ad-accounts-report-btn {
    background-color: #dcf343;
    color: #4a56e2;
    border: none;
    border-radius: 20px;
    padding: 8px 20px;
    font-size: 14px;
    font-weight: 600;
    transition: all 0.3s ease;
    margin-right: 10px;
}

.ad-accounts-report-btn:hover {
    background-color: #c9e030;
    color: #3a46d2;
    transform: translateY(-2px);
}

.manage-types-btn {
    background-color: #ffc107;
    color: #212529;
    border: none;
    border-radius: 20px;
    padding: 8px 20px;
    font-size: 14px;
    font-weight: 600;
    transition: all 0.3s ease;
    margin-right: 10px;
    text-decoration: none;
}

.manage-types-btn:hover {
    background-color: #e0a800;
    color: #1e2125;
    transform: translateY(-2px);
    text-decoration: none;
}

/* تنسيقات محتوى عملاء الإعلانات */
.ad-clients-content {
    margin-top: 20px;
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
}

.loading-message {
    text-align: center;
    padding: 30px;
    color: #4a56e2;
    font-weight: 600;
    width: 100%;
}

/* تنسيقات بطاقة العميل */
.client-card {
    background-color: #fff;
    border: 1px solid #dcf343;
    border-radius: 0;
    overflow: hidden;
    width: calc(50% - 10px);
    margin-bottom: 20px;
    box-shadow: none;
}

.client-header {
    background-color: #4a56e2;
    color: white;
    padding: 10px 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
    border-radius: 0;
    border-bottom: 1px solid #dcf343;
}

.client-name {
    font-weight: 600;
    font-size: 16px;
    text-align: center;
    flex-grow: 1;
}

.client-label {
    font-weight: 600;
    font-size: 16px;
    text-align: left;
    color: white;
    width: 100px;
}

.ads-label {
    cursor: pointer;
}

.ads-label:hover {
    text-decoration: underline;
}

.client-toggle-icon {
    transition: transform 0.3s ease;
    width: 30px;
}

.client-header:not(.collapsed) .client-toggle-icon {
    transform: rotate(180deg);
}

.client-body {
    padding: 0;
}

/* تنسيقات جدول الإعلانات */
.ads-table-container {
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid #dcf343;
    margin-bottom: 0;

    /* إخفاء السكروول */
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none;  /* Internet Explorer 10+ */
}

.ads-table-container::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
}


.ads-table {
    width: 100%;
    border-collapse: collapse;
    border-spacing: 0;
    table-layout: fixed;
}

.ads-table th {
    background-color: #4a56e2;
    color: white;
    text-align: center;
    padding: 8px;
    font-weight: 600;
    font-size: 14px;
    border: none;
    white-space: nowrap;
    position: sticky;
    top: 0;
    z-index: 10;
}

.ads-table td {
    text-align: center;
    padding: 8px;
    border: 1px solid #dcf343;
    font-size: 14px;
    background-color: white;
}

.ads-table tr:nth-child(even) td {
    background-color: white;
}

.ads-table tr:hover td {
    background-color: #f8f9fa;
}

.exchange-rate {
    color: #dcf343;
    font-weight: bold;
}

/* تنسيقات الإجماليات */
.totals-section {
    width: 100%;
    border-top: none;
}

.totals-table {
    width: 100%;
    border-collapse: collapse;
    border-spacing: 0;
    table-layout: fixed;
}

.totals-table td {
    text-align: center;
    padding: 8px;
    border: 1px solid #dcf343;
    font-size: 14px;
    background-color: white;
    color: #4a56e2;
    font-weight: 600;
    height: 40px;
    vertical-align: middle;
}

.totals-label {
    background-color: white !important;
    color: #4a56e2 !important;
    font-weight: 600;
    text-align: right !important;
        width: 10%;
}

.totals-value {
    background-color: white !important;
    color: #4a56e2 !important;
    font-weight: 600;
    width: 11%;
}

.add-ad-cell {
    width: 5%;
    background-color: white !important;
    text-align: center !important;
}

.add-ad-label {
    background-color: white !important;
    color: #4a56e2 !important;
    font-weight: 600;
    text-align: right !important;
    width: 20%;
}

/* تنسيقات حالة الإعلان */
.ad-status-active {
    color: #28a745;
    font-weight: 600;
}

/* تنسيقات الحقول القابلة للتعديل */
.editable {
    cursor: pointer;
    position: relative;
}

.editable:hover {
    background-color: rgba(220, 243, 67, 0.3);
}

.editable:hover::after {
    content: "انقر مرتين للتعديل";
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background-color: #4a56e2;
    color: white;
    padding: 5px;
    border-radius: 3px;
    font-size: 12px;
    white-space: nowrap;
    z-index: 10;
}

.editable-input {
    width: 100%;
    padding: 5px;
    box-sizing: border-box;
    border: 2px solid #4a56e2;
    border-radius: 3px;
    font-size: 14px;
    text-align: center;
    outline: none;
}

/* تنسيقات زر إضافة إعلان */
.add-ad-btn, .add-payment-btn {
    background-color: #dcf343;
    color: #4a56e2;
    border: none;
    border-radius: 50%;
    width: 25px;
    height: 25px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
    padding: 0;
    margin: 0 5px;
}

.add-ad-btn:hover, .add-payment-btn:hover {
    transform: scale(1.1);
}

/* تنسيقات الموديل */
.modal-header {
    background-color: #4a56e2;
    color: white;
    border-bottom: none;
    padding: 0px;
}

.modal-title {
    font-weight: 600;
    width: 100%;
    text-align: center;
    margin: 0;
}

.btn-close {
    color: white;
    opacity: 1;
}

.modal-content {
    border-radius: 0;
    border: none;
}

/* تنسيقات نموذج إضافة إعلان */
.ad-form-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
    background-color: white;
    padding: 10px 0;
    border-bottom: 1px solid #eee;
    width: 100%;
}

.ad-form-header-item {
    flex: 1;
    text-align: center;
    font-weight: 600;
    font-size: 14px;
    color: #4a56e2;
    padding: 0 5px;
}

.ad-form-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
    background-color: white;
    padding: 10px 0;
    width: 100%;
}

.ad-form-item {
    flex: 1;
    padding: 0 5px;
}

.ad-form-item .form-control {
    font-size: 14px;
    padding: 6px;
    height: auto;
    border-radius: 0;
    border: 1px solid #ddd;
    width: 100%;
}

.ad-form-divider {
    border-top: 1px solid #dcf343;
    margin: 20px 0;
}

.add-ad-submit {
    background-color: #4a56e2;
    border-color: #4a56e2;
    padding: 8px 50px;
    font-weight: 600;
    border-radius: 0;
    font-size: 16px;
    color: white;
}

.modal-body {
    padding: 0;
}

.modal-body form {
    padding: 15px;
}

/* تنسيقات المدفوعات */
.payments-container {
    padding: 15px;
}

.payments-header {
    display: flex;
    justify-content: space-between;
    background-color: #4a56e2;
    color: white;
    padding: 10px;
    margin-bottom: 10px;
}

.payment-header-item {
    flex: 1;
    text-align: center;
    font-weight: 600;
    font-size: 14px;
}

.payments-list {
    max-height: 300px;
    overflow-y: auto;
}

.payment-item {
    display: flex;
    justify-content: space-between;
    padding: 10px;
    border-bottom: 1px solid #eee;
}

.payment-item:nth-child(even) {
    background-color: #f9f9f9;
}

.payment-data {
    flex: 1;
    text-align: center;
    font-size: 14px;
}

.payments-total {
    display: flex;
    justify-content: space-between;
    padding: 15px 10px;
    background-color: #f0f2ff;
    margin-top: 10px;
    border-top: 1px solid #dcf343;
}

.payment-total-label {
    font-weight: 600;
    color: #4a56e2;
    text-align: right;
}

.payment-total-value {
    font-weight: 600;
    color: #4a56e2;
    text-align: left;
}

.add-payment-container {
    display: flex;
    justify-content: center;
    margin-top: 20px;
}

.add-new-payment-btn {
    background-color: #dcf343;
    color: #4a56e2;
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 18px;
}

/* تنسيقات تقرير العميل */
.client-report-container {
    padding: 0;
    position: relative;
    background-color: white;
}

.client-report-header {
    background-color: #4a56e2;
    color: white;
    padding: 0px;
    text-align: center;
    border-bottom: 1px solid #dcf343;
}

.client-report-name {
    font-size: 18px;
    font-weight: bold;
    color: #dcf343;
}

.client-report-content {
    max-height: 300px;
    overflow-y: auto;
    border-bottom: 1px solid #dcf343;

    /* إخفاء السكروول */
    scrollbar-width: none;            /* Firefox */
    -ms-overflow-style: none;         /* Internet Explorer 10+ */
}

.client-report-content::-webkit-scrollbar {
    display: none;                    /* Chrome, Safari, Opera */
}


.client-report-table {
    width: 100%;
    border-collapse: collapse;
    border-spacing: 0;
    table-layout: fixed;
}

.client-report-table th {
    background-color: #4a56e2;
    color: white;
    text-align: center;
    padding: 6px;
    font-weight: 600;
    font-size: 13px;
    border: none;
    position: sticky;
    top: 0;
    z-index: 10;
}

.client-report-table td {
    text-align: center;
    padding: 6px;
    border: 1px solid #dcf343;
    font-size: 13px;
    background-color: white;
}

.client-report-table .exchange-rate {
    color: #dcf343;
    font-weight: bold;
}

.client-report-table .ad-status-active {
    color: #28a745;
    font-weight: 600;
}

.client-report-table .ad-status-inactive {
    color: #dc3545;
    font-weight: 600;
}

.client-report-summary {
    margin-top: 15px;
    padding: 0 10px;
}

.client-report-summary-table {
    width: 100%;
    border-collapse: collapse;
    border-spacing: 0;
    table-layout: fixed;
}

.client-report-summary-table td {
    padding: 6px;
    border: 1px solid #dcf343;
    font-size: 13px;
    background-color: white;
}

.summary-label {
    color: #4a56e2;
    font-weight: 600;
    text-align: right;
    width: 25%;
}

.summary-value {
    color: #4a56e2;
    font-weight: 600;
    text-align: center;
    width: 25%;
}

.client-report-actions {
    margin: 15px 0;
    text-align: center;
}

.client-report-actions .btn-primary {
    background-color: #dcf343;
    border-color: #dcf343;
    color: #4a56e2;
    font-weight: 600;
    padding: 5px 30px;
    border-radius: 0;
    font-size: 14px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.client-report-actions .btn-primary:hover {
    background-color: #c9e03c;
}

.client-report-pagination {
    position: absolute;
    bottom: 10px;
    left: 50%;
    transform: translateX(-50%);
    background-color: rgba(0, 0, 0, 0.5);
    padding: 5px 15px;
    border-radius: 20px;
    display: none;
    align-items: center;
    justify-content: center;
}

.pagination-prev, .pagination-next {
    background: none;
    border: none;
    color: white;
    cursor: pointer;
    font-size: 14px;
    padding: 0 5px;
}

.pagination-info {
    color: white;
    margin: 0 10px;
    font-size: 14px;
}

/* تنسيقات للطباعة */
@media print {
    body * {
        visibility: hidden;
    }

    .modal-backdrop {
        display: none !important;
    }

    .client-report-container,
    .client-report-container * {
        visibility: visible;
    }

    .client-report-container {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
    }

    .client-report-pagination,
    .client-report-actions,
    .modal-header .btn-close {
        display: none !important;
    }

    .client-report-content {
        max-height: none;
        overflow: visible;
    }
}

/* تنسيقات للشاشات الصغيرة */
@media (max-width: 768px) {
    .search-container {
        flex-direction: column;
        align-items: flex-start;
    }

    .search-box {
        width: 100%;
    }
}
