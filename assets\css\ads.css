/* تنسيقات الإعلانات */
.ads-container {
    padding: 0;
    background-color: #fff;
    overflow: hidden;
}

.ads-header {
    background-color: #fff;
    padding: 10px 0;
    border-bottom: 1px solid #dcf343;
    font-weight: 600;
    color: #4a56e2;
}

.ads-header .row {
    margin: 0;
    text-align: center;
}

.ads-header .col {
    padding: 5px;
    font-size: 14px;
    text-align: center;
    white-space: nowrap;
}

.ads-list {
    max-height: 400px;
    overflow-y: auto;
}

.ad-item {
    padding: 5px 0;
    border-bottom: 1px solid #eee;
}

.ad-item .row {
    margin: 0;
    text-align: center;
    align-items: center;
}

.ad-item .col {
    padding: 5px;
    font-size: 14px;
    text-align: center;
    white-space: nowrap;
}

/* تنسيقات حالة الإعلان */
.ad-status-مستمر {
    color: #28a745;
    font-weight: 600;
}

.ad-status-متوقف {
    color: #dc3545;
    font-weight: 600;
}

.ads-footer {
    background-color: #fff;
    padding: 8px 0;
    border-top: 1px solid #dcf343;
    position: relative;
}

.ads-total {
    display: flex;
    justify-content: center;
    font-weight: 600;
    padding: 5px;
    gap: 5px;
    font-size: 14px;
    height: 100%;
    align-items: center;
}

.total-value {
    color: #dcf343;
    font-weight: bold;
}

.add-ad-container {
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
}

.btn-add-ad {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background-color: #4CAF50;
    color: white;
    border: 2px solid #fff;
    font-size: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

/* تنسيقات إضافية للإعلانات */
.ad-date {
    font-weight: normal;
    color: #000;
}

.ad-type {
    font-weight: normal;
    color: #000;
}

.ad-post {
    font-weight: normal;
    color: #000;
}

.ad-cost {
    font-weight: normal;
    color: #000;
}

.ad-account {
    color: #000;
    font-weight: normal;
}

.ad-exchange {
    font-weight: normal;
    color: #dcf343 !important;
    position: relative;
}

.ad-percentage {
    font-size: 12px;
    color: #4a56e2;
    margin-right: 5px;
    background-color: rgba(220, 243, 67, 0.2);
    padding: 2px 5px;
    border-radius: 3px;
    display: inline-block;
}

/* تنسيقات نافذة إضافة إعلان */
#addAdModal .modal-dialog {
    max-width: 900px;
}

#addAdModal .modal-content {
    border: none;
    border-radius: 0;
    background-color: #f8f9fa;
}

#addAdModal .modal-header {
    background-color: #4a56e2;
    color: white;
    border-bottom: none;
    padding: 8px 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

#addAdModal .modal-title {
    font-weight: 600;
    margin: 0;
    font-size: 16px;
    text-align: center;
}

#addAdModal .btn-close {
    color: white;
    background: none;
    opacity: 1;
    font-size: 16px;
    padding: 0;
    box-shadow: none;
}

#addAdModal .btn-close i {
    font-size: 16px;
}

#addAdModal .modal-body {
    padding: 0;
}

.add-ad-header {
    background-color: #f8f9fa;
    padding: 10px 15px;
    border-bottom: 1px solid #dcf343;
}

.add-ad-header .row {
    margin: 0;
    text-align: center;
}

.add-ad-header .col {
    padding: 5px;
    font-size: 14px;
    text-align: center;
    white-space: nowrap;
    color: #4a56e2;
    font-weight: 600;
}

.add-ad-content {
    background-color: #fff;
    padding: 15px;
    border-bottom: 1px solid #dcf343;
}

.separator-line {
    height: 1px;
    background-color: #dcf343;
    margin: 15px 0 5px 0;
}

.add-ad-content .row {
    margin: 0;
    text-align: center;
    align-items: center;
}

.add-ad-content .col {
    padding: 5px;
}

.add-ad-content .form-control {
    border: 1px solid #ced4da;
    border-radius: 4px;
    padding: 5px 10px;
    font-size: 14px;
    height: auto;
    text-align: center;
}

.add-ad-footer {
    background-color: #fff;
    padding: 15px;
    text-align: center;
}

.btn-add-ad-submit {
    background-color: #4a56e2;
    color: white;
    border: none;
    padding: 8px 25px;
    font-weight: 600;
    border-radius: 4px;
    transition: all 0.3s ease;
    font-size: 14px;
}

.btn-add-ad-submit:hover {
    background-color: #3a46d2;
}

/* تنسيقات إضافية للإعلانات */
.ad-date {
    font-weight: normal;
    color: #495057;
}

.ad-type {
    font-weight: normal;
}

.ad-cost {
    font-weight: normal;
    color: #000;
}

.ad-account {
    color: #000;
    font-weight: normal;
}



/* تنسيقات للنافذة المنبثقة */
#adsModal .modal-dialog {
    max-width: 900px;
}

#adsModal .modal-content {
    border: none;
    border-radius: 0;
}

#adsModal .modal-header {
    background-color: #4a56e2;
    color: white;
    border-bottom: none;
    padding: 8px 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-title-container {
    display: flex;
    align-items: center;
    gap: 30px;
}

.filter-dropdown {
    position: relative;
}

.btn-collapse {
    background: none;
    border: none;
    color: white;
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    font-size: 14px;
    padding: 0;
}

.btn-collapse i {
    font-size: 12px;
}

.filter-menu {
    min-width: 120px;
    padding: 0.5rem 0;
    margin: 0;
    font-size: 14px;
    text-align: right;
    background-color: #fff;
    border: 1px solid rgba(0, 0, 0, 0.15);
    border-radius: 0.25rem;
    list-style: none;
    top: 100%;
    right: 0;
    margin-top: 5px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.filter-menu li {
    margin: 0;
    padding: 0;
}

.filter-menu .dropdown-item {
    display: block;
    padding: 0.5rem 1rem;
    clear: both;
    font-weight: 400;
    color: #212529;
    text-align: right;
    white-space: nowrap;
    background-color: transparent;
    border: 0;
    text-decoration: none;
}

.filter-menu .dropdown-item.active,
.filter-menu .dropdown-item:active {
    color: #fff;
    text-decoration: none;
    background-color: #4a56e2;
}

.filter-menu .dropdown-item:hover {
    background-color: #f8f9fa;
    color: #212529;
}

#adsModal .modal-title {
    font-weight: 600;
    margin: 0;
    font-size: 16px;
    text-align: center;
}

#adsModal .btn-close {
    color: white;
    background: none;
    opacity: 1;
    font-size: 16px;
    padding: 0;
    box-shadow: none;
}

#adsModal .btn-close i {
    font-size: 16px;
}

#adsModal .modal-body {
    padding: 0;
}

/* تنسيقات للحقول القابلة للتعديل */
.editable {
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.editable:hover {
    background-color: #f8f9fa;
}

.editable:hover::after {
    content: '\f044';
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    margin-right: 5px;
    font-size: 12px;
    color: #4a56e2;
    opacity: 0.5;
}

.edit-input {
    border: 1px solid #4a56e2;
    border-radius: 3px;
    outline: none;
}

.edit-input:focus {
    box-shadow: 0 0 3px rgba(74, 86, 226, 0.5);
}

/* تنسيقات نافذة اختيار النسبة */
.percentage-container {
    padding: 20px;
}

.percentage-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.percentage-value {
    font-size: 24px;
    font-weight: bold;
    color: #4a56e2;
}

.percentage-slider-container {
    margin-bottom: 20px;
}

.form-range {
    width: 100%;
    height: 10px;
    background-color: #e9ecef;
    border-radius: 5px;
    outline: none;
}

.form-range::-webkit-slider-thumb {
    width: 20px;
    height: 20px;
    background-color: #4a56e2;
    border-radius: 50%;
    cursor: pointer;
}

.percentage-input-container {
    margin-bottom: 20px;
}

.percentage-input-container .input-group {
    width: 150px;
    margin: 0 auto;
}

.percentage-footer {
    text-align: center;
}

.percentage-footer .btn-primary {
    background-color: #4a56e2;
    border-color: #4a56e2;
    padding: 8px 25px;
    font-weight: 600;
    border-radius: 4px;
}

.editable-percentage {
    cursor: pointer;
    transition: all 0.3s ease;
}

.editable-percentage:hover {
    color: #4a56e2;
    text-decoration: underline;
}

/* تنسيقات للرسائل */
.no-ads-message {
    text-align: center;
    padding: 30px;
    color: #6c757d;
    font-size: 16px;
    font-weight: 600;
}

.loading-ads {
    text-align: center;
    padding: 30px;
    color: #4a56e2;
    font-size: 16px;
    font-weight: 600;
}
