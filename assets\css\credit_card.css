/**
 * تنسيقات صفحة اكونتات كريديت كارد
 */

/* تنسيقات عامة */
body {
    font-family: 'Cairo', sans-serif;
    background-color: #fff;
    margin: 0;
    padding: 0;
    direction: rtl;
}

.page-container {
    max-width: 100%;
    margin: 0 auto;
    padding: 0;
}

/* تنسيقات الهيدر */
.page-header {
    background-color: white;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 20px;
    border-bottom: 1px solid #eee;
}

.logo-link {
    display: flex;
    align-items: center;
    text-decoration: none;
}

.page-logo {
    height: 50px;
}

.header-links {
    display: flex;
    gap: 20px;
    margin-right: auto;
    margin-left: 20px;
}

.header-link {
    color: #4a56e2;
    text-decoration: none;
    font-weight: 600;
    font-size: 16px;
    padding: 5px 10px;
    transition: all 0.3s ease;
}

.header-link:hover {
    color: #3a46d2;
}

.header-link.active {
    color: #4a56e2;
}

/* تنسيقات البحث */
.search-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background-color: white;
    border-bottom: 1px solid #eee;
}

.search-box {
    position: relative;
    width: 87%;
    display: flex;
    align-items: center;
}

.search-input {
    width: 100%;
    padding: 8px 40px 8px 15px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 14px;
}

.search-input:focus {
    outline: none;
    border-color: #4a56e2;
}

.search-icon {
    position: absolute;
    right: 15px;
    color: #4a56e2;
}

.account-label {
    display: flex;
    align-items: center;
    font-weight: 600;
    color: #4a56e2;
    font-size: 16px;
}

.account-label i {
    margin-left: 10px;
}

/* تنسيقات الحسابات */
.accounts-container {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 0;
    padding: 0;
}

.account-section {
    background-color: white;
    border: 1px solid #dcf343;
    overflow: hidden;
    margin-bottom: 0;
    display: flex;
    flex-direction: column;
    height: 499px;
    width: 95%;
    margin: 2%;
}

.account-header {
    background-color: #4a56e2;
    color: white;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
}

.account-name {
    font-weight: 600;
    font-size: 16px;
}

.account-actions {
    cursor: pointer;
}

/* تنسيقات جدول المعاملات */
.transactions-table-container {
    overflow-x: auto;
    overflow-y: auto;
    background-color: #fff;

    /* لإخفاء السكرول */
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none;  /* Internet Explorer 10+ */
}

.transactions-table-container::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
}

.transactions-table {
    width: 100%;
    border-collapse: collapse;
    border: 1px solid #dcf343;
}

.transactions-table th {
    background-color: #4a56e2;
    color: white;
    padding: 8px;
    text-align: center;
    position: sticky;
    top: 0;
    z-index: 10;
    border-left: 1px solid #dcf343;
    border-right: 1px solid #dcf343;
    border-bottom: 1px solid #dcf343;
    font-weight: normal;
}

.transactions-table td {
    padding: 8px;
    text-align: center;
    border-left: 1px solid #dcf343;
    border-right: 1px solid #dcf343;
    border-bottom: 1px solid #dcf343;
    color: #4a56e2;
}

.transactions-table tr:last-child td {
    border-bottom: 1px solid #dcf343;
}

/* تنسيقات فوتر الحساب */
.account-footer {
    background-color: #fcffd9;
    padding: 0;
    position: relative;
    border-top: 1px solid #dcf343;
}

.footer-row {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 0;
    margin-bottom: 0;
    border-left: 1px solid #dcf343;
    border-right: 1px solid #dcf343;
    border-bottom: 1px solid #dcf343;
    border-top: none;
}

.footer-row:last-child {
    border-bottom: none;
}

.footer-cell {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 15px;
    border-left: 1px solid #dcf343;
    background-color: #fcffd9;
}

.footer-cell:last-child {
    border-left: none;
}

.footer-label {
    color: #4a56e2;
    font-weight: normal;
    font-size: 14px;
}

.footer-value {
    color: #4a56e2;
    font-weight: normal;
    font-size: 14px;
    margin-right: 5px;
}

.footer-value.highlight {
    color: blue;
    font-weight: bold;
}

.add-transaction-button {
    width: 30px;
    height: 30px;
    background-color: #dcf343;
    color: #4a56e2;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-left: 10px;
}

.add-transaction-button:hover {
    transform: scale(1.1);
}

/* تنسيقات الموديل */
.modal-header {
    background-color: #4a56e2;
    color: white;
}

.modal-title {
    font-weight: 600;
}

.btn-close {
    color: white;
}

.btn-primary {
    background-color: #4a56e2;
    border-color: #4a56e2;
}

.btn-primary:hover {
    background-color: #3a46d2;
    border-color: #3a46d2;
}

/* تنسيقات المحتوى */
.content-container {
    padding: 20px;
}

/* تنسيقات للشاشات الصغيرة */
@media (max-width: 992px) {
    .accounts-container {
        grid-template-columns: 1fr;
    }

    .footer-row {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .header-links {
        gap: 10px;
    }

    .header-link {
        font-size: 14px;
        padding: 5px;
    }

    .search-container {
        flex-direction: column;
        gap: 10px;
    }

    .search-box {
        width: 100%;
    }

    .account-label {
        font-size: 16px;
    }
}
