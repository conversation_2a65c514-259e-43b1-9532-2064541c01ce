/* CSS لصفحة إدارة فئات العملاء */

/* تنسيقات عامة */
body {
    background-color: white;
    margin: 0;
    padding: 0;
    font-family: 'Cairo', sans-serif;
    overflow-x: hidden;
}

.page-container {
    width: 100%;
    margin: 0;
    padding: 0 15px;
}

.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 0;
    border-bottom: 1px solid #eee;
    margin-bottom: 20px;
}

.logo-link {
    display: block;
    transition: all 0.3s ease;
}

.logo-link:hover {
    transform: scale(1.05);
}

.page-logo {
    max-width: 80px;
    height: auto;
    cursor: pointer;
}

.page-title {
    color: #4a56e2;
    font-size: 24px;
    font-weight: 600;
    text-align: center;
    flex-grow: 1;
}

.back-btn {
    background-color: transparent;
    color: #4a56e2;
    border: none;
    padding: 8px 15px;
    font-size: 16px;
    cursor: pointer;
    display: flex;
    align-items: center;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
}

.back-btn:hover {
    color: #3a46d2;
    text-decoration: underline;
}

.back-btn i {
    margin-left: 8px;
    font-size: 20px;
}

.content-area {
    width: 100%;
    padding: 0;
}

/* تنسيقات فئات العملاء */
.client-types-container {
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    padding: 20px;
    margin-bottom: 30px;
}

.client-types-header {
    margin-bottom: 20px;
}

.header-actions {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    gap: 15px;
}

.add-client-type-btn {
    background-color: #4a56e2;
    color: white;
    border: none;
    border-radius: 8px;
    padding: 10px 20px;
    font-size: 14px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.add-client-type-btn:hover {
    background-color: #3a46d2;
    transform: translateY(-2px);
}

.refresh-btn {
    background-color: #6c757d;
    color: white;
    border: none;
    border-radius: 8px;
    padding: 10px 20px;
    font-size: 14px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.refresh-btn:hover {
    background-color: #5a6268;
    transform: translateY(-2px);
}

/* تنسيقات محتوى فئات العملاء */
.client-types-content {
    margin-top: 20px;
}

.loading-message {
    text-align: center;
    padding: 30px;
    color: #4a56e2;
    font-weight: 600;
    width: 100%;
}

/* تنسيقات بطاقة الفئة */
.client-type-card {
    background-color: #fff;
    border: 2px solid #dcf343;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 15px;
    transition: all 0.3s ease;
}

.client-type-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.client-type-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.client-type-name {
    font-size: 18px;
    font-weight: 600;
    color: #4a56e2;
}

.client-type-display-name {
    font-size: 16px;
    color: #666;
    margin-bottom: 5px;
}

.client-type-description {
    font-size: 14px;
    color: #888;
    margin-bottom: 10px;
}

.client-type-info {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 10px;
}

.client-type-color {
    display: inline-block;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    border: 2px solid #ddd;
}

.client-type-sort {
    font-size: 14px;
    color: #666;
}

.client-type-actions {
    display: flex;
    gap: 10px;
}

.edit-btn, .delete-btn {
    padding: 5px 10px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.3s ease;
}

.edit-btn {
    background-color: #28a745;
    color: white;
}

.edit-btn:hover {
    background-color: #218838;
}

.delete-btn {
    background-color: #dc3545;
    color: white;
}

.delete-btn:hover {
    background-color: #c82333;
}

/* تنسيقات الموديل */
.modal-header {
    background-color: #4a56e2;
    color: white;
    border-bottom: none;
}

.modal-title {
    font-weight: 600;
    width: 100%;
    text-align: center;
    margin: 0;
}

.btn-close {
    color: white;
    opacity: 1;
}

.modal-content {
    border-radius: 10px;
    border: none;
}

/* تنسيقات حقل اللون */
.color-input-container {
    display: flex;
    gap: 10px;
    align-items: center;
}

.color-picker {
    width: 60px;
    height: 40px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
}

.color-text {
    flex: 1;
}

/* تنسيقات الرسائل */
.alert {
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
}

.alert-success {
    background-color: #d4edda;
    border-color: #c3e6cb;
    color: #155724;
}

.alert-danger {
    background-color: #f8d7da;
    border-color: #f5c6cb;
    color: #721c24;
}

.alert-info {
    background-color: #d1ecf1;
    border-color: #bee5eb;
    color: #0c5460;
}

/* تنسيقات متجاوبة */
@media (max-width: 768px) {
    .page-header {
        flex-direction: column;
        gap: 15px;
    }
    
    .page-title {
        order: -1;
    }
    
    .header-actions {
        flex-direction: column;
        width: 100%;
    }
    
    .add-client-type-btn,
    .refresh-btn {
        width: 100%;
    }
    
    .client-type-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .client-type-actions {
        width: 100%;
        justify-content: flex-end;
    }
}
