/* تنسيقات صفحة إدارة الصفحات */
body {
    background-color: white;
    margin: 0;
    padding: 0;
    font-family: 'Cairo', sans-serif;
    overflow-x: hidden;
}

.page-container {
    width: 100%;
    margin: 0;
    padding: 0 15px;
}

.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 0;
    border-bottom: 1px solid #eee;
}

.logo-link {
    display: block;
    transition: all 0.3s ease;
}

.logo-link:hover {
    transform: scale(1.05);
}

.page-logo {
    max-width: 80px;
    height: auto;
    cursor: pointer;
}

.page-title {
    color: #4a56e2;
    font-size: 24px;
    font-weight: 600;
    text-align: center;
    flex-grow: 1;
}

.add-client-btn {
    background-color: transparent;
    color: #4a56e2;
    border: none;
    padding: 8px 15px;
    font-size: 16px;
    cursor: pointer;
    display: flex;
    align-items: center;
    font-weight: 600;
}

.add-client-btn i {
    margin-left: 8px;
    font-size: 20px;
}

.search-container {
    margin: 20px 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #f9f9f9;
    padding: 15px;
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.05);
}

.search-box {
    flex-grow: 1;
    width: 100%;
    position: relative;
    margin-right: 20px;
}

.search-input {
    width: 100%;
    padding: 10px 45px 10px 15px;
    border: 1px solid #ddd;
    border-radius: 5px;
    background-color: #f9f9f9;
    font-size: 14px;
    transition: all 0.3s ease;
}

.search-input:focus {
    border-color: #4a56e2;
    box-shadow: 0 0 0 0.2rem rgba(74, 86, 226, 0.25);
    outline: none;
}

.search-icon {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: #4a56e2;
    font-size: 16px;
}

.admin-accounts {
    font-weight: 600;
    color: #4a56e2;
    font-size: 16px;
    text-decoration: none;
    transition: all 0.3s ease;
    cursor: pointer;
}

.admin-accounts:hover {
    color: #3a46d2;
    text-decoration: underline;
}

.pages-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 15px;
    margin-top: 20px;
    width: 100%;
}

.page-card {
    border: 1px solid #dcf343;
    border-radius: 0;
    overflow: hidden;
    margin-bottom: 15px;
    width: 100%;
    transition: all 0.3s ease;
}

/* تنسيقات البحث */
.page-card.hidden {
    display: none;
}

.page-card.highlight {
    box-shadow: 0 0 15px rgba(74, 86, 226, 0.5);
    transform: translateY(-5px);
}

.no-results {
    grid-column: 1 / -1;
    text-align: center;
    padding: 30px;
    background-color: #f9f9f9;
    border-radius: 8px;
    margin-top: 20px;
    color: #666;
    font-size: 18px;
    display: none;
}

.page-card-header {
    background-color: #4a56e2;
    color: white;
    padding: 10px 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    text-align: center;
}

.page-name {
    font-weight: 600;
    font-size: 16px;
    flex-grow: 1;
}

.page-options {
    cursor: pointer;
    position: relative;
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    z-index: 1000;
    display: none;
    min-width: 160px;
    padding: 5px 0;
    margin: 2px 0 0;
    font-size: 14px;
    text-align: right;
    list-style: none;
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid rgba(0,0,0,.15);
    border-radius: 4px;
    box-shadow: 0 6px 12px rgba(0,0,0,.175);
}

.dropdown-menu.show {
    display: block;
}

.dropdown-item {
    display: block;
    padding: 8px 20px;
    clear: both;
    font-weight: 400;
    color: #333;
    text-align: inherit;
    white-space: nowrap;
    background-color: transparent;
    border: 0;
    text-decoration: none;
}

.dropdown-item:hover {
    background-color: #f5f5f5;
    color: #262626;
}

.dropdown-item.delete {
    color: #dc3545;
}

/* تنسيقات الجدول */
.page-details {
    border-collapse: collapse;
    width: 100%;
    background-color: white;
    table-layout: fixed;
    margin: 0;
}

.page-details tr {
    border-bottom: 1px solid #dcf343;
    background-color: white;
    height: 40px;
}

.page-details tr:last-child {
    border-bottom: none;
}

/* الصفوف الثلاثة الأولى */
.page-details tr:nth-child(-n+3) {
    height: 30px;
}

.page-details tr:nth-child(-n+3) td {
    padding: 8px 15px;
    vertical-align: middle;
    line-height: 1;
}

/* أول 3 صفوف: خلية النص صغيرة وخلية الأرقام كبيرة */
.page-details tr:nth-child(-n+3) td:first-child {
    text-align: center;
    color: #4a56e2;
    border-left: 1px solid #dcf343;
    width: 50%;
    font-weight: 700;
    font-size: 15px;
    padding-right: 20px;
}

.page-details tr:nth-child(-n+3) td:last-child {
    text-align: center;
    color: #4a56e2;
    font-weight: 700;
    font-size: 17px;
    width: 50%;
}

/* الصفين الأخيرين */
.page-details tr:nth-child(n+4) {
    height: 40px;
}

.page-details tr:nth-child(n+4) td {
    padding: 8px 15px;
    vertical-align: middle;
    line-height: 1;
}

/* الصفين الأخيرين: خلية النص كبيرة وخلية الأرقام صغيرة */
.page-details tr:nth-child(n+4) td:first-child {
    text-align: right;
    color: #4a56e2;
    border-left: 1px solid #dcf343;
    width: 60%;
    font-weight: 700;
    font-size: 15px;
    padding-right: 20px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.page-details tr:nth-child(n+4) td:last-child {
    text-align: center;
    color: #dcf343; /* تغيير اللون إلى الأخضر */
    font-weight: 700;
    font-size: 15px;
    width: 40%;
}

/* تنسيق خاص للصف الرابع */
.page-details tr:nth-child(4) td:first-child {
    font-weight: 700;
    font-size: 15px; /* زيادة حجم النص أكثر */
    color: #4a56e2;
}

/* تنسيق خاص للصف الأخير */
.page-details tr:last-child td:first-child {
    font-weight: 700;
    font-size: 15px; /* زيادة حجم النص أكثر */
    color: #4a56e2;
}

.page-details .balance {
    color: #dcf343; /* تغيير اللون إلى الأخضر */
    font-weight: 700;
    font-size: 12px; /* تقليل حجم الأرقام أكثر */
}

/* تنسيقات للشاشات المختلفة */
@media (max-width: 992px) {
    .pages-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .pages-grid {
        grid-template-columns: 1fr;
    }
}

/* تنسيقات النافذة المنبثقة */
.modal-header {
    background-color: #f8f9fa;
    border-bottom: none;
    padding-bottom: 0;
}

.modal-title {
    font-weight: 600;
    color: #4a56e2;
    text-align: center;
    width: 100%;
    font-size: 22px;
}

.modal-body {
    padding: 20px;
}

.btn-primary {
    background-color: #4a56e2;
    border-color: #4a56e2;
}

.btn-primary:hover {
    background-color: #3a46d2;
    border-color: #3a46d2;
}

/* تنسيقات تفاصيل إدارة الصفحة */
.services-grid {
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;
    margin-bottom: 30px;
    text-align: center;
    gap: 15px;
}

.service-card {
    flex: 0 0 calc(25% - 15px);
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 15px 10px;
    background-color: #f8f9fa;
    border-radius: 8px;
    transition: all 0.3s ease;
    position: relative;
    cursor: pointer;
}

.service-card:hover {
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transform: translateY(-3px);
}

.service-card .edit-icons {
    position: absolute;
    top: 5px;
    left: 5px;
    display: flex;
    gap: 5px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.service-card:hover .edit-icons {
    opacity: 1;
}

.edit-service, .delete-service {
    background-color: white;
    border-radius: 50%;
    width: 25px;
    height: 25px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    transition: all 0.2s ease;
}

.edit-service {
    color: #4a56e2;
}

.delete-service {
    color: #dc3545;
}

.edit-service:hover, .delete-service:hover {
    transform: scale(1.1);
}

@media (max-width: 992px) {
    .service-card {
        flex: 0 0 calc(33.333% - 15px);
    }
}

@media (max-width: 768px) {
    .service-card {
        flex: 0 0 calc(50% - 15px);
    }
}

@media (max-width: 576px) {
    .service-card {
        flex: 0 0 100%;
    }
}

.service-price {
    font-weight: 700;
    color: #4a56e2;
    font-size: 24px;
    margin-bottom: 10px;
}

.service-name {
    font-weight: 500;
    color: #666;
    font-size: 16px;
}

.add-service-container {
    margin: 25px 0;
    text-align: center;
}

#addServiceBtn {
    background-color: transparent;
    color: #4a56e2;
    border: 2px solid #4a56e2;
    border-radius: 30px;
    padding: 8px 25px;
    font-size: 16px;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 2px 10px rgba(74, 86, 226, 0.1);
}

#addServiceBtn:hover {
    background-color: #4a56e2;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(74, 86, 226, 0.2);
}

#addServiceForm {
    background-color: #f8f9fa;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    margin: 20px 0;
    border: 1px solid #eee;
}

#saveServiceBtn {
    background-color: #4a56e2;
    border-color: #4a56e2;
    font-weight: 600;
    transition: all 0.3s ease;
}

#saveServiceBtn:hover {
    background-color: #3a46d2;
    transform: translateY(-2px);
    box-shadow: 0 4px 10px rgba(74, 86, 226, 0.2);
}

.separator {
    height: 2px;
    background-color: #dcf343;
    margin: 25px 0;
}

.total-row {
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
}

.total-label {
    font-weight: 600;
    color: #666;
    font-size: 18px;
}

.total-value {
    font-weight: 700;
    color: #4a56e2;
    font-size: 28px;
}

.save-changes-container {
    text-align: center;
    margin-top: 20px;
}

#saveChangesBtn {
    background-color: #4a56e2;
    border-color: #4a56e2;
    padding: 10px 25px;
    font-weight: 600;
    border-radius: 30px;
    box-shadow: 0 4px 10px rgba(74, 86, 226, 0.2);
    transition: all 0.3s ease;
}

#saveChangesBtn:hover {
    background-color: #3a46d2;
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(74, 86, 226, 0.3);
}

#editServiceForm {
    background-color: #f8f9fa;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    margin: 20px 0;
    border: 1px solid #eee;
    border-left: 4px solid #4a56e2;
}

/* تنسيق نافذة المدفوعات */
#paymentsModal .modal-content,
#addPaymentModal .modal-content {
    background-color: #f8f9fa;
    border-radius: 15px;
    border: none;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
}

#paymentsModal .modal-header,
#addPaymentModal .modal-header {
    border-bottom: none;
    padding: 15px 20px;
}

#paymentsModal .modal-title,
#addPaymentModal .modal-title {
    color: #4a56e2;
    font-weight: 600;
    font-size: 1.2rem;
}

#paymentsModal .btn-close,
#addPaymentModal .btn-close {
    background: none;
    font-size: 1.2rem;
    color: #6c757d;
    opacity: 1;
}

#paymentsModal .modal-body,
#addPaymentModal .modal-body {
    padding: 0 20px 20px;
}

.payments-container,
.add-payment-container {
    background-color: #fff;
    border-radius: 10px;
    padding: 15px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
}

.payments-header,
.add-payment-header {
    font-weight: 600;
    color: #4a56e2;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #e9ecef;
}

.payments-list {
    max-height: 300px;
    overflow-y: auto;
    margin-bottom: 15px;
}

.payment-item {
    padding: 10px 0;
    border-bottom: 1px solid #f0f0f0;
}

.payment-item:last-child {
    border-bottom: none;
}

.payments-footer,
.add-payment-footer {
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid #e9ecef;
}

.payments-total,
.payment-total {
    font-weight: 600;
    color: #4a56e2;
    font-size: 1.1rem;
}

.total-value {
    margin-right: 10px;
    color: #28a745;
}

.btn-add-payment {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #4CAF50;
    color: white;
    border: none;
    font-size: 1.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
}



.btn-add-payment:hover {
    background-color: #45a049;
    transform: scale(1.05);
}

.btn-add-payment-submit {
    padding: 8px 15px;
    background-color: #4a56e2;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-add-payment-submit:hover {
    background-color: #3a46d2;
}

.separator-line {
    height: 1px;
    background-color: #e9ecef;
    margin: 15px 0;
}

.add-payment-form {
    margin: 15px 0;
}

.add-payment-form .form-control {
    border-radius: 5px;
    border: 1px solid #ced4da;
    padding: 8px 12px;
}

.add-payment-form .form-control:focus {
    border-color: #4a56e2;
    box-shadow: 0 0 0 0.2rem rgba(74, 86, 226, 0.25);
}

/* تنسيقات الإشعارات */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px 20px;
    border-radius: 5px;
    color: white;
    font-weight: 600;
    z-index: 9999;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
    transform: translateY(-20px);
    opacity: 0;
    transition: all 0.3s ease;
}

.notification.show {
    transform: translateY(0);
    opacity: 1;
}

.notification.success {
    background-color: #4CAF50;
}

.notification.info {
    background-color: #4a56e2;
}

.notification.warning {
    background-color: #ff9800;
}

.notification.error {
    background-color: #f44336;
}

/* تنسيقات رسائل التحميل والخطأ */
.loading-message {
    text-align: center;
    padding: 30px;
    color: #4a56e2;
    font-size: 18px;
    font-weight: 600;
}

.error-message {
    text-align: center;
    padding: 30px;
    color: #dc3545;
    font-size: 18px;
    font-weight: 600;
    background-color: #f8d7da;
    border-radius: 8px;
    margin: 20px 0;
}

.no-services {
    text-align: center;
    padding: 30px;
    color: #6c757d;
    font-size: 18px;
    font-weight: 600;
    background-color: #f8f9fa;
    border-radius: 8px;
    margin: 20px 0;
    border: 2px dashed #dee2e6;
}

.no-clients {
    text-align: center;
    padding: 50px 20px;
    color: #6c757d;
    font-size: 18px;
    font-weight: 600;
    background-color: #f8f9fa;
    border-radius: 8px;
    margin: 20px 0;
    border: 2px dashed #dee2e6;
    grid-column: 1 / -1;
}

/* تنسيقات البحث */
.page-card.hidden {
    display: none;
}

.page-card.highlight {
    animation: highlight 1.5s ease-in-out;
}

@keyframes highlight {
    0% { box-shadow: 0 0 0 0 rgba(74, 86, 226, 0.5); }
    50% { box-shadow: 0 0 0 10px rgba(74, 86, 226, 0.2); }
    100% { box-shadow: 0 0 0 0 rgba(74, 86, 226, 0); }
}

/* جعل خلايا الجدول قابلة للنقر */
.clickable-cell {
    cursor: pointer;
    transition: background-color 0.2s;
}

.clickable-cell:hover {
    background-color: rgba(74, 86, 226, 0.1);
}
