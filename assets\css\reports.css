/* CSS للتقارير */

/* تنسيقات الهيدر */
body {
    background-color: white;
    margin: 0;
    padding: 0;
    font-family: 'Cairo', sans-serif;
    overflow-x: hidden;
}

.page-container {
    width: 100%;
    margin: 0;
    padding: 0 15px;
}

.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 0;
    border-bottom: 1px solid #eee;
    margin-bottom: 20px;
}

.logo-link {
    display: block;
    transition: all 0.3s ease;
}

.logo-link:hover {
    transform: scale(1.05);
}

.page-logo {
    max-width: 80px;
    height: auto;
    cursor: pointer;
}

.page-title {
    color: #4a56e2;
    font-size: 24px;
    font-weight: 600;
    text-align: center;
    flex-grow: 1;
}

.back-btn {
    background-color: transparent;
    color: #4a56e2;
    border: none;
    padding: 8px 15px;
    font-size: 16px;
    cursor: pointer;
    display: flex;
    align-items: center;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
}

.back-btn:hover {
    color: #3a46d2;
    text-decoration: underline;
}

.back-btn i {
    margin-left: 8px;
    font-size: 20px;
}

.content-area {
    width: 100%;
    padding: 0;
}

.admin-reports-container {
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    padding: 20px;
    margin-bottom: 30px;
}

.admin-reports-header {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.admin-reports-actions {
    display: flex;
    align-items: center;
    gap: 15px;
    flex-wrap: wrap;
}

.search-box {
    position: relative;
    width: 250px;
}

.search-icon {
    position: absolute;
    left: 10px;
    top: 50%;
    transform: translateY(-50%);
    color: #4a56e2;
}

#searchInput {
    padding-left: 35px;
    border-radius: 20px;
    border: 1px solid #ddd;
}

.date-filter {
    display: flex;
    align-items: center;
    gap: 5px;
}

.date-filter label {
    margin-bottom: 0;
    font-weight: 600;
    color: #4a56e2;
}

.date-filter .form-select {
    width: auto;
    border-radius: 20px;
    border: 1px solid #ddd;
    padding: 5px 30px 5px 10px;
}

/* تنسيقات الجدول ذو الهيدر والفوتر الثابتين */
.table-container {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 250px);
    min-height: 400px;
    border: 1px solid #ddd;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
}

.table-header, .table-footer {
    flex: 0 0 auto;
    width: 100%;
    overflow: hidden;
}

.table-body {
    flex: 1 1 auto;
    overflow-y: auto;
    width: 100%;
}

.table-header table, .table-body table, .table-footer table {
    width: 100%;
    table-layout: fixed;
    border-collapse: separate;
    border-spacing: 0;
    margin: 0;
}

/* تأكد من أن عرض الأعمدة متطابق في جميع الجداول */
.table-header th, .table-body td, .table-footer td {
    width: calc(100% / 7);
}

.admin-reports-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
}

.admin-reports-table th {
    background-color: #4a56e2;
    color: white;
    text-align: center;
    padding: 12px;
    font-weight: 600;
    border: none;
}

.admin-reports-table td {
    text-align: center;
    padding: 12px;
    border: none;
    border-bottom: 1px solid #dcf343;
    color: #4a56e2;
    font-weight: 600;
}

/* إزالة الحدود السفلية من آخر صف في الجسم */
.table-body .admin-reports-table tr:last-child td {
    border-bottom: none;
}

.admin-reports-table tbody tr:nth-child(even) {
    background-color: #f8f9fa;
}

.admin-reports-table tbody tr:hover {
    background-color: #f0f2ff;
}

.admin-reports-table .totals-row {
    background-color: #dcf343;
    font-weight: 700;
}

.admin-reports-table .totals-row td {
    border-bottom: none;
    color: #4a56e2;
    font-weight: 700;
    font-size: 16px;
}

/* تنسيقات خاصة للفوتر */
.table-footer {
    border-top: 2px solid #dcf343;
}

.loading-row td {
    text-align: center;
    padding: 30px;
    color: #4a56e2;
    font-weight: 600;
}

/* تنسيق الطباعة */
@media print {
    .sidebar, .header, .admin-reports-actions, .footer {
        display: none !important;
    }

    .content-area {
        width: 100% !important;
        margin: 0 !important;
        padding: 0 !important;
    }

    .admin-reports-container {
        box-shadow: none;
        padding: 0;
    }

    .table-container {
        height: auto !important;
        min-height: auto !important;
        border: none !important;
        box-shadow: none !important;
    }

    .table-body {
        overflow: visible !important;
    }

    .admin-reports-title {
        text-align: center;
        margin-bottom: 20px;
    }

    .admin-reports-table th {
        background-color: #4a56e2 !important;
        color: white !important;
        -webkit-print-color-adjust: exact;
        print-color-adjust: exact;
    }

    .admin-reports-table .totals-row {
        background-color: #dcf343 !important;
        -webkit-print-color-adjust: exact;
        print-color-adjust: exact;
    }
}
