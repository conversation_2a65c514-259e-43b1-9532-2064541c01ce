/* Import Arabic font */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap');

/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', sans-serif;
    background-color: #ffffff;
    color: #333;

}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* Header styles */
header {
    text-align: center;
    margin-bottom: 30px;
}

.logo-img {
    max-width: 100px;
    height: auto;
    margin-bottom: 10px;
}

.logo h1 {
    color: #4a56e2;
    font-size: 28px;
    margin-bottom: 10px;
}

/* Dashboard grid layout */
.services-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
    max-width: 800px;
    margin: 0 auto;
}

/* Service card styles */
.service-card {
    background-color: #f0f2ff;
    border-radius: 15px;
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    cursor: pointer;
    border: 1px solid #e0e4ff;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
}

.service-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.service-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 25px;
    height: 100%;
}

.service-text {
    font-size: 18px;
    font-weight: 600;
    color: #4a56e2;
}

.service-icon {
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 24px;
    color: #4a56e2;
    width: 50px;
    height: 50px;
    opacity: 0.8;
}

.service-icon i {
    font-size: 28px;
}

.fawry-icon {
    width: 40px;
    height: auto;
}

/* Login and Register Forms */
.auth-form {
    max-width: 500px;
    margin: 0 auto;
    padding: 30px;
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
}

.auth-form h2 {
    color: #4a56e2;
    margin-bottom: 20px;
    text-align: center;
}

/* New Login Page Styles */
body {
    overflow-x: hidden;
}

.logo-top-right {
    position: absolute;
    top: 30px;
    right: 30px;
    z-index: 1000;
}

.logo-top-right img {
    width: 70px;
    height: auto;
}

.login-container {
    margin: 0;
    padding: 0;
    min-height: 100vh;
    display: flex;
    align-items: center;
    background-color: #ffffff;
    overflow: hidden;
}

.login-container .row {
    width: 100%;
    margin: 0;
    height: 100vh;
}

.login-container .col-md-6 {
    padding: 0;
    height: 100%;
}

.login-image {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    padding: 0;
    overflow: hidden;
}

.login-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
}

.login-form-wrapper {
    padding: 0 80px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    height: 100%;
    max-width: 550px;
    margin: 0 auto;
}

.login-header {
    margin-bottom: 50px;
}

.login-title {
    color: #4a56e2;
    font-size: 32px;
    font-weight: 700;
    margin-bottom: 0;
    text-align: center;
}

.login-form {
    width: 100%;
}

.login-input {
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 15px;
    font-size: 16px;
    text-align: right;
    height: 55px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.05);
}

.login-btn {
    background-color: #4a56e2;
    border: none;
    border-radius: 8px;
    padding: 12px;
    font-size: 18px;
    font-weight: 600;
    height: 55px;
    box-shadow: 0 4px 10px rgba(74, 86, 226, 0.3);
    transition: all 0.3s ease;
}

.login-btn:hover {
    background-color: #3a46d2;
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(74, 86, 226, 0.4);
}

@media (max-width: 768px) {
    .login-image {
        display: none;
    }

    .login-form-wrapper {
        padding: 20px;
    }
}

/* Dashboard */
.dashboard-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background-color: #fff;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.05);
    text-align: center;
}

.stat-card .stat-icon {
    font-size: 36px;
    color: #4a56e2;
    margin-bottom: 10px;
}

.stat-card .stat-value {
    font-size: 24px;
    font-weight: 700;
    color: #333;
    margin-bottom: 5px;
}

.stat-card .stat-label {
    color: #777;
    font-size: 14px;
}

/* Tables */
.table-container {
    background-color: #fff;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.05);
    margin-bottom: 30px;
}

.table-container h3 {
    color: #4a56e2;
    margin-bottom: 20px;
}

/* Responsive styles */
@media (max-width: 768px) {
    .services-grid {
        grid-template-columns: 1fr;
    }

    .service-text {
        font-size: 16px;
    }

    .service-icon {
        font-size: 20px;
    }

    .dashboard-stats {
        grid-template-columns: 1fr;
    }
}

/* Edit Percentage Button */
.edit-percentage-btn {
    background: none;
    border: none;
    color: #4a56e2;
    font-size: 14px;
    cursor: pointer;
    padding: 0 5px;
    margin-right: 5px;
}

.edit-percentage-btn:hover {
    color: #3a46d2;
}

.client-percentage {
    font-size: 12px;
    color: #666;
    margin-right: 5px;
}

/* Animation for cards */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.service-card {
    animation: fadeIn 0.5s ease forwards;
}

.service-card:nth-child(1) { animation-delay: 0.1s; }
.service-card:nth-child(2) { animation-delay: 0.2s; }
.service-card:nth-child(3) { animation-delay: 0.3s; }
.service-card:nth-child(4) { animation-delay: 0.4s; }
.service-card:nth-child(5) { animation-delay: 0.5s; }
.service-card:nth-child(6) { animation-delay: 0.6s; }
.service-card:nth-child(7) { animation-delay: 0.7s; }
.service-card:nth-child(8) { animation-delay: 0.8s; }
.service-card:nth-child(9) { animation-delay: 0.9s; }
.service-card:nth-child(10) { animation-delay: 1s; }

/* Modal Dialog Centered - Basic Style */
.modal-dialog-centered {
    display: flex;
    align-items: center;
    min-height: calc(100% - 3rem);
    margin: 0 auto;
}

/* Previous Account Modal Styles */
.prev-account-modal .modal-dialog {
    max-width: 400px;
}

.prev-account-modal .modal-content {
    border-radius: 15px;
    border: none;
    overflow: hidden;
}

.prev-account-modal .modal-header {
    background-color: #4a56e2;
    color: white;
    border-bottom: none;
    padding: 15px;
    position: relative;
    justify-content: center;
    align-items: center;
    text-align: center;
}

.prev-account-modal .modal-title {
    color: white;
    font-weight: 600;
    font-size: 18px;
    margin: 0;
    width: 100%;
    text-align: center;
}

.prev-account-modal .modal-body {
    padding: 0;
}

/* Print Button */
.prev-print-btn {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: white;
    font-size: 18px;
    cursor: pointer;
}

/* Close Button */
.prev-close-btn {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: white;
    font-size: 18px;
    cursor: pointer;
}

/* Previous Account Content */
.prev-account-content {
    padding: 0;
}

/* Previous Account Table */
.prev-account-table {
    width: 100%;
    border-collapse: collapse;
}

.prev-account-table tr {
    border-bottom: 1px solid #dcf343;
}

.prev-account-table tr:last-child {
    border-bottom: none;
}

/* Previous Balance Row */
.prev-balance-row {
    background-color: #f9f9f9;
}

.prev-balance-row td {
    padding: 15px;
}

.prev-balance-row .amount {
    color: #e74c3c;
    font-weight: 600;
    text-align: right;
}

.prev-balance-row .label {
    color: #4a56e2;
    font-weight: 600;
    text-align: left;
}

/* Month Row */
.month-row {
    background-color: #f0f2ff;
}

.month-row td {
    padding: 10px 15px;
    color: #4a56e2;
    font-weight: 600;
    text-align: center;
}

/* Item Row */
.item-row td {
    padding: 10px 15px;
}

.item-row .amount {
    color: #4a56e2;
    font-weight: 600;
    text-align: right;
}

.item-row .label {
    color: #4a56e2;
    font-weight: 600;
    text-align: left;
}

/* Total Row */
.total-row {
    background-color: #f9f9f9;
}

.total-row td {
    padding: 10px 15px;
}

.total-row .amount {
    color: #4a56e2;
    font-weight: 700;
    text-align: right;
}

.total-row .label {
    color: #4a56e2;
    font-weight: 700;
    text-align: left;
}

/* Payment Header */
.payment-header {
    background-color: #f0f2ff;
}

.payment-header td {
    padding: 10px 15px;
    color: #4a56e2;
    font-weight: 600;
    text-align: center;
}

/* Payment Titles */
.payment-titles td {
    padding: 10px 15px;
    color: #4a56e2;
    font-weight: 600;
}

.payment-titles .date {
    text-align: left;
}

.payment-titles .amount {
    text-align: right;
}

/* Payment Row */
.payment-row td {
    padding: 10px 15px;
}

.payment-row .date {
    color: #4a56e2;
    font-weight: 600;
    text-align: left;
}

.payment-row .amount {
    color: #4a56e2;
    font-weight: 600;
    text-align: right;
}

/* Section Separator */
.section-separator {
    height: 1px;
    background-color: #dcf343;
    margin: 0;
}

.btn-add-payment, .btn-pay-previous {
    background-color: #4a56e2;
    color: white;
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-left: 10px;
}

.btn-add-payment:hover, .btn-pay-previous:hover {
    background-color: #3a46d2;
    transform: scale(1.1);
}

.btn-pay-previous {
    background-color: #28a745;
}

.btn-pay-previous:hover {
    background-color: #218838;
}

/* حساب العميل نهاية الشهر */
.end-month-balance-container {
    display: flex;
    flex-direction: column;
    height: 500px;
}

.end-month-tabs {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.nav-tabs {
    background-color: #f8f9fa;
    border-bottom: 2px solid #dcf343;
}

.nav-tabs .nav-link {
    color: #4a56e2;
    font-weight: 600;
    border: none;
    padding: 10px 20px;
}

.nav-tabs .nav-link.active {
    color: #4a56e2;
    background-color: #dcf343;
    border: none;
}

.tab-content {
    flex: 1;
    overflow: hidden;
    padding: 15px;
    background-color: #fff;
}

.tab-pane {
    height: 100%;
    overflow-y: auto;
}

.end-month-footer {
    background-color: #f8f9fa;
    padding: 15px;
    border-top: 2px solid #dcf343;
    text-align: center;
}

.totals-summary {
    margin-bottom: 15px;
    border-bottom: 1px solid #dcf343;
    padding-bottom: 15px;
}

.totals-row {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
}

.total-item {
    flex: 1;
    min-width: 150px;
    margin: 5px;
    padding: 10px;
    background-color: #f0f2ff;
    border-radius: 5px;
    text-align: center;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.total-item-label {
    display: block;
    font-weight: 600;
    color: #4a56e2;
    margin-bottom: 5px;
}

.total-item-value {
    display: block;
    font-size: 16px;
    font-weight: 700;
    color: #4a56e2;
}

.total-balance {
    font-size: 18px;
    font-weight: 700;
    color: #4a56e2;
    padding: 10px;
    background-color: #dcf343;
    border-radius: 5px;
}

.total-label {
    margin-left: 10px;
}

.total-value {
    font-size: 20px;
    color: #4a56e2;
}

.end-month-print-btn {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #4a56e2;
    font-size: 18px;
    cursor: pointer;
}

.loading-spinner {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100px;
    color: #4a56e2;
}

/* تنسيق الجداول داخل التبويبات */
.tab-table {
    width: 100%;
    border-collapse: collapse;
}

.tab-table th, .tab-table td {
    padding: 10px;
    text-align: center;
    border-bottom: 1px solid #dcf343;
}

.tab-table th {
    background-color: #f0f2ff;
    color: #4a56e2;
    font-weight: 600;
}

.tab-table tr:hover {
    background-color: #f8f9fa;
}

.amount-cell {
    font-weight: 600;
    color: #4a56e2;
}

.total-row td {
    background-color: #dcf343;
    font-weight: 700;
    color: #4a56e2;
}
