/**
 * ملف JavaScript لصفحة اكونتات فوري
 */

// عند تحميل المستند
document.addEventListener('DOMContentLoaded', function() {
    // تهيئة البحث
    initSearch();

    // تهيئة أزرار إضافة الإعلانات
    initAddAdButtons();

    // تهيئة نموذج إضافة الإعلان
    initAddAdForm();

    // تهيئة خاصية النقر المزدوج على صفوف الإعلانات
    initAdRowsDblClick();

    // تهيئة نموذج تعديل الإعلان
    initEditAdForm();
});

/**
 * تهيئة البحث
 */
function initSearch() {
    const searchInput = document.getElementById('searchInput');
    if (searchInput) {
        searchInput.addEventListener('keyup', function() {
            const searchText = this.value.toLowerCase();
            const accountSections = document.querySelectorAll('.account-section');

            accountSections.forEach(section => {
                const accountName = section.querySelector('.account-name').textContent.toLowerCase();

                if (accountName.includes(searchText)) {
                    section.style.display = '';
                } else {
                    // البحث في المعاملات
                    const transactions = section.querySelectorAll('tbody tr');
                    let found = false;

                    transactions.forEach(transaction => {
                        const cells = transaction.querySelectorAll('td');
                        cells.forEach(cell => {
                            if (cell.textContent.toLowerCase().includes(searchText)) {
                                found = true;
                            }
                        });
                    });

                    section.style.display = found ? '' : 'none';
                }
            });
        });
    }
}

/**
 * تهيئة أزرار إضافة الإعلانات
 */
function initAddAdButtons() {
    const addButtons = document.querySelectorAll('.add-transaction-button');

    addButtons.forEach(button => {
        button.addEventListener('click', function() {
            const accountSection = this.closest('.account-card');
            const accountId = accountSection.getAttribute('data-account-id');

            // تعيين معرف الحساب في النموذج
            document.getElementById('adAccountIdHidden').value = accountId;
            document.getElementById('adAccount').value = accountId;

            // عرض النافذة المنبثقة
            const modal = new bootstrap.Modal(document.getElementById('addAdModal'));
            modal.show();
        });
    });
}

/**
 * تهيئة نموذج إضافة الإعلان
 */
function initAddAdForm() {
    const form = document.getElementById('addAdForm');

    if (form) {
        form.addEventListener('submit', function(e) {
            e.preventDefault();

            // منع إرسال النموذج مرتين
            if (window.isSubmitting) {
                console.log('تم منع إرسال النموذج مرتين');
                return;
            }

            window.isSubmitting = true;

            const clientSelect = document.getElementById('adClient');
            const clientId = clientSelect.value;
            const clientType = clientSelect.options[clientSelect.selectedIndex].getAttribute('data-type') === 'client' ? 'client' : 'ad_client';
            const date = document.getElementById('adDate').value;
            const type = document.getElementById('adType').value;
            const dailyTotal = document.getElementById('adDailyTotal').value;
            const post = document.getElementById('adPost').value;
            let days = document.getElementById('adDays').value;
            const cost = document.getElementById('adCost').value;
            const exchangeRate = document.getElementById('adExchangeRate').value;
            const adAccountId = document.getElementById('adAccount').value;

            if (!date || !cost || !exchangeRate || !adAccountId || !clientId) {
                alert('يرجى إدخال جميع البيانات المطلوبة');
                window.isSubmitting = false;
                return;
            }

            // تحديد الحالة بناءً على القيم المدخلة
            let status = '';
            if (dailyTotal === 'يومي') {
                status = 'يومي مستمر';
                // تأكد من أن عدد الأيام هو 1 للإعلانات اليومية
                days = 1;
            } else {
                status = 'نشط ' + days + ' أيام';
            }

            const adData = {
                client_id: clientId,
                client_type: clientType,
                date: date,
                type: type,
                payment_type: dailyTotal,
                post: post,
                days: days,
                cost: cost,
                status: status,
                exchange_rate: exchangeRate,
                ad_account_id: adAccountId,
                egyptian_cost: 0 // قيمة افتراضية
            };

            // تعطيل زر الإرسال
            const submitButton = document.querySelector('.add-ad-submit');
            if (submitButton) {
                submitButton.disabled = true;
                submitButton.innerHTML = 'جاري الإضافة...';
            }

            // إرسال البيانات إلى الخادم
            fetch('api/ads/index.php?action=add', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(adData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // إغلاق النافذة المنبثقة
                    bootstrap.Modal.getInstance(document.getElementById('addAdModal')).hide();

                    // إعادة تعيين النموذج
                    document.getElementById('addAdForm').reset();

                    // إعادة تحميل الصفحة
                    window.location.reload();
                } else {
                    alert('حدث خطأ أثناء إضافة الإعلان: ' + (data.message || 'خطأ غير معروف'));
                }
            })
            .catch(error => {
                console.error('Error adding ad:', error);
                alert('حدث خطأ أثناء إضافة الإعلان');
            })
            .finally(() => {
                // إعادة تفعيل زر الإرسال
                if (submitButton) {
                    submitButton.disabled = false;
                    submitButton.innerHTML = 'إضافة اعلان';
                }

                // إعادة تعيين متغير الإرسال
                window.isSubmitting = false;
            });
        });
    }
}

/**
 * تهيئة خاصية النقر المزدوج على صفوف الإعلانات
 */
function initAdRowsDblClick() {
    // إضافة مستمع النقر المزدوج على صفوف الإعلانات
    const adRows = document.querySelectorAll('.transactions-table tbody tr');

    adRows.forEach(row => {
        row.addEventListener('dblclick', function() {
            // الحصول على بيانات الإعلان من الصف
            const cells = this.querySelectorAll('td');
            if (cells.length < 7) return; // تأكد من وجود عدد كافٍ من الخلايا

            const clientName = cells[0].textContent.trim();
            const post = cells[1].textContent.trim();
            const cost = parseFloat(cells[2].textContent.replace(/,/g, ''));
            const exchangeRate = parseFloat(cells[3].textContent.replace(/,/g, ''));
            const days = parseInt(cells[4].textContent.trim());
            const dailyAmount = parseFloat(cells[5].textContent.replace(/,/g, ''));
            const status = cells[6].textContent.trim();

            // الحصول على معرف الإعلان من سمة data-ad-id
            const adId = this.getAttribute('data-ad-id');
            if (!adId) {
                console.error('لم يتم العثور على معرف الإعلان');
                return;
            }

            // الحصول على معرف الحساب الإعلاني من أقرب عنصر account-card
            const accountSection = this.closest('.account-card');
            const accountId = accountSection.getAttribute('data-account-id');

            // تعبئة نموذج تعديل الإعلان
            document.getElementById('editAdId').value = adId;
            document.getElementById('editAdAccountId').value = accountId;

            // البحث عن العميل في القائمة المنسدلة
            const clientSelect = document.getElementById('editAdClient');
            let clientFound = false;

            for (let i = 0; i < clientSelect.options.length; i++) {
                if (clientSelect.options[i].text === clientName) {
                    clientSelect.selectedIndex = i;
                    clientFound = true;
                    break;
                }
            }

            if (!clientFound) {
                console.warn('لم يتم العثور على العميل في القائمة المنسدلة');
            }

            // تعبئة باقي الحقول
            // نحتاج إلى الحصول على التاريخ ونوع الإعلان من API
            fetchAdDetails(adId, function(adDetails) {
                if (adDetails) {
                    document.getElementById('editAdDate').value = adDetails.date;
                    document.getElementById('editAdType').value = adDetails.type;
                    document.getElementById('editAdDailyTotal').value = adDetails.payment_type;
                    document.getElementById('editAdPost').value = post;
                    document.getElementById('editAdDays').value = days;
                    document.getElementById('editAdCost').value = cost;
                    document.getElementById('editAdExchangeRate').value = exchangeRate;

                    // تعيين الحالة
                    const statusSelect = document.getElementById('editAdStatus');
                    if (status.includes('متوقف')) {
                        statusSelect.value = 'متوقف';
                    } else {
                        statusSelect.value = 'نشط';
                    }

                    // عرض النافذة المنبثقة
                    const modal = new bootstrap.Modal(document.getElementById('editAdModal'));
                    modal.show();
                }
            });
        });
    });
}

/**
 * جلب تفاصيل الإعلان من API
 */
function fetchAdDetails(adId, callback) {
    fetch(`api/ads/index.php?action=get_ad&id=${adId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                callback(data.ad);
            } else {
                console.error('حدث خطأ أثناء جلب تفاصيل الإعلان:', data.message);
                alert('حدث خطأ أثناء جلب تفاصيل الإعلان: ' + data.message);
                callback(null);
            }
        })
        .catch(error => {
            console.error('Error fetching ad details:', error);
            alert('حدث خطأ أثناء جلب تفاصيل الإعلان');
            callback(null);
        });
}

/**
 * تهيئة نموذج تعديل الإعلان
 */
function initEditAdForm() {
    const form = document.getElementById('editAdForm');

    if (form) {
        form.addEventListener('submit', function(e) {
            e.preventDefault();

            // منع إرسال النموذج مرتين
            if (window.isSubmittingEdit) {
                console.log('تم منع إرسال النموذج مرتين');
                return;
            }

            window.isSubmittingEdit = true;

            const adId = document.getElementById('editAdId').value;
            const clientSelect = document.getElementById('editAdClient');
            const clientId = clientSelect.value;
            const clientType = clientSelect.options[clientSelect.selectedIndex].getAttribute('data-type') === 'client' ? 'client' : 'ad_client';
            const date = document.getElementById('editAdDate').value;
            const type = document.getElementById('editAdType').value;
            const dailyTotal = document.getElementById('editAdDailyTotal').value;
            const post = document.getElementById('editAdPost').value;
            let days = document.getElementById('editAdDays').value;
            const cost = document.getElementById('editAdCost').value;
            const exchangeRate = document.getElementById('editAdExchangeRate').value;
            const adAccountId = document.getElementById('editAdAccountId').value;
            const status = document.getElementById('editAdStatus').value;

            if (!date || !cost || !exchangeRate || !adAccountId || !clientId) {
                alert('يرجى إدخال جميع البيانات المطلوبة');
                window.isSubmittingEdit = false;
                return;
            }

            // تحديد الحالة بناءً على القيم المدخلة
            let finalStatus = status;
            if (status === 'نشط') {
                if (dailyTotal === 'يومي') {
                    finalStatus = 'يومي مستمر';
                    // تأكد من أن عدد الأيام هو 1 للإعلانات اليومية
                    days = 1;
                } else {
                    finalStatus = 'نشط ' + days + ' أيام';
                }
            }

            const adData = {
                id: adId,
                client_id: clientId,
                client_type: clientType,
                date: date,
                type: type,
                payment_type: dailyTotal,
                post: post,
                days: days,
                cost: cost,
                status: finalStatus,
                exchange_rate: exchangeRate,
                ad_account_id: adAccountId,
                egyptian_cost: 0 // قيمة افتراضية
            };

            // تعطيل زر الإرسال
            const submitButton = document.querySelector('.edit-ad-submit');
            if (submitButton) {
                submitButton.disabled = true;
                submitButton.innerHTML = 'جاري الحفظ...';
            }

            // إرسال البيانات إلى الخادم
            fetch('api/ads/index.php?action=update', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(adData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // إغلاق النافذة المنبثقة
                    bootstrap.Modal.getInstance(document.getElementById('editAdModal')).hide();

                    // إعادة تعيين النموذج
                    document.getElementById('editAdForm').reset();

                    // إعادة تحميل الصفحة
                    window.location.reload();
                } else {
                    alert('حدث خطأ أثناء تعديل الإعلان: ' + (data.message || 'خطأ غير معروف'));
                }
            })
            .catch(error => {
                console.error('Error updating ad:', error);
                alert('حدث خطأ أثناء تعديل الإعلان');
            })
            .finally(() => {
                // إعادة تفعيل زر الإرسال
                if (submitButton) {
                    submitButton.disabled = false;
                    submitButton.innerHTML = 'حفظ التعديلات';
                }

                // إعادة تعيين متغير الإرسال
                window.isSubmittingEdit = false;
            });
        });
    }
}