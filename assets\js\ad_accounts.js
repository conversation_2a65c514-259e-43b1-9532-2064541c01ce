/**
 * ملف JavaScript لصفحة إدارة الحسابات الإعلانية
 */

// عند تحميل المستند
$(document).ready(function() {
    // تهيئة البحث
    initSearch();

    // تهيئة نموذج إضافة حساب
    initAddAccountForm();

    // تهيئة نموذج تعديل حساب
    initEditAccountForm();

    // تهيئة حذف الحساب
    initDeleteAccount();
});

/**
 * تهيئة البحث
 */
function initSearch() {
    $('#searchInput').on('keyup', function() {
        const searchText = $(this).val().toLowerCase();

        $('#accountsTableBody tr').each(function() {
            const accountName = $(this).find('td:nth-child(2)').text().toLowerCase();
            const accountStatus = $(this).find('td:nth-child(3)').text().toLowerCase();
            const accountNotes = $(this).find('td:nth-child(8)').text().toLowerCase();

            if (accountName.includes(searchText) || accountStatus.includes(searchText) || accountNotes.includes(searchText)) {
                $(this).show();
            } else {
                $(this).hide();
            }
        });
    });
}

/**
 * تهيئة نموذج إضافة حساب
 */
function initAddAccountForm() {
    // تهيئة التبديل بين أنواع الحسابات المرتبطة
    $('#accountLinkedType').on('change', function() {
        const selectedType = $(this).val();
        $('.linked-account-container').hide();

        if (selectedType === 'credit_card') {
            $('#creditCardContainer').show();
        } else if (selectedType === 'visa') {
            $('#visaContainer').show();
        } else if (selectedType === 'fawry') {
            $('#fawryContainer').show();
        }
    });

    $('#addAccountForm').on('submit', function(e) {
        e.preventDefault();

        const linkedType = $('#accountLinkedType').val();
        let linkedId = null;

        if (linkedType === 'credit_card') {
            linkedId = $('#accountCreditCard').val();
        } else if (linkedType === 'visa') {
            linkedId = $('#accountVisa').val();
        } else if (linkedType === 'fawry') {
            linkedId = $('#accountFawry').val();
        }

        const formData = {
            name: $('#accountName').val(),
            status: $('#accountStatus').val(),
            balance: $('#accountBalance').val(),
            spending_limit: $('#accountSpendingLimit').val(),
            notes: $('#accountNotes').val(),
            linked_account_type: linkedType,
            linked_account_id: linkedId
        };

        // إرسال البيانات إلى الخادم
        $.ajax({
            url: 'api/ad_accounts/add.php',
            type: 'POST',
            data: JSON.stringify(formData),
            contentType: 'application/json',
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    // إغلاق النافذة المنبثقة
                    $('#addAccountModal').modal('hide');

                    // إعادة تحميل الصفحة
                    window.location.reload();
                } else {
                    alert('حدث خطأ أثناء إضافة الحساب: ' + response.message);
                }
            },
            error: function(xhr, status, error) {
                alert('حدث خطأ أثناء إضافة الحساب');
                console.error(xhr.responseText);
            }
        });
    });

    // إعادة تعيين النموذج عند فتح النافذة المنبثقة
    $('#addAccountModal').on('show.bs.modal', function() {
        $('#addAccountForm')[0].reset();
        $('.linked-account-container').hide();
    });
}

/**
 * تهيئة نموذج تعديل حساب
 */
function initEditAccountForm() {
    // تهيئة التبديل بين أنواع الحسابات المرتبطة
    $('#editAccountLinkedType').on('change', function() {
        const selectedType = $(this).val();
        $('.linked-account-container').hide();

        if (selectedType === 'credit_card') {
            $('#editCreditCardContainer').show();
        } else if (selectedType === 'visa') {
            $('#editVisaContainer').show();
        } else if (selectedType === 'fawry') {
            $('#editFawryContainer').show();
        }
    });

    // عند النقر على زر التعديل
    $('.edit-account-btn').on('click', function() {
        const id = $(this).data('id');
        const name = $(this).data('name');
        const status = $(this).data('status');
        const balance = $(this).data('balance');
        const spendingLimit = $(this).data('spending-limit');
        const notes = $(this).data('notes');
        const linkedType = $(this).data('linked-type');
        const linkedId = $(this).data('linked-id');

        // تعبئة النموذج بالبيانات
        $('#editAccountId').val(id);
        $('#editAccountName').val(name);
        $('#editAccountStatus').val(status);
        $('#editAccountBalance').val(balance);
        $('#editAccountSpendingLimit').val(spendingLimit);
        $('#editAccountNotes').val(notes);
        $('#editAccountLinkedType').val(linkedType);

        // إظهار الحقول المناسبة بناءً على نوع الحساب المرتبط
        $('.linked-account-container').hide();
        if (linkedType === 'credit_card') {
            $('#editCreditCardContainer').show();
            $('#editAccountCreditCard').val(linkedId);
        } else if (linkedType === 'visa') {
            $('#editVisaContainer').show();
            $('#editAccountVisa').val(linkedId);
        } else if (linkedType === 'fawry') {
            $('#editFawryContainer').show();
            $('#editAccountFawry').val(linkedId);
        }

        // فتح النافذة المنبثقة
        $('#editAccountModal').modal('show');
    });

    // عند تقديم نموذج التعديل
    $('#editAccountForm').on('submit', function(e) {
        e.preventDefault();

        const linkedType = $('#editAccountLinkedType').val();
        let linkedId = null;

        if (linkedType === 'credit_card') {
            linkedId = $('#editAccountCreditCard').val();
        } else if (linkedType === 'visa') {
            linkedId = $('#editAccountVisa').val();
        } else if (linkedType === 'fawry') {
            linkedId = $('#editAccountFawry').val();
        }

        const formData = {
            id: $('#editAccountId').val(),
            name: $('#editAccountName').val(),
            status: $('#editAccountStatus').val(),
            balance: $('#editAccountBalance').val(),
            spending_limit: $('#editAccountSpendingLimit').val(),
            notes: $('#editAccountNotes').val(),
            linked_account_type: linkedType,
            linked_account_id: linkedId
        };

        // إرسال البيانات إلى الخادم
        $.ajax({
            url: 'api/ad_accounts/update.php',
            type: 'POST',
            data: JSON.stringify(formData),
            contentType: 'application/json',
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    // إغلاق النافذة المنبثقة
                    $('#editAccountModal').modal('hide');

                    // إعادة تحميل الصفحة
                    window.location.reload();
                } else {
                    alert('حدث خطأ أثناء تعديل الحساب: ' + response.message);
                }
            },
            error: function(xhr, status, error) {
                alert('حدث خطأ أثناء تعديل الحساب');
                console.error(xhr.responseText);
            }
        });
    });
}

/**
 * تهيئة حذف الحساب
 */
function initDeleteAccount() {
    // عند النقر على زر الحذف
    $('.delete-account-btn').on('click', function() {
        const id = $(this).data('id');
        const name = $(this).data('name');

        // تعيين اسم الحساب في نافذة التأكيد
        $('#deleteAccountName').text(name);

        // تعيين معرف الحساب لزر التأكيد
        $('#confirmDeleteBtn').data('id', id);

        // فتح نافذة التأكيد
        $('#deleteAccountModal').modal('show');
    });

    // عند النقر على زر تأكيد الحذف
    $('#confirmDeleteBtn').on('click', function() {
        const id = $(this).data('id');

        // إرسال طلب الحذف إلى الخادم
        $.ajax({
            url: 'api/ad_accounts/delete.php',
            type: 'POST',
            data: JSON.stringify({ id: id }),
            contentType: 'application/json',
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    // إغلاق النافذة المنبثقة
                    $('#deleteAccountModal').modal('hide');

                    // إعادة تحميل الصفحة
                    window.location.reload();
                } else {
                    alert('حدث خطأ أثناء حذف الحساب: ' + response.message);
                }
            },
            error: function(xhr, status, error) {
                alert('حدث خطأ أثناء حذف الحساب');
                console.error(xhr.responseText);
            }
        });
    });
}
