/**
 * ملف JavaScript لصفحة تقرير حسابات الإعلانات
 */

// المتغيرات العامة
let adAccountsData = [];
let clientTypesData = [];
let currentClientTypeFilter = '';
let currentSearchTerm = '';

// عند تحميل المستند
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM Content Loaded - Ad Accounts Report JS');

    // إعداد مستمعي الأحداث
    setupEventListeners();

    // جلب فئات العملاء أولاً
    fetchClientTypes();

    // جلب بيانات حسابات الإعلانات
    fetchAdAccountsReportData();
});

/**
 * إعداد مستمعي الأحداث
 */
function setupEventListeners() {
    // مستمع حدث للبحث
    const searchInput = document.getElementById('searchInput');
    if (searchInput) {
        searchInput.addEventListener('input', function() {
            currentSearchTerm = this.value;
            applyFilters();
        });
    }

    // مستمع حدث لفلتر نوع العميل
    const clientTypeFilter = document.getElementById('clientTypeFilter');
    if (clientTypeFilter) {
        clientTypeFilter.addEventListener('change', function() {
            currentClientTypeFilter = this.value;
            applyFilters();
        });
    }
}

/**
 * جلب فئات العملاء من قاعدة البيانات
 */
function fetchClientTypes() {
    console.log('Fetching client types data');

    // جلب البيانات من الخادم
    fetch('api/client_types.php')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                console.log('Client types data:', data);

                // تخزين البيانات
                clientTypesData = data.client_types;

                // تحديث dropdown الفلتر
                updateClientTypeFilter();
            } else {
                console.error('Error fetching client types data:', data.message);
            }
        })
        .catch(error => {
            console.error('Error fetching client types data:', error);
        });
}

/**
 * تحديث dropdown فلتر فئات العملاء
 */
function updateClientTypeFilter() {
    const filterSelect = document.getElementById('clientTypeFilter');
    if (!filterSelect) return;

    // الاحتفاظ بالقيمة المحددة حالياً
    const currentValue = filterSelect.value;

    // مسح الخيارات الموجودة (عدا الخيار الأول)
    filterSelect.innerHTML = '<option value="">جميع العملاء</option>';

    // إضافة فئات العملاء
    clientTypesData.forEach(clientType => {
        const option = document.createElement('option');
        option.value = clientType.name;
        option.textContent = clientType.display_name;
        filterSelect.appendChild(option);
    });

    // إعادة تعيين القيمة المحددة
    filterSelect.value = currentValue;
}

/**
 * جلب بيانات تقرير حسابات الإعلانات
 */
function fetchAdAccountsReportData() {
    console.log('Fetching ad accounts report data');

    // إعادة تعيين القيم إلى 0
    resetTotals();

    // عرض مؤشر التحميل
    document.getElementById('adAccountsReportTableBody').innerHTML = '<tr><td colspan="6" class="text-center">جاري تحميل البيانات...</td></tr>';

    // جلب البيانات من الخادم
    fetch('api/ad_accounts_report.php')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                console.log('Ad accounts report data:', data);

                // تخزين البيانات
                adAccountsData = data.accounts;

                // عرض البيانات مع تطبيق الفلاتر الحالية
                applyFilters();
            } else {
                console.error('Error fetching ad accounts report data:', data.message);
                document.getElementById('adAccountsReportTableBody').innerHTML = `<tr><td colspan="6" class="text-center">حدث خطأ أثناء جلب البيانات: ${data.message}</td></tr>`;

                // عرض بيانات تجريبية في حالة الخطأ
                displaySampleData();
            }
        })
        .catch(error => {
            console.error('Error fetching ad accounts report data:', error);
            document.getElementById('adAccountsReportTableBody').innerHTML = '<tr><td colspan="6" class="text-center">حدث خطأ أثناء جلب البيانات</td></tr>';

            // عرض بيانات تجريبية في حالة الخطأ
            displaySampleData();
        });
}

/**
 * عرض بيانات تجريبية للاختبار
 */
function displaySampleData() {
    const sampleData = [
        // Client A accounts
        { id: 1, name: 'Dr M7med Elraf3y', type: 'A', total_exchange: 387.00, total_exchange_with_percentage: 0.00, payments: 0.00, daily_exchange: 387.00, current_total: 387.00 },
        { id: 2, name: 'Ahmed Ali', type: 'A', total_exchange: 437.00, total_exchange_with_percentage: 50.00, payments: 100.00, daily_exchange: 412.00, current_total: 487.00 },
        { id: 3, name: 'Sara Mohamed', type: 'A', total_exchange: 487.00, total_exchange_with_percentage: 100.00, payments: 200.00, daily_exchange: 437.00, current_total: 587.00 },
        { id: 4, name: 'Omar Hassan', type: 'A', total_exchange: 537.00, total_exchange_with_percentage: 150.00, payments: 300.00, daily_exchange: 462.00, current_total: 687.00 },

        // Client B accounts
        { id: 5, name: 'Fatma Khaled', type: 'B', total_exchange: 587.00, total_exchange_with_percentage: 200.00, payments: 400.00, daily_exchange: 487.00, current_total: 787.00 },
        { id: 6, name: 'Mohamed Saeed', type: 'B', total_exchange: 637.00, total_exchange_with_percentage: 250.00, payments: 500.00, daily_exchange: 512.00, current_total: 887.00 },
        { id: 7, name: 'Nour Ahmed', type: 'B', total_exchange: 687.00, total_exchange_with_percentage: 300.00, payments: 600.00, daily_exchange: 537.00, current_total: 987.00 },
        { id: 8, name: 'Youssef Ibrahim', type: 'B', total_exchange: 737.00, total_exchange_with_percentage: 350.00, payments: 700.00, daily_exchange: 562.00, current_total: 1087.00 },

        // Client C accounts
        { id: 9, name: 'Mona Farouk', type: 'C', total_exchange: 787.00, total_exchange_with_percentage: 400.00, payments: 800.00, daily_exchange: 587.00, current_total: 1187.00 },
        { id: 10, name: 'Karim Mostafa', type: 'C', total_exchange: 837.00, total_exchange_with_percentage: 450.00, payments: 900.00, daily_exchange: 612.00, current_total: 1287.00 },
        { id: 11, name: 'Aya Mahmoud', type: 'C', total_exchange: 887.00, total_exchange_with_percentage: 500.00, payments: 1000.00, daily_exchange: 637.00, current_total: 1387.00 },
        { id: 12, name: 'Hassan Adel', type: 'C', total_exchange: 937.00, total_exchange_with_percentage: 550.00, payments: 1100.00, daily_exchange: 662.00, current_total: 1487.00 }
    ];

    adAccountsData = sampleData;
    console.log('Sample data loaded:', adAccountsData);
    applyFilters(); // تطبيق الفلاتر بدلاً من العرض المباشر
}

/**
 * عرض بيانات تقرير حسابات الإعلانات
 * @param {Array} accounts بيانات الحسابات
 */
function displayAdAccountsReportData(accounts) {
    const tableBody = document.getElementById('adAccountsReportTableBody');

    if (!accounts || accounts.length === 0) {
        let message = 'لا توجد بيانات';
        if (currentClientTypeFilter && currentSearchTerm) {
            message = `لا توجد نتائج للعميل "Client ${currentClientTypeFilter}" والبحث "${currentSearchTerm}"`;
        } else if (currentClientTypeFilter) {
            message = `لا توجد نتائج للعميل "Client ${currentClientTypeFilter}"`;
        } else if (currentSearchTerm) {
            message = `لا توجد نتائج للبحث "${currentSearchTerm}"`;
        }
        tableBody.innerHTML = `<tr><td colspan="6" class="text-center">${message}</td></tr>`;
        return;
    }

    console.log('Displaying', accounts.length, 'accounts');
    let html = '';

    accounts.forEach(account => {
        // تحديد لون الصف بناءً على إجمالي الحساب الحالي
        // القيمة الموجبة تعني مديونية (العميل مدين للشركة) - لون أحمر
        // القيمة السالبة تعني مسبق الدفع (الشركة مدينة للعميل) - لون أخضر
        const amountClass = account.current_total > 0 ? 'negative' : 'positive';

        html += `
        <tr>
            <td class="account-name">${account.name} <small>(Client ${account.type || 'N/A'})</small></td>
            <td class="amount">EGP ${account.total_exchange.toFixed(2)}</td>
            <td class="amount">EGP ${account.total_exchange_with_percentage.toFixed(2)}</td>
            <td class="amount">EGP ${account.payments.toFixed(2)}</td>
            <td class="amount">EGP ${account.daily_exchange.toFixed(2)}</td>
            <td class="amount ${amountClass}">EGP ${account.current_total.toFixed(2)}</td>
        </tr>`;
    });

    tableBody.innerHTML = html;
    console.log('Table updated with filtered data');
}

/**
 * تطبيق جميع الفلاتر
 */
function applyFilters() {
    if (!adAccountsData || adAccountsData.length === 0) {
        console.log('No data available for filtering');
        return;
    }

    console.log('Applying filters - Client Type:', currentClientTypeFilter, 'Search Term:', currentSearchTerm);

    let filteredAccounts = [...adAccountsData]; // نسخة من البيانات الأصلية

    // فلترة حسب نوع العميل أولاً
    if (currentClientTypeFilter && currentClientTypeFilter !== '') {
        console.log('Filtering by client type:', currentClientTypeFilter);
        filteredAccounts = filteredAccounts.filter(account => {
            const matches = account.type === currentClientTypeFilter;
            console.log(`Account ${account.name} (${account.type}) matches: ${matches}`);
            return matches;
        });
        console.log('After client type filter:', filteredAccounts.length, 'accounts');
    }

    // ثم فلترة حسب البحث النصي
    if (currentSearchTerm && currentSearchTerm.trim() !== '') {
        const searchTerm = currentSearchTerm.toLowerCase().trim();
        console.log('Filtering by search term:', searchTerm);
        filteredAccounts = filteredAccounts.filter(account => {
            const matches = account.name.toLowerCase().includes(searchTerm);
            console.log(`Account ${account.name} matches search: ${matches}`);
            return matches;
        });
        console.log('After search filter:', filteredAccounts.length, 'accounts');
    }

    console.log('Final filtered accounts:', filteredAccounts);

    // عرض البيانات المفلترة
    displayAdAccountsReportData(filteredAccounts);
    updateTotals(filteredAccounts);
}

/**
 * تصفية بيانات تقرير حسابات الإعلانات (للتوافق مع الكود القديم)
 * @param {string} searchTerm مصطلح البحث
 */
function filterAdAccountsReport(searchTerm) {
    currentSearchTerm = searchTerm;
    applyFilters();
}

/**
 * إعادة تعيين الإجماليات إلى 0
 */
function resetTotals() {
    document.getElementById('totalExchange').textContent = 'EGP 0.00';
    document.getElementById('totalExchangeRaw').textContent = 'EGP 0.00';
    document.getElementById('totalExchangeWithPercentage').textContent = 'EGP 0.00';
    document.getElementById('totalPayments').textContent = 'EGP 0.00';
    document.getElementById('totalDailyExchange').textContent = 'EGP 0.00';
    document.getElementById('totalDebts').textContent = 'EGP 0.00';
    document.getElementById('totalPrepaidSecond').textContent = 'EGP 0.00';

    // إزالة الكلاسات الملونة
    document.getElementById('totalExchange').classList.remove('positive-value', 'negative-value');
    document.getElementById('totalDebts').classList.remove('positive-value', 'negative-value');
    document.getElementById('totalPrepaidSecond').classList.remove('positive-value', 'negative-value');
}

/**
 * تحديث الإجماليات
 * @param {Array} accounts بيانات الحسابات
 */
function updateTotals(accounts) {
    if (!accounts || accounts.length === 0) {
        return;
    }

    // حساب الإجماليات
    const totals = accounts.reduce((acc, account) => {
        acc.totalExchange += account.total_exchange;
        acc.totalExchangeWithPercentage += account.total_exchange_with_percentage;
        acc.totalPayments += account.payments;
        acc.totalDailyExchange += account.daily_exchange;
        acc.totalCurrent += account.current_total;
        return acc;
    }, {
        totalExchange: 0,
        totalExchangeWithPercentage: 0,
        totalPayments: 0,
        totalDailyExchange: 0,
        totalCurrent: 0
    });

    // تحديد قيم المديونيات ومسبق الدفع (عكس المنطق السابق)
    // القيمة الموجبة تعني مديونية (العميل مدين للشركة)
    // القيمة السالبة تعني مسبق الدفع (الشركة مدينة للعميل)
    const totalDebts = accounts.reduce((sum, account) => {
        return sum + (account.current_total > 0 ? account.current_total : 0);
    }, 0);

    const totalPrepaid = accounts.reduce((sum, account) => {
        return sum + (account.current_total < 0 ? Math.abs(account.current_total) : 0);
    }, 0);

    // تحديث العناصر في الفوتر الجديد مع تنسيق الأرقام
    document.getElementById('totalExchange').textContent = `EGP ${totals.totalCurrent.toFixed(2)}`;
    document.getElementById('totalExchangeRaw').textContent = `EGP ${totals.totalExchange.toFixed(2)}`;
    document.getElementById('totalExchangeWithPercentage').textContent = `EGP ${totals.totalExchangeWithPercentage.toFixed(2)}`;
    document.getElementById('totalPayments').textContent = `EGP ${totals.totalPayments.toFixed(2)}`;
    document.getElementById('totalDailyExchange').textContent = `EGP ${totals.totalDailyExchange.toFixed(2)}`;
    document.getElementById('totalDebts').textContent = `EGP ${totalDebts.toFixed(2)}`;
    document.getElementById('totalPrepaidSecond').textContent = `EGP ${totalPrepaid.toFixed(2)}`;

    // إضافة الألوان المناسبة للقيم
    const totalExchangeElement = document.getElementById('totalExchange');
    const totalDebtsElement = document.getElementById('totalDebts');
    const totalPrepaidElement = document.getElementById('totalPrepaidSecond');

    // إزالة الكلاسات السابقة
    totalExchangeElement.classList.remove('positive-value', 'negative-value');
    totalDebtsElement.classList.remove('positive-value', 'negative-value');
    totalPrepaidElement.classList.remove('positive-value', 'negative-value');

    // إضافة الكلاسات المناسبة
    if (totals.totalCurrent > 0) {
        totalExchangeElement.classList.add('negative-value'); // مديونية
    } else if (totals.totalCurrent < 0) {
        totalExchangeElement.classList.add('positive-value'); // مسبق الدفع
    }

    if (totalDebts > 0) {
        totalDebtsElement.classList.add('negative-value');
    }

    if (totalPrepaid > 0) {
        totalPrepaidElement.classList.add('positive-value');
    }
}
