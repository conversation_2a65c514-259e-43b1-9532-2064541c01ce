/**
 * ملف JavaScript للتعامل مع عملاء الإعلانات
 */

// المتغيرات العامة
let adClientsData = [];
let clientTypesData = [];
let currentClientTypeFilter = '';
let currentSearchTerm = '';

// عند تحميل المستند
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM Content Loaded - Ad Clients JS');

    // إعداد مستمعي الأحداث
    setupEventListeners();

    // جلب فئات العملاء أولاً
    fetchClientTypes();

    // جلب بيانات العملاء
    fetchAdClientsData();
});

/**
 * إعداد مستمعي الأحداث
 */
function setupEventListeners() {
    // مستمع حدث لزر إضافة عميل
    const addClientBtn = document.getElementById('addClientBtn');
    if (addClientBtn) {
        addClientBtn.addEventListener('click', function() {
            // تحديث فئات العملاء في النموذج قبل فتحه
            updateAddClientTypeDropdown();

            const addClientModal = new bootstrap.Modal(document.getElementById('addClientModal'));
            addClientModal.show();
        });
    }

    // مستمع حدث لنموذج إضافة عميل
    const addClientForm = document.getElementById('addClientForm');
    if (addClientForm) {
        addClientForm.addEventListener('submit', function(e) {
            e.preventDefault();
            addNewClient();
        });
    }

    // مستمع حدث لنموذج إضافة إعلان
    const addAdForm = document.getElementById('addAdForm');
    if (addAdForm) {
        addAdForm.addEventListener('submit', function(e) {
            e.preventDefault();
            addNewAd();
        });
    }

    // مستمع حدث للبحث
    const searchInput = document.getElementById('searchInput');
    if (searchInput) {
        searchInput.addEventListener('input', function() {
            currentSearchTerm = this.value;
            applyFilters();
        });
    }

    // مستمع حدث لفلتر نوع العميل
    const clientTypeFilter = document.getElementById('clientTypeFilter');
    if (clientTypeFilter) {
        clientTypeFilter.addEventListener('change', function() {
            currentClientTypeFilter = this.value;
            applyFilters();
        });
    }
}

/**
 * جلب فئات العملاء من قاعدة البيانات
 */
function fetchClientTypes() {
    console.log('Fetching client types data');

    // جلب البيانات من الخادم
    fetch('api/client_types.php')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                console.log('Client types data:', data);

                // تخزين البيانات
                clientTypesData = data.client_types;

                // تحديث dropdown الفلتر
                updateClientTypeFilter();
            } else {
                console.error('Error fetching client types data:', data.message);
            }
        })
        .catch(error => {
            console.error('Error fetching client types data:', error);
        });
}

/**
 * تحديث dropdown فلتر فئات العملاء
 */
function updateClientTypeFilter() {
    const filterSelect = document.getElementById('clientTypeFilter');
    if (!filterSelect) return;

    // الاحتفاظ بالقيمة المحددة حالياً
    const currentValue = filterSelect.value;

    // مسح الخيارات الموجودة (عدا الخيار الأول)
    filterSelect.innerHTML = '<option value="">جميع العملاء</option>';

    // إضافة فئات العملاء
    clientTypesData.forEach(clientType => {
        const option = document.createElement('option');
        option.value = clientType.name;
        option.textContent = clientType.display_name;
        filterSelect.appendChild(option);
    });

    // إعادة تعيين القيمة المحددة
    filterSelect.value = currentValue;

    // تحديث dropdown نوع العميل في نموذج الإضافة
    updateAddClientTypeDropdown();
}

/**
 * تحديث dropdown نوع العميل في نموذج إضافة العميل
 */
function updateAddClientTypeDropdown() {
    const clientTypeSelect = document.getElementById('clientType');
    if (!clientTypeSelect) return;

    // الاحتفاظ بالقيمة المحددة حالياً
    const currentValue = clientTypeSelect.value;

    // مسح الخيارات الموجودة (عدا الخيار الأول)
    clientTypeSelect.innerHTML = '<option value="">-- اختر نوع العميل --</option>';

    // إضافة فئات العملاء
    clientTypesData.forEach(clientType => {
        const option = document.createElement('option');
        option.value = clientType.name;
        option.textContent = clientType.display_name;
        option.style.color = clientType.color || '#4a56e2';
        clientTypeSelect.appendChild(option);
    });

    // إعادة تعيين القيمة المحددة
    clientTypeSelect.value = currentValue;
}

/**
 * جلب بيانات عملاء الإعلانات
 */
function fetchAdClientsData() {
    // عرض مؤشر التحميل
    document.getElementById('adClientsContent').innerHTML = '<div class="loading-message">جاري تحميل البيانات...</div>';

    // جلب البيانات من الخادم
    fetch('api/ad_clients.php')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                console.log('Ad clients data:', data);

                // تخزين البيانات
                adClientsData = data.clients;

                // عرض البيانات مع تطبيق الفلاتر الحالية
                applyFilters();

                // تحديث قيم الدفع وإجمالي الحساب الحالي لجميع العملاء
                setTimeout(updateAllClientPaymentTotals, 500);
            } else {
                console.error('Error fetching ad clients data:', data.message);
                document.getElementById('adClientsContent').innerHTML = `<div class="alert alert-danger">حدث خطأ أثناء جلب البيانات: ${data.message}</div>`;
            }
        })
        .catch(error => {
            console.error('Error fetching ad clients data:', error);
            document.getElementById('adClientsContent').innerHTML = '<div class="alert alert-danger">حدث خطأ أثناء جلب البيانات</div>';
        });
}

/**
 * عرض بيانات عملاء الإعلانات
 * @param {Array} clients بيانات العملاء
 */
function displayAdClientsData(clients) {
    const contentContainer = document.getElementById('adClientsContent');

    if (!clients || clients.length === 0) {
        let message = 'لا يوجد عملاء إعلانات';
        if (currentClientTypeFilter && currentSearchTerm) {
            message = `لا توجد نتائج للعميل "${currentClientTypeFilter}" والبحث "${currentSearchTerm}"`;
        } else if (currentClientTypeFilter) {
            message = `لا توجد نتائج للعميل "${currentClientTypeFilter}"`;
        } else if (currentSearchTerm) {
            message = `لا توجد نتائج للبحث "${currentSearchTerm}"`;
        }
        contentContainer.innerHTML = `<div class="alert alert-info">${message}</div>`;
        return;
    }

    console.log('Displaying', clients.length, 'clients');

    let html = '';

    // تجميع العملاء حسب النوع
    const clientsByType = {};

    clients.forEach(client => {
        if (!clientsByType[client.type]) {
            clientsByType[client.type] = [];
        }

        clientsByType[client.type].push(client);
    });

    // إنشاء بطاقات العملاء
    for (const type in clientsByType) {
        const typeClients = clientsByType[type];

        typeClients.forEach((client, index) => {
            const isFirst = index === 0;

            html += `
            <div class="client-card" data-client-id="${client.id}" data-client-type="${client.type}">
                <div class="client-header ${isFirst ? '' : 'collapsed'}" data-bs-toggle="collapse" data-bs-target="#client-${client.id}">
                    <div class="client-toggle-icon">
                        <i class="fas fa-chevron-${isFirst ? 'up' : 'down'}"></i>
                    </div>
                    <div class="client-name">${client.name}</div>
                    <div class="client-label ads-label" data-client-id="${client.id}" data-client-type="${client.type}">الإعلانات</div>
                </div>
                <div id="client-${client.id}" class="collapse ${isFirst ? 'show' : ''}">
                    <div class="client-body">
                        <div class="ads-table-container">
                            <table class="ads-table">
                                <thead>
                                    <tr>
                                        <th width="15%">تاريخ</th>
                                        <th width="15%">نوع</th>
                                        <th width="15%">تكلفة</th>
                                        <th width="20%">حالة</th>
                                        <th width="20%">الاكونت</th>
                                        <th width="15%">الصرف</th>
                                    </tr>
                                </thead>
                                <tbody>`;

            // إضافة الإعلانات
            if (client.ads && client.ads.length > 0) {
                client.ads.forEach(ad => {
                    const statusClass = ad.status.includes('مستمر') || ad.status.includes('نشط') ? 'ad-status-active' : '';

                    // الحصول على اسم الحساب الإعلاني
                    let accountName = ad.account || 'غير محدد';
                    if (ad.ad_account_id && !accountName) {
                        accountName = `حساب ${ad.ad_account_id}`;
                    }

                    html += `
                    <tr data-ad-id="${ad.id}" class="ad-row">
                        <td>${ad.date}</td>
                        <td>${ad.type}</td>
                        <td class="editable" data-field="cost" data-original="${ad.cost}">${ad.cost}</td>
                        <td class="${statusClass} editable" data-field="status" data-original="${ad.status}">${ad.status}</td>
                        <td>${accountName}</td>
                        <td class="exchange-rate editable" data-field="exchange_rate" data-original="${ad.exchange_rate}">${ad.exchange_rate}</td>
                    </tr>`;
                });
            } else {
                html += `
                <tr>
                    <td colspan="6">لا توجد إعلانات</td>
                </tr>`;
            }

            html += `
                            </tbody>
                            </table>
                        </div>

                        <div class="totals-section">
                            <table class="totals-table">
                                <tr>
                                    <td class="totals-label">الصرف يومي</td>
                                    <td class="totals-value">${client.daily_exchange ? client.daily_exchange.toLocaleString() : '0'}</td>
                                    <td class="add-ad-label" colspan="2" style="text-align: center !important;"> إضافة اعلان   <button class="add-ad-btn" data-client-id="${client.id}" data-client-type="${client.type}">
                                            <i class="fas fa-plus"></i>
                                        </button></td>

                                </tr>
                                <tr>
                                    <td class="totals-label">توتال الصرف</td>
                                    <td class="totals-value">${client.total_exchange ? client.total_exchange.toLocaleString() : '0'}</td>
                                    <td class="totals-label">
                                        <span>توتال الصرف بالنسبة</span>
                                        <button class="edit-percentage-btn" data-client-id="${client.id}" data-percentage="${client.commission_percentage || 50}" title="تعديل النسبة">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <span class="client-percentage" data-client-id="${client.id}">(${client.commission_percentage || 50}%)</span>
                                    </td>
                                    <td class="totals-value">${client.total_exchange_with_percentage ? client.total_exchange_with_percentage.toLocaleString() : '0'}</td>
                                </tr>
                                <tr>
                                    <td class="totals-label">الدفع</td>
                                    <td class="totals-value">
                                        <span class="client-payment-total" data-client-id="${client.id}">0</span>
                                        <button class="add-payment-btn" data-client-id="${client.id}" data-client-type="${client.type}">
                                            <i class="fas fa-plus"></i>
                                        </button>
                                    </td>
                                    <td class="totals-label">اجمالي الحساب حاليا</td>
                                    <td class="totals-value client-current-total" data-client-id="${client.id}">0</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>`;
        });
    }

    contentContainer.innerHTML = html;

    // إضافة مستمعي الأحداث لأزرار إضافة الإعلانات
    document.querySelectorAll('.add-ad-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const clientId = this.getAttribute('data-client-id');
            const clientType = this.getAttribute('data-client-type');

            // تعيين قيم النموذج
            document.getElementById('adClientId').value = clientId;
            document.getElementById('adClientType').value = clientType;

            // إعادة تعيين قيمة عدد الأيام بناءً على نوع الإعلان
            const dailyTotalSelect = document.getElementById('adDailyTotal');
            const daysInput = document.getElementById('adDays');

            // إضافة مستمع حدث لتغيير عدد الأيام بناءً على نوع الإعلان
      
       

            // عرض الموديل
            const addAdModal = new bootstrap.Modal(document.getElementById('addAdModal'));
            addAdModal.show();
        });
    });

    // إضافة مستمعي الأحداث لأزرار المدفوعات
    document.querySelectorAll('.add-payment-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const clientId = this.getAttribute('data-client-id');
            const clientType = this.getAttribute('data-client-type');

            // عرض نافذة المدفوعات
            showPaymentsModal(clientId, clientType);
        });
    });

    // إضافة مستمعي الأحداث لأزرار تعديل النسبة
    document.querySelectorAll('.edit-percentage-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const clientId = this.getAttribute('data-client-id');
            const percentage = this.getAttribute('data-percentage');

            // تعيين قيم النموذج
            document.getElementById('percentageClientId').value = clientId;
            document.getElementById('clientPercentage').value = percentage;

            // عرض نافذة تعديل النسبة
            const editPercentageModal = new bootstrap.Modal(document.getElementById('editPercentageModal'));
            editPercentageModal.show();
        });
    });

    // إضافة مستمعي الأحداث لكلمة "الإعلانات"
    document.querySelectorAll('.ads-label').forEach(label => {
        label.addEventListener('click', function(e) {
            e.stopPropagation(); // منع انتشار الحدث إلى العنصر الأب

            const clientId = this.getAttribute('data-client-id');

            // عرض نافذة تقرير العميل
            showClientReportModal(clientId);
        });
    });

    // إضافة مستمعي الأحداث لأزرار تقرير العميل
    document.querySelectorAll('.client-report-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const clientId = this.getAttribute('data-client-id');

            // عرض نافذة تقرير العميل
            showClientReportModal(clientId);
        });
    });

    // إضافة مستمع الحدث لزر الطباعة
    document.getElementById('printReportBtn').addEventListener('click', function() {
        printClientReport();
    });

    // إضافة مستمعي الأحداث للحقول القابلة للتعديل
    setupEditableFields();
}

/**
 * إعداد الحقول القابلة للتعديل
 */
function setupEditableFields() {
    // إضافة مستمعي الأحداث للحقول القابلة للتعديل
    document.querySelectorAll('.editable').forEach(field => {
        // إضافة مستمع حدث النقر المزدوج
        field.addEventListener('dblclick', function() {
            // الحصول على البيانات
            const adId = this.closest('tr').getAttribute('data-ad-id');
            const fieldName = this.getAttribute('data-field');
            const originalValue = this.getAttribute('data-original');

            // إنشاء حقل الإدخال
            let inputElement;

            if (fieldName === 'status') {
                // إنشاء قائمة منسدلة للحالة
                inputElement = document.createElement('select');

                // إضافة الخيارات
                const options = [
                    { value: 'يومي مستمر', text: 'يومي مستمر' },
                    { value: 'نشط 7 أيام', text: 'نشط 7 أيام' },
                    { value: 'نشط 15 أيام', text: 'نشط 15 أيام' },
                    { value: 'نشط 30 أيام', text: 'نشط 30 أيام' },
                    { value: 'متوقف', text: 'متوقف' }
                ];

                options.forEach(option => {
                    const optionElement = document.createElement('option');
                    optionElement.value = option.value;
                    optionElement.textContent = option.text;
                    if (option.value === originalValue) {
                        optionElement.selected = true;
                    }
                    inputElement.appendChild(optionElement);
                });
            } else {
                // إنشاء حقل نصي للقيم الأخرى
                inputElement = document.createElement('input');
                inputElement.type = 'text';
                inputElement.value = originalValue;
            }

            // تنسيق حقل الإدخال
            inputElement.className = 'editable-input';
            inputElement.style.width = '100%';
            inputElement.style.boxSizing = 'border-box';

            // حفظ النص الأصلي
            const originalText = this.textContent;

            // استبدال المحتوى بحقل الإدخال
            this.textContent = '';
            this.appendChild(inputElement);

            // تركيز حقل الإدخال
            inputElement.focus();

            // إضافة مستمع حدث فقدان التركيز
            inputElement.addEventListener('blur', function() {
                // الحصول على القيمة الجديدة
                const newValue = this.value;

                // إذا لم تتغير القيمة، نعيد النص الأصلي
                if (newValue === originalValue) {
                    field.textContent = originalText;
                    return;
                }

                // تحديث القيمة في قاعدة البيانات
                updateAdField(adId, fieldName, newValue, field, originalText);
            });

            // إضافة مستمع حدث الضغط على مفتاح الإدخال
            inputElement.addEventListener('keydown', function(e) {
                if (e.key === 'Enter') {
                    this.blur();
                } else if (e.key === 'Escape') {
                    field.textContent = originalText;
                }
            });
        });
    });
}

/**
 * تحديث حقل الإعلان في قاعدة البيانات
 */
function updateAdField(adId, fieldName, newValue, fieldElement, originalText) {
    // عرض مؤشر التحميل
    fieldElement.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';

    // إرسال البيانات إلى الخادم
    fetch('api/ad_clients.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            action: 'update_ad_field',
            ad_id: adId,
            field: fieldName,
            value: newValue
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // تحديث القيمة في الواجهة
            fieldElement.textContent = newValue;
            fieldElement.setAttribute('data-original', newValue);

            // إذا كان الحقل هو الحالة، نحدث الفئة
            if (fieldName === 'status') {
                const statusClass = newValue.includes('مستمر') || newValue.includes('نشط') ? 'ad-status-active' : '';
                fieldElement.className = statusClass + ' editable';
                fieldElement.setAttribute('data-field', 'status');
            }

            // إعادة تحميل البيانات
            fetchAdClientsData();
        } else {
            // إعادة النص الأصلي في حالة الخطأ
            fieldElement.textContent = originalText;
            alert('حدث خطأ أثناء تحديث البيانات: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error updating ad field:', error);
        fieldElement.textContent = originalText;
        alert('حدث خطأ أثناء تحديث البيانات');
    });
}

/**
 * تطبيق جميع الفلاتر
 */
function applyFilters() {
    if (!adClientsData || adClientsData.length === 0) {
        console.log('No data available for filtering');
        return;
    }

    console.log('Applying filters - Client Type:', currentClientTypeFilter, 'Search Term:', currentSearchTerm);

    let filteredClients = [...adClientsData]; // نسخة من البيانات الأصلية

    // فلترة حسب نوع العميل أولاً
    if (currentClientTypeFilter && currentClientTypeFilter !== '') {
        console.log('Filtering by client type:', currentClientTypeFilter);
        filteredClients = filteredClients.filter(client => {
            const matches = client.type === currentClientTypeFilter;
            console.log(`Client ${client.name} (${client.type}) matches: ${matches}`);
            return matches;
        });
        console.log('After client type filter:', filteredClients.length, 'clients');
    }

    // ثم فلترة حسب البحث النصي
    if (currentSearchTerm && currentSearchTerm.trim() !== '') {
        const searchTerm = currentSearchTerm.toLowerCase().trim();
        console.log('Filtering by search term:', searchTerm);
        filteredClients = filteredClients.filter(client => {
            const matches = client.name.toLowerCase().includes(searchTerm) ||
                           client.type.toLowerCase().includes(searchTerm);
            console.log(`Client ${client.name} matches search: ${matches}`);
            return matches;
        });
        console.log('After search filter:', filteredClients.length, 'clients');
    }

    console.log('Final filtered clients:', filteredClients);

    // عرض البيانات المفلترة
    displayAdClientsData(filteredClients);
}

/**
 * تصفية عملاء الإعلانات (للتوافق مع الكود القديم)
 * @param {string} searchTerm مصطلح البحث
 */
function filterAdClients(searchTerm) {
    currentSearchTerm = searchTerm;
    applyFilters();
}

/**
 * إضافة عميل جديد
 */
function addNewClient() {
    const name = document.getElementById('clientName').value.trim();
    const typeValue = document.getElementById('clientType').value;
    const commissionPercentage = document.getElementById('clientCommissionPercentage').value;

    if (!name) {
        alert('يرجى إدخال اسم العميل');
        return;
    }

    if (!typeValue) {
        alert('يرجى اختيار نوع العميل');
        return;
    }

    if (!commissionPercentage || isNaN(commissionPercentage) || parseFloat(commissionPercentage) < 0) {
        alert('يرجى إدخال نسبة عمولة صحيحة');
        return;
    }

    // البحث عن معرف نوع العميل
    const clientType = clientTypesData.find(ct => ct.name === typeValue);
    if (!clientType) {
        alert('نوع العميل المحدد غير صالح');
        return;
    }

    const clientData = {
        name: name,
        client_type_id: clientType.id,
        type: typeValue, // للتوافق مع الكود الموجود
        commission_percentage: parseFloat(commissionPercentage)
    };

    // إرسال البيانات إلى الخادم
    fetch('api/ad_clients.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            action: 'add_client',
            client: clientData
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // إغلاق الموديل
            bootstrap.Modal.getInstance(document.getElementById('addClientModal')).hide();

            // إعادة تعيين النموذج
            document.getElementById('addClientForm').reset();

            // إعادة تحميل البيانات
            fetchAdClientsData();
        } else {
            alert('حدث خطأ أثناء إضافة العميل: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error adding client:', error);
        alert('حدث خطأ أثناء إضافة العميل');
    });
}

/**
 * إضافة إعلان جديد
 */
function addNewAd() {
    // منع إرسال النموذج مرتين
    if (window.isSubmitting) {
        console.log('تم منع إرسال النموذج مرتين');
        return;
    }

    window.isSubmitting = true;

    const clientId = document.getElementById('adClientId').value;
    const clientType = document.getElementById('adClientType').value;
    const date = document.getElementById('adDate').value;
    const type = document.getElementById('adType').value;
    const dailyTotal = document.getElementById('adDailyTotal').value;
    const post = document.getElementById('adPost').value;
    let days = document.getElementById('adDays').value; // تغيير من const إلى let لإمكانية تعديل القيمة لاحقًا
    const cost = document.getElementById('adCost').value;
    const exchangeRate = document.getElementById('adExchangeRate').value;
    const adAccountId = document.getElementById('adAccount').value;

    if (!date || !cost || !exchangeRate || !adAccountId) {
        alert('يرجى إدخال جميع البيانات المطلوبة');
        window.isSubmitting = false;
        return;
    }

    // تحديد الحالة بناءً على القيم المدخلة
    let status = '';
    if (dailyTotal === 'يومي') {
        status = 'يومي مستمر';
        // تأكد من أن عدد الأيام هو 1 للإعلانات اليومية
        days = 1;
    } else {
        status = 'نشط ' + days + ' أيام';
    }

    const adData = {
        client_id: clientId,
        client_type: clientType,
        date: date,
        type: type,
        daily_total: dailyTotal,
        post: post,
        days: days,
        cost: cost,
        exchange_rate: exchangeRate,
        ad_account_id: adAccountId,
        status: status
    };

    // تعطيل زر الإرسال
    const submitButton = document.querySelector('.add-ad-submit');
    if (submitButton) {
        submitButton.disabled = true;
        submitButton.innerHTML = 'جاري الإضافة...';
    }

    // إرسال البيانات إلى الخادم
    fetch('api/ad_clients.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            action: 'add_ad',
            ad: adData
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // إغلاق الموديل
            bootstrap.Modal.getInstance(document.getElementById('addAdModal')).hide();

            // إعادة تعيين النموذج
            document.getElementById('addAdForm').reset();

            // إعادة تحميل البيانات
            fetchAdClientsData();
        } else {
            alert('حدث خطأ أثناء إضافة الإعلان: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error adding ad:', error);
        alert('حدث خطأ أثناء إضافة الإعلان');
    })
    .finally(() => {
        // إعادة تفعيل زر الإرسال
        if (submitButton) {
            submitButton.disabled = false;
            submitButton.innerHTML = 'إضافة اعلان';
        }

        // إعادة تعيين متغير الإرسال
        window.isSubmitting = false;
    });
}

/**
 * عرض نافذة المدفوعات
 */
function showPaymentsModal(clientId, clientType) {
    // تعيين معرف العميل ونوعه
    document.getElementById('paymentClientId').value = clientId;
    document.getElementById('paymentClientType').value = clientType;

    // جلب المدفوعات من الخادم
    fetchClientPayments(clientId, clientType);

    // عرض النافذة
    const paymentsModal = new bootstrap.Modal(document.getElementById('paymentsModal'));
    paymentsModal.show();
}

/**
 * عرض نافذة تقرير العميل
 */
function showClientReportModal(clientId) {
    // جلب بيانات العميل
    const client = adClientsData.find(c => c.id === clientId);

    if (!client) {
        alert('لم يتم العثور على بيانات العميل');
        return;
    }

    // تعيين اسم العميل
    document.getElementById('reportClientName').textContent = client.name;

    // عرض إعلانات العميل
    displayClientAdsReport(client);

    // عرض النافذة
    const clientReportModal = new bootstrap.Modal(document.getElementById('clientReportModal'));
    clientReportModal.show();
}

/**
 * عرض إعلانات العميل في التقرير
 */
function displayClientAdsReport(client) {
    const reportAdsList = document.getElementById('reportAdsList');
    reportAdsList.innerHTML = '';

    // استخدام إعلانات العميل الفعلية
    if (client.ads && client.ads.length > 0) {
        // ترتيب الإعلانات حسب التاريخ (الأحدث أولاً)
        const sortedAds = [...client.ads].sort((a, b) => new Date(b.date) - new Date(a.date));

        sortedAds.forEach(ad => {
            const statusClass = ad.status.includes('مستمر') ? 'ad-status-active' :
                               ad.status.includes('نشط') ? 'ad-status-active' :
                               ad.status === 'متوقف' ? 'ad-status-inactive' : '';

            // تنسيق التاريخ
            const adDate = new Date(ad.date);
            const formattedDate = `${adDate.getDate()}-${adDate.getMonth() + 1}`;

            // الحصول على اسم الحساب الإعلاني
            let accountName = ad.account || 'غير محدد';
            if (ad.ad_account_id && !accountName) {
                // في الحالة الحقيقية، يمكننا جلب اسم الحساب من قاعدة البيانات
                accountName = `حساب ${ad.ad_account_id}`;
            }

            // إضافة معلومات المنشور إذا كانت متوفرة
            const postInfo = ad.post ? ad.post : '';

            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${formattedDate}</td>
                <td>${ad.type || ''}</td>
                <td>${parseFloat(ad.cost || 0).toLocaleString()}</td>
                <td class="${statusClass}">${ad.status || ''}</td>
                <td>${accountName}${postInfo ? ` - ${postInfo}` : ''}</td>
                <td class="exchange-rate">${parseFloat(ad.exchange_rate || 0).toLocaleString()}</td>
            `;

            reportAdsList.appendChild(row);
        });
    } else {
        // إذا لم يكن هناك إعلانات، نعرض رسالة
        reportAdsList.innerHTML = '<tr><td colspan="6" class="text-center">لا توجد إعلانات لهذا العميل</td></tr>';
    }

    // تحديث الإجماليات
    updateReportSummary(client);
}

/**
 * تحديث ملخص التقرير
 */
function updateReportSummary(client) {
    if (!client) return;

    // استخدام البيانات الفعلية من العميل
    document.getElementById('reportTotalExchange').textContent = client.total_exchange ? client.total_exchange.toLocaleString() : '0';

    // عرض النسبة المئوية للعمولة مع إجمالي الصرف بالنسبة
    const commissionPercentage = client.commission_percentage || 50;
    const totalExchangeWithPercentageElement = document.getElementById('reportTotalExchangeWithPercentage');
    totalExchangeWithPercentageElement.textContent = client.total_exchange_with_percentage ? client.total_exchange_with_percentage.toLocaleString() : '0';
    totalExchangeWithPercentageElement.setAttribute('data-percentage', commissionPercentage);

    // إضافة النسبة المئوية إلى عنوان العمود
    const totalExchangeWithPercentageLabel = document.querySelector('.summary-label:nth-child(3)');
    if (totalExchangeWithPercentageLabel) {
        totalExchangeWithPercentageLabel.textContent = `توتال الصرف بالنسبة (${commissionPercentage}%)`;
    }

    document.getElementById('reportDailyExchange').textContent = client.daily_exchange ? client.daily_exchange.toLocaleString() : '0';

    // جلب إجمالي المدفوعات من API
    fetch(`api/payments.php?action=get_client_payments_total&client_id=${client.id}&client_type=${client.type}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const totalPayments = parseFloat(data.total) || 0;
                document.getElementById('reportTotalPayments').textContent = totalPayments.toLocaleString();

                // حساب إجمالي الحساب الحالي (الصرف بالنسبة - المدفوعات)
                const totalExchangeWithPercentage = client.total_exchange_with_percentage || 0;
                const currentTotal = totalExchangeWithPercentage - totalPayments;

                document.getElementById('reportCurrentTotal').textContent = currentTotal.toLocaleString();
            } else {
                document.getElementById('reportTotalPayments').textContent = '0';
                document.getElementById('reportCurrentTotal').textContent = (client.total_exchange_with_percentage || 0).toLocaleString();
            }
        })
        .catch(error => {
            console.error('Error fetching client payments total:', error);
            document.getElementById('reportTotalPayments').textContent = '0';
            document.getElementById('reportCurrentTotal').textContent = (client.total_exchange_with_percentage || 0).toLocaleString();
        });
}

/**
 * طباعة تقرير العميل
 */
function printClientReport() {
    // الحصول على محتوى التقرير
    const printContent = document.getElementById('printableArea').innerHTML;

    // إنشاء نافذة طباعة جديدة
    const printWindow = window.open('', '_blank');

    // إضافة محتوى التقرير إلى نافذة الطباعة
    printWindow.document.write(`
        <!DOCTYPE html>
        <html dir="rtl">
        <head>
            <title>تقرير العميل</title>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <link rel="stylesheet" href="assets/css/styles.css">
            <style>
                body {
                    font-family: Arial, sans-serif;
                    padding: 20px;
                    direction: rtl;
                }
                .client-report-container {
                    max-width: 800px;
                    margin: 0 auto;
                }
                .client-report-header {
                    text-align: center;
                    margin-bottom: 20px;
                }
                .client-report-name {
                    font-size: 24px;
                    font-weight: bold;
                }
                .client-report-table {
                    width: 100%;
                    border-collapse: collapse;
                    margin-bottom: 20px;
                }
                .client-report-table th, .client-report-table td {
                    border: 1px solid #ddd;
                    padding: 8px;
                    text-align: center;
                }
                .client-report-table th {
                    background-color: #f2f2f2;
                }
                .client-report-summary-table {
                    width: 100%;
                    border-collapse: collapse;
                }
                .client-report-summary-table td {
                    border: 1px solid #ddd;
                    padding: 8px;
                }
                .summary-label {
                    font-weight: bold;
                    background-color: #f2f2f2;
                }
                .ad-status-active {
                    color: green;
                    font-weight: bold;
                }
                .ad-status-inactive {
                    color: red;
                }
                .client-report-actions, .client-report-pagination {
                    display: none;
                }
                @media print {
                    .client-report-actions, .client-report-pagination {
                        display: none;
                    }
                }
            </style>
        </head>
        <body>
            <div class="client-report-container">
                ${printContent}
            </div>
        </body>
        </html>
    `);

    // إغلاق مستند الطباعة
    printWindow.document.close();

    // انتظار تحميل المستند
    printWindow.onload = function() {
        // طباعة المستند
        printWindow.print();
        // إغلاق النافذة بعد الطباعة
        printWindow.onafterprint = function() {
            printWindow.close();
        };
    };
}

/**
 * جلب مدفوعات العميل
 */
function fetchClientPayments(clientId, clientType) {
    // إظهار مؤشر التحميل
    const paymentsList = document.getElementById('paymentsList');
    paymentsList.innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin"></i> جاري تحميل المدفوعات...</div>';

    // جلب البيانات من الخادم
    fetch(`api/payments.php?action=get_client_payments&client_id=${clientId}&client_type=${clientType}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // عرض المدفوعات
                displayPayments(data.payments);
            } else {
                // عرض رسالة الخطأ
                paymentsList.innerHTML = `<div class="text-center text-danger">${data.message}</div>`;
                // تحديث الإجمالي
                document.getElementById('paymentsTotal').textContent = '0';
            }
        })
        .catch(error => {
            console.error('Error fetching payments:', error);
            paymentsList.innerHTML = '<div class="text-center text-danger">حدث خطأ أثناء جلب المدفوعات</div>';
            // تحديث الإجمالي
            document.getElementById('paymentsTotal').textContent = '0';
        });
}

/**
 * عرض المدفوعات في النافذة
 */
function displayPayments(payments) {
    const paymentsList = document.getElementById('paymentsList');
    paymentsList.innerHTML = '';

    let total = 0;

    payments.forEach(payment => {
        const paymentItem = document.createElement('div');
        paymentItem.className = 'payment-item';

        paymentItem.innerHTML = `
            <div class="payment-data">${payment.date}</div>
            <div class="payment-data">${payment.method}</div>
            <div class="payment-data">${payment.amount}</div>
        `;

        paymentsList.appendChild(paymentItem);

        total += parseFloat(payment.amount);
    });

    // تحديث الإجمالي في نافذة المدفوعات
    document.getElementById('paymentsTotal').textContent = total.toLocaleString();

    // تحديث الإجمالي في جدول العميل
    const clientId = document.getElementById('paymentClientId').value;
    const clientPaymentTotal = document.querySelector(`.client-payment-total[data-client-id="${clientId}"]`);
    if (clientPaymentTotal) {
        clientPaymentTotal.textContent = total.toLocaleString();

        // تحديث إجمالي الحساب الحالي
        updateClientCurrentTotal(clientId, total);
    }
}

/**
 * عرض نافذة إضافة دفعة
 */
function showAddPaymentModal(clientId, clientType) {
    // تعيين معرف العميل ونوعه
    document.getElementById('paymentClientId').value = clientId;
    document.getElementById('paymentClientType').value = clientType;

    // تعيين التاريخ الحالي
    const today = new Date().toISOString().split('T')[0];
    document.getElementById('paymentDate').value = today;

    // تعيين المبلغ من إجمالي المدفوعات في نافذة إضافة الدفعة
    const totalPaymentText = document.getElementById('paymentsTotal').textContent;
    // تحويل النص إلى رقم (إزالة الفواصل)
    const totalPayment = parseFloat(totalPaymentText.replace(/,/g, ''));

    // تعيين المبلغ في حقل المبلغ
    document.getElementById('paymentAmount').value = totalPayment;

    // عرض نافذة إضافة دفعة
    const addPaymentModal = new bootstrap.Modal(document.getElementById('addPaymentModal'));
    addPaymentModal.show();
}

/**
 * إضافة دفعة جديدة
 */
function addNewPayment() {
    // منع إرسال النموذج مرتين
    if (window.isSubmittingPayment) {
        console.log('تم منع إرسال النموذج مرتين');
        return;
    }

    window.isSubmittingPayment = true;

    const clientId = document.getElementById('paymentClientId').value;
    const clientType = document.getElementById('paymentClientType').value;
    const date = document.getElementById('paymentDate').value;
    const method = document.getElementById('paymentMethod').value;
    const amount = document.getElementById('paymentAmount').value;
    const notes = document.getElementById('paymentNotes').value;

    if (!date || !amount) {
        alert('يرجى إدخال جميع البيانات المطلوبة');
        window.isSubmittingPayment = false;
        return;
    }

    console.log('بيانات الدفعة:', {
        client_id: clientId,
        client_type: clientType,
        date: date,
        method: method,
        amount: amount,
        notes: notes
    });

    const paymentData = {
        client_id: clientId,
        client_type: clientType,
        date: date,
        method: method,
        amount: amount,
        notes: notes
    };

    // تعطيل زر الإرسال
    const submitButton = document.querySelector('#addPaymentForm button[type="submit"]');
    if (submitButton) {
        submitButton.disabled = true;
        submitButton.innerHTML = 'جاري الإضافة...';
    }

    // إرسال البيانات إلى الخادم
    fetch('api/payments.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            action: 'add_payment',
            payment: paymentData
        })
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        console.log('استجابة الخادم:', data);

        if (data.success) {
            // إغلاق نافذة إضافة دفعة
            bootstrap.Modal.getInstance(document.getElementById('addPaymentModal')).hide();

            // إعادة تعيين النموذج
            document.getElementById('addPaymentForm').reset();

            // إعادة فتح نافذة المدفوعات وتحديث البيانات
            showPaymentsModal(clientId, clientType);

            // تحديث قيمة الدفع في جدول العميل
            updateClientPaymentTotal(clientId, clientType);
        } else {
            alert('حدث خطأ أثناء إضافة الدفعة: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error adding payment:', error);
        alert('حدث خطأ أثناء إضافة الدفعة: ' + error.message);
    })
    .finally(() => {
        // إعادة تعيين متغير الإرسال
        window.isSubmittingPayment = false;

        // إعادة تفعيل زر الإرسال
        if (submitButton) {
            submitButton.disabled = false;
            submitButton.innerHTML = 'إضافة الدفعة';
        }
    });
}

/**
 * تحديث إجمالي مدفوعات العميل في جدول العميل
 */
function updateClientPaymentTotal(clientId, clientType) {
    // جلب إجمالي مدفوعات العميل من الخادم
    fetch(`api/payments.php?action=get_client_payments_total&client_id=${clientId}&client_type=${clientType}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // تحديث قيمة الدفع في جدول العميل
                const clientPaymentTotal = document.querySelector(`.client-payment-total[data-client-id="${clientId}"]`);
                if (clientPaymentTotal) {
                    const totalPayments = parseFloat(data.total) || 0;
                    clientPaymentTotal.textContent = totalPayments.toLocaleString();

                    // تحديث إجمالي الحساب الحالي
                    updateClientCurrentTotal(clientId, totalPayments);
                }
            }
        })
        .catch(error => {
            console.error('Error updating client payment total:', error);
        });
}

/**
 * تحديث إجمالي الحساب الحالي للعميل
 */
function updateClientCurrentTotal(clientId, totalPayments) {
    // البحث عن العميل في البيانات
    const client = adClientsData.find(c => c.id === clientId);
    if (!client) return;

    // حساب إجمالي الحساب الحالي (الصرف بالنسبة - المدفوعات)
    const totalExchangeWithPercentage = client.total_exchange_with_percentage || 0;
    const currentTotal = totalExchangeWithPercentage - totalPayments;

    // تحديث قيمة إجمالي الحساب الحالي في جدول العميل
    const clientCurrentTotal = document.querySelector(`.client-current-total[data-client-id="${clientId}"]`);
    if (clientCurrentTotal) {
        clientCurrentTotal.textContent = currentTotal.toLocaleString();
    }
}

/**
 * تحديث إجمالي مدفوعات جميع العملاء في جدول العملاء
 */
function updateAllClientPaymentTotals() {
    // الحصول على جميع عناصر العملاء
    const clientCards = document.querySelectorAll('.client-card');

    clientCards.forEach(card => {
        const clientId = card.getAttribute('data-client-id');
        const clientType = card.getAttribute('data-client-type');

        if (clientId && clientType) {
            updateClientPaymentTotal(clientId, clientType);
        }
    });
}

/**
 * تعديل نسبة العمولة للعميل
 */
function updateClientPercentage() {
    const clientId = document.getElementById('percentageClientId').value;
    const percentage = document.getElementById('clientPercentage').value;

    if (!percentage || isNaN(percentage) || parseFloat(percentage) < 0) {
        alert('يرجى إدخال نسبة عمولة صحيحة');
        return;
    }

    // إرسال البيانات إلى الخادم
    fetch('api/update_percentage.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            client_id: clientId,
            commission_percentage: parseFloat(percentage)
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // إغلاق النافذة
            bootstrap.Modal.getInstance(document.getElementById('editPercentageModal')).hide();

            // تحديث النسبة في واجهة المستخدم
            const percentageElement = document.querySelector(`.client-percentage[data-client-id="${clientId}"]`);
            if (percentageElement) {
                percentageElement.textContent = `(${percentage}%)`;
            }

            // تحديث زر تعديل النسبة
            const editButton = document.querySelector(`.edit-percentage-btn[data-client-id="${clientId}"]`);
            if (editButton) {
                editButton.setAttribute('data-percentage', percentage);
            }

            // إعادة تحميل البيانات
            fetchAdClientsData();
        } else {
            alert('حدث خطأ أثناء تعديل النسبة: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error updating percentage:', error);
        alert('حدث خطأ أثناء تعديل النسبة');
    });
}

// إضافة مستمعي الأحداث عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // إضافة مستمعي الأحداث للنماذج
    document.getElementById('addClientForm').addEventListener('submit', function(e) {
        e.preventDefault();
        addNewClient();
    });

    document.getElementById('editPercentageForm').addEventListener('submit', function(e) {
        e.preventDefault();
        updateClientPercentage();
    });

    // إضافة مستمع حدث لزر الطباعة
    document.getElementById('printReportBtn').addEventListener('click', function() {
        printClientReport();
    });

    // إضافة مستمع حدث لنموذج إضافة الإعلان
    const addAdForm = document.getElementById('addAdForm');
    if (addAdForm) {
        // إزالة أي مستمعي أحداث سابقة
        const clonedForm = addAdForm.cloneNode(true);
        addAdForm.parentNode.replaceChild(clonedForm, addAdForm);

        // إضافة مستمع حدث جديد
        clonedForm.addEventListener('submit', function(e) {
            e.preventDefault();
            addNewAd();
        });
    }

    document.getElementById('addPaymentForm').addEventListener('submit', function(e) {
        e.preventDefault();
        addNewPayment();
    });

    document.getElementById('addNewPaymentBtn').addEventListener('click', function() {
        // إخفاء نافذة المدفوعات
        bootstrap.Modal.getInstance(document.getElementById('paymentsModal')).hide();

        // تأخير قصير لضمان إغلاق النافذة الأولى قبل فتح النافذة الثانية
        setTimeout(() => {
            // عرض نافذة إضافة دفعة
            const addPaymentModal = new bootstrap.Modal(document.getElementById('addPaymentModal'));
            addPaymentModal.show();
        }, 300);
    });

    // تحميل البيانات عند تحميل الصفحة
    fetchAdClientsData();

    // تحديث قيم الدفع لجميع العملاء بعد تحميل البيانات
    setTimeout(updateAllClientPaymentTotals, 1000);
});