/**
 * ملف JavaScript للتعامل مع تقرير حسابات الإدارة
 */

// المتغيرات العامة
let reportsData = [];
let filteredData = [];

// عند تحميل المستند
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM Content Loaded - Admin Reports JS');

    // تعيين الشهر والسنة الحالية
    const currentDate = new Date();
    const currentMonth = currentDate.getMonth() + 1; // الشهور تبدأ من 0
    const currentYear = currentDate.getFullYear();

    document.getElementById('monthFilter').value = currentMonth;
    document.getElementById('yearFilter').value = currentYear;

    // إعداد مستمعي الأحداث
    setupEventListeners();

    // جلب البيانات
    fetchReportsData(currentMonth, currentYear);
});

/**
 * إعداد مستمعي الأحداث
 */
function setupEventListeners() {
    // مستمع حدث لزر الطباعة
    const printReportBtn = document.getElementById('printReportBtn');
    if (printReportBtn) {
        printReportBtn.addEventListener('click', function() {
            printReport();
        });
    }

    // مستمع حدث لتغيير الشهر
    const monthFilter = document.getElementById('monthFilter');
    if (monthFilter) {
        monthFilter.addEventListener('change', function() {
            const selectedMonth = parseInt(this.value);
            const selectedYear = parseInt(document.getElementById('yearFilter').value);
            fetchReportsData(selectedMonth, selectedYear);
        });
    }

    // مستمع حدث لتغيير السنة
    const yearFilter = document.getElementById('yearFilter');
    if (yearFilter) {
        yearFilter.addEventListener('change', function() {
            const selectedMonth = parseInt(document.getElementById('monthFilter').value);
            const selectedYear = parseInt(this.value);
            fetchReportsData(selectedMonth, selectedYear);
        });
    }

    // مستمع حدث للبحث
    const searchInput = document.getElementById('searchInput');
    if (searchInput) {
        searchInput.addEventListener('input', function() {
            filterReportsData(this.value);
        });
    }
}

/**
 * جلب بيانات التقارير
 * @param {number} month الشهر
 * @param {number} year السنة
 */
function fetchReportsData(month, year) {
    console.log(`Fetching reports data for month: ${month}, year: ${year}`);

    // عرض مؤشر التحميل
    document.getElementById('adminReportsTableBody').innerHTML = '<tr class="loading-row"><td colspan="7">جاري تحميل البيانات...</td></tr>';

    // إعادة تعيين الإجماليات
    resetTotals();

    // جلب البيانات من الخادم
    fetch(`api/admin_reports.php?month=${month}&year=${year}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                console.log('Reports data:', data);

                // تخزين البيانات
                reportsData = data.clients;

                // عرض البيانات
                displayReportsData(reportsData);

                // حساب الإجماليات
                calculateTotals(reportsData);
            } else {
                console.error('Error fetching reports data:', data.message);
                document.getElementById('adminReportsTableBody').innerHTML = `<tr><td colspan="7">حدث خطأ أثناء جلب البيانات: ${data.message}</td></tr>`;
            }
        })
        .catch(error => {
            console.error('Error fetching reports data:', error);
            document.getElementById('adminReportsTableBody').innerHTML = '<tr><td colspan="7">حدث خطأ أثناء جلب البيانات</td></tr>';
        });
}

/**
 * عرض بيانات التقارير
 * @param {Array} data بيانات التقارير
 */
function displayReportsData(data) {
    const tableBody = document.getElementById('adminReportsTableBody');

    if (!data || data.length === 0) {
        tableBody.innerHTML = '<tr><td colspan="7">لا توجد بيانات</td></tr>';
        return;
    }

    let html = '';

    data.forEach(client => {
        html += `<tr>
            <td>${client.name}</td>
            <td>${parseFloat(client.page_management).toLocaleString()} EGP</td>
            <td>${parseFloat(client.ads_ratio).toLocaleString()} EGP</td>
            <td>${parseFloat(client.payments).toLocaleString()} EGP</td>
            <td>${parseFloat(client.previous_balance).toLocaleString()} EGP</td>
            <td>${parseFloat(client.end_month_balance).toLocaleString()} EGP</td>
            <td>${parseFloat(client.total).toLocaleString()} EGP</td>
        </tr>`;
    });

    tableBody.innerHTML = html;

    // ضبط عرض الأعمدة ليكون متطابقًا بين الجداول الثلاثة
    syncTableColumnWidths();
}

/**
 * مزامنة عرض الأعمدة بين الجداول الثلاثة
 */
function syncTableColumnWidths() {
    const headerTable = document.getElementById('adminReportsTableHeader');
    const bodyTable = document.getElementById('adminReportsTable');
    const footerTable = document.getElementById('adminReportsTableFooter');

    if (!headerTable || !bodyTable || !footerTable) {
        return;
    }

    // الحصول على عناصر الصف الأول من كل جدول
    const headerRow = headerTable.querySelector('thead tr');
    const bodyRow = bodyTable.querySelector('tbody tr');
    const footerRow = footerTable.querySelector('tfoot tr');

    if (!headerRow || !bodyRow || !footerRow) {
        return;
    }

    // الحصول على خلايا الصف الأول من كل جدول
    const headerCells = headerRow.querySelectorAll('th');
    const bodyCells = bodyRow.querySelectorAll('td');
    const footerCells = footerRow.querySelectorAll('td');

    // ضبط عرض الأعمدة
    for (let i = 0; i < headerCells.length; i++) {
        const width = `${100 / headerCells.length}%`;

        if (headerCells[i]) {
            headerCells[i].style.width = width;
        }

        if (bodyCells[i]) {
            bodyCells[i].style.width = width;
        }

        if (footerCells[i]) {
            footerCells[i].style.width = width;
        }
    }
}

/**
 * حساب الإجماليات
 * @param {Array} data بيانات التقارير
 */
function calculateTotals(data) {
    if (!data || data.length === 0) {
        resetTotals();
        return;
    }

    let totalPageManagement = 0;
    let totalAds = 0;
    let totalPayments = 0;
    let totalPreviousBalance = 0;
    let totalCurrentBalance = 0;
    let grandTotal = 0;

    data.forEach(client => {
        totalPageManagement += parseFloat(client.page_management);
        totalAds += parseFloat(client.ads_ratio);
        totalPayments += parseFloat(client.payments);
        totalPreviousBalance += parseFloat(client.previous_balance);
        totalCurrentBalance += parseFloat(client.end_month_balance);
        grandTotal += parseFloat(client.total);
    });

    document.getElementById('totalPageManagement').textContent = totalPageManagement.toLocaleString() + ' EGP';
    document.getElementById('totalAds').textContent = totalAds.toLocaleString() + ' EGP';
    document.getElementById('totalPayments').textContent = totalPayments.toLocaleString() + ' EGP';
    document.getElementById('totalPreviousBalance').textContent = totalPreviousBalance.toLocaleString() + ' EGP';
    document.getElementById('totalCurrentBalance').textContent = totalCurrentBalance.toLocaleString() + ' EGP';
    document.getElementById('grandTotal').textContent = grandTotal.toLocaleString() + ' EGP';
}

/**
 * إعادة تعيين الإجماليات
 */
function resetTotals() {
    document.getElementById('totalPageManagement').textContent = '0';
    document.getElementById('totalAds').textContent = '0';
    document.getElementById('totalPayments').textContent = '0';
    document.getElementById('totalPreviousBalance').textContent = '0';
    document.getElementById('totalCurrentBalance').textContent = '0';
    document.getElementById('grandTotal').textContent = '0';
}

/**
 * تصفية بيانات التقارير
 * @param {string} searchTerm مصطلح البحث
 */
function filterReportsData(searchTerm) {
    if (!reportsData || reportsData.length === 0) {
        return;
    }

    searchTerm = searchTerm.toLowerCase().trim();

    if (searchTerm === '') {
        filteredData = [...reportsData];
    } else {
        filteredData = reportsData.filter(client => {
            return client.name.toLowerCase().includes(searchTerm);
        });
    }

    displayReportsData(filteredData);
    calculateTotals(filteredData);
}

/**
 * طباعة التقرير
 */
function printReport() {
    window.print();
}
