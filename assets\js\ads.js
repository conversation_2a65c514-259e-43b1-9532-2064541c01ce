/**
 * ملف JavaScript للتعامل مع وظائف الإعلانات
 */

// المتغيرات العامة
let currentClientId = null;
let adsData = [];
let adAccounts = {}; // كاش للحسابات الإعلانية

/**
 * الحصول على اسم الحساب الإعلاني من المعرف
 * @param {number} accountId معرف الحساب الإعلاني
 * @returns {string} اسم الحساب الإعلاني
 */
function getAccountName(accountId) {
    // إذا كان المعرف غير موجود، نعيد "غير محدد"
    if (!accountId) {
        return "غير محدد";
    }

    // إذا كان الحساب موجودًا في الكاش، نعيد اسمه
    if (adAccounts[accountId]) {
        return adAccounts[accountId];
    }

    // إذا لم يكن موجودًا، نعيد المعرف كنص
    return "حساب " + accountId;
}

/**
 * جلب الحسابات الإعلانية
 */
function fetchAdAccounts() {
    fetch('api/ad_accounts/get.php')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // تخزين الحسابات في الكاش
                data.accounts.forEach(account => {
                    adAccounts[account.id] = account.name;
                });
                console.log('Ad accounts loaded:', adAccounts);
            }
        })
        .catch(error => {
            console.error('Error fetching ad accounts:', error);
        });
}

// عند تحميل المستند
document.addEventListener('DOMContentLoaded', function() {
    // جلب الحسابات الإعلانية
    fetchAdAccounts();

    // إضافة مستمعي الأحداث للأزرار
    setupEventListeners();
});

/**
 * إعداد مستمعي الأحداث
 */
function setupEventListeners() {
    // زر إضافة إعلان
    const addAdBtn = document.getElementById('addAdBtn');
    console.log('addAdBtn found:', addAdBtn);

    if (addAdBtn) {
        addAdBtn.addEventListener('click', function(e) {
            e.preventDefault();
            console.log('addAdBtn clicked');
            console.log('Current client ID in global variable:', currentClientId);

            try {
                // استخدام المتغير العام currentClientId بدلاً من محاولة الحصول عليه من السمة
                if (!currentClientId) {
                    console.error('Client ID is missing from global variable');
                    alert('حدث خطأ: لم يتم تحديد العميل. يرجى إعادة فتح نافذة الإعلانات.');
                    return;
                }

                // تعيين معرف العميل في نموذج إضافة إعلان
                document.getElementById('adClientId').value = currentClientId;
                console.log('Set client ID in form:', currentClientId);

                // تعيين التاريخ الحالي كتاريخ افتراضي
                const today = new Date();
                const formattedDate = today.toISOString().split('T')[0];
                document.getElementById('adDate').value = formattedDate;

                // إخفاء نافذة الإعلانات
                const adsModalElement = document.getElementById('adsModal');
                const adsModal = bootstrap.Modal.getInstance(adsModalElement);
                if (adsModal) {
                    adsModal.hide();
                }

                // عرض نافذة إضافة إعلان بعد تأخير قصير
                setTimeout(() => {
                    const addAdModalElement = document.getElementById('addAdModal');
                    if (addAdModalElement) {
                        console.log('Found addAdModal element, showing modal');
                        const addAdModal = new bootstrap.Modal(addAdModalElement);
                        addAdModal.show();
                    } else {
                        console.error('addAdModal element not found');
                        alert('حدث خطأ: لم يتم العثور على نافذة إضافة إعلان.');
                    }
                }, 300);
            } catch (error) {
                console.error('Error in addAdBtn click handler:', error);
                alert('حدث خطأ أثناء محاولة فتح نافذة إضافة إعلان.');
            }
        });
    }

    // إجمالي الصرف بالنسبة - سيتم إعداده عند فتح نافذة الإعلانات

    // شريط تمرير النسبة - سيتم إعداده عند فتح نافذة الإعلانات

    // زر حفظ النسبة - سيتم إعداده عند فتح نافذة الإعلانات

    // نموذج إضافة إعلان - سيتم إعداده عند فتح النافذة

    // إعداد مستمعي الأحداث للفلتر عند فتح نافذة الإعلانات
    setupFilterEvents();

    // إضافة مستمعي الأحداث لصفوف الإعلانات في الجدول
    setupAdsRowsEvents();
}

/**
 * فلترة الإعلانات حسب الحالة
 * @param {string} filterValue - قيمة الفلتر (all, مستمر, متوقف)
 */
function filterAds(filterValue) {
    const adItems = document.querySelectorAll('.ad-item');
    console.log(`Filtering ads with value: ${filterValue}`);
    console.log(`Found ${adItems.length} ad items`);

    adItems.forEach(item => {
        if (filterValue === 'all') {
            // إظهار جميع الإعلانات
            item.style.display = '';
            console.log('Showing all ads');
        } else {
            // التحقق من حالة الإعلان
            const statusElement = item.querySelector('.ad-status');
            if (statusElement) {
                console.log(`Ad status: ${statusElement.textContent}, Filter: ${filterValue}`);
                if (statusElement.textContent.trim() === filterValue) {
                    // إظهار الإعلان إذا كانت حالته تطابق الفلتر
                    item.style.display = '';
                    console.log('Showing ad - status matches filter');
                } else {
                    // إخفاء الإعلان إذا كانت حالته لا تطابق الفلتر
                    item.style.display = 'none';
                    console.log('Hiding ad - status does not match filter');
                }
            } else {
                console.log('No status element found');
            }
        }
    });
}

/**
 * إعداد مستمعي الأحداث لصفوف الإعلانات
 * يتم استدعاء هذه الوظيفة عند تحميل الصفحة وبعد إضافة إعلان جديد
 */
function setupAdsRowsEvents() {
    document.querySelectorAll('.ads-row .clickable-cell').forEach(cell => {
        // إزالة مستمعي الأحداث السابقة لتجنب التكرار
        const newCell = cell.cloneNode(true);
        cell.parentNode.replaceChild(newCell, cell);

        // إضافة مستمع الحدث الجديد
        newCell.addEventListener('click', function() {
            const clientId = this.closest('.ads-row').getAttribute('data-client-id');
            openAdsModal(clientId);
        });
    });
}

/**
 * إعداد الحقول القابلة للتعديل
 */
function setupEditableFields() {
    document.querySelectorAll('.editable').forEach(field => {
        // إزالة مستمعي الأحداث السابقة لتجنب التكرار
        const newField = field.cloneNode(true);
        field.parentNode.replaceChild(newField, field);

        // إضافة مستمع الحدث للنقر المزدوج
        newField.addEventListener('dblclick', function() {
            const adId = this.closest('.ad-item').getAttribute('data-id');
            const fieldName = this.getAttribute('data-field');
            const currentValue = this.getAttribute('data-value');

            // إنشاء عنصر الإدخال المناسب
            let inputElement;

            if (fieldName === 'status') {
                // إنشاء قائمة منسدلة لحالة الإعلان
                inputElement = document.createElement('select');

                // إضافة الخيارات
                const option1 = document.createElement('option');
                option1.value = 'مستمر';
                option1.textContent = 'مستمر';

                const option2 = document.createElement('option');
                option2.value = 'متوقف';
                option2.textContent = 'متوقف';

                inputElement.appendChild(option1);
                inputElement.appendChild(option2);

                // تعيين القيمة الحالية
                inputElement.value = currentValue;
            } else {
                // إنشاء حقل نصي للقيم الأخرى
                inputElement = document.createElement('input');
                inputElement.type = 'text';
                inputElement.value = currentValue;
            }

            // تنسيق عنصر الإدخال
            inputElement.className = 'edit-input';
            inputElement.style.width = '100%';
            inputElement.style.padding = '2px 5px';
            inputElement.style.textAlign = 'center';

            // حفظ النص الأصلي
            const originalText = this.innerHTML;

            // استبدال النص بعنصر الإدخال
            this.innerHTML = '';
            this.appendChild(inputElement);

            // تركيز على عنصر الإدخال
            inputElement.focus();

            // إضافة مستمع الحدث لفقدان التركيز
            inputElement.addEventListener('blur', function() {
                // استعادة النص الأصلي إذا لم يتم إدخال قيمة
                if (!this.value.trim()) {
                    field.innerHTML = originalText;
                    return;
                }

                // تحديث القيمة في قاعدة البيانات
                updateAdField(adId, fieldName, this.value);
            });

            // إضافة مستمع الحدث للضغط على مفتاح Enter
            inputElement.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    this.blur();
                }
            });
        });
    });
}

/**
 * تحديث قيمة حقل في قاعدة البيانات
 */
function updateAdField(adId, fieldName, newValue) {
    // إعداد البيانات للإرسال
    const updateData = {
        id: adId,
        field: fieldName,
        value: newValue
    };

    // إرسال البيانات إلى الخادم
    fetch('api/ads/index.php?action=update_field', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(updateData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // تحديث واجهة المستخدم
            const adItem = document.querySelector(`.ad-item[data-id="${adId}"]`);
            const field = adItem.querySelector(`[data-field="${fieldName}"]`);

            // تحديث قيمة البيانات
            field.setAttribute('data-value', newValue);

            // تحديث العرض حسب نوع الحقل
            if (fieldName === 'status') {
                // تحديث حالة الإعلان
                field.textContent = newValue;

                // تحديث الفئة
                field.classList.remove('ad-status-مستمر', 'ad-status-متوقف');
                field.classList.add(`ad-status-${newValue}`);
            } else if (fieldName === 'cost') {
                // تحديث التكلفة
                field.textContent = parseFloat(newValue).toLocaleString();
            } else if (fieldName === 'exchange_rate') {
                // تحديث الصرف بالمصري (القيمة المدخلة مباشرة)
                field.textContent = parseFloat(newValue).toLocaleString();
            } else if (fieldName === 'percentage') {
                // تحديث النسبة
                field.textContent = `${newValue}%`;

                // إعادة تحميل الإعلانات لتحديث الصرف بالنسبة
                fetchAdsData(currentClientId);
            }

            // تحديث إجمالي الإعلانات
            if (data.total_spend) {
                document.getElementById('totalAdsSpend').textContent = parseFloat(data.total_spend).toLocaleString();
            }

            if (data.total_spend_ratio) {
                document.getElementById('totalAdsSpendRatio').textContent = parseFloat(data.total_spend_ratio).toLocaleString();
            }

            // تحديث قيمة الإعلانات في الجدول الرئيسي
            if (data.client_data) {
                updateClientBalanceValue(data.client_data.client_id, data.client_data.end_month_balance);

                // تحديث قيمة الصرف بالنسبة في جدول العميل
                if (data.total_spend_ratio) {
                    updateClientAdsRatioValue(data.client_data.client_id, data.total_spend_ratio);
                }
            }

            // عرض رسالة نجاح
            showNotification('تم تحديث البيانات بنجاح', 'success');
        } else {
            // عرض رسالة خطأ
            showNotification(data.message || 'حدث خطأ أثناء تحديث البيانات', 'error');
        }
    })
    .catch(error => {
        console.error('Error updating ad field:', error);
        showNotification('حدث خطأ أثناء تحديث البيانات', 'error');
    });
}

/**
 * عرض إشعار للمستخدم
 */
function showNotification(message, type = 'info') {
    // إنشاء عنصر الإشعار
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.textContent = message;

    // إضافة الإشعار إلى الصفحة
    document.body.appendChild(notification);

    // تنسيق الإشعار
    notification.style.position = 'fixed';
    notification.style.top = '20px';
    notification.style.left = '50%';
    notification.style.transform = 'translateX(-50%)';
    notification.style.padding = '10px 20px';
    notification.style.borderRadius = '5px';
    notification.style.zIndex = '9999';
    notification.style.backgroundColor = type === 'success' ? '#28a745' : (type === 'error' ? '#dc3545' : '#17a2b8');
    notification.style.color = 'white';
    notification.style.boxShadow = '0 2px 5px rgba(0, 0, 0, 0.2)';

    // إزالة الإشعار بعد 3 ثواني
    setTimeout(() => {
        notification.style.opacity = '0';
        notification.style.transition = 'opacity 0.5s ease';
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 500);
    }, 3000);
}

/**
 * تحديث النسبة لجميع الإعلانات
 */
function updateAllAdsPercentage(percentage) {
    // التحقق من وجود معرف العميل الحالي
    if (!currentClientId) {
        showNotification('حدث خطأ: لم يتم تحديد العميل', 'error');
        return;
    }

    // إعداد البيانات للإرسال
    const updateData = {
        client_id: currentClientId,
        percentage: percentage
    };

    // إرسال البيانات إلى الخادم
    console.log('Sending percentage update request:', JSON.stringify(updateData));
    fetch('api/ads/index.php?action=update_all_percentage', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(updateData)
    })
    .then(response => {
        console.log('Server response status:', response.status);
        return response.json();
    })
    .then(data => {
        console.log('Server response data:', data);
        if (data.success) {
            // تحديث واجهة المستخدم
            document.getElementById('totalAdsSpendRatio').textContent = parseFloat(data.total_spend_ratio).toLocaleString();

            // تحديث قيمة الصرف بالنسبة في جدول العميل
            updateClientAdsRatioValue(data.client_id, data.total_spend_ratio);

            // تحديث النسبة في جميع الإعلانات
            const adItems = document.querySelectorAll('.ad-item');
            adItems.forEach(adItem => {
                const percentageField = adItem.querySelector('.ad-percentage');
                if (percentageField) {
                    // تحديث قيمة النسبة
                    percentageField.textContent = `${percentage}%`;
                    percentageField.setAttribute('data-value', percentage);
                }
            });

            // إعادة تحميل الإعلانات لتحديث الصرف بالنسبة
            fetchAdsData(currentClientId);

            // عرض رسالة نجاح
            showNotification('تم تحديث النسبة بنجاح', 'success');
        } else {
            // عرض رسالة خطأ
            showNotification(data.message || 'حدث خطأ أثناء تحديث النسبة', 'error');
        }
    })
    .catch(error => {
        console.error('Error updating percentage:', error);
        showNotification('حدث خطأ أثناء تحديث النسبة', 'error');
    });
}

/**
 * فتح نافذة الإعلانات
 */
function openAdsModal(clientId) {
    currentClientId = clientId;
    console.log('Opening ads modal for client ID:', clientId);

    // تعيين معرف العميل في نموذج إضافة إعلان
    document.getElementById('adClientId').value = clientId;

    // تعيين التاريخ الحالي كتاريخ افتراضي
    const today = new Date();
    const formattedDate = today.toISOString().split('T')[0];
    document.getElementById('adDate').value = formattedDate;

    // تعيين معرف العميل كسمة في نافذة الإعلانات
    const adsModalElement = document.getElementById('adsModal');
    adsModalElement.setAttribute('data-client-id', clientId);

    // الحصول على اسم العميل
    fetch(`api/clients/index.php?action=get&id=${clientId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success && data.client) {
                // تحديث عنوان النافذة بإضافة اسم العميل
                const modalTitle = document.getElementById('adsModalLabel');
                if (modalTitle) {
                    modalTitle.textContent = `الإعلانات - ${data.client.name}`;
                }
            }
        })
        .catch(error => {
            console.error('Error fetching client data:', error);
        });

    // عرض نافذة الإعلانات
    const adsModal = new bootstrap.Modal(adsModalElement);
    adsModal.show();

    // جلب بيانات الإعلانات
    fetchAdsData(clientId);

    // إعداد معالج حدث زر إضافة إعلان
    setupAddAdButton();

    // إعداد معالج حدث نموذج إضافة إعلان
    setupAddAdForm();

    // إعداد معالج حدث إجمالي الصرف بالنسبة
    setupTotalAdsSpendRatioEvent();

    // إعداد معالج حدث زر حفظ النسبة
    setupSavePercentageButton();

    // إعداد معالج حدث شريط تمرير النسبة
    setupPercentageSlider();
}

/**
 * جلب بيانات الإعلانات من الخادم
 */
function fetchAdsData(clientId) {
    // عرض رسالة التحميل
    document.getElementById('adsList').innerHTML = '<div class="loading-ads">جاري تحميل الإعلانات...</div>';

    // إعادة تعيين المجاميع
    document.getElementById('totalAdsSpend').textContent = '0';
    document.getElementById('totalAdsSpendRatio').textContent = '0';

    // جلب البيانات من الخادم
    fetch(`api/ads/index.php?action=get&client_id=${clientId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // تخزين البيانات
                adsData = data.ads;

                // عرض البيانات
                displayAdsData(data);
            } else {
                // عرض رسالة الخطأ
                document.getElementById('adsList').innerHTML = `<div class="no-ads-message">${data.message}</div>`;
            }
        })
        .catch(error => {
            console.error('Error fetching ads data:', error);
            document.getElementById('adsList').innerHTML = '<div class="no-ads-message">حدث خطأ أثناء تحميل البيانات</div>';
        });
}

/**
 * عرض بيانات الإعلانات
 */
function displayAdsData(data) {
    const adsList = document.getElementById('adsList');

    // التحقق من وجود إعلانات
    if (!data.ads || data.ads.length === 0) {
        adsList.innerHTML = '<div class="no-ads-message">لا توجد إعلانات لهذا العميل</div>';
        return;
    }

    // إنشاء HTML للإعلانات
    let html = '';

    data.ads.forEach(ad => {
        // تنسيق التاريخ
        const date = new Date(ad.date);
        const formattedDate = `${date.getDate()}-${date.getMonth() + 1}`;

        // تحديد لون حالة الإعلان
        const statusClass = ad.status === 'مستمر' ? 'ad-status-مستمر' : 'ad-status-متوقف';

        html += `
        <div class="ad-item" data-id="${ad.id}">
            <div class="row">
                <div class="col ad-date">${formattedDate}</div>
                <div class="col ad-type">${ad.type}</div>
                <div class="col">${ad.payment_type}</div>
                <div class="col ad-post">${ad.post || 'بوست'}</div>
                <div class="col ad-cost editable" data-field="cost" data-value="${ad.cost}">${parseFloat(ad.cost).toLocaleString()}</div>
                <div class="col ad-status ${statusClass} editable" data-field="status" data-value="${ad.status}">${ad.status}</div>
                <div class="col ad-account">${getAccountName(ad.ad_account_id)}</div>
                <div class="col ad-exchange editable" data-field="exchange_rate" data-value="${ad.exchange_rate}">
                    ${parseFloat(ad.exchange_rate).toLocaleString()}
                    <span class="ad-percentage editable" data-field="percentage" data-value="${ad.percentage || 0}">${ad.percentage || 0}%</span>
                </div>
            </div>
        </div>
        `;
    });

    adsList.innerHTML = html;

    // تحديث المجاميع مع تنسيق الأرقام
    document.getElementById('totalAdsSpend').textContent = parseFloat(data.total_spend).toLocaleString();
    document.getElementById('totalAdsSpendRatio').textContent = parseFloat(data.total_spend_ratio).toLocaleString();

    // إضافة مستمعي الأحداث للحقول القابلة للتعديل
    setupEditableFields();
}

/**
 * إرسال نموذج إضافة إعلان
 */
function submitAddAdForm() {
    // جمع البيانات من النموذج
    const clientId = document.getElementById('adClientId').value;
    const date = document.getElementById('adDate').value;
    const type = document.getElementById('adType').value;
    const paymentType = document.getElementById('adPaymentType').value;
    const post = document.getElementById('adPost').value;
    const cost = document.getElementById('adCost').value;
    const account = document.getElementById('adAccount').value;
    const days = document.getElementById('adDays').value;
    const exchangeRate = document.getElementById('adExchangeRate').value;

    // الصرف بالمصري هو نفس قيمة سعر الصرف المدخلة

    // التحقق من صحة البيانات
    if (!clientId || !date || !type || !paymentType || !post || !cost || !account || !days || !exchangeRate) {
        alert('يرجى ملء جميع الحقول المطلوبة');
        return;
    }

    console.log('Form data validated successfully');
    console.log('Client ID:', clientId);
    console.log('Date:', date);
    console.log('Type:', type);
    console.log('Payment Type:', paymentType);
    console.log('Post:', post);
    console.log('Cost:', cost);
    console.log('Account:', account);
    console.log('Days:', days);
    console.log('Exchange Rate:', exchangeRate);

    // إعداد البيانات للإرسال
    const adData = {
        client_id: clientId,
        date: date,
        type: type,
        payment_type: paymentType,
        post: post,
        cost: parseFloat(cost),
        status: 'مستمر', // القيمة الافتراضية
        ad_account_id: account, // استخدام معرف الحساب الإعلاني
        days: parseInt(days),
        exchange_rate: parseFloat(exchangeRate),
        egyptian_cost: parseFloat(exchangeRate) // إضافة الصرف بالمصري كقيمة مباشرة
    };

    // إرسال البيانات إلى الخادم
    console.log('Sending data to server:', JSON.stringify(adData));

    fetch('api/ads/index.php?action=add', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(adData)
    })
    .then(response => {
        console.log('Server response status:', response.status);
        return response.json();
    })
    .then(data => {
        console.log('Server response data:', data);
        if (data.success) {
            // إغلاق نافذة إضافة إعلان
            const addAdModal = bootstrap.Modal.getInstance(document.getElementById('addAdModal'));
            addAdModal.hide();

            // إعادة فتح نافذة الإعلانات وتحديث البيانات
            openAdsModal(clientId);

            // إعادة تعيين النموذج
            document.getElementById('addAdForm').reset();

            // عرض رسالة نجاح
            const successMessage = document.createElement('div');
            successMessage.className = 'alert alert-success';
            successMessage.textContent = 'تم إضافة الإعلان بنجاح';
            successMessage.style.position = 'fixed';
            successMessage.style.top = '20px';
            successMessage.style.left = '50%';
            successMessage.style.transform = 'translateX(-50%)';
            successMessage.style.zIndex = '9999';
            successMessage.style.padding = '10px 20px';
            successMessage.style.borderRadius = '5px';
            successMessage.style.boxShadow = '0 4px 8px rgba(0,0,0,0.1)';
            document.body.appendChild(successMessage);

            // إزالة الرسالة بعد 3 ثواني
            setTimeout(() => {
                successMessage.style.opacity = '0';
                successMessage.style.transition = 'opacity 0.5s ease';
                setTimeout(() => {
                    document.body.removeChild(successMessage);
                }, 500);
            }, 3000);

            // تحديث قيمة الإعلانات في الجدول الرئيسي فورًا
            // استخدام القيمة المرجعة من الخادم مباشرة
            const newBalanceValue = parseFloat(data.client_data.end_month_balance);
            console.log('Immediate update - New balance value:', newBalanceValue);

            // تحديث إجمالي حساب العميل نهاية الشهر في الجدول الرئيسي
            updateClientBalanceValue(clientId, newBalanceValue);

            // تحديث قيمة الصرف بالنسبة في جدول العميل
            if (data.total_spend_ratio) {
                updateClientAdsRatioValue(clientId, data.total_spend_ratio);
            }

            // تحديث بيانات الجدول بعد تأخير قصير لضمان تحديث البيانات في قاعدة البيانات
            setTimeout(() => {
                updateClientTableData(clientId);
            }, 300);
        } else {
            alert(data.message);
        }
    })
    .catch(error => {
        console.error('Error adding ad:', error);
        alert('حدث خطأ أثناء إضافة الإعلان');
    });
}

/**
 * تحديث بيانات الجدول بعد إضافة إعلان
 */
function updateClientTableData(clientId) {
    // تحديث بيانات العميل في الجدول
    fetch(`api/clients/index.php?action=get&id=${clientId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const client = data.client;

                // تحديث قيمة الصرف بالنسبة في جميع بطاقات العميل
                if (client.ads_ratio) {
                    updateClientAdsRatioValue(clientId, client.ads_ratio);
                }

                // تحديث إجمالي حساب العميل نهاية الشهر
                updateClientBalanceValue(clientId, client.end_month_balance);

                // إعادة إعداد مستمعي الأحداث لصفوف الإعلانات بعد التحديث
                setupAdsRowsEvents();
            }
        })
        .catch(error => {
            console.error('Error updating client data:', error);
        });
}

/**
 * إعداد مستمعي الأحداث للفلتر
 */
function setupFilterEvents() {
    // زر الفلتر والقائمة المنسدلة
    const filterButton = document.querySelector('.btn-collapse');
    const filterMenu = document.querySelector('.filter-menu');

    if (filterButton && filterMenu) {
        console.log('Setting up filter events');

        // إزالة مستمعي الأحداث السابقة
        const newFilterButton = filterButton.cloneNode(true);
        filterButton.parentNode.replaceChild(newFilterButton, filterButton);

        // إظهار/إخفاء القائمة المنسدلة عند النقر على الزر
        newFilterButton.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            console.log('Filter button clicked');

            // تبديل حالة العرض للقائمة المنسدلة
            if (filterMenu.style.display === 'block') {
                filterMenu.style.display = 'none';
                // تغيير اتجاه السهم
                const icon = this.querySelector('i');
                if (icon) {
                    icon.classList.remove('fa-chevron-up');
                    icon.classList.add('fa-chevron-down');
                }
            } else {
                filterMenu.style.display = 'block';
                // تغيير اتجاه السهم
                const icon = this.querySelector('i');
                if (icon) {
                    icon.classList.remove('fa-chevron-down');
                    icon.classList.add('fa-chevron-up');
                }
            }
        });

        // إخفاء القائمة المنسدلة عند النقر في أي مكان آخر في الصفحة
        document.addEventListener('click', function(e) {
            if (!newFilterButton.contains(e.target) && !filterMenu.contains(e.target)) {
                filterMenu.style.display = 'none';
                // إعادة السهم إلى الأسفل
                const icon = newFilterButton.querySelector('i');
                if (icon) {
                    icon.classList.remove('fa-chevron-up');
                    icon.classList.add('fa-chevron-down');
                }
            }
        });

        // عناصر الفلترة في القائمة المنسدلة
        const filterItems = document.querySelectorAll('.filter-menu .dropdown-item');
        if (filterItems.length > 0) {
            filterItems.forEach(item => {
                // إزالة مستمعي الأحداث السابقة
                const newItem = item.cloneNode(true);
                item.parentNode.replaceChild(newItem, item);

                newItem.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    console.log('Filter item clicked:', this.textContent);

                    // إزالة الفئة النشطة من جميع العناصر
                    document.querySelectorAll('.filter-menu .dropdown-item').forEach(i => i.classList.remove('active'));

                    // إضافة الفئة النشطة للعنصر المحدد
                    this.classList.add('active');

                    // الحصول على قيمة الفلتر
                    const filterValue = this.getAttribute('data-filter');
                    console.log('Filter value:', filterValue);

                    // تحديث نص زر الفلتر
                    const filterButtonText = newFilterButton.querySelector('span');
                    if (filterButtonText) {
                        filterButtonText.textContent = this.textContent;
                    }

                    // إخفاء القائمة المنسدلة
                    filterMenu.style.display = 'none';

                    // إعادة السهم إلى الأسفل
                    const icon = newFilterButton.querySelector('i');
                    if (icon) {
                        icon.classList.remove('fa-chevron-up');
                        icon.classList.add('fa-chevron-down');
                    }

                    // تطبيق الفلتر على الإعلانات
                    filterAds(filterValue);
                });
            });
        }
    } else {
        console.error('Filter button or menu not found');
    }
}

/**
 * تحديث قيمة الصرف بالنسبة في جدول العميل
 */
function updateClientAdsRatioValue(clientId, ratioValue) {
    // البحث عن جميع صفوف الإعلانات للعميل
    const adsRows = document.querySelectorAll(`.ads-row[data-client-id="${clientId}"]`);

    adsRows.forEach(row => {
        // الحصول على جميع الخلايا في الصف
        const cells = row.querySelectorAll('td');

        // التأكد من وجود خليتين على الأقل (العنوان والقيمة)
        if (cells.length >= 2) {
            // الخلية الثانية هي التي تحتوي على قيمة الإعلانات
            const adsCell = cells[1];

            // تحديث قيمة الصرف بالنسبة مباشرة
            adsCell.textContent = parseFloat(ratioValue).toLocaleString();

            // إضافة تأثير مرئي للتحديث
            adsCell.style.transition = 'background-color 0.5s ease';
            adsCell.style.backgroundColor = '#e6f7ff';
            setTimeout(() => {
                adsCell.style.backgroundColor = '';
            }, 1000);
        }
    });
}

/**
 * تحديث قيمة إجمالي حساب العميل نهاية الشهر
 */
function updateClientBalanceValue(clientId, balanceValue) {
    // البحث عن بطاقات العميل
    const clientCards = document.querySelectorAll(`.page-card`);

    clientCards.forEach(card => {
        // التحقق مما إذا كانت هذه البطاقة تنتمي للعميل المطلوب
        const adsRow = card.querySelector(`.ads-row[data-client-id="${clientId}"]`);

        if (adsRow) {
            // الحصول على جميع الصفوف في الجدول
            const rows = card.querySelectorAll('tr');

            // الصف الأخير يحتوي على إجمالي حساب العميل نهاية الشهر
            if (rows.length > 0) {
                const lastRow = rows[rows.length - 1];
                const cells = lastRow.querySelectorAll('td');

                if (cells.length >= 2) {
                    const balanceCell = cells[1];

                    // تحديث القيمة
                    balanceCell.textContent = parseFloat(balanceValue).toLocaleString();

                    // إضافة تأثير مرئي للتحديث
                    balanceCell.style.transition = 'background-color 0.5s ease';
                    balanceCell.style.backgroundColor = '#e6f7ff';
                    setTimeout(() => {
                        balanceCell.style.backgroundColor = '';
                    }, 1000);
                }
            }
        }
    });
}

/**
 * إعداد معالج حدث شريط تمرير النسبة
 */
function setupPercentageSlider() {
    console.log('Setting up percentage slider');
    const percentageSlider = document.getElementById('percentageSlider');
    const percentageInput = document.getElementById('percentageInput');
    const currentPercentage = document.getElementById('currentPercentage');

    if (percentageSlider && percentageInput && currentPercentage) {
        console.log('Found percentage slider elements, setting up event listeners');

        // إزالة مستمعي الأحداث السابقة
        const newPercentageSlider = percentageSlider.cloneNode(true);
        percentageSlider.parentNode.replaceChild(newPercentageSlider, percentageSlider);

        const newPercentageInput = percentageInput.cloneNode(true);
        percentageInput.parentNode.replaceChild(newPercentageInput, percentageInput);

        // إضافة مستمع الحدث لشريط التمرير
        newPercentageSlider.addEventListener('input', function() {
            console.log('Percentage slider changed:', this.value);
            newPercentageInput.value = this.value;
            currentPercentage.textContent = `% ${this.value}`;
        });

        // إضافة مستمع الحدث لحقل الإدخال
        newPercentageInput.addEventListener('input', function() {
            console.log('Percentage input changed:', this.value);
            newPercentageSlider.value = this.value;
            currentPercentage.textContent = `% ${this.value}`;
        });
    } else {
        console.error('Percentage slider elements not found');
    }
}

/**
 * إعداد معالج حدث زر حفظ النسبة
 */
function setupSavePercentageButton() {
    console.log('Setting up save percentage button');
    const savePercentageBtn = document.getElementById('savePercentageBtn');

    if (savePercentageBtn) {
        console.log('Found save percentage button, setting up event listener');

        // إزالة مستمعي الأحداث السابقة
        const newSavePercentageBtn = savePercentageBtn.cloneNode(true);
        savePercentageBtn.parentNode.replaceChild(newSavePercentageBtn, savePercentageBtn);

        // إضافة مستمع الحدث الجديد
        newSavePercentageBtn.addEventListener('click', function() {
            console.log('Save percentage button clicked');

            try {
                // الحصول على قيمة النسبة
                const percentageInput = document.getElementById('percentageInput');
                if (!percentageInput) {
                    console.error('Percentage input not found');
                    alert('حدث خطأ: لم يتم العثور على حقل النسبة.');
                    return;
                }

                const percentage = percentageInput.value;
                console.log('Percentage value:', percentage);

                // تحديث النسبة لجميع الإعلانات
                updateAllAdsPercentage(percentage);

                // إغلاق نافذة اختيار النسبة
                const percentageModal = bootstrap.Modal.getInstance(document.getElementById('percentageModal'));
                if (percentageModal) {
                    percentageModal.hide();
                } else {
                    console.error('Percentage modal instance not found');
                }
            } catch (error) {
                console.error('Error in save percentage button click handler:', error);
                alert('حدث خطأ أثناء محاولة حفظ النسبة.');
            }
        });
    } else {
        console.error('Save percentage button not found');
    }
}

/**
 * إعداد معالج حدث إجمالي الصرف بالنسبة
 */
function setupTotalAdsSpendRatioEvent() {
    console.log('Setting up total ads spend ratio event');
    const totalAdsSpendRatio = document.getElementById('totalAdsSpendRatio');

    if (totalAdsSpendRatio) {
        console.log('Found total ads spend ratio element, setting up event listener');

        // إزالة مستمعي الأحداث السابقة
        const newTotalAdsSpendRatio = totalAdsSpendRatio.cloneNode(true);
        totalAdsSpendRatio.parentNode.replaceChild(newTotalAdsSpendRatio, totalAdsSpendRatio);

        // إضافة مستمع الحدث الجديد
        newTotalAdsSpendRatio.addEventListener('click', function() {
            console.log('Total ads spend ratio clicked');

            try {
                // التحقق من وجود معرف العميل
                if (!currentClientId) {
                    console.error('Client ID is missing');
                    alert('حدث خطأ: لم يتم تحديد العميل. يرجى إعادة فتح نافذة الإعلانات.');
                    return;
                }

                // الحصول على النسبة الحالية
                fetch(`api/ads/index.php?action=get_percentage&client_id=${currentClientId}`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            // تعيين النسبة الحالية في نافذة اختيار النسبة
                            const percentage = data.percentage || 0;
                            document.getElementById('percentageSlider').value = percentage;
                            document.getElementById('percentageInput').value = percentage;
                            document.getElementById('currentPercentage').textContent = `% ${percentage}`;

                            // عرض نافذة اختيار النسبة
                            const percentageModal = new bootstrap.Modal(document.getElementById('percentageModal'));
                            percentageModal.show();
                        } else {
                            alert(data.message);
                        }
                    })
                    .catch(error => {
                        console.error('Error getting percentage:', error);
                        alert('حدث خطأ أثناء محاولة الحصول على النسبة الحالية.');
                    });
            } catch (error) {
                console.error('Error in total ads spend ratio click handler:', error);
                alert('حدث خطأ أثناء محاولة فتح نافذة اختيار النسبة.');
            }
        });
    } else {
        console.error('Total ads spend ratio element not found');
    }
}

/**
 * إعداد معالج حدث نموذج إضافة إعلان
 */
function setupAddAdForm() {
    console.log('Setting up add ad form');
    const addAdForm = document.getElementById('addAdForm');

    if (addAdForm) {
        console.log('Found add ad form, setting up event listener');

        // إزالة مستمعي الأحداث السابقة
        const newAddAdForm = addAdForm.cloneNode(true);
        addAdForm.parentNode.replaceChild(newAddAdForm, addAdForm);

        // إضافة مستمع الحدث الجديد
        newAddAdForm.addEventListener('submit', function(e) {
            e.preventDefault();
            console.log('Add ad form submitted');
            submitAddAdForm();
        });
    } else {
        console.error('Add ad form not found');
    }
}

/**
 * إعداد معالج حدث زر إضافة إعلان
 */
function setupAddAdButton() {
    console.log('Setting up add ad button');
    const addAdBtn = document.getElementById('addAdBtn');

    if (addAdBtn) {
        console.log('Found add ad button, setting up event listener');

        // إزالة مستمعي الأحداث السابقة
        const newAddAdBtn = addAdBtn.cloneNode(true);
        addAdBtn.parentNode.replaceChild(newAddAdBtn, addAdBtn);

        // إضافة مستمع الحدث الجديد
        newAddAdBtn.addEventListener('click', function(e) {
            e.preventDefault();
            console.log('Add ad button clicked');
            console.log('Current client ID:', currentClientId);

            try {
                // التحقق من وجود معرف العميل
                if (!currentClientId) {
                    console.error('Client ID is missing');
                    alert('حدث خطأ: لم يتم تحديد العميل. يرجى إعادة فتح نافذة الإعلانات.');
                    return;
                }

                // تعيين معرف العميل في نموذج إضافة إعلان
                document.getElementById('adClientId').value = currentClientId;
                console.log('Set client ID in form:', currentClientId);

                // تعيين التاريخ الحالي كتاريخ افتراضي
                const today = new Date();
                const formattedDate = today.toISOString().split('T')[0];
                document.getElementById('adDate').value = formattedDate;

                // إخفاء نافذة الإعلانات
                const adsModalElement = document.getElementById('adsModal');
                const adsModal = bootstrap.Modal.getInstance(adsModalElement);
                if (adsModal) {
                    adsModal.hide();
                }

                // عرض نافذة إضافة إعلان بعد تأخير قصير
                setTimeout(() => {
                    const addAdModalElement = document.getElementById('addAdModal');
                    if (addAdModalElement) {
                        console.log('Found addAdModal element, showing modal');
                        const addAdModal = new bootstrap.Modal(addAdModalElement);
                        addAdModal.show();
                    } else {
                        console.error('addAdModal element not found');
                        alert('حدث خطأ: لم يتم العثور على نافذة إضافة إعلان.');
                    }
                }, 300);
            } catch (error) {
                console.error('Error in add ad button click handler:', error);
                alert('حدث خطأ أثناء محاولة فتح نافذة إضافة إعلان.');
            }
        });
    } else {
        console.error('Add ad button not found');
    }
}