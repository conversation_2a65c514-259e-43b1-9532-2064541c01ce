/**
 * ملف JavaScript لصفحة اكونتات كريديت كارد
 */

// عند تحميل المستند
document.addEventListener('DOMContentLoaded', function() {
    // تهيئة البحث
    initSearch();
    
    // تهيئة أزرار إضافة المعاملات
    initAddTransactionButtons();
    
    // تهيئة نموذج إضافة المعاملة
    initAddTransactionForm();
});

/**
 * تهيئة البحث
 */
function initSearch() {
    const searchInput = document.getElementById('searchInput');
    if (searchInput) {
        searchInput.addEventListener('keyup', function() {
            const searchText = this.value.toLowerCase();
            const accountSections = document.querySelectorAll('.account-section');
            
            accountSections.forEach(section => {
                const accountName = section.querySelector('.account-name').textContent.toLowerCase();
                
                if (accountName.includes(searchText)) {
                    section.style.display = '';
                } else {
                    // البحث في المعاملات
                    const transactions = section.querySelectorAll('tbody tr');
                    let found = false;
                    
                    transactions.forEach(transaction => {
                        const cells = transaction.querySelectorAll('td');
                        cells.forEach(cell => {
                            if (cell.textContent.toLowerCase().includes(searchText)) {
                                found = true;
                            }
                        });
                    });
                    
                    section.style.display = found ? '' : 'none';
                }
            });
        });
    }
}

/**
 * تهيئة أزرار إضافة المعاملات
 */
function initAddTransactionButtons() {
    const addButtons = document.querySelectorAll('.add-transaction-button');
    const modal = new bootstrap.Modal(document.getElementById('addTransactionModal'));
    
    addButtons.forEach(button => {
        button.addEventListener('click', function() {
            const accountSection = this.closest('.account-section');
            const accountId = accountSection.dataset.accountId;
            const accountName = accountSection.querySelector('.account-name').textContent;
            
            // تعيين معرف الحساب في النموذج
            document.getElementById('accountId').value = accountId;
            
            // تعيين عنوان الموديل
            document.getElementById('addTransactionModalLabel').textContent = 'إضافة معاملة لـ ' + accountName;
            
            // تعيين التاريخ الحالي
            const today = new Date().toISOString().split('T')[0];
            document.getElementById('transactionDate').value = today;
            
            // عرض الموديل
            modal.show();
        });
    });
}

/**
 * تهيئة نموذج إضافة المعاملة
 */
function initAddTransactionForm() {
    const form = document.getElementById('addTransactionForm');
    
    if (form) {
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            
            // جمع بيانات النموذج
            const formData = new FormData(this);
            
            // حساب المبلغ اليومي
            const amount = parseFloat(formData.get('amount'));
            const days = parseInt(formData.get('days'));
            const dailyAmount = amount / days;
            
            // إضافة المبلغ اليومي إلى البيانات
            formData.append('daily_amount', dailyAmount.toFixed(2));
            
            // إرسال البيانات إلى الخادم
            fetch('api/credit_card/add_transaction.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // إغلاق الموديل
                    const modal = bootstrap.Modal.getInstance(document.getElementById('addTransactionModal'));
                    modal.hide();
                    
                    // إعادة تحميل الصفحة
                    location.reload();
                } else {
                    alert('حدث خطأ: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('حدث خطأ أثناء إرسال البيانات');
            });
        });
    }
}

/**
 * تحديث المبلغ اليومي عند تغيير المبلغ أو عدد الأيام
 */
function updateDailyAmount() {
    const amountInput = document.getElementById('transactionAmount');
    const daysInput = document.getElementById('transactionDays');
    const dailyAmountDisplay = document.getElementById('dailyAmountDisplay');
    
    if (amountInput && daysInput && dailyAmountDisplay) {
        const amount = parseFloat(amountInput.value) || 0;
        const days = parseInt(daysInput.value) || 1;
        const dailyAmount = amount / days;
        
        dailyAmountDisplay.textContent = dailyAmount.toFixed(2);
    }
}

// إضافة مستمعي الأحداث لتحديث المبلغ اليومي
document.addEventListener('DOMContentLoaded', function() {
    const amountInput = document.getElementById('transactionAmount');
    const daysInput = document.getElementById('transactionDays');
    
    if (amountInput && daysInput) {
        amountInput.addEventListener('input', updateDailyAmount);
        daysInput.addEventListener('input', updateDailyAmount);
        
        // تحديث القيمة الأولية
        updateDailyAmount();
    }
});
