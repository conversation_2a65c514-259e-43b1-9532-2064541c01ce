/**
 * ملف JavaScript للتعامل مع حساب العميل نهاية الشهر
 */

// المتغيرات العامة
let currentEndMonthClientId = null;
let endMonthData = null;

// عند تحميل المستند
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM Content Loaded - End Month Balance JS');

    // إعداد مستمعي الأحداث
    setupEndMonthEventListeners();
});

/**
 * إعداد مستمعي الأحداث
 */
function setupEndMonthEventListeners() {
    // إضافة مستمع حدث لزر الطباعة
    const printEndMonthBtn = document.getElementById('printEndMonthBtn');
    if (printEndMonthBtn) {
        printEndMonthBtn.addEventListener('click', function() {
            printEndMonthBalance();
        });
    }

    // إضافة مستمعي الأحداث للتبويبات
    const endMonthTabs = document.querySelectorAll('#endMonthTabs .nav-link');
    endMonthTabs.forEach(tab => {
        tab.addEventListener('click', function() {
            const tabId = this.id;
            console.log(`Tab clicked: ${tabId}`);

            // تحديث المحتوى عند تغيير التبويب
            if (currentEndMonthClientId) {
                updateTabContent(tabId, currentEndMonthClientId);
            }
        });
    });
}

/**
 * فتح نافذة حساب العميل نهاية الشهر
 * @param {string} clientId معرف العميل
 */
function openEndMonthBalanceModal(clientId) {
    console.log(`Opening end month balance modal for client ID: ${clientId}`);

    // تعيين معرف العميل الحالي
    currentEndMonthClientId = clientId;

    // تعيين معرف العميل في النافذة
    const modalElement = document.getElementById('endMonthBalanceModal');
    modalElement.setAttribute('data-client-id', clientId);

    // جلب بيانات حساب العميل نهاية الشهر
    fetchEndMonthBalanceData(clientId);

    // عرض النافذة
    try {
        const modal = new bootstrap.Modal(modalElement);
        modal.show();
    } catch (error) {
        console.error('Error showing modal:', error);
        // محاولة بديلة
        $(modalElement).modal('show');
    }
}

/**
 * جلب بيانات حساب العميل نهاية الشهر
 * @param {string} clientId معرف العميل
 */
function fetchEndMonthBalanceData(clientId) {
    console.log(`Fetching end month balance data for client ID: ${clientId}`);

    // عرض مؤشر التحميل
    document.getElementById('servicesContent').innerHTML = '<div class="loading-spinner">جاري تحميل البيانات...</div>';
    document.getElementById('adsContent').innerHTML = '<div class="loading-spinner">جاري تحميل البيانات...</div>';
    document.getElementById('paymentsContent').innerHTML = '<div class="loading-spinner">جاري تحميل البيانات...</div>';
    document.getElementById('previousContent').innerHTML = '<div class="loading-spinner">جاري تحميل البيانات...</div>';
    document.getElementById('totalEndMonthBalance').textContent = '0';

    // جلب البيانات من الخادم
    fetch(`api/end_month_balance.php?client_id=${clientId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                console.log('End month balance data:', data);

                // تخزين البيانات
                endMonthData = data;

                // تحديث عنوان النافذة
                document.getElementById('endMonthBalanceModalLabel').textContent = `حساب العميل نهاية الشهر - ${data.client.name}`;

                // تحديث الإجماليات
                updateTotals(data);

                // تحديث محتوى التبويب النشط
                const activeTab = document.querySelector('#endMonthTabs .nav-link.active');
                if (activeTab) {
                    updateTabContent(activeTab.id, clientId);
                }
            } else {
                console.error('Error fetching end month balance data:', data.message);
                showErrorMessage('حدث خطأ أثناء جلب البيانات');
            }
        })
        .catch(error => {
            console.error('Error fetching end month balance data:', error);
            showErrorMessage('حدث خطأ أثناء جلب البيانات');
        });
}

/**
 * تحديث محتوى التبويب
 * @param {string} tabId معرف التبويب
 * @param {string} clientId معرف العميل
 */
function updateTabContent(tabId, clientId) {
    console.log(`Updating tab content for tab ID: ${tabId}, client ID: ${clientId}`);

    if (!endMonthData) {
        console.error('End month data is not available');
        return;
    }

    switch (tabId) {
        case 'services-tab':
            updateServicesTabContent();
            break;
        case 'ads-tab':
            updateAdsTabContent();
            break;
        case 'payments-tab':
            updatePaymentsTabContent();
            break;
        case 'previous-tab':
            updatePreviousTabContent();
            break;
        default:
            console.error(`Unknown tab ID: ${tabId}`);
    }
}

/**
 * تحديث محتوى تبويب إدارة الصفحة
 */
function updateServicesTabContent() {
    const servicesContent = document.getElementById('servicesContent');

    if (!endMonthData || !endMonthData.services || endMonthData.services.length === 0) {
        servicesContent.innerHTML = '<div class="no-data">لا توجد خدمات</div>';
        return;
    }

    let html = '<table class="tab-table">';
    html += '<thead><tr><th>الخدمة</th><th>السعر</th></tr></thead>';
    html += '<tbody>';

    let total = 0;
    endMonthData.services.forEach(service => {
        html += `<tr>
            <td>${service.name}</td>
            <td class="amount-cell">${parseFloat(service.price).toLocaleString()}</td>
        </tr>`;
        total += parseFloat(service.price);
    });

    html += `<tr class="total-row">
        <td>الإجمالي</td>
        <td class="amount-cell">${total.toLocaleString()}</td>
    </tr>`;

    html += '</tbody></table>';

    servicesContent.innerHTML = html;
}

/**
 * تحديث محتوى تبويب الإعلانات
 */
function updateAdsTabContent() {
    const adsContent = document.getElementById('adsContent');

    if (!endMonthData || !endMonthData.ads || endMonthData.ads.length === 0) {
        adsContent.innerHTML = '<div class="no-data">لا توجد إعلانات</div>';
        return;
    }

    let html = '<table class="tab-table">';
    html += '<thead><tr><th>التاريخ</th><th>النوع</th><th>البوست</th><th>التكلفة</th><th>الصرف بالنسبة</th></tr></thead>';
    html += '<tbody>';

    let totalCost = 0;
    let totalExchangeRate = 0;

    endMonthData.ads.forEach(ad => {
        const date = new Date(ad.date);
        const formattedDate = `${date.getDate()}-${date.getMonth() + 1}`;

        html += `<tr>
            <td>${formattedDate}</td>
            <td>${ad.type}</td>
            <td>${ad.post || 'بوست'}</td>
            <td class="amount-cell">${parseFloat(ad.cost).toLocaleString()}</td>
            <td class="amount-cell">${parseFloat(ad.exchange_rate_with_percentage).toLocaleString()}</td>
        </tr>`;

        totalCost += parseFloat(ad.cost);
        totalExchangeRate += parseFloat(ad.exchange_rate_with_percentage);
    });

    html += `<tr class="total-row">
        <td colspan="3">الإجمالي</td>
        <td class="amount-cell">${totalCost.toLocaleString()}</td>
        <td class="amount-cell">${totalExchangeRate.toLocaleString()}</td>
    </tr>`;

    html += '</tbody></table>';

    adsContent.innerHTML = html;
}

/**
 * تحديث محتوى تبويب المدفوعات
 */
function updatePaymentsTabContent() {
    const paymentsContent = document.getElementById('paymentsContent');

    if (!endMonthData || !endMonthData.payments || endMonthData.payments.length === 0) {
        paymentsContent.innerHTML = '<div class="no-data">لا توجد مدفوعات</div>';
        return;
    }

    let html = '<table class="tab-table">';
    html += '<thead><tr><th>التاريخ</th><th>طريقة الدفع</th><th>المبلغ</th></tr></thead>';
    html += '<tbody>';

    let total = 0;
    endMonthData.payments.forEach(payment => {
        const date = new Date(payment.date);
        const formattedDate = `${date.getDate()}-${date.getMonth() + 1}`;

        html += `<tr>
            <td>${formattedDate}</td>
            <td>${payment.payment_method}</td>
            <td class="amount-cell">${parseFloat(payment.amount).toLocaleString()}</td>
        </tr>`;

        total += parseFloat(payment.amount);
    });

    html += `<tr class="total-row">
        <td colspan="2">الإجمالي</td>
        <td class="amount-cell">${total.toLocaleString()}</td>
    </tr>`;

    html += '</tbody></table>';

    paymentsContent.innerHTML = html;
}

/**
 * تحديث محتوى تبويب الحسابات السابقة
 */
function updatePreviousTabContent() {
    const previousContent = document.getElementById('previousContent');

    if (!endMonthData || !endMonthData.previous_balance) {
        previousContent.innerHTML = '<div class="no-data">لا توجد حسابات سابقة</div>';
        return;
    }

    let html = '<table class="tab-table">';
    html += '<thead><tr><th>البيان</th><th>المبلغ</th></tr></thead>';
    html += '<tbody>';

    html += `<tr>
        <td>المتبقي من الشهر السابق</td>
        <td class="amount-cell">${parseFloat(endMonthData.previous_debt).toLocaleString()}</td>
    </tr>`;

    html += `<tr>
        <td>إجمالي حساب سابق</td>
        <td class="amount-cell">${parseFloat(endMonthData.previous_balance).toLocaleString()}</td>
    </tr>`;

    html += '</tbody></table>';

    previousContent.innerHTML = html;
}

/**
 * تحديث الإجماليات
 * @param {Object} data بيانات حساب العميل نهاية الشهر
 */
function updateTotals(data) {
    // تحديث إجمالي إدارة الصفحة
    const servicesTotal = data.services_total || 0;
    document.getElementById('totalServices').textContent = parseFloat(servicesTotal).toLocaleString();

    // تحديث إجمالي الإعلانات
    const adsTotal = data.ads_exchange_total || 0;
    document.getElementById('totalAds').textContent = parseFloat(adsTotal).toLocaleString();

    // تحديث إجمالي المدفوعات
    const paymentsTotal = data.payments_total || 0;
    document.getElementById('totalPayments').textContent = parseFloat(paymentsTotal).toLocaleString();

    // تحديث إجمالي الحسابات السابقة
    const previousTotal = data.previous_balance || 0;
    document.getElementById('totalPrevious').textContent = parseFloat(previousTotal).toLocaleString();

    // تحديث إجمالي حساب العميل نهاية الشهر
    const totalBalance = data.total_balance || 0;
    document.getElementById('totalEndMonthBalance').textContent = parseFloat(totalBalance).toLocaleString();
}

/**
 * عرض رسالة خطأ
 * @param {string} message رسالة الخطأ
 */
function showErrorMessage(message) {
    document.getElementById('servicesContent').innerHTML = `<div class="error-message">${message}</div>`;
    document.getElementById('adsContent').innerHTML = `<div class="error-message">${message}</div>`;
    document.getElementById('paymentsContent').innerHTML = `<div class="error-message">${message}</div>`;
    document.getElementById('previousContent').innerHTML = `<div class="error-message">${message}</div>`;
}

/**
 * طباعة حساب العميل نهاية الشهر
 */
function printEndMonthBalance() {
    if (!endMonthData) {
        console.error('No data to print');
        return;
    }

    const printWindow = window.open('', '_blank');

    let printHTML = `
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>طباعة حساب العميل نهاية الشهر</title>
        <style>
            body {
                font-family: Arial, sans-serif;
                padding: 20px;
                direction: rtl;
            }
            .print-header {
                text-align: center;
                margin-bottom: 20px;
            }
            .print-header h1 {
                color: #4a56e2;
                font-size: 24px;
                margin-bottom: 5px;
            }
            .print-header p {
                color: #666;
                font-size: 14px;
                margin: 0;
            }
            .section {
                margin-bottom: 20px;
            }
            .section-title {
                background-color: #4a56e2;
                color: white;
                padding: 10px;
                margin-bottom: 10px;
            }
            table {
                width: 100%;
                border-collapse: collapse;
                margin-bottom: 20px;
            }
            th, td {
                padding: 8px;
                text-align: right;
                border-bottom: 1px solid #ddd;
            }
            th {
                background-color: #f2f2f2;
                font-weight: bold;
            }
            .total-row {
                font-weight: bold;
                background-color: #f2f2f2;
            }
            .total-section {
                text-align: center;
                margin-top: 20px;
                padding: 10px;
                background-color: #f2f2f2;
                font-weight: bold;
                font-size: 18px;
            }
        </style>
    </head>
    <body>
        <div class="print-header">
            <h1>حساب العميل نهاية الشهر - ${endMonthData.client.name}</h1>
            <p>تاريخ الطباعة: ${new Date().toLocaleDateString('ar-EG')}</p>
        </div>
    `;

    // إضافة قسم إدارة الصفحة
    if (endMonthData.services && endMonthData.services.length > 0) {
        printHTML += `
        <div class="section">
            <div class="section-title">إدارة الصفحة</div>
            <table>
                <thead>
                    <tr>
                        <th>الخدمة</th>
                        <th>السعر</th>
                    </tr>
                </thead>
                <tbody>
        `;

        let total = 0;
        endMonthData.services.forEach(service => {
            printHTML += `
            <tr>
                <td>${service.name}</td>
                <td>${parseFloat(service.price).toLocaleString()}</td>
            </tr>
            `;
            total += parseFloat(service.price);
        });

        printHTML += `
                <tr class="total-row">
                    <td>الإجمالي</td>
                    <td>${total.toLocaleString()}</td>
                </tr>
                </tbody>
            </table>
        </div>
        `;
    }

    // إضافة قسم الإعلانات
    if (endMonthData.ads && endMonthData.ads.length > 0) {
        printHTML += `
        <div class="section">
            <div class="section-title">الإعلانات</div>
            <table>
                <thead>
                    <tr>
                        <th>التاريخ</th>
                        <th>النوع</th>
                        <th>البوست</th>
                        <th>التكلفة</th>
                        <th>الصرف بالنسبة</th>
                    </tr>
                </thead>
                <tbody>
        `;

        let totalCost = 0;
        let totalExchangeRate = 0;

        endMonthData.ads.forEach(ad => {
            const date = new Date(ad.date);
            const formattedDate = `${date.getDate()}-${date.getMonth() + 1}`;

            printHTML += `
            <tr>
                <td>${formattedDate}</td>
                <td>${ad.type}</td>
                <td>${ad.post || 'بوست'}</td>
                <td>${parseFloat(ad.cost).toLocaleString()}</td>
                <td>${parseFloat(ad.exchange_rate_with_percentage).toLocaleString()}</td>
            </tr>
            `;

            totalCost += parseFloat(ad.cost);
            totalExchangeRate += parseFloat(ad.exchange_rate_with_percentage);
        });

        printHTML += `
                <tr class="total-row">
                    <td colspan="3">الإجمالي</td>
                    <td>${totalCost.toLocaleString()}</td>
                    <td>${totalExchangeRate.toLocaleString()}</td>
                </tr>
                </tbody>
            </table>
        </div>
        `;
    }

    // إضافة قسم المدفوعات
    if (endMonthData.payments && endMonthData.payments.length > 0) {
        printHTML += `
        <div class="section">
            <div class="section-title">المدفوعات</div>
            <table>
                <thead>
                    <tr>
                        <th>التاريخ</th>
                        <th>طريقة الدفع</th>
                        <th>المبلغ</th>
                    </tr>
                </thead>
                <tbody>
        `;

        let total = 0;
        endMonthData.payments.forEach(payment => {
            const date = new Date(payment.date);
            const formattedDate = `${date.getDate()}-${date.getMonth() + 1}`;

            printHTML += `
            <tr>
                <td>${formattedDate}</td>
                <td>${payment.payment_method}</td>
                <td>${parseFloat(payment.amount).toLocaleString()}</td>
            </tr>
            `;

            total += parseFloat(payment.amount);
        });

        printHTML += `
                <tr class="total-row">
                    <td colspan="2">الإجمالي</td>
                    <td>${total.toLocaleString()}</td>
                </tr>
                </tbody>
            </table>
        </div>
        `;
    }

    // إضافة قسم الحسابات السابقة
    printHTML += `
    <div class="section">
        <div class="section-title">الحسابات السابقة</div>
        <table>
            <thead>
                <tr>
                    <th>البيان</th>
                    <th>المبلغ</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>المتبقي من الشهر السابق</td>
                    <td>${parseFloat(endMonthData.previous_debt).toLocaleString()}</td>
                </tr>
                <tr>
                    <td>إجمالي حساب سابق</td>
                    <td>${parseFloat(endMonthData.previous_balance).toLocaleString()}</td>
                </tr>
            </tbody>
        </table>
    </div>
    `;

    // إضافة قسم الإجماليات
    printHTML += `
    <div class="summary-section">
        <div class="section-title">ملخص الإجماليات</div>
        <table>
            <tr>
                <th>البيان</th>
                <th>المبلغ</th>
            </tr>
            <tr>
                <td>إجمالي إدارة الصفحة</td>
                <td>${parseFloat(endMonthData.services_total || 0).toLocaleString()}</td>
            </tr>
            <tr>
                <td>إجمالي الإعلانات</td>
                <td>${parseFloat(endMonthData.ads_exchange_total || 0).toLocaleString()}</td>
            </tr>
            <tr>
                <td>إجمالي المدفوعات</td>
                <td>${parseFloat(endMonthData.payments_total || 0).toLocaleString()}</td>
            </tr>
            <tr>
                <td>إجمالي الحسابات السابقة</td>
                <td>${parseFloat(endMonthData.previous_balance || 0).toLocaleString()}</td>
            </tr>
        </table>
    </div>

    <div class="total-section">
        إجمالي حساب العميل نهاية الشهر: ${parseFloat(endMonthData.total_balance).toLocaleString()}
    </div>

    <script>
        window.onload = function() {
            window.print();
            setTimeout(function() { window.close(); }, 500);
        };
    </script>
    </body>
    </html>
    `;

    printWindow.document.open();
    printWindow.document.write(printHTML);
    printWindow.document.close();
}
