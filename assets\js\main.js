/**
 * Main JavaScript File
 */

// Document Ready
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Bootstrap tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // Initialize Bootstrap popovers
    var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });
    
    // Service card click handler
    const serviceCards = document.querySelectorAll('.service-card');
    serviceCards.forEach(card => {
        card.addEventListener('click', function() {
            const serviceText = this.querySelector('.service-text').textContent;
            // You can redirect to specific pages based on the service clicked
            // For now, we'll just log the service name
            console.log('Service clicked:', serviceText);
            
            // Example of redirection:
            // window.location.href = 'service.php?name=' + encodeURIComponent(serviceText);
        });
    });
    
    // Auto-hide alerts after 5 seconds
    const alerts = document.querySelectorAll('.alert');
    alerts.forEach(alert => {
        setTimeout(() => {
            const bsAlert = new bootstrap.Alert(alert);
            bsAlert.close();
        }, 5000);
    });
    
    // Form validation
    const forms = document.querySelectorAll('.needs-validation');
    Array.from(forms).forEach(form => {
        form.addEventListener('submit', event => {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            form.classList.add('was-validated');
        }, false);
    });
});

/**
 * AJAX form submission
 * 
 * @param {string} formId Form ID
 * @param {string} url URL to submit form to
 * @param {function} successCallback Function to call on success
 * @param {function} errorCallback Function to call on error
 */
function submitFormAjax(formId, url, successCallback, errorCallback) {
    const form = document.getElementById(formId);
    const formData = new FormData(form);
    
    fetch(url, {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            if (typeof successCallback === 'function') {
                successCallback(data);
            }
        } else {
            if (typeof errorCallback === 'function') {
                errorCallback(data);
            }
        }
    })
    .catch(error => {
        console.error('Error:', error);
        if (typeof errorCallback === 'function') {
            errorCallback({ success: false, message: 'An error occurred. Please try again.' });
        }
    });
}

/**
 * Show loading spinner
 * 
 * @param {string} elementId Element ID to show spinner in
 * @param {string} size Size of spinner (sm, md, lg)
 * @param {string} color Color of spinner (primary, secondary, success, danger, warning, info, light, dark)
 */
function showSpinner(elementId, size = 'md', color = 'primary') {
    const element = document.getElementById(elementId);
    const spinner = document.createElement('div');
    spinner.className = `spinner-border text-${color} spinner-border-${size}`;
    spinner.setAttribute('role', 'status');
    
    const span = document.createElement('span');
    span.className = 'visually-hidden';
    span.textContent = 'Loading...';
    
    spinner.appendChild(span);
    element.innerHTML = '';
    element.appendChild(spinner);
}

/**
 * Format date
 * 
 * @param {string} dateString Date string
 * @param {string} format Format (short, medium, long)
 * @return {string} Formatted date
 */
function formatDate(dateString, format = 'medium') {
    const date = new Date(dateString);
    
    const options = {
        short: { day: 'numeric', month: 'numeric', year: 'numeric' },
        medium: { day: 'numeric', month: 'short', year: 'numeric' },
        long: { day: 'numeric', month: 'long', year: 'numeric', hour: '2-digit', minute: '2-digit' }
    };
    
    return date.toLocaleDateString('ar-EG', options[format]);
}
