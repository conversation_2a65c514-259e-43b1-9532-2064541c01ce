/**
 * ملف JavaScript لإدارة فئات العملاء
 */

// المتغيرات العامة
let clientTypesData = [];
let currentEditingId = null;

// عند تحميل المستند
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM Content Loaded - Manage Client Types JS');

    // إعداد مستمعي الأحداث
    setupEventListeners();

    // جلب بيانات فئات العملاء
    fetchClientTypes();
});

/**
 * إعداد مستمعي الأحداث
 */
function setupEventListeners() {
    // مستمع حدث لزر إضافة فئة
    const addClientTypeBtn = document.getElementById('addClientTypeBtn');
    if (addClientTypeBtn) {
        addClientTypeBtn.addEventListener('click', function() {
            openClientTypeModal();
        });
    }

    // مستمع حدث لزر التحديث
    const refreshBtn = document.getElementById('refreshBtn');
    if (refreshBtn) {
        refreshBtn.addEventListener('click', function() {
            fetchClientTypes();
        });
    }

    // مستمع حدث لنموذج فئة العميل
    const clientTypeForm = document.getElementById('clientTypeForm');
    if (clientTypeForm) {
        clientTypeForm.addEventListener('submit', function(e) {
            e.preventDefault();
            saveClientType();
        });
    }

    // مستمع حدث لزر تأكيد الحذف
    const confirmDeleteBtn = document.getElementById('confirmDeleteBtn');
    if (confirmDeleteBtn) {
        confirmDeleteBtn.addEventListener('click', function() {
            deleteClientType();
        });
    }

    // مستمع حدث لتغيير لون الفئة
    const colorPicker = document.getElementById('clientTypeColor');
    const colorText = document.getElementById('clientTypeColorText');
    
    if (colorPicker && colorText) {
        colorPicker.addEventListener('change', function() {
            colorText.value = this.value;
        });
        
        colorText.addEventListener('input', function() {
            if (this.value.match(/^#[0-9A-F]{6}$/i)) {
                colorPicker.value = this.value;
            }
        });
    }
}

/**
 * جلب بيانات فئات العملاء
 */
function fetchClientTypes() {
    console.log('Fetching client types data');

    // عرض مؤشر التحميل
    document.getElementById('clientTypesContent').innerHTML = '<div class="loading-message">جاري تحميل البيانات...</div>';

    // جلب البيانات من الخادم
    fetch('api/client_types.php')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                console.log('Client types data:', data);

                // تخزين البيانات
                clientTypesData = data.client_types;

                // عرض البيانات
                displayClientTypes(clientTypesData);
            } else {
                console.error('Error fetching client types data:', data.message);
                document.getElementById('clientTypesContent').innerHTML = `<div class="alert alert-danger">حدث خطأ أثناء جلب البيانات: ${data.message}</div>`;
            }
        })
        .catch(error => {
            console.error('Error fetching client types data:', error);
            document.getElementById('clientTypesContent').innerHTML = '<div class="alert alert-danger">حدث خطأ أثناء جلب البيانات</div>';
        });
}

/**
 * عرض بيانات فئات العملاء
 * @param {Array} clientTypes بيانات فئات العملاء
 */
function displayClientTypes(clientTypes) {
    const contentContainer = document.getElementById('clientTypesContent');

    if (!clientTypes || clientTypes.length === 0) {
        contentContainer.innerHTML = '<div class="alert alert-info">لا توجد فئات عملاء</div>';
        return;
    }

    let html = '';

    clientTypes.forEach(clientType => {
        html += `
        <div class="client-type-card" data-id="${clientType.id}">
            <div class="client-type-header">
                <div class="client-type-name">${clientType.name}</div>
                <div class="client-type-actions">
                    <button class="edit-btn" onclick="editClientType(${clientType.id})">
                        <i class="fas fa-edit"></i> تعديل
                    </button>
                    <button class="delete-btn" onclick="confirmDeleteClientType(${clientType.id})">
                        <i class="fas fa-trash"></i> حذف
                    </button>
                </div>
            </div>
            <div class="client-type-display-name">${clientType.display_name}</div>
            ${clientType.description ? `<div class="client-type-description">${clientType.description}</div>` : ''}
            <div class="client-type-info">
                <div class="client-type-color" style="background-color: ${clientType.color}"></div>
                <div class="client-type-sort">ترتيب: ${clientType.sort_order}</div>
            </div>
        </div>`;
    });

    contentContainer.innerHTML = html;
}

/**
 * فتح نافذة إضافة/تعديل فئة العميل
 * @param {Object} clientType بيانات الفئة للتعديل (اختياري)
 */
function openClientTypeModal(clientType = null) {
    const modal = new bootstrap.Modal(document.getElementById('clientTypeModal'));
    const form = document.getElementById('clientTypeForm');
    const modalTitle = document.getElementById('clientTypeModalLabel');

    // إعادة تعيين النموذج
    form.reset();
    currentEditingId = null;

    if (clientType) {
        // وضع التعديل
        modalTitle.textContent = 'تعديل الفئة';
        document.getElementById('clientTypeId').value = clientType.id;
        document.getElementById('clientTypeName').value = clientType.name;
        document.getElementById('clientTypeDisplayName').value = clientType.display_name;
        document.getElementById('clientTypeDescription').value = clientType.description || '';
        document.getElementById('clientTypeColor').value = clientType.color;
        document.getElementById('clientTypeColorText').value = clientType.color;
        document.getElementById('clientTypeSortOrder').value = clientType.sort_order;
        currentEditingId = clientType.id;
    } else {
        // وضع الإضافة
        modalTitle.textContent = 'إضافة فئة جديدة';
        document.getElementById('clientTypeColor').value = '#4a56e2';
        document.getElementById('clientTypeColorText').value = '#4a56e2';
    }

    modal.show();
}

/**
 * حفظ فئة العميل
 */
function saveClientType() {
    const form = document.getElementById('clientTypeForm');
    const formData = new FormData(form);
    
    const clientTypeData = {
        name: formData.get('name').trim(),
        display_name: formData.get('display_name').trim(),
        description: formData.get('description').trim(),
        color: formData.get('color'),
        sort_order: parseInt(formData.get('sort_order')) || 0
    };

    // التحقق من صحة البيانات
    if (!clientTypeData.name || !clientTypeData.display_name) {
        alert('يرجى ملء جميع الحقول المطلوبة');
        return;
    }

    const isEditing = currentEditingId !== null;
    const url = 'api/client_types.php';
    const method = isEditing ? 'PUT' : 'POST';

    if (isEditing) {
        clientTypeData.id = currentEditingId;
    }

    // إرسال البيانات إلى الخادم
    fetch(url, {
        method: method,
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(clientTypeData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // إغلاق النافذة
            const modal = bootstrap.Modal.getInstance(document.getElementById('clientTypeModal'));
            modal.hide();

            // إعادة تحميل البيانات
            fetchClientTypes();

            // عرض رسالة نجاح
            showAlert('success', data.message);
        } else {
            alert('حدث خطأ: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error saving client type:', error);
        alert('حدث خطأ أثناء حفظ البيانات');
    });
}

/**
 * تعديل فئة العميل
 * @param {number} id معرف الفئة
 */
function editClientType(id) {
    const clientType = clientTypesData.find(ct => ct.id == id);
    if (clientType) {
        openClientTypeModal(clientType);
    }
}

/**
 * تأكيد حذف فئة العميل
 * @param {number} id معرف الفئة
 */
function confirmDeleteClientType(id) {
    currentEditingId = id;
    const modal = new bootstrap.Modal(document.getElementById('deleteConfirmModal'));
    modal.show();
}

/**
 * حذف فئة العميل
 */
function deleteClientType() {
    if (currentEditingId === null) {
        return;
    }

    // إرسال طلب الحذف إلى الخادم
    fetch(`api/client_types.php?id=${currentEditingId}`, {
        method: 'DELETE'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // إغلاق نافذة التأكيد
            const modal = bootstrap.Modal.getInstance(document.getElementById('deleteConfirmModal'));
            modal.hide();

            // إعادة تحميل البيانات
            fetchClientTypes();

            // عرض رسالة نجاح
            showAlert('success', data.message);
        } else {
            alert('حدث خطأ: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error deleting client type:', error);
        alert('حدث خطأ أثناء حذف البيانات');
    })
    .finally(() => {
        currentEditingId = null;
    });
}

/**
 * عرض رسالة تنبيه
 * @param {string} type نوع الرسالة (success, danger, info)
 * @param {string} message نص الرسالة
 */
function showAlert(type, message) {
    const alertHtml = `
    <div class="alert alert-${type} alert-dismissible fade show" role="alert">
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>`;
    
    const contentContainer = document.getElementById('clientTypesContent');
    contentContainer.insertAdjacentHTML('beforebegin', alertHtml);
    
    // إزالة الرسالة تلقائياً بعد 5 ثوان
    setTimeout(() => {
        const alert = document.querySelector('.alert');
        if (alert) {
            alert.remove();
        }
    }, 5000);
}
