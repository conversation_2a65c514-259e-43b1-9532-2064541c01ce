document.addEventListener('DOMContentLoaded', function() {
    // عناصر الصفحة
    const pagesGrid = document.querySelector('.pages-grid');
    const searchInput = document.getElementById('searchInput');
    const noResults = document.getElementById('noResults');

    // عناصر نافذة تفاصيل إدارة الصفحة
    const pageManagementModal = new bootstrap.Modal(document.getElementById('pageManagementModal'));
    const pageManagementDetails = document.getElementById('pageManagementDetails');
    const totalPageManagement = document.getElementById('totalPageManagement');

    // عناصر نموذج إضافة خدمة
    const addServiceBtn = document.getElementById('addServiceBtn');
    const addServiceForm = document.getElementById('addServiceForm');
    const newServiceName = document.getElementById('newServiceName');
    const newServicePrice = document.getElementById('newServicePrice');
    const saveServiceBtn = document.getElementById('saveServiceBtn');

    // عناصر نموذج تعديل خدمة
    const editServiceForm = document.getElementById('editServiceForm');
    const editServiceName = document.getElementById('editServiceName');
    const editServicePrice = document.getElementById('editServicePrice');
    const editServiceId = document.getElementById('editServiceId');
    const updateServiceBtn = document.getElementById('updateServiceBtn');

    // عناصر نافذة إضافة عميل
    const addClientBtn = document.querySelector('.add-client-btn');
    const addClientModal = new bootstrap.Modal(document.getElementById('addClientModal'));
    const addClientForm = document.getElementById('addClientForm');

    // زر حفظ التغييرات
    const saveChangesBtn = document.getElementById('saveChangesBtn');

    // متغير لتخزين معرف العميل الحالي
    let currentClientId = null;

    // جلب العملاء عند تحميل الصفحة
    loadClients();

    // فتح نافذة إضافة عميل
    if (addClientBtn) {
        addClientBtn.addEventListener('click', function() {
            addClientModal.show();
        });
    }

    // إضافة عميل جديد
    if (addClientForm) {
        addClientForm.addEventListener('submit', function(e) {
            e.preventDefault();

            const clientName = document.getElementById('clientName').value;
            const previousDebt = parseFloat(document.getElementById('previousDebt').value);
            const paymentDay = parseInt(document.getElementById('paymentDay').value);

            // إنشاء بيانات النموذج
            const formData = new FormData();
            formData.append('name', clientName);
            formData.append('previous_debt', previousDebt);
            formData.append('payment_day', paymentDay);

            // عرض حالة التحميل
            const submitBtn = addClientForm.querySelector('button[type="submit"]');
            const originalBtnText = submitBtn.innerHTML;
            submitBtn.innerHTML = 'جاري الإضافة...';
            submitBtn.disabled = true;

            // إرسال طلب إضافة العميل
            fetch('api/clients/index.php?action=add', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // إضافة العميل إلى واجهة المستخدم
                    addClientToUI(data.client);

                    // عرض رسالة نجاح
                    alert(data.message);

                    // إغلاق النافذة وإعادة تعيين النموذج
                    addClientModal.hide();
                    addClientForm.reset();
                } else {
                    // عرض رسالة خطأ
                    alert(data.message || 'حدث خطأ أثناء إضافة العميل');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('حدث خطأ أثناء إضافة العميل');
            })
            .finally(() => {
                // إعادة تعيين حالة الزر
                submitBtn.innerHTML = originalBtnText;
                submitBtn.disabled = false;
            });
        });
    }

    // جلب العملاء
    function loadClients() {
        // عرض رسالة تحميل
        pagesGrid.innerHTML = '<div class="loading-message">جاري تحميل البيانات...</div>';

        // جلب العملاء من الخادم
        fetch('api/clients/index.php?action=get')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // عرض العملاء
                    pagesGrid.innerHTML = '';

                    if (data.clients.length === 0) {
                        pagesGrid.innerHTML = '<div class="no-clients">لا يوجد عملاء حاليًا</div>';
                    } else {
                        data.clients.forEach(client => {
                            addClientToUI(client);
                        });
                    }
                } else {
                    // عرض رسالة خطأ
                    pagesGrid.innerHTML = `<div class="error-message">${data.message || 'حدث خطأ أثناء جلب العملاء'}</div>`;
                }
            })
            .catch(error => {
                console.error('Error:', error);
                pagesGrid.innerHTML = '<div class="error-message">حدث خطأ أثناء جلب العملاء</div>';
            });
    }

    // إضافة عميل إلى واجهة المستخدم
    function addClientToUI(client) {
        const clientCard = document.createElement('div');
        clientCard.className = 'page-card';

        clientCard.innerHTML = `
            <div class="page-card-header">
                <div class="page-name">${client.name}</div>
                <div class="page-options dropdown">
                    <i class="fas fa-ellipsis-h dropdown-toggle" data-page-id="${client.id}"></i>
                    <div class="dropdown-menu dropdown-menu-end">
                        <a href="#" class="dropdown-item payment-day">تاريخ الدفع: ${client.payment_day}</a>
                        <a href="#" class="dropdown-item delete-client">إزالة العميل</a>
                        <a href="#" class="dropdown-item">ملاحظات</a>
                        <a href="#" class="dropdown-item">استخدامات</a>
                    </div>
                </div>
            </div>
            <table class="page-details">
                <tr class="page-management-row" data-client-id="${client.id}">
                    <td style="display: block; width: 60%;" class="clickable-cell">إدارة الصفحة</td>
                    <td class="clickable-cell">${(client.page_management || 0).toLocaleString()}</td>
                </tr>
                <tr class="ads-row" data-client-id="${client.id}">
                    <td style="display: block; width: 60%;" class="clickable-cell">إعلانات</td>
                    <td class="clickable-cell">${((client.ads_ratio !== undefined) ? client.ads_ratio : (client.ads || 0)).toLocaleString()}</td>
                </tr>
                <tr class="payments-row" data-client-id="${client.id}">
                    <td style="display: block; width: 60%;" class="clickable-cell">المدفوعات</td>
                    <td class="clickable-cell">${(client.payments || 0).toLocaleString()}</td>
                </tr>
                <tr class="account-row" data-client-id="${client.id}">
                    <td style="width: 60%;" class="clickable-cell">اجمالي حساب سابق</td>
                    <td class="clickable-cell">${(client.previous_balance || 0).toLocaleString()}</td>
                </tr>
                <tr class="end-month-balance-row" data-client-id="${client.id}">
                    <td style="width: 60%;" class="clickable-cell end-month-balance-cell">اجمالي حساب العميل نهاية الشهر</td>
                    <td class="clickable-cell end-month-balance-cell">${(client.end_month_balance || 0).toLocaleString()}</td>
                </tr>
            </table>
        `;

        // إضافة العميل إلى الشبكة
        pagesGrid.appendChild(clientCard);

        // إعداد أحداث النقر
        setupClientEvents(clientCard);
    }

    // إعداد أحداث النقر للعميل
    function setupClientEvents(clientCard) {
        // إعداد القائمة المنسدلة
        const dropdownToggle = clientCard.querySelector('.dropdown-toggle');
        if (dropdownToggle) {
            dropdownToggle.addEventListener('click', function(e) {
                e.stopPropagation();
                const dropdown = this.nextElementSibling;

                // إغلاق جميع القوائم المنسدلة الأخرى
                document.querySelectorAll('.dropdown-menu.show').forEach(menu => {
                    if (menu !== dropdown) {
                        menu.classList.remove('show');
                    }
                });

                // تبديل القائمة الحالية
                dropdown.classList.toggle('show');
            });
        }

        // إعداد خلايا الإعلانات
        const adsCells = clientCard.querySelectorAll('.ads-row .clickable-cell');
        adsCells.forEach(cell => {
            cell.addEventListener('click', function() {
                // الحصول على معرف العميل
                const clientId = this.closest('.ads-row').getAttribute('data-client-id');

                // فتح نافذة الإعلانات
                if (typeof openAdsModal === 'function') {
                    openAdsModal(clientId);
                }
            });
        });

        // إعداد خلايا المدفوعات
        const paymentCells = clientCard.querySelectorAll('.payments-row .clickable-cell');
        paymentCells.forEach(cell => {
            cell.addEventListener('click', function() {
                // الحصول على معرف العميل
                const clientId = this.closest('.payments-row').getAttribute('data-client-id');
                console.log(`Payment cell clicked for client ID: ${clientId} from manage_pages.js`);

                // فتح نافذة المدفوعات
                if (typeof openPaymentsModal === 'function') {
                    openPaymentsModal(clientId);
                } else {
                    console.error('openPaymentsModal function not found');
                }
            });
        });

        // إعداد خلايا الحسابات السابقة
        const accountCells = clientCard.querySelectorAll('.account-row .clickable-cell');
        accountCells.forEach(cell => {
            cell.addEventListener('click', function() {
                // الحصول على معرف العميل
                const clientId = this.closest('.account-row').getAttribute('data-client-id');
                console.log(`Account cell clicked for client ID: ${clientId} from manage_pages.js`);

                // فتح نافذة الحساب السابق
                if (typeof openPreviousAccountModal === 'function') {
                    openPreviousAccountModal(clientId);
                } else {
                    console.error('openPreviousAccountModal function not found');
                }
            });
        });

        // إعداد خلايا حساب العميل نهاية الشهر
        const endMonthCells = clientCard.querySelectorAll('.end-month-balance-cell');
        console.log(`Found ${endMonthCells.length} end month balance cells in client card`);
        endMonthCells.forEach(cell => {
            cell.addEventListener('click', function() {
                // الحصول على معرف العميل
                const clientId = this.closest('.end-month-balance-row').getAttribute('data-client-id');
                console.log(`End month balance cell clicked for client ID: ${clientId} from manage_pages.js`);

                // فتح نافذة حساب العميل نهاية الشهر
                if (typeof openEndMonthBalanceModal === 'function') {
                    openEndMonthBalanceModal(clientId);
                } else {
                    console.error('openEndMonthBalanceModal function not found');
                }
            });
        });

        // إعداد زر حذف العميل
        const deleteBtn = clientCard.querySelector('.delete-client');
        if (deleteBtn) {
            deleteBtn.addEventListener('click', function(e) {
                e.preventDefault();
                if (confirm('هل أنت متأكد من رغبتك في حذف هذا العميل؟')) {
                    const pageId = this.closest('.page-options').querySelector('.dropdown-toggle').getAttribute('data-page-id');

                    // إنشاء بيانات النموذج
                    const formData = new FormData();
                    formData.append('id', pageId);

                    // إرسال طلب حذف العميل
                    fetch('api/clients/index.php?action=delete', {
                        method: 'POST',
                        body: formData
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            // إزالة العميل من واجهة المستخدم
                            clientCard.remove();

                            // عرض رسالة نجاح
                            alert(data.message);
                        } else {
                            // عرض رسالة خطأ
                            alert(data.message || 'حدث خطأ أثناء حذف العميل');
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        alert('حدث خطأ أثناء حذف العميل');
                    });
                }
            });
        }

        // إعداد خلايا إدارة الصفحة
        const managementCells = clientCard.querySelectorAll('.page-management-row .clickable-cell');
        managementCells.forEach(cell => {
            cell.addEventListener('click', function() {
                // الحصول على معرف العميل
                currentClientId = this.closest('.page-management-row').getAttribute('data-client-id');

                // إظهار رسالة تحميل
                pageManagementDetails.innerHTML = '<div class="loading-message">جاري تحميل الخدمات...</div>';

                // جلب خدمات إدارة الصفحة من الخادم
                fetch(`api/page_services/index.php?action=get&client_id=${currentClientId}`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            // تحديث عنوان النافذة
                            const modalTitle = document.getElementById('pageManagementModalLabel');
                            if (modalTitle) {
                                modalTitle.textContent = `إدارة الصفحة - ${data.client.name}`;
                            }

                            // عرض الخدمات
                            pageManagementDetails.innerHTML = '';

                            if (data.services.length === 0) {
                                pageManagementDetails.innerHTML = '<div class="no-services">لا توجد خدمات مضافة بعد</div>';
                            } else {
                                data.services.forEach(service => {
                                    const serviceCard = document.createElement('div');
                                    serviceCard.className = 'service-card';
                                    serviceCard.setAttribute('data-id', service.id);
                                    serviceCard.setAttribute('data-name', service.name);
                                    serviceCard.setAttribute('data-price', service.price);

                                    serviceCard.innerHTML = `
                                        <div class="edit-icons">
                                            <div class="edit-service"><i class="fas fa-edit"></i></div>
                                            <div class="delete-service"><i class="fas fa-times"></i></div>
                                        </div>
                                        <div class="service-price">${service.price}</div>
                                        <div class="service-name">${service.name}</div>
                                    `;

                                    pageManagementDetails.appendChild(serviceCard);
                                });
                            }

                            // إعداد أزرار التعديل والحذف
                            setupEditDeleteButtons();

                            // تحديث المجموع
                            if (totalPageManagement) {
                                totalPageManagement.textContent = data.total.toLocaleString().replace(/,/g, '.');
                            }
                        } else {
                            pageManagementDetails.innerHTML = `<div class="error-message">${data.message || 'حدث خطأ أثناء جلب الخدمات'}</div>`;
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        pageManagementDetails.innerHTML = '<div class="error-message">حدث خطأ أثناء جلب الخدمات</div>';
                    });

                // إخفاء نماذج الإضافة والتعديل
                addServiceForm.style.display = 'none';
                editServiceForm.style.display = 'none';

                // عرض النافذة
                pageManagementModal.show();
            });
        });
    }

    // إظهار/إخفاء نموذج إضافة خدمة
    if (addServiceBtn) {
        addServiceBtn.addEventListener('click', function() {
            addServiceForm.style.display = 'block';
            newServiceName.focus();
        });
    }

    // وظيفة إضافة خدمة جديدة
    function addNewService() {
        const serviceName = newServiceName.value.trim();
        const servicePrice = parseFloat(newServicePrice.value);

        if (serviceName && !isNaN(servicePrice) && servicePrice > 0) {
            // إنشاء معرف فريد للخدمة الجديدة
            const serviceId = 'new_' + Date.now();

            // إنشاء عنصر الخدمة الجديدة
            const serviceCard = document.createElement('div');
            serviceCard.className = 'service-card';
            serviceCard.setAttribute('data-id', serviceId);
            serviceCard.setAttribute('data-name', serviceName);
            serviceCard.setAttribute('data-price', servicePrice);

            // إضافة تأثير ظهور تدريجي
            serviceCard.style.opacity = '0';
            serviceCard.style.transform = 'translateY(20px)';

            serviceCard.innerHTML = `
                <div class="edit-icons">
                    <div class="edit-service"><i class="fas fa-edit"></i></div>
                    <div class="delete-service"><i class="fas fa-times"></i></div>
                </div>
                <div class="service-price">${servicePrice}</div>
                <div class="service-name">${serviceName}</div>
            `;

            // إضافة الخدمة إلى القائمة
            pageManagementDetails.appendChild(serviceCard);

            // تطبيق تأثير الظهور التدريجي
            setTimeout(() => {
                serviceCard.style.transition = 'all 0.5s ease';
                serviceCard.style.opacity = '1';
                serviceCard.style.transform = 'translateY(0)';
            }, 10);

            // إعداد أزرار التعديل والحذف
            setupEditDeleteButtons();

            // إعادة حساب المجموع
            calculateTotal();

            // إعادة تعيين النموذج
            newServiceName.value = '';
            newServicePrice.value = '';
            addServiceForm.style.display = 'none';
        } else {
            alert('يرجى إدخال اسم الخدمة والسعر بشكل صحيح');
        }
    }

    // حفظ الخدمة الجديدة عند النقر على زر الحفظ
    if (saveServiceBtn) {
        saveServiceBtn.addEventListener('click', addNewService);
    }

    // حفظ الخدمة الجديدة عند الضغط على Enter
    if (newServiceName && newServicePrice) {
        [newServiceName, newServicePrice].forEach(input => {
            input.addEventListener('keydown', function(e) {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    addNewService();
                }
            });
        });
    }

    // إعداد أزرار التعديل والحذف للخدمات الموجودة
    function setupEditDeleteButtons() {
        const serviceCards = pageManagementDetails.querySelectorAll('.service-card');

        serviceCards.forEach(card => {
            // الحصول على أزرار التعديل والحذف
            const editBtn = card.querySelector('.edit-service');
            const deleteBtn = card.querySelector('.delete-service');

            // إزالة أي أحداث سابقة
            if (editBtn) {
                const newEditBtn = editBtn.cloneNode(true);
                editBtn.parentNode.replaceChild(newEditBtn, editBtn);

                // إضافة حدث النقر لزر التعديل
                newEditBtn.addEventListener('click', function(e) {
                    e.stopPropagation(); // منع انتشار الحدث

                    // الحصول على بيانات الخدمة
                    const serviceId = card.getAttribute('data-id');
                    const serviceName = card.getAttribute('data-name');
                    const servicePrice = card.getAttribute('data-price');

                    // ملء نموذج التعديل
                    editServiceId.value = serviceId;
                    editServiceName.value = serviceName;
                    editServicePrice.value = servicePrice;

                    // إظهار نموذج التعديل وإخفاء نموذج الإضافة
                    editServiceForm.style.display = 'block';
                    addServiceForm.style.display = 'none';

                    // التركيز على حقل الاسم
                    editServiceName.focus();
                });
            }

            if (deleteBtn) {
                const newDeleteBtn = deleteBtn.cloneNode(true);
                deleteBtn.parentNode.replaceChild(newDeleteBtn, deleteBtn);

                // إضافة حدث النقر لزر الحذف
                newDeleteBtn.addEventListener('click', function(e) {
                    e.stopPropagation(); // منع انتشار الحدث

                    if (confirm('هل أنت متأكد من رغبتك في حذف هذه الخدمة؟')) {
                        // إضافة تأثير اختفاء تدريجي
                        card.style.transition = 'all 0.5s ease';
                        card.style.opacity = '0';
                        card.style.transform = 'translateY(20px)';

                        // إزالة البطاقة بعد انتهاء التأثير
                        setTimeout(() => {
                            card.remove();
                            calculateTotal();
                        }, 500);
                    }
                });
            }
        });
    }

    // تحديث الخدمة
    if (updateServiceBtn) {
        updateServiceBtn.addEventListener('click', function() {
            const serviceId = editServiceId.value;
            const serviceName = editServiceName.value.trim();
            const servicePrice = parseFloat(editServicePrice.value);

            if (serviceName && !isNaN(servicePrice) && servicePrice > 0) {
                // البحث عن البطاقة المطلوب تعديلها
                const card = pageManagementDetails.querySelector(`.service-card[data-id="${serviceId}"]`);

                if (card) {
                    // تحديث البيانات المخزنة
                    card.setAttribute('data-name', serviceName);
                    card.setAttribute('data-price', servicePrice);

                    // تحديث العناصر المرئية
                    card.querySelector('.service-name').textContent = serviceName;
                    card.querySelector('.service-price').textContent = servicePrice;

                    // إضافة تأثير تحديث
                    card.style.transition = 'background-color 0.5s ease';
                    card.style.backgroundColor = '#e6f7ff';
                    setTimeout(() => {
                        card.style.backgroundColor = '#f8f9fa';
                    }, 1000);

                    // إعادة حساب المجموع
                    calculateTotal();

                    // إخفاء نموذج التعديل
                    editServiceForm.style.display = 'none';
                }
            } else {
                alert('يرجى إدخال اسم الخدمة والسعر بشكل صحيح');
            }
        });
    }

    // حفظ التغييرات في قاعدة البيانات
    if (saveChangesBtn) {
        saveChangesBtn.addEventListener('click', function() {
            if (!currentClientId) {
                alert('لم يتم تحديد العميل');
                return;
            }

            // جمع بيانات الخدمات
            const services = [];
            const serviceCards = pageManagementDetails.querySelectorAll('.service-card');

            serviceCards.forEach(card => {
                services.push({
                    id: card.getAttribute('data-id'),
                    name: card.getAttribute('data-name'),
                    price: card.getAttribute('data-price')
                });
            });

            // حساب المجموع
            const total = calculateTotal(true);

            // إنشاء بيانات الطلب
            const data = {
                client_id: currentClientId,
                services: services,
                total: total
            };

            // عرض رسالة تحميل
            saveChangesBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الحفظ...';
            saveChangesBtn.disabled = true;

            // إرسال البيانات إلى الخادم
            fetch('api/page_services/index.php?action=save', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(result => {
                if (result.success) {
                    // تحديث قيمة إدارة الصفحة في الجدول
                    const row = document.querySelector(`.page-management-row[data-client-id="${currentClientId}"]`);
                    if (row) {
                        const cell = row.querySelector('td:last-child');
                        if (cell) {
                            cell.textContent = total.toLocaleString();
                        }
                    }

                    // إغلاق النافذة المنبثقة
                    pageManagementModal.hide();

                    // عرض رسالة نجاح
                    alert(result.message || 'تم حفظ التغييرات بنجاح');
                } else {
                    // عرض رسالة خطأ
                    alert(result.message || 'حدث خطأ أثناء حفظ التغييرات');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('حدث خطأ أثناء حفظ التغييرات');
            })
            .finally(() => {
                // إعادة تعيين زر الحفظ
                saveChangesBtn.innerHTML = '<i class="fas fa-save"></i> حفظ التغييرات';
                saveChangesBtn.disabled = false;
            });
        });
    }

    // حساب المجموع
    function calculateTotal(returnValue = false) {
        if (pageManagementDetails && totalPageManagement) {
            let total = 0;
            const priceElements = pageManagementDetails.querySelectorAll('.service-price');

            priceElements.forEach(element => {
                const price = parseFloat(element.textContent.replace(/,/g, ''));
                if (!isNaN(price)) {
                    total += price;
                }
            });

            // تنسيق المجموع بالنقطة بدلاً من الفاصلة
            totalPageManagement.textContent = total.toLocaleString().replace(/,/g, '.');

            // إرجاع القيمة إذا طلب ذلك
            if (returnValue) {
                return total;
            }
        }

        return 0;
    }

    // وظيفة البحث
    if (searchInput) {
        searchInput.addEventListener('input', function() {
            const searchTerm = this.value.trim().toLowerCase();
            let resultsFound = false;

            const pageCards = document.querySelectorAll('.page-card');
            pageCards.forEach(card => {
                const clientName = card.querySelector('.page-name').textContent.toLowerCase();

                if (clientName.includes(searchTerm)) {
                    card.classList.remove('hidden');

                    // إضافة تأثير التمييز إذا كان هناك نص بحث
                    if (searchTerm.length > 0) {
                        card.classList.add('highlight');
                    } else {
                        card.classList.remove('highlight');
                    }

                    resultsFound = true;
                } else {
                    card.classList.add('hidden');
                    card.classList.remove('highlight');
                }
            });

            // إظهار أو إخفاء رسالة عدم وجود نتائج
            if (!resultsFound && searchTerm.length > 0) {
                noResults.style.display = 'block';
            } else {
                noResults.style.display = 'none';
            }
        });

        // إضافة وظيفة مسح البحث عند الضغط على Escape
        searchInput.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                this.value = '';
                this.dispatchEvent(new Event('input'));
            }
        });
    }

    // إغلاق القوائم المنسدلة عند النقر خارجها
    document.addEventListener('click', function() {
        document.querySelectorAll('.dropdown-menu.show').forEach(menu => {
            menu.classList.remove('show');
        });
    });
});
