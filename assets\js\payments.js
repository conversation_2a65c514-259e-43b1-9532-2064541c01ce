/**
 * ملف JavaScript للتعامل مع المدفوعات
 */

// المتغيرات العامة
let currentPaymentClientId = null;

// عند تحميل المستند
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM Content Loaded - Payments.js');

    // إعداد مستمعي الأحداث
    setupEventListeners();

    // إعداد مستمعي الأحداث لصفوف المدفوعات
    setupPaymentsRowsEvents();
});

/**
 * إعداد مستمعي الأحداث
 */
function setupEventListeners() {
    // تعيين التاريخ الحالي في حقل التاريخ
    const today = new Date();
    const formattedDate = today.toISOString().split('T')[0];

    // التحقق من وجود عناصر DOM
    if (document.getElementById('paymentDate')) {
        document.getElementById('paymentDate').value = formattedDate;
    }

    // إضافة مستمع أحداث لحقل المبلغ لتحديث الإجمالي
    if (document.getElementById('paymentAmount')) {
        document.getElementById('paymentAmount').addEventListener('input', updateTotalAfterAddition);
    }

    // إضافة مستمع أحداث للنقر على زر إضافة مدفوعات
    const addPaymentBtn = document.getElementById('addPaymentBtn');
    if (addPaymentBtn) {
        addPaymentBtn.addEventListener('click', function() {
            const clientId = document.getElementById('paymentsModal').getAttribute('data-client-id');
            console.log(`Add payment button clicked for client ID: ${clientId}`);
            openAddPaymentModal(clientId);
        });
    }

    // إضافة مستمع أحداث للنقر على زر إضافة دفعة - سيتم إعداده عند فتح نافذة إضافة دفعة
}

/**
 * إعداد مستمعي الأحداث لصفوف المدفوعات
 * يتم استدعاء هذه الوظيفة عند تحميل الصفحة
 */
function setupPaymentsRowsEvents() {
    console.log('Setting up payments rows events');

    // الحصول على جميع خلايا المدفوعات القابلة للنقر
    const paymentCells = document.querySelectorAll('.payments-row .clickable-cell');
    console.log(`Found ${paymentCells.length} payment cells`);

    paymentCells.forEach(cell => {
        // إزالة مستمعي الأحداث السابقة لتجنب التكرار
        const newCell = cell.cloneNode(true);
        cell.parentNode.replaceChild(newCell, cell);

        // إضافة مستمع الحدث الجديد
        newCell.addEventListener('click', function() {
            const clientId = this.closest('.payments-row').getAttribute('data-client-id');
            console.log(`Payment cell clicked for client ID: ${clientId}`);
            openPaymentsModal(clientId);
        });
    });
}

/**
 * فتح نافذة المدفوعات
 * @param {string} clientId معرف العميل
 */
function openPaymentsModal(clientId) {
    console.log(`Opening payments modal for client ID: ${clientId}`);

    // تعيين معرف العميل الحالي
    currentPaymentClientId = clientId;

    // تعيين معرف العميل في النافذة
    const modalElement = document.getElementById('paymentsModal');
    modalElement.setAttribute('data-client-id', clientId);

    // إعداد معالج حدث زر دفع المتبقي من الشهر السابق
    setupPayPreviousMonthButton();

    // جلب المدفوعات
    fetchPayments(clientId);

    // عرض النافذة
    try {
        const modal = new bootstrap.Modal(modalElement);
        modal.show();
    } catch (error) {
        console.error('Error showing modal:', error);
        // محاولة بديلة
        $(modalElement).modal('show');
    }
}

/**
 * فتح نافذة إضافة مدفوعات
 * @param {string} clientId معرف العميل
 */
function openAddPaymentModal(clientId) {
    console.log(`Opening add payment modal for client ID: ${clientId}`);

    // تعيين معرف العميل في النافذة
    document.getElementById('addPaymentModal').setAttribute('data-client-id', clientId);

    // إعادة تعيين النموذج
    document.getElementById('paymentAmount').value = '';
    document.getElementById('totalAfterAddition').textContent = '0';

    // تعيين التاريخ الحالي
    const today = new Date();
    const formattedDate = today.toISOString().split('T')[0];
    document.getElementById('paymentDate').value = formattedDate;

    // إعداد معالج حدث زر إضافة دفعة
    setupSubmitPaymentButton();

    // عرض النافذة
    try {
        const addPaymentModal = new bootstrap.Modal(document.getElementById('addPaymentModal'));
        addPaymentModal.show();
    } catch (error) {
        console.error('Error showing add payment modal:', error);
        // محاولة بديلة
        $('#addPaymentModal').modal('show');
    }
}

/**
 * إعداد معالج حدث زر دفع المتبقي من الشهر السابق
 */
function setupPayPreviousMonthButton() {
    console.log('Setting up pay previous month button');
    const payPreviousMonthBtn = document.getElementById('payPreviousMonthBtn');

    if (payPreviousMonthBtn) {
        console.log('Found pay previous month button, setting up event listener');

        // إزالة مستمعي الأحداث السابقة
        const newPayPreviousMonthBtn = payPreviousMonthBtn.cloneNode(true);
        payPreviousMonthBtn.parentNode.replaceChild(newPayPreviousMonthBtn, payPreviousMonthBtn);

        // إضافة مستمع الحدث الجديد
        newPayPreviousMonthBtn.addEventListener('click', function() {
            console.log('Pay previous month button clicked');

            try {
                // التحقق من وجود معرف العميل
                const clientId = document.getElementById('paymentsModal').getAttribute('data-client-id');
                if (!clientId) {
                    console.error('Client ID is missing');
                    alert('حدث خطأ: لم يتم تحديد العميل. يرجى إعادة فتح نافذة المدفوعات.');
                    return;
                }

                // تأكيد العملية
                if (confirm('هل أنت متأكد من دفع المتبقي من الشهر السابق؟')) {
                    payPreviousMonth(clientId);
                }
            } catch (error) {
                console.error('Error in pay previous month button click handler:', error);
                alert('حدث خطأ أثناء محاولة دفع المتبقي من الشهر السابق.');
            }
        });
    } else {
        console.error('Pay previous month button not found');
    }
}

/**
 * إعداد معالج حدث زر إضافة دفعة
 */
function setupSubmitPaymentButton() {
    console.log('Setting up submit payment button');
    const submitPaymentBtn = document.getElementById('submitPaymentBtn');

    if (submitPaymentBtn) {
        console.log('Found submit payment button, setting up event listener');

        // إزالة مستمعي الأحداث السابقة
        const newSubmitPaymentBtn = submitPaymentBtn.cloneNode(true);
        submitPaymentBtn.parentNode.replaceChild(newSubmitPaymentBtn, submitPaymentBtn);

        // إضافة مستمع الحدث الجديد
        newSubmitPaymentBtn.addEventListener('click', function() {
            console.log('Submit payment button clicked');
            submitAddPaymentForm();
        });
    } else {
        console.error('Submit payment button not found');
    }
}

/**
 * تحديث إجمالي المبلغ بعد التزويد
 */
function updateTotalAfterAddition() {
    const amount = parseFloat(document.getElementById('paymentAmount').value) || 0;
    document.getElementById('totalAfterAddition').textContent = amount.toLocaleString();
}

/**
 * جلب المدفوعات من الخادم
 * @param {string} clientId معرف العميل
 */
function fetchPayments(clientId) {
    console.log(`Fetching payments for client ID: ${clientId}`);
    document.getElementById('paymentsList').innerHTML = '<div class="text-center p-3">جاري التحميل...</div>';

    // جلب البيانات من الخادم
    fetch(`api/payments/index.php?action=get_payments&client_id=${clientId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // تحديث عنوان النافذة
                document.getElementById('paymentsModalLabel').textContent = `المدفوعات - ${data.client.name}`;

                // تحديث قائمة المدفوعات
                updatePaymentsList(data.payments);

                // تحديث إجمالي المدفوعات
                document.getElementById('totalPayments').textContent = parseFloat(data.total_payments).toLocaleString();
            } else {
                // عرض رسالة الخطأ
                document.getElementById('paymentsList').innerHTML = `<div class="text-center p-3 text-danger">${data.message}</div>`;
            }
        })
        .catch(error => {
            console.error('Error fetching payments:', error);
            document.getElementById('paymentsList').innerHTML = '<div class="text-center p-3 text-danger">حدث خطأ أثناء جلب البيانات</div>';
        });
}

/**
 * تحديث قائمة المدفوعات
 * @param {Array} payments قائمة المدفوعات
 */
function updatePaymentsList(payments) {
    const paymentsList = document.getElementById('paymentsList');

    if (payments.length === 0) {
        paymentsList.innerHTML = '<div class="text-center p-3">لا توجد مدفوعات</div>';
        return;
    }

    let html = '';

    payments.forEach(payment => {
        // تنسيق التاريخ
        const date = new Date(payment.date);
        const formattedDate = `${date.getDate()}-${date.getMonth() + 1}`;

        html += `
            <div class="payment-item" data-id="${payment.id}">
                <div class="row">
                    <div class="col">${formattedDate}</div>
                    <div class="col">${payment.payment_method}</div>
                    <div class="col">${parseFloat(payment.amount).toLocaleString()}</div>
                </div>
            </div>
        `;
    });

    paymentsList.innerHTML = html;
}

/**
 * إرسال نموذج إضافة دفعة
 */
function submitAddPaymentForm() {
    // الحصول على البيانات
    const clientId = document.getElementById('addPaymentModal').getAttribute('data-client-id');
    const date = document.getElementById('paymentDate').value;
    const amount = document.getElementById('paymentAmount').value;
    const paymentMethod = document.getElementById('paymentMethod').value;

    // التحقق من صحة البيانات
    if (!date || !amount || parseFloat(amount) <= 0) {
        alert('يرجى ملء جميع الحقول بشكل صحيح');
        return;
    }

    // إعداد البيانات للإرسال
    const formData = new FormData();
    formData.append('client_id', clientId);
    formData.append('date', date);
    formData.append('amount', amount);
    formData.append('payment_method', paymentMethod);

    console.log('Sending payment data:');
    console.log('Client ID:', clientId);
    console.log('Date:', date);
    console.log('Amount:', amount);
    console.log('Payment Method:', paymentMethod);

    // إرسال البيانات إلى الخادم
    fetch('api/payments/index.php?action=add_payment', {
        method: 'POST',
        body: formData
    })
    .then(response => {
        console.log('Server response status:', response.status);
        return response.json();
    })
    .then(data => {
        console.log('Server response data:', data);
        if (data.success) {
            // إغلاق نافذة إضافة دفعة
            try {
                const addPaymentModal = bootstrap.Modal.getInstance(document.getElementById('addPaymentModal'));
                addPaymentModal.hide();
            } catch (error) {
                console.error('Error hiding add payment modal:', error);
                // محاولة بديلة
                $('#addPaymentModal').modal('hide');
            }

            // تحديث قائمة المدفوعات
            fetchPayments(clientId);

            // تحديث قيمة المدفوعات في جدول العميل
            updateClientPaymentsValue(clientId, data.client_data.payments);

            // عرض رسالة نجاح
            alert('تمت إضافة الدفعة بنجاح');
        } else {
            // عرض رسالة الخطأ
            alert(data.message || 'حدث خطأ أثناء إضافة الدفعة');
        }
    })
    .catch(error => {
        console.error('Error adding payment:', error);
        alert('حدث خطأ أثناء إضافة الدفعة');
    });
}

/**
 * دفع المتبقي من الشهر السابق
 * @param {string} clientId معرف العميل
 */
function payPreviousMonth(clientId) {
    console.log(`Paying previous month debt for client ID: ${clientId}`);

    // إعداد البيانات للإرسال
    const formData = new FormData();
    formData.append('client_id', clientId);

    console.log('Sending pay previous month request for client ID:', clientId);

    // إرسال البيانات إلى الخادم
    fetch('api/payments/index.php?action=pay_previous_month', {
        method: 'POST',
        body: formData
    })
    .then(response => {
        console.log('Server response status:', response.status);
        return response.json();
    })
    .then(data => {
        console.log('Server response data:', data);
        if (data.success) {
            // تحديث قائمة المدفوعات
            fetchPayments(clientId);

            // تحديث قيمة المدفوعات في جدول العميل
            updateClientPaymentsValue(clientId, data.client_data.payments);

            // تحديث قيمة المتبقي من الشهر السابق في جدول العميل
            updateClientPreviousDebtValue(clientId, 0);

            // تحديث قيمة إجمالي حساب سابق في جدول العميل
            if (data.client_data.previous_balance !== undefined) {
                updateClientPreviousBalanceValue(clientId, data.client_data.previous_balance);
            }

            // عرض رسالة نجاح
            alert(`تم دفع المتبقي من الشهر السابق بنجاح (${parseFloat(data.amount_paid).toLocaleString()})`);
        } else {
            // عرض رسالة الخطأ
            alert(data.message || 'حدث خطأ أثناء دفع المتبقي من الشهر السابق');
        }
    })
    .catch(error => {
        console.error('Error paying previous month debt:', error);
        alert('حدث خطأ أثناء دفع المتبقي من الشهر السابق');
    });
}

/**
 * تحديث قيمة المدفوعات في جدول العميل
 * @param {string} clientId معرف العميل
 * @param {number} paymentsValue قيمة المدفوعات
 */
function updateClientPaymentsValue(clientId, paymentsValue) {
    // البحث عن صف المدفوعات للعميل
    const row = document.querySelector(`.payments-row[data-client-id="${clientId}"]`);
    if (row) {
        // تحديث قيمة المدفوعات
        const cell = row.querySelector('td:last-child');
        if (cell) {
            cell.textContent = parseFloat(paymentsValue).toLocaleString();

            // إضافة تأثير مرئي للتحديث
            cell.style.transition = 'background-color 0.5s ease';
            cell.style.backgroundColor = '#e6f7ff';
            setTimeout(() => {
                cell.style.backgroundColor = '';
            }, 1000);
        }
    }
}

/**
 * تحديث قيمة المتبقي من الشهر السابق في جدول العميل
 * @param {string} clientId معرف العميل
 * @param {number} previousDebtValue قيمة المتبقي من الشهر السابق
 */
function updateClientPreviousDebtValue(clientId, previousDebtValue) {
    // البحث عن صف العميل
    const row = document.querySelector(`tr[data-client-id="${clientId}"]`);
    if (row) {
        // البحث عن خلية المتبقي من الشهر السابق
        const cells = row.querySelectorAll('td');
        if (cells.length >= 5) { // تأكد من وجود الخلية المناسبة
            const cell = cells[4]; // الخلية الخامسة (المتبقي من الشهر السابق)
            cell.textContent = parseFloat(previousDebtValue).toLocaleString();

            // إضافة تأثير مرئي للتحديث
            cell.style.transition = 'background-color 0.5s ease';
            cell.style.backgroundColor = '#e6f7ff';
            setTimeout(() => {
                cell.style.backgroundColor = '';
            }, 1000);
        }
    }
}

/**
 * تحديث قيمة إجمالي حساب سابق في جدول العميل
 * @param {string} clientId معرف العميل
 * @param {number} previousBalanceValue قيمة إجمالي حساب سابق
 */
function updateClientPreviousBalanceValue(clientId, previousBalanceValue) {
    // البحث عن صف العميل
    const row = document.querySelector(`tr[data-client-id="${clientId}"]`);
    if (row) {
        // البحث عن خلية إجمالي حساب سابق
        const cells = row.querySelectorAll('td');
        if (cells.length >= 6) { // تأكد من وجود الخلية المناسبة
            const cell = cells[5]; // الخلية السادسة (إجمالي حساب سابق)
            cell.textContent = parseFloat(previousBalanceValue).toLocaleString();

            // إضافة تأثير مرئي للتحديث
            cell.style.transition = 'background-color 0.5s ease';
            cell.style.backgroundColor = '#e6f7ff';
            setTimeout(() => {
                cell.style.backgroundColor = '';
            }, 1000);
        }
    }
}
