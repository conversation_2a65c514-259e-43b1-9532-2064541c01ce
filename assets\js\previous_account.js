// وظيفة عرض الحساب السابق
function openPreviousAccountModal(clientId) {
    console.log('openPreviousAccountModal called with clientId:', clientId);

    // عرض مؤشر التحميل
    document.getElementById('previousAccountContent').innerHTML = '<div class="text-center p-5"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">جاري التحميل...</span></div><p class="mt-2">جاري تحميل البيانات...</p></div>';

    // عرض النافذة
    var modal = new bootstrap.Modal(document.getElementById('previousAccountModal'));
    modal.show();

    // جلب البيانات من API
    fetch('api/get_previous_account.php?client_id=' + clientId)
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.json();
        })
        .then(response => {
            if (response.status === 'error') {
                throw new Error(response.message);
            }

            // استخدام البيانات من API
            var data = response.data;
            updatePreviousAccountUI(data);
        })
        .catch(error => {
            console.error('Error fetching previous account data:', error);
            document.getElementById('previousAccountContent').innerHTML = '<div class="alert alert-danger">حدث خطأ أثناء تحميل البيانات: ' + error.message + '</div>';
        });
}

// وظيفة تحديث واجهة المستخدم بالبيانات
function updatePreviousAccountUI(data) {
    // إنشاء HTML
    var html = '<div class="prev-account-container">';

    // قسم الرصيد السابق والشهر الحالي (ثابت)
    html += '<div class="fixed-header-section">';
    html += '<table class="prev-account-table">';
    html += '<tr class="prev-balance-row">';
    html += '<td class="amount">متبقي ' + data.previous_balance.toLocaleString() + '</td>';
    html += '<td class="label">تقفيل ' + data.previous_month + '</td>';
    html += '</tr>';

    // قسم الشهر الحالي
    html += '<tr class="month-row">';
    html += '<td colspan="2">' + data.current_month + '</td>';
    html += '</tr>';
    html += '</table>';
    html += '</div>';

    // قسم الخدمات (ثابت تحت الشهر)
    html += '<div class="fixed-services-section">';
    html += '<table class="prev-account-table">';

    // إضافة الخدمات
    if (data.services && data.services.length > 0) {
        for (var i = 0; i < data.services.length; i++) {
            html += '<tr class="item-row">';
            html += '<td class="amount">' + parseFloat(data.services[i].amount).toLocaleString() + '</td>';
            html += '<td class="label">' + data.services[i].title + '</td>';
            html += '</tr>';
        }
    } else {
        html += '<tr class="item-row"><td colspan="2" class="text-center">لا توجد خدمات إدارة</td></tr>';
    }
    html += '</table>';
    html += '</div>';

    // قسم الإعلانات (قابل للتمرير)
    html += '<div class="scrollable-section ads-section">';
    html += '<table class="prev-account-table">';

    // إضافة الإعلانات
    if (data.ads && data.ads.length > 0) {
        for (var i = 0; i < data.ads.length; i++) {
            var statusClass = data.ads[i].status === 'active' ? 'active-ad' : 'inactive-ad';
            html += '<tr class="item-row ' + statusClass + '">';
            html += '<td class="amount">' + parseFloat(data.ads[i].amount).toLocaleString() + '</td>';
            html += '<td class="label">' + data.ads[i].title + '</td>';
            html += '</tr>';
        }
    } else {
        html += '<tr class="item-row"><td colspan="2" class="text-center">لا توجد إعلانات</td></tr>';
    }
    html += '</table>';
    html += '</div>';

    // إجمالي الإعلانات (ثابت)
    html += '<div class="fixed-ads-total-section">';
    html += '<table class="prev-account-table">';
    html += '<tr class="total-row">';
    html += '<td class="amount">' + parseFloat(data.ads_total).toLocaleString() + '</td>';
    html += '<td class="label">اجمالي الاعلانات</td>';
    html += '</tr>';
    html += '</table>';
    html += '</div>';

    // قسم المدفوعات (عنوان ثابت)
    html += '<div class="fixed-section">';
    html += '<table class="prev-account-table">';
    html += '<tr class="payment-header">';
    html += '<td colspan="3">تم الدفع</td>';
    html += '</tr>';

    // عناوين المدفوعات
    html += '<tr class="payment-titles">';
    html += '<td class="amount">المبلغ</td>';
    html += '<td class="method">طريقة الدفع</td>';
    html += '<td class="date">التاريخ</td>';
    html += '</tr>';
    html += '</table>';
    html += '</div>';

    // قسم المدفوعات (قابل للتمرير)
    html += '<div class="scrollable-section payments-section">';
    html += '<table class="prev-account-table">';

    // إضافة المدفوعات
    if (data.payments && data.payments.length > 0) {
        for (var i = 0; i < data.payments.length; i++) {
            var paymentMethod = data.payments[i].payment_method || 'نقدي'; // القيمة الافتراضية هي "نقدي"
            html += '<tr class="payment-row">';
            html += '<td class="amount">' + parseFloat(data.payments[i].amount).toLocaleString() + '</td>';
            html += '<td class="method">' + paymentMethod + '</td>';
            html += '<td class="date">' + data.payments[i].date + '</td>';
            html += '</tr>';
        }
    } else {
        html += '<tr class="payment-row"><td colspan="3" class="text-center">لا توجد مدفوعات</td></tr>';
    }
    html += '</table>';
    html += '</div>';

    // قسم الإجماليات (ثابت)
    html += '<div class="fixed-footer-section">';
    html += '<table class="prev-account-table">';

    // إضافة إجمالي المدفوعات
    html += '<tr class="total-row">';
    html += '<td class="amount">' + parseFloat(data.payments_total).toLocaleString() + '</td>';
    html += '<td class="label">اجمالي الدفع</td>';
    html += '</tr>';

    // إضافة إجمالي حسابات الشهر الحالي
    html += '<tr class="month-total-row">';
    html += '<td class="amount">' + parseFloat(data.current_month_total).toLocaleString() + '</td>';
    html += '<td class="label">اجمالي حسابات ' + data.current_month + '</td>';
    html += '</tr>';

    // إضافة إجمالي حساب العميل حتى الشهر الحالي
    html += '<tr class="final-total-row">';
    html += '<td class="amount">' + parseFloat(data.total_month_balance).toLocaleString() + '</td>';
    html += '<td class="label">اجمالي حساب العميل حتى ' + data.current_month + '</td>';
    html += '</tr>';
    html += '</table>';
    html += '</div>';

    html += '</div>'; // نهاية prev-account-container

    // إضافة CSS للتنسيق
    var style = document.createElement('style');
    style.textContent = `
        .prev-account-container {
            height: 600px;
            display: flex;
            flex-direction: column;
        }
        .fixed-header-section, .fixed-services-section, .fixed-ads-total-section, .fixed-section, .fixed-footer-section {
            flex-shrink: 0;
        }
        .scrollable-section {
            overflow-y: auto;
            border: 1px solid #eee;
            margin: 5px 0;
            border-radius: 5px;
        }
        .ads-section {
            flex: 2;
            max-height: 180px;
        }
        .payments-section {
            flex: 1;
            max-height: 120px;
        }
        .fixed-services-section {
            margin: 5px 0;
        }
        .fixed-ads-total-section {
            margin: 5px 0;
        }
        .prev-account-table {
            width: 100%;
            border-collapse: collapse;
            direction: ltr;
        }
        .prev-account-table tr {
            border-bottom: 1px solid #dcf343;
        }
        .prev-account-table td {
            padding: 10px 15px;
            color: #4a56e2;
            font-weight: 600;
        }
        .prev-account-table .amount {
            text-align: right;
        }
        .prev-account-table .label {
            text-align: right;
        }
        .prev-balance-row {
            background-color: #f9f9f9;
        }
        .month-row {
            background-color: #f0f2ff;
            text-align: center;
        }
        .month-row td {
            text-align: center;
        }
        .payment-header {
            background-color: #f0f2ff;
            text-align: center;
        }
        .payment-header td {
            text-align: center;
        }
        .total-row {
            background-color: #f9f9f9;
            font-weight: 700;
        }
        .month-total-row {
            background-color: #dcf343;
            font-weight: 700;
        }
        .final-total-row {
            background-color: #f0f2ff;
            font-weight: 700;
            border: 2px solid #4a56e2;
        }
        .active-ad {
            color: #28a745;
        }
        .inactive-ad {
            color: #dc3545;
        }
        .text-center {
            text-align: center;
        }
        .fixed-services-section .prev-account-table {
            background-color: #f8f9fa;
        }
        .fixed-ads-total-section .total-row {
            background-color: #e3f2fd;
            font-weight: 700;
            border: 1px solid #2196f3;
        }
        .ads-section {
            background-color: #fafafa;
        }
    `;
    document.head.appendChild(style);

    // تحديث المحتوى
    document.getElementById('previousAccountContent').innerHTML = html;
}

// وظيفة طباعة الحساب السابق
function printPreviousAccount() {
    var content = document.getElementById('previousAccountContent').innerHTML;
    var printWindow = window.open('', '_blank');

    var printHTML = '<!DOCTYPE html>' +
        '<html dir="rtl" lang="ar">' +
        '<head>' +
        '<meta charset="UTF-8">' +
        '<meta name="viewport" content="width=device-width, initial-scale=1.0">' +
        '<title>طباعة الحساب السابق</title>' +
        '<style>' +
        'body { font-family: Arial, sans-serif; padding: 20px; direction: rtl; }' +
        '.print-header { text-align: center; margin-bottom: 20px; }' +
        '.print-header h1 { color: #4a56e2; font-size: 24px; margin-bottom: 5px; }' +
        '.print-header p { color: #666; font-size: 14px; margin: 0; }' +
        '.prev-account-container { width: 100%; }' +
        '.scrollable-section { width: 100%; }' +
        '.prev-account-table { width: 100%; border-collapse: collapse; }' +
        '.prev-account-table tr { border-bottom: 1px solid #dcf343; }' +
        '.prev-account-table td { padding: 10px 15px; color: #4a56e2; font-weight: 600; }' +
        '.prev-account-table .amount { text-align: right; }' +
        '.prev-account-table .label { text-align: left; }' +
        '.prev-balance-row { background-color: #f9f9f9; }' +
        '.month-row { background-color: #f0f2ff; text-align: center; }' +
        '.month-row td { text-align: center; }' +
        '.payment-header { background-color: #f0f2ff; text-align: center; }' +
        '.payment-header td { text-align: center; }' +
        '.total-row { background-color: #f9f9f9; font-weight: 700; }' +
        '.section-title { background-color: #4a56e2; color: white; padding: 10px; margin: 15px 0 5px 0; border-radius: 5px; }' +
        '</style>' +
        '</head>' +
        '<body>' +
        '<div class="print-header">' +
        '<h1>إجمالي حساب سابق</h1>' +
        '<p>تاريخ الطباعة: ' + new Date().toLocaleDateString('ar-EG') + '</p>' +
        '</div>' +
        content +
        '<script>' +
        'window.onload = function() { window.print(); setTimeout(function() { window.close(); }, 500); };' +
        '</script>' +
        '</body>' +
        '</html>';

    printWindow.document.open();
    printWindow.document.write(printHTML);
    printWindow.document.close();
}

// إضافة مستمع حدث لزر الطباعة
document.addEventListener('DOMContentLoaded', function() {
    var printBtn = document.getElementById('printAccountBtn');
    if (printBtn) {
        printBtn.addEventListener('click', function() {
            printPreviousAccount();
        });
    }
});
