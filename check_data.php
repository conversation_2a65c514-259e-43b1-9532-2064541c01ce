<?php
/**
 * صفحة للتحقق من البيانات المحفوظة في قاعدة البيانات
 */

// تضمين ملف الاتصال بقاعدة البيانات
require_once 'config/database.php';

// إنشاء اتصال بقاعدة البيانات
$database = new Database();
$db = $database->getConnection();

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فحص البيانات المحفوظة</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap">
    <style>
        body { font-family: 'Cairo', sans-serif; background-color: #f8f9fa; }
        .container { margin-top: 30px; }
        .table-container { background: white; border-radius: 10px; padding: 20px; margin-bottom: 30px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .section-title { color: #4a56e2; font-weight: 600; margin-bottom: 20px; border-bottom: 2px solid #4a56e2; padding-bottom: 10px; }
        .table { font-size: 14px; }
        .table th { background-color: #4a56e2; color: white; text-align: center; }
        .table td { text-align: center; vertical-align: middle; }
        .badge { font-size: 12px; }
        .no-data { text-align: center; color: #999; padding: 40px; font-style: italic; }
    </style>
</head>
<body>
    <div class="container">
        <div class="row">
            <div class="col-12">
                <h1 class="text-center mb-4" style="color: #4a56e2;">فحص البيانات المحفوظة في قاعدة البيانات</h1>
            </div>
        </div>

        <!-- بطاقات الفيزا -->
        <div class="table-container">
            <h3 class="section-title">بطاقات الفيزا</h3>
            <?php
            try {
                $stmt = $db->prepare("SELECT * FROM visa_cards ORDER BY id DESC");
                $stmt->execute();
                $visaCards = $stmt->fetchAll(PDO::FETCH_ASSOC);
                
                if (!empty($visaCards)) {
                    echo '<div class="table-responsive">';
                    echo '<table class="table table-striped table-hover">';
                    echo '<thead><tr>';
                    echo '<th>ID</th><th>اسم البطاقة</th><th>الرصيد الأساسي</th><th>الحد اليومي</th><th>الرصيد المتبقي</th><th>إجمالي المديونية</th><th>الحالة</th>';
                    echo '</tr></thead><tbody>';
                    
                    foreach ($visaCards as $card) {
                        echo '<tr>';
                        echo '<td>' . $card['id'] . '</td>';
                        echo '<td>' . htmlspecialchars($card['name']) . '</td>';
                        echo '<td>' . number_format($card['base_balance'], 2) . '</td>';
                        echo '<td>' . number_format($card['daily_limit'], 2) . '</td>';
                        echo '<td>' . number_format($card['remaining_balance'], 2) . '</td>';
                        echo '<td>' . number_format($card['total_debt'], 2) . '</td>';
                        echo '<td><span class="badge bg-success">' . $card['status'] . '</span></td>';
                        echo '</tr>';
                    }
                    
                    echo '</tbody></table></div>';
                } else {
                    echo '<div class="no-data">لا توجد بطاقات فيزا مسجلة</div>';
                }
            } catch (PDOException $e) {
                echo '<div class="alert alert-danger">خطأ في جلب بطاقات الفيزا: ' . $e->getMessage() . '</div>';
            }
            ?>
        </div>

        <!-- الحسابات الإعلانية -->
        <div class="table-container">
            <h3 class="section-title">الحسابات الإعلانية المرتبطة بالفيزا</h3>
            <?php
            try {
                $stmt = $db->prepare("
                    SELECT aa.*, vc.name as card_name 
                    FROM ad_accounts aa 
                    LEFT JOIN visa_cards vc ON aa.linked_account_id = vc.id 
                    WHERE aa.linked_account_type = 'visa' 
                    ORDER BY aa.id DESC
                ");
                $stmt->execute();
                $adAccounts = $stmt->fetchAll(PDO::FETCH_ASSOC);
                
                if (!empty($adAccounts)) {
                    echo '<div class="table-responsive">';
                    echo '<table class="table table-striped table-hover">';
                    echo '<thead><tr>';
                    echo '<th>ID</th><th>اسم الحساب</th><th>البطاقة المرتبطة</th><th>الحالة</th><th>الرصيد</th><th>حد الصرف</th>';
                    echo '</tr></thead><tbody>';
                    
                    foreach ($adAccounts as $account) {
                        echo '<tr>';
                        echo '<td>' . $account['id'] . '</td>';
                        echo '<td>' . htmlspecialchars($account['name']) . '</td>';
                        echo '<td>' . htmlspecialchars($account['card_name'] ?? 'غير محدد') . '</td>';
                        echo '<td><span class="badge bg-info">' . $account['status'] . '</span></td>';
                        echo '<td>' . number_format($account['balance'], 2) . '</td>';
                        echo '<td>' . number_format($account['spending_limit'], 2) . '</td>';
                        echo '</tr>';
                    }
                    
                    echo '</tbody></table></div>';
                } else {
                    echo '<div class="no-data">لا توجد حسابات إعلانية مرتبطة بالفيزا</div>';
                }
            } catch (PDOException $e) {
                echo '<div class="alert alert-danger">خطأ في جلب الحسابات الإعلانية: ' . $e->getMessage() . '</div>';
            }
            ?>
        </div>

        <!-- الإعلانات/المعاملات -->
        <div class="table-container">
            <h3 class="section-title">الإعلانات والمعاملات</h3>
            <?php
            try {
                $stmt = $db->prepare("
                    SELECT a.*, aa.name as account_name, vc.name as card_name,
                           COALESCE(c.name, 'عميل افتراضي') as client_name
                    FROM ads a
                    JOIN ad_accounts aa ON a.ad_account_id = aa.id
                    LEFT JOIN visa_cards vc ON aa.linked_account_id = vc.id
                    LEFT JOIN clients c ON a.client_id = c.id
                    WHERE aa.linked_account_type = 'visa'
                    ORDER BY a.id DESC
                    LIMIT 50
                ");
                $stmt->execute();
                $ads = $stmt->fetchAll(PDO::FETCH_ASSOC);
                
                if (!empty($ads)) {
                    echo '<div class="table-responsive">';
                    echo '<table class="table table-striped table-hover">';
                    echo '<thead><tr>';
                    echo '<th>ID</th><th>التاريخ</th><th>نوع الإعلان</th><th>المنشور</th><th>الحساب</th><th>البطاقة</th><th>التكلفة المتوقعة</th><th>المبلغ المصروف</th><th>الحالة</th>';
                    echo '</tr></thead><tbody>';
                    
                    foreach ($ads as $ad) {
                        echo '<tr>';
                        echo '<td>' . $ad['id'] . '</td>';
                        echo '<td>' . date('Y-m-d', strtotime($ad['date'])) . '</td>';
                        echo '<td>' . htmlspecialchars($ad['type']) . '</td>';
                        echo '<td>' . htmlspecialchars(substr($ad['post'], 0, 30)) . '...</td>';
                        echo '<td>' . htmlspecialchars($ad['account_name']) . '</td>';
                        echo '<td>' . htmlspecialchars($ad['card_name'] ?? 'غير محدد') . '</td>';
                        echo '<td style="color: #28a745; font-weight: bold;">' . number_format($ad['cost'], 2) . '</td>';
                        echo '<td style="color: #dc3545; font-weight: bold;">' . number_format($ad['egyptian_cost'], 2) . '</td>';
                        echo '<td><span class="badge bg-primary">' . $ad['status'] . '</span></td>';
                        echo '</tr>';
                    }
                    
                    echo '</tbody></table></div>';
                } else {
                    echo '<div class="no-data">لا توجد إعلانات أو معاملات مسجلة</div>';
                }
            } catch (PDOException $e) {
                echo '<div class="alert alert-danger">خطأ في جلب الإعلانات: ' . $e->getMessage() . '</div>';
            }
            ?>
        </div>

        <!-- إحصائيات سريعة -->
        <div class="table-container">
            <h3 class="section-title">إحصائيات سريعة</h3>
            <div class="row">
                <?php
                try {
                    // عدد بطاقات الفيزا
                    $stmt = $db->prepare("SELECT COUNT(*) as count FROM visa_cards");
                    $stmt->execute();
                    $visaCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
                    
                    // عدد الحسابات الإعلانية
                    $stmt = $db->prepare("SELECT COUNT(*) as count FROM ad_accounts WHERE linked_account_type = 'visa'");
                    $stmt->execute();
                    $accountsCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
                    
                    // عدد الإعلانات
                    $stmt = $db->prepare("
                        SELECT COUNT(*) as count 
                        FROM ads a 
                        JOIN ad_accounts aa ON a.ad_account_id = aa.id 
                        WHERE aa.linked_account_type = 'visa'
                    ");
                    $stmt->execute();
                    $adsCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
                    
                    // إجمالي المبالغ المتوقعة
                    $stmt = $db->prepare("
                        SELECT SUM(a.cost) as total 
                        FROM ads a 
                        JOIN ad_accounts aa ON a.ad_account_id = aa.id 
                        WHERE aa.linked_account_type = 'visa'
                    ");
                    $stmt->execute();
                    $totalExpected = $stmt->fetch(PDO::FETCH_ASSOC)['total'] ?? 0;
                    
                    // إجمالي المبالغ المصروفة
                    $stmt = $db->prepare("
                        SELECT SUM(a.egyptian_cost) as total 
                        FROM ads a 
                        JOIN ad_accounts aa ON a.ad_account_id = aa.id 
                        WHERE aa.linked_account_type = 'visa'
                    ");
                    $stmt->execute();
                    $totalSpent = $stmt->fetch(PDO::FETCH_ASSOC)['total'] ?? 0;
                    
                    echo '<div class="col-md-2">';
                    echo '<div class="text-center p-3 bg-primary text-white rounded">';
                    echo '<h4>' . $visaCount . '</h4>';
                    echo '<small>بطاقات فيزا</small>';
                    echo '</div></div>';
                    
                    echo '<div class="col-md-2">';
                    echo '<div class="text-center p-3 bg-info text-white rounded">';
                    echo '<h4>' . $accountsCount . '</h4>';
                    echo '<small>حسابات إعلانية</small>';
                    echo '</div></div>';
                    
                    echo '<div class="col-md-2">';
                    echo '<div class="text-center p-3 bg-secondary text-white rounded">';
                    echo '<h4>' . $adsCount . '</h4>';
                    echo '<small>إعلانات/معاملات</small>';
                    echo '</div></div>';
                    
                    echo '<div class="col-md-3">';
                    echo '<div class="text-center p-3 bg-success text-white rounded">';
                    echo '<h4>' . number_format($totalExpected, 2) . '</h4>';
                    echo '<small>إجمالي المتوقع</small>';
                    echo '</div></div>';
                    
                    echo '<div class="col-md-3">';
                    echo '<div class="text-center p-3 bg-danger text-white rounded">';
                    echo '<h4>' . number_format($totalSpent, 2) . '</h4>';
                    echo '<small>إجمالي المصروف</small>';
                    echo '</div></div>';
                    
                } catch (PDOException $e) {
                    echo '<div class="col-12"><div class="alert alert-danger">خطأ في جلب الإحصائيات: ' . $e->getMessage() . '</div></div>';
                }
                ?>
            </div>
        </div>

        <div class="text-center mb-4">
            <a href="visa_banks_dynamic.php" class="btn btn-primary">العودة للصفحة الرئيسية</a>
        </div>
    </div>
</body>
</html>
