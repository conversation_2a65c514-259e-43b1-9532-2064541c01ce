<?php
/**
 * صفحة فحص تنبيهات الحد الدولي
 */

// تضمين ملف الاتصال بقاعدة البيانات
require_once 'config/database.php';

// إنشاء اتصال بقاعدة البيانات
$database = new Database();
$db = $database->getConnection();

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تنبيهات الحد الدولي</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body { font-family: 'Cairo', sans-serif; background-color: #f8f9fa; }
        .container { margin-top: 30px; }
        .alert-container { background: white; border-radius: 10px; padding: 20px; margin-bottom: 30px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .section-title { color: #4a56e2; font-weight: 600; margin-bottom: 20px; border-bottom: 2px solid #4a56e2; padding-bottom: 10px; }
        .alert-card { border-radius: 10px; padding: 15px; margin-bottom: 15px; border-left: 5px solid; }
        .alert-critical { background: #ffebee; border-left-color: #f44336; }
        .alert-danger { background: #fff3e0; border-left-color: #ff9800; }
        .alert-warning { background: #f3e5f5; border-left-color: #9c27b0; }
        .alert-safe { background: #e8f5e8; border-left-color: #4caf50; }
        .progress-bar-critical { background-color: #f44336; }
        .progress-bar-danger { background-color: #ff9800; }
        .progress-bar-warning { background-color: #9c27b0; }
        .progress-bar-safe { background-color: #4caf50; }
        .card-stats { background: linear-gradient(135deg, #5a67d8 0%, #667eea 100%); color: white; border-radius: 10px; padding: 20px; text-align: center; }
        .refresh-btn { position: fixed; bottom: 20px; right: 20px; z-index: 1000; }
    </style>
</head>
<body>
    <div class="container">
        <div class="row">
            <div class="col-12">
                <h1 class="text-center mb-4" style="color: #4a56e2;">
                    <i class="fas fa-exclamation-triangle"></i>
                    تنبيهات الحد الدولي للفيزا
                </h1>
            </div>
        </div>

        <!-- إحصائيات سريعة -->
        <div class="row mb-4">
            <?php
            try {
                // إحصائيات التنبيهات
                $stmt = $db->prepare("
                    SELECT 
                        alert_type,
                        COUNT(*) as count
                    FROM visa_international_alerts 
                    WHERE DATE(created_at) = CURDATE()
                    GROUP BY alert_type
                ");
                $stmt->execute();
                $alertStats = $stmt->fetchAll(PDO::FETCH_ASSOC);
                
                $criticalCount = 0;
                $dangerCount = 0;
                $warningCount = 0;
                
                foreach ($alertStats as $stat) {
                    switch ($stat['alert_type']) {
                        case 'حرج': $criticalCount = $stat['count']; break;
                        case 'خطر': $dangerCount = $stat['count']; break;
                        case 'تحذير': $warningCount = $stat['count']; break;
                    }
                }
                
                // عدد البطاقات الآمنة
                $stmt = $db->prepare("
                    SELECT COUNT(*) as count 
                    FROM visa_cards 
                    WHERE (international_spent / daily_limit * 100) < 50
                ");
                $stmt->execute();
                $safeCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
                
                echo '<div class="col-md-3">';
                echo '<div class="card-stats" style="background: linear-gradient(135deg, #f44336 0%, #e57373 100%);">';
                echo '<h4><i class="fas fa-exclamation-circle"></i> ' . $criticalCount . '</h4>';
                echo '<small>تنبيهات حرجة</small>';
                echo '</div></div>';
                
                echo '<div class="col-md-3">';
                echo '<div class="card-stats" style="background: linear-gradient(135deg, #ff9800 0%, #ffb74d 100%);">';
                echo '<h4><i class="fas fa-exclamation-triangle"></i> ' . $dangerCount . '</h4>';
                echo '<small>تنبيهات خطر</small>';
                echo '</div></div>';
                
                echo '<div class="col-md-3">';
                echo '<div class="card-stats" style="background: linear-gradient(135deg, #9c27b0 0%, #ba68c8 100%);">';
                echo '<h4><i class="fas fa-bell"></i> ' . $warningCount . '</h4>';
                echo '<small>تحذيرات</small>';
                echo '</div></div>';
                
                echo '<div class="col-md-3">';
                echo '<div class="card-stats" style="background: linear-gradient(135deg, #4caf50 0%, #81c784 100%);">';
                echo '<h4><i class="fas fa-check-circle"></i> ' . $safeCount . '</h4>';
                echo '<small>بطاقات آمنة</small>';
                echo '</div></div>';
                
            } catch (PDOException $e) {
                echo '<div class="col-12"><div class="alert alert-danger">خطأ في جلب الإحصائيات: ' . $e->getMessage() . '</div></div>';
            }
            ?>
        </div>

        <!-- حالة البطاقات الحالية -->
        <div class="alert-container">
            <h3 class="section-title">حالة البطاقات الحالية</h3>
            <?php
            try {
                $stmt = $db->prepare("
                    SELECT 
                        vc.*,
                        COALESCE(vc.international_spent, 0) as current_spent,
                        (COALESCE(vc.international_spent, 0) / vc.daily_limit * 100) as percentage_used,
                        (vc.daily_limit - COALESCE(vc.international_spent, 0)) as remaining_limit
                    FROM visa_cards vc
                    ORDER BY percentage_used DESC
                ");
                $stmt->execute();
                $cards = $stmt->fetchAll(PDO::FETCH_ASSOC);
                
                if (!empty($cards)) {
                    foreach ($cards as $card) {
                        $percentage = round($card['percentage_used'], 1);
                        $alertClass = 'alert-safe';
                        $progressClass = 'progress-bar-safe';
                        $icon = 'fa-check-circle';
                        $status = 'آمن';
                        
                        if ($percentage >= 90) {
                            $alertClass = 'alert-critical';
                            $progressClass = 'progress-bar-critical';
                            $icon = 'fa-exclamation-circle';
                            $status = 'حرج';
                        } elseif ($percentage >= 75) {
                            $alertClass = 'alert-danger';
                            $progressClass = 'progress-bar-danger';
                            $icon = 'fa-exclamation-triangle';
                            $status = 'خطر';
                        } elseif ($percentage >= 50) {
                            $alertClass = 'alert-warning';
                            $progressClass = 'progress-bar-warning';
                            $icon = 'fa-bell';
                            $status = 'تحذير';
                        }
                        
                        echo '<div class="alert-card ' . $alertClass . '">';
                        echo '<div class="row align-items-center">';
                        echo '<div class="col-md-3">';
                        echo '<h5><i class="fas ' . $icon . '"></i> ' . htmlspecialchars($card['name']) . '</h5>';
                        echo '<span class="badge bg-secondary">' . $status . '</span>';
                        echo '</div>';
                        echo '<div class="col-md-6">';
                        echo '<div class="progress mb-2" style="height: 20px;">';
                        echo '<div class="progress-bar ' . $progressClass . '" style="width: ' . $percentage . '%">';
                        echo $percentage . '%';
                        echo '</div>';
                        echo '</div>';
                        echo '<small>مصروف: ' . number_format($card['current_spent'], 2) . ' من ' . number_format($card['daily_limit'], 2) . '</small>';
                        echo '</div>';
                        echo '<div class="col-md-3 text-end">';
                        echo '<strong>متبقي: ' . number_format($card['remaining_limit'], 2) . '</strong>';
                        echo '</div>';
                        echo '</div>';
                        echo '</div>';
                    }
                } else {
                    echo '<div class="text-center text-muted py-4">';
                    echo '<i class="fas fa-credit-card fa-3x mb-3"></i>';
                    echo '<p>لا توجد بطاقات فيزا مسجلة</p>';
                    echo '</div>';
                }
            } catch (PDOException $e) {
                echo '<div class="alert alert-danger">خطأ في جلب بيانات البطاقات: ' . $e->getMessage() . '</div>';
            }
            ?>
        </div>

        <!-- التنبيهات الأخيرة -->
        <div class="alert-container">
            <h3 class="section-title">التنبيهات الأخيرة</h3>
            <?php
            try {
                $stmt = $db->prepare("
                    SELECT a.*, vc.name as card_name 
                    FROM visa_international_alerts a 
                    JOIN visa_cards vc ON a.visa_card_id = vc.id 
                    ORDER BY a.created_at DESC 
                    LIMIT 20
                ");
                $stmt->execute();
                $alerts = $stmt->fetchAll(PDO::FETCH_ASSOC);
                
                if (!empty($alerts)) {
                    foreach ($alerts as $alert) {
                        $alertClass = 'alert-safe';
                        $icon = 'fa-bell';
                        
                        switch ($alert['alert_type']) {
                            case 'حرج':
                                $alertClass = 'alert-critical';
                                $icon = 'fa-exclamation-circle';
                                break;
                            case 'خطر':
                                $alertClass = 'alert-danger';
                                $icon = 'fa-exclamation-triangle';
                                break;
                            case 'تحذير':
                                $alertClass = 'alert-warning';
                                $icon = 'fa-bell';
                                break;
                        }
                        
                        echo '<div class="alert-card ' . $alertClass . '">';
                        echo '<div class="row align-items-center">';
                        echo '<div class="col-md-1 text-center">';
                        echo '<i class="fas ' . $icon . ' fa-2x"></i>';
                        echo '</div>';
                        echo '<div class="col-md-8">';
                        echo '<h6>' . htmlspecialchars($alert['card_name']) . ' - ' . $alert['alert_type'] . '</h6>';
                        echo '<p class="mb-1">' . htmlspecialchars($alert['message']) . '</p>';
                        echo '<small class="text-muted">' . date('Y-m-d H:i:s', strtotime($alert['created_at'])) . '</small>';
                        echo '</div>';
                        echo '<div class="col-md-3 text-end">';
                        echo '<strong>' . round($alert['percentage_used'], 1) . '%</strong><br>';
                        echo '<small>متبقي: ' . number_format($alert['remaining_limit'], 2) . '</small>';
                        echo '</div>';
                        echo '</div>';
                        echo '</div>';
                    }
                } else {
                    echo '<div class="text-center text-muted py-4">';
                    echo '<i class="fas fa-bell-slash fa-3x mb-3"></i>';
                    echo '<p>لا توجد تنبيهات</p>';
                    echo '</div>';
                }
            } catch (PDOException $e) {
                echo '<div class="alert alert-danger">خطأ في جلب التنبيهات: ' . $e->getMessage() . '</div>';
            }
            ?>
        </div>

        <div class="text-center mb-4">
            <a href="visa_banks_dynamic.php" class="btn btn-primary">
                <i class="fas fa-arrow-left"></i> العودة للصفحة الرئيسية
            </a>
            <a href="update_visa_tables.php" class="btn btn-warning">
                <i class="fas fa-sync"></i> تحديث النظام
            </a>
        </div>
    </div>

    <!-- زر التحديث العائم -->
    <button class="btn btn-success refresh-btn" onclick="location.reload()">
        <i class="fas fa-sync-alt"></i> تحديث
    </button>

    <script>
        // تحديث تلقائي كل 30 ثانية
        setInterval(function() {
            location.reload();
        }, 30000);
        
        // إضافة صوت تنبيه للتنبيهات الحرجة
        document.addEventListener('DOMContentLoaded', function() {
            const criticalAlerts = document.querySelectorAll('.alert-critical');
            if (criticalAlerts.length > 0) {
                // يمكن إضافة صوت تنبيه هنا
                console.log('تنبيه: يوجد ' + criticalAlerts.length + ' تنبيه حرج!');
            }
        });
    </script>
</body>
</html>
