<?php
/**
 * صفحة لفحص البيانات في الجداول المخصصة للفيزا
 */

// تضمين ملف الاتصال بقاعدة البيانات
require_once 'config/database.php';

// إنشاء اتصال بقاعدة البيانات
$database = new Database();
$db = $database->getConnection();

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فحص بيانات الفيزا المخصصة</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap">
    <style>
        body { font-family: 'Cairo', sans-serif; background-color: #f8f9fa; }
        .container { margin-top: 30px; }
        .table-container { background: white; border-radius: 10px; padding: 20px; margin-bottom: 30px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .section-title { color: #4a56e2; font-weight: 600; margin-bottom: 20px; border-bottom: 2px solid #4a56e2; padding-bottom: 10px; }
        .table { font-size: 14px; }
        .table th { background-color: #4a56e2; color: white; text-align: center; }
        .table td { text-align: center; vertical-align: middle; }
        .badge { font-size: 12px; }
        .no-data { text-align: center; color: #999; padding: 40px; font-style: italic; }
        .amount-expected { color: #28a745; font-weight: bold; }
        .amount-spent { color: #dc3545; font-weight: bold; }
        .stats-card { background: linear-gradient(135deg, #5a67d8 0%, #667eea 100%); color: white; border-radius: 10px; padding: 20px; text-align: center; }
    </style>
</head>
<body>
    <div class="container">
        <div class="row">
            <div class="col-12">
                <h1 class="text-center mb-4" style="color: #4a56e2;">فحص بيانات الفيزا في الجداول المخصصة</h1>
            </div>
        </div>

        <!-- إحصائيات سريعة -->
        <div class="row mb-4">
            <?php
            try {
                // عدد المعاملات المتوقعة
                $stmt = $db->prepare("SELECT COUNT(*) as count, SUM(expected_amount) as total FROM visa_expected_transactions WHERE status = 'نشط'");
                $stmt->execute();
                $expectedStats = $stmt->fetch(PDO::FETCH_ASSOC);
                
                // عدد المعاملات المصروفة
                $stmt = $db->prepare("SELECT COUNT(*) as count, SUM(spent_amount) as total FROM visa_spent_transactions WHERE status = 'نشط'");
                $stmt->execute();
                $spentStats = $stmt->fetch(PDO::FETCH_ASSOC);
                
                echo '<div class="col-md-3">';
                echo '<div class="stats-card">';
                echo '<h4>' . ($expectedStats['count'] ?? 0) . '</h4>';
                echo '<small>معاملات متوقعة</small>';
                echo '</div></div>';
                
                echo '<div class="col-md-3">';
                echo '<div class="stats-card">';
                echo '<h4>' . number_format($expectedStats['total'] ?? 0, 2) . '</h4>';
                echo '<small>إجمالي المتوقع</small>';
                echo '</div></div>';
                
                echo '<div class="col-md-3">';
                echo '<div class="stats-card">';
                echo '<h4>' . ($spentStats['count'] ?? 0) . '</h4>';
                echo '<small>معاملات مصروفة</small>';
                echo '</div></div>';
                
                echo '<div class="col-md-3">';
                echo '<div class="stats-card">';
                echo '<h4>' . number_format($spentStats['total'] ?? 0, 2) . '</h4>';
                echo '<small>إجمالي المصروف</small>';
                echo '</div></div>';
                
            } catch (PDOException $e) {
                echo '<div class="col-12"><div class="alert alert-danger">خطأ في جلب الإحصائيات: ' . $e->getMessage() . '</div></div>';
            }
            ?>
        </div>

        <!-- جدول الصرف المتوقع -->
        <div class="table-container">
            <h3 class="section-title">الصرف المتوقع (visa_expected_transactions)</h3>
            <?php
            try {
                $stmt = $db->prepare("
                    SELECT vet.*, vc.name as card_name 
                    FROM visa_expected_transactions vet 
                    JOIN visa_cards vc ON vet.visa_card_id = vc.id 
                    ORDER BY vet.payment_date DESC, vet.id DESC
                ");
                $stmt->execute();
                $expectedTransactions = $stmt->fetchAll(PDO::FETCH_ASSOC);
                
                if (!empty($expectedTransactions)) {
                    echo '<div class="table-responsive">';
                    echo '<table class="table table-striped table-hover">';
                    echo '<thead><tr>';
                    echo '<th>ID</th><th>البطاقة</th><th>تاريخ الدفع</th><th>اسم الحساب</th><th>المبلغ المتوقع</th><th>الوصف</th><th>الحالة</th><th>تاريخ الإنشاء</th>';
                    echo '</tr></thead><tbody>';
                    
                    foreach ($expectedTransactions as $transaction) {
                        echo '<tr>';
                        echo '<td>' . $transaction['id'] . '</td>';
                        echo '<td>' . htmlspecialchars($transaction['card_name']) . '</td>';
                        echo '<td>' . date('Y-m-d', strtotime($transaction['payment_date'])) . '</td>';
                        echo '<td>' . htmlspecialchars($transaction['account_name']) . '</td>';
                        echo '<td class="amount-expected">' . number_format($transaction['expected_amount'], 2) . '</td>';
                        echo '<td>' . htmlspecialchars(substr($transaction['description'], 0, 30)) . '...</td>';
                        echo '<td><span class="badge bg-success">' . $transaction['status'] . '</span></td>';
                        echo '<td>' . date('Y-m-d H:i', strtotime($transaction['created_at'])) . '</td>';
                        echo '</tr>';
                    }
                    
                    echo '</tbody></table></div>';
                } else {
                    echo '<div class="no-data">لا توجد معاملات متوقعة مسجلة</div>';
                }
            } catch (PDOException $e) {
                echo '<div class="alert alert-danger">خطأ في جلب المعاملات المتوقعة: ' . $e->getMessage() . '</div>';
            }
            ?>
        </div>

        <!-- جدول المبلغ المصروف -->
        <div class="table-container">
            <h3 class="section-title">المبلغ المصروف (visa_spent_transactions)</h3>
            <?php
            try {
                $stmt = $db->prepare("
                    SELECT vst.*, vc.name as card_name 
                    FROM visa_spent_transactions vst 
                    JOIN visa_cards vc ON vst.visa_card_id = vc.id 
                    ORDER BY vst.spent_date DESC, vst.id DESC
                ");
                $stmt->execute();
                $spentTransactions = $stmt->fetchAll(PDO::FETCH_ASSOC);
                
                if (!empty($spentTransactions)) {
                    echo '<div class="table-responsive">';
                    echo '<table class="table table-striped table-hover">';
                    echo '<thead><tr>';
                    echo '<th>ID</th><th>البطاقة</th><th>تاريخ الصرف</th><th>اسم الحساب</th><th>المبلغ المصروف</th><th>نوع المعاملة</th><th>الوصف</th><th>الحالة</th><th>تاريخ الإنشاء</th>';
                    echo '</tr></thead><tbody>';
                    
                    foreach ($spentTransactions as $transaction) {
                        echo '<tr>';
                        echo '<td>' . $transaction['id'] . '</td>';
                        echo '<td>' . htmlspecialchars($transaction['card_name']) . '</td>';
                        echo '<td>' . date('Y-m-d', strtotime($transaction['spent_date'])) . '</td>';
                        echo '<td>' . htmlspecialchars($transaction['account_name']) . '</td>';
                        echo '<td class="amount-spent">' . number_format($transaction['spent_amount'], 2) . '</td>';
                        echo '<td><span class="badge bg-info">' . $transaction['transaction_type'] . '</span></td>';
                        echo '<td>' . htmlspecialchars(substr($transaction['description'], 0, 30)) . '...</td>';
                        echo '<td><span class="badge bg-primary">' . $transaction['status'] . '</span></td>';
                        echo '<td>' . date('Y-m-d H:i', strtotime($transaction['created_at'])) . '</td>';
                        echo '</tr>';
                    }
                    
                    echo '</tbody></table></div>';
                } else {
                    echo '<div class="no-data">لا توجد معاملات مصروفة مسجلة</div>';
                }
            } catch (PDOException $e) {
                echo '<div class="alert alert-danger">خطأ في جلب المعاملات المصروفة: ' . $e->getMessage() . '</div>';
            }
            ?>
        </div>

        <!-- بطاقات الفيزا مع الأرصدة المحدثة -->
        <div class="table-container">
            <h3 class="section-title">بطاقات الفيزا مع الأرصدة المحدثة</h3>
            <?php
            try {
                $stmt = $db->prepare("SELECT * FROM visa_cards ORDER BY id DESC");
                $stmt->execute();
                $visaCards = $stmt->fetchAll(PDO::FETCH_ASSOC);
                
                if (!empty($visaCards)) {
                    echo '<div class="table-responsive">';
                    echo '<table class="table table-striped table-hover">';
                    echo '<thead><tr>';
                    echo '<th>ID</th><th>اسم البطاقة</th><th>الرصيد الأساسي</th><th>الرصيد المتبقي</th><th>إجمالي المديونية</th><th>الحد اليومي</th><th>الحالة</th>';
                    echo '</tr></thead><tbody>';
                    
                    foreach ($visaCards as $card) {
                        echo '<tr>';
                        echo '<td>' . $card['id'] . '</td>';
                        echo '<td>' . htmlspecialchars($card['name']) . '</td>';
                        echo '<td>' . number_format($card['base_balance'], 2) . '</td>';
                        echo '<td class="amount-expected">' . number_format($card['remaining_balance'], 2) . '</td>';
                        echo '<td class="amount-spent">' . number_format($card['total_debt'], 2) . '</td>';
                        echo '<td>' . number_format($card['daily_limit'], 2) . '</td>';
                        echo '<td><span class="badge bg-success">' . $card['status'] . '</span></td>';
                        echo '</tr>';
                    }
                    
                    echo '</tbody></table></div>';
                } else {
                    echo '<div class="no-data">لا توجد بطاقات فيزا مسجلة</div>';
                }
            } catch (PDOException $e) {
                echo '<div class="alert alert-danger">خطأ في جلب بطاقات الفيزا: ' . $e->getMessage() . '</div>';
            }
            ?>
        </div>

        <div class="text-center mb-4">
            <a href="visa_banks_dynamic.php" class="btn btn-primary">العودة للصفحة الرئيسية</a>
            <a href="test_new_api.php" class="btn btn-success">اختبار APIs الجديدة</a>
            <a href="create_visa_tables.php" class="btn btn-warning">إعادة إنشاء الجداول</a>
        </div>
    </div>
</body>
</html>
