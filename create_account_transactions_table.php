<?php
/**
 * إنشاء جدول معاملات الحسابات الإعلانية
 */

require_once 'config/database.php';

try {
    // إنشاء اتصال بقاعدة البيانات
    $database = new Database();
    $db = $database->getConnection();

    // SQL لإنشاء جدول معاملات الحسابات الإعلانية
    $sql = "
    CREATE TABLE IF NOT EXISTS account_transactions (
        id INT AUTO_INCREMENT PRIMARY KEY,
        account_id INT NOT NULL,
        transaction_type ENUM('charge', 'withdraw', 'transfer') NOT NULL,
        amount DECIMAL(10,2) NOT NULL,
        source_type ENUM('credit_card', 'visa', 'fawry', 'cash', 'transfer') NOT NULL,
        source_id INT NULL,
        target_account_id INT NULL,
        notes TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        created_by INT NULL,
        
        INDEX idx_account_id (account_id),
        INDEX idx_transaction_type (transaction_type),
        INDEX idx_source_type (source_type),
        INDEX idx_created_at (created_at)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";

    // تنفيذ الاستعلام
    $db->exec($sql);
    
    echo "✅ تم إنشاء جدول account_transactions بنجاح!<br>";

    // إضافة تعليق على الجدول
    $commentSql = "ALTER TABLE account_transactions COMMENT = 'جدول معاملات الحسابات الإعلانية - شحن وسحب وتحويل'";
    $db->exec($commentSql);
    
    echo "✅ تم إضافة التعليق على الجدول بنجاح!<br>";

    // التحقق من إنشاء الجدول
    $checkSql = "SHOW TABLES LIKE 'account_transactions'";
    $stmt = $db->prepare($checkSql);
    $stmt->execute();
    $result = $stmt->fetch();

    if ($result) {
        echo "✅ تم التحقق من وجود الجدول بنجاح!<br>";
        
        // عرض هيكل الجدول
        $describeSql = "DESCRIBE account_transactions";
        $stmt = $db->prepare($describeSql);
        $stmt->execute();
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<h3>هيكل جدول account_transactions:</h3>";
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>اسم العمود</th><th>النوع</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
        
        foreach ($columns as $column) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($column['Field']) . "</td>";
            echo "<td>" . htmlspecialchars($column['Type']) . "</td>";
            echo "<td>" . htmlspecialchars($column['Null']) . "</td>";
            echo "<td>" . htmlspecialchars($column['Key']) . "</td>";
            echo "<td>" . htmlspecialchars($column['Default']) . "</td>";
            echo "<td>" . htmlspecialchars($column['Extra']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
    } else {
        echo "❌ فشل في إنشاء الجدول!<br>";
    }

} catch (PDOException $e) {
    echo "❌ خطأ في قاعدة البيانات: " . $e->getMessage() . "<br>";
} catch (Exception $e) {
    echo "❌ خطأ عام: " . $e->getMessage() . "<br>";
}

echo "<br><a href='ad_accounts_direct.php'>العودة إلى صفحة إدارة الحسابات</a>";
?>
