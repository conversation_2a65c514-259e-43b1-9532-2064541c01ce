<?php
// تضمين ملف الاتصال بقاعدة البيانات
require_once 'config/database.php';

// إنشاء اتصال بقاعدة البيانات
$database = new Database();
$db = $database->getConnection();

try {
    // إنشاء جدول حسابات الكاش
    $sql = "
    CREATE TABLE IF NOT EXISTS cash_accounts (
        id INT AUTO_INCREMENT PRIMARY KEY,
        account_name VARCHAR(255) NOT NULL COMMENT 'اسم الحساب',
        balance DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT 'الرصيد',
        account_type ENUM('main', 'personal') DEFAULT 'personal' COMMENT 'نوع الحساب',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    
    $db->exec($sql);
    echo "✅ تم إنشاء جدول cash_accounts بنجاح!<br>";
    
    // التحقق من وجود بيانات
    $stmt = $db->prepare("SELECT COUNT(*) FROM cash_accounts");
    $stmt->execute();
    $count = $stmt->fetchColumn();
    
    // إضافة بيانات افتراضية إذا كان الجدول فارغ
    if ($count == 0) {
        $stmt = $db->prepare("
            INSERT INTO cash_accounts (account_name, balance, account_type) VALUES
            ('كاش وسام', 0.00, 'personal'),
            ('تجاري', 0.00, 'personal'),
            ('احمد عرفة', 0.00, 'personal'),
            ('احمد شلبي', 0.00, 'personal')
        ");
        $stmt->execute();
        echo "✅ تم إضافة البيانات الافتراضية بنجاح!<br>";
    }
    
    echo "✅ جدول حسابات الكاش جاهز للاستخدام!<br>";
    
} catch (PDOException $e) {
    echo "❌ خطأ في إنشاء الجدول: " . $e->getMessage();
}
?>
