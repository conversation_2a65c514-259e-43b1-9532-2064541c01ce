<?php
/**
 * <PERSON><PERSON>t to create credit_cards table
 */

// تضمين ملف التكوين
require_once 'config/config.php';

// تضمين اتصال قاعدة البيانات
require_once 'includes/db.php';

try {
    // التحقق من وجود الجدول
    $tableExists = false;
    $tables = $db->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);

    if (in_array('credit_cards', $tables)) {
        echo "جدول credit_cards موجود بالفعل.<br>";
        $tableExists = true;
    }

    // إنشاء الجدول إذا لم يكن موجودًا
    if (!$tableExists) {
        $db->exec("CREATE TABLE credit_cards (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            balance DECIMAL(10,2) NOT NULL DEFAULT 0.00,
            daily_spend DECIMAL(10,2) NOT NULL DEFAULT 0.00,
            remaining_balance DECIMAL(10,2) NOT NULL DEFAULT 0.00,
            total_debt DECIMAL(10,2) NOT NULL DEFAULT 0.00,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )");

        echo "تم إنشاء جدول credit_cards بنجاح.<br>";

        // إضافة بيانات تجريبية
        $db->exec("INSERT INTO credit_cards (name, balance, daily_spend, remaining_balance, total_debt) VALUES
            ('إيمان إبراهيم', 4225.00, 445.00, 445.00, 445.00),
            ('محمد أحمد', 3500.00, 350.00, 350.00, 350.00),
            ('سارة محمود', 5000.00, 500.00, 500.00, 500.00)
        ");

        echo "تم إضافة بيانات تجريبية إلى جدول credit_cards بنجاح.<br>";
    }

    // إضافة عمود linked_account_id و linked_account_type إلى جدول ad_accounts إذا لم يكن موجودًا
    $columnsQuery = $db->query("SHOW COLUMNS FROM ad_accounts");
    $columns = $columnsQuery->fetchAll(PDO::FETCH_COLUMN);
    
    if (!in_array('linked_account_id', $columns)) {
        $db->exec("ALTER TABLE ad_accounts ADD COLUMN linked_account_id INT NULL DEFAULT NULL");
        echo "تم إضافة عمود linked_account_id إلى جدول ad_accounts بنجاح.<br>";
    }
    
    if (!in_array('linked_account_type', $columns)) {
        $db->exec("ALTER TABLE ad_accounts ADD COLUMN linked_account_type ENUM('credit_card', 'fawry', 'none') NOT NULL DEFAULT 'none'");
        echo "تم إضافة عمود linked_account_type إلى جدول ad_accounts بنجاح.<br>";
    }

    echo "تم تنفيذ العملية بنجاح.";
} catch (PDOException $e) {
    echo "حدث خطأ: " . $e->getMessage();
}
?>
