<?php
// تضمين ملف الاتصال بقاعدة البيانات
require_once 'config/database.php';

// إنشاء اتصال بقاعدة البيانات
$database = new Database();
$db = $database->getConnection();

try {
    // إنشاء جدول تقرير فوري
    $sql = "
    CREATE TABLE IF NOT EXISTS fawry_report (
        id INT AUTO_INCREMENT PRIMARY KEY,
        basic_balance DECIMAL(10,2) NOT NULL DEFAULT 14034.00 COMMENT 'رصيد اساسي',
        cash_out DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT 'كاش اوت',
        total_balance DECIMAL(10,2) NOT NULL DEFAULT 14034.00 COMMENT 'اجمالي رصيد فوري',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    
    $db->exec($sql);
    echo "✅ تم إنشاء جدول fawry_report بنجاح!<br>";
    
    // التحقق من وجود بيانات
    $stmt = $db->prepare("SELECT COUNT(*) FROM fawry_report");
    $stmt->execute();
    $count = $stmt->fetchColumn();
    
    // إضافة بيانات افتراضية إذا كان الجدول فارغ
    if ($count == 0) {
        $stmt = $db->prepare("
            INSERT INTO fawry_report (basic_balance, cash_out, total_balance) 
            VALUES (14034.00, 0.00, 14034.00)
        ");
        $stmt->execute();
        echo "✅ تم إضافة البيانات الافتراضية بنجاح!<br>";
    }
    
    echo "✅ جدول تقرير فوري جاهز للاستخدام!<br>";
    
} catch (PDOException $e) {
    echo "❌ خطأ في إنشاء الجدول: " . $e->getMessage();
}
?>
