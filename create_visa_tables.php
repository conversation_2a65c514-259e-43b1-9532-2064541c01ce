<?php
/**
 * إنشاء جداول مخصصة للفيزا
 */

// تضمين ملف الاتصال بقاعدة البيانات
require_once 'config/database.php';

// إنشاء اتصال بقاعدة البيانات
$database = new Database();
$db = $database->getConnection();

try {
    echo "<h2>إنشاء جداول الفيزا المخصصة...</h2>";

    // 1. جدول الصرف المتوقع للفيزا
    $createExpectedTable = "
        CREATE TABLE IF NOT EXISTS visa_expected_transactions (
            id INT AUTO_INCREMENT PRIMARY KEY,
            visa_card_id INT NOT NULL,
            payment_date DATE NOT NULL,
            account_name VARCHAR(255) NOT NULL,
            expected_amount DECIMAL(10,2) NOT NULL DEFAULT 0.00,
            description TEXT,
            status ENUM('نشط', 'ملغي', 'مكتمل') DEFAULT 'نشط',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_visa_card_id (visa_card_id),
            INDEX idx_payment_date (payment_date),
            INDEX idx_status (status),
            FOREIGN KEY (visa_card_id) REFERENCES visa_cards(id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";

    if ($db->exec($createExpectedTable)) {
        echo "✅ تم إنشاء جدول الصرف المتوقع (visa_expected_transactions) بنجاح<br>";
    } else {
        echo "❌ فشل في إنشاء جدول الصرف المتوقع<br>";
    }

    // 2. جدول المبلغ المصروف من الفيزا
    $createSpentTable = "
        CREATE TABLE IF NOT EXISTS visa_spent_transactions (
            id INT AUTO_INCREMENT PRIMARY KEY,
            visa_card_id INT NOT NULL,
            spent_date DATE NOT NULL,
            account_name VARCHAR(255) NOT NULL,
            spent_amount DECIMAL(10,2) NOT NULL DEFAULT 0.00,
            description TEXT,
            transaction_type ENUM('صرف عادي', 'صرف دولي', 'رسوم', 'أخرى') DEFAULT 'صرف عادي',
            status ENUM('نشط', 'ملغي', 'مراجعة') DEFAULT 'نشط',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_visa_card_id (visa_card_id),
            INDEX idx_spent_date (spent_date),
            INDEX idx_status (status),
            INDEX idx_transaction_type (transaction_type),
            FOREIGN KEY (visa_card_id) REFERENCES visa_cards(id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";

    if ($db->exec($createSpentTable)) {
        echo "✅ تم إنشاء جدول المبلغ المصروف (visa_spent_transactions) بنجاح<br>";
    } else {
        echo "❌ فشل في إنشاء جدول المبلغ المصروف<br>";
    }

    // 3. إضافة بيانات تجريبية للاختبار
    echo "<br><h3>إضافة بيانات تجريبية...</h3>";

    // التحقق من وجود بطاقة فيزا
    $stmt = $db->prepare("SELECT id FROM visa_cards LIMIT 1");
    $stmt->execute();
    $visaCard = $stmt->fetch(PDO::FETCH_ASSOC);

    if ($visaCard) {
        $cardId = $visaCard['id'];
        
        // إضافة معاملات متوقعة تجريبية
        $expectedTransactions = [
            ['2024-01-15', 'حساب فيسبوك الرئيسي', 1500.00, 'إعلان منتج جديد'],
            ['2024-01-16', 'حساب جوجل الإعلاني', 2000.00, 'حملة تسويقية'],
            ['2024-01-17', 'حساب انستجرام', 800.00, 'إعلان قصة'],
        ];

        foreach ($expectedTransactions as $transaction) {
            $stmt = $db->prepare("
                INSERT INTO visa_expected_transactions 
                (visa_card_id, payment_date, account_name, expected_amount, description) 
                VALUES (?, ?, ?, ?, ?)
            ");
            $stmt->execute([$cardId, $transaction[0], $transaction[1], $transaction[2], $transaction[3]]);
        }
        echo "✅ تم إضافة " . count($expectedTransactions) . " معاملة متوقعة تجريبية<br>";

        // إضافة معاملات مصروفة تجريبية
        $spentTransactions = [
            ['2024-01-14', 'حساب فيسبوك الرئيسي', 750.00, 'صرف جزئي من الحملة', 'صرف عادي'],
            ['2024-01-15', 'حساب جوجل الإعلاني', 1200.00, 'صرف كامل للحملة', 'صرف عادي'],
            ['2024-01-16', 'حساب انستجرام', 300.00, 'صرف جزئي', 'صرف دولي'],
        ];

        foreach ($spentTransactions as $transaction) {
            $stmt = $db->prepare("
                INSERT INTO visa_spent_transactions 
                (visa_card_id, spent_date, account_name, spent_amount, description, transaction_type) 
                VALUES (?, ?, ?, ?, ?, ?)
            ");
            $stmt->execute([$cardId, $transaction[0], $transaction[1], $transaction[2], $transaction[3], $transaction[4]]);
        }
        echo "✅ تم إضافة " . count($spentTransactions) . " معاملة مصروفة تجريبية<br>";

    } else {
        echo "⚠️ لا توجد بطاقات فيزا لإضافة البيانات التجريبية<br>";
    }

    // 4. عرض هيكل الجداول
    echo "<br><h3>هيكل الجداول المنشأة:</h3>";
    
    echo "<h4>جدول الصرف المتوقع (visa_expected_transactions):</h4>";
    $stmt = $db->prepare("DESCRIBE visa_expected_transactions");
    $stmt->execute();
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>العمود</th><th>النوع</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>" . $column['Field'] . "</td>";
        echo "<td>" . $column['Type'] . "</td>";
        echo "<td>" . $column['Null'] . "</td>";
        echo "<td>" . $column['Key'] . "</td>";
        echo "<td>" . $column['Default'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";

    echo "<h4>جدول المبلغ المصروف (visa_spent_transactions):</h4>";
    $stmt = $db->prepare("DESCRIBE visa_spent_transactions");
    $stmt->execute();
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>العمود</th><th>النوع</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>" . $column['Field'] . "</td>";
        echo "<td>" . $column['Type'] . "</td>";
        echo "<td>" . $column['Null'] . "</td>";
        echo "<td>" . $column['Key'] . "</td>";
        echo "<td>" . $column['Default'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";

    // 5. عرض البيانات المضافة
    echo "<br><h3>البيانات المضافة:</h3>";
    
    echo "<h4>الصرف المتوقع:</h4>";
    $stmt = $db->prepare("
        SELECT vet.*, vc.name as card_name 
        FROM visa_expected_transactions vet 
        JOIN visa_cards vc ON vet.visa_card_id = vc.id 
        ORDER BY vet.payment_date DESC
    ");
    $stmt->execute();
    $expectedData = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($expectedData)) {
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>ID</th><th>البطاقة</th><th>التاريخ</th><th>الحساب</th><th>المبلغ</th><th>الوصف</th></tr>";
        foreach ($expectedData as $row) {
            echo "<tr>";
            echo "<td>" . $row['id'] . "</td>";
            echo "<td>" . $row['card_name'] . "</td>";
            echo "<td>" . $row['payment_date'] . "</td>";
            echo "<td>" . $row['account_name'] . "</td>";
            echo "<td>" . number_format($row['expected_amount'], 2) . "</td>";
            echo "<td>" . $row['description'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }

    echo "<h4>المبلغ المصروف:</h4>";
    $stmt = $db->prepare("
        SELECT vst.*, vc.name as card_name 
        FROM visa_spent_transactions vst 
        JOIN visa_cards vc ON vst.visa_card_id = vc.id 
        ORDER BY vst.spent_date DESC
    ");
    $stmt->execute();
    $spentData = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($spentData)) {
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>ID</th><th>البطاقة</th><th>التاريخ</th><th>الحساب</th><th>المبلغ</th><th>النوع</th><th>الوصف</th></tr>";
        foreach ($spentData as $row) {
            echo "<tr>";
            echo "<td>" . $row['id'] . "</td>";
            echo "<td>" . $row['card_name'] . "</td>";
            echo "<td>" . $row['spent_date'] . "</td>";
            echo "<td>" . $row['account_name'] . "</td>";
            echo "<td>" . number_format($row['spent_amount'], 2) . "</td>";
            echo "<td>" . $row['transaction_type'] . "</td>";
            echo "<td>" . $row['description'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }

    echo "<br><h2 style='color: green;'>✅ تم إنشاء جميع الجداول والبيانات بنجاح!</h2>";
    echo "<p><a href='visa_banks_dynamic.php'>العودة للصفحة الرئيسية</a></p>";
    echo "<p><a href='check_visa_data.php'>فحص البيانات الجديدة</a></p>";

} catch (PDOException $e) {
    echo "<h2 style='color: red;'>❌ حدث خطأ:</h2>";
    echo "<p>" . $e->getMessage() . "</p>";
}
?>
