<?php
// تضمين ملف الاتصال بقاعدة البيانات
require_once 'config/database.php';

// إنشاء اتصال بقاعدة البيانات
$database = new Database();
$db = $database->getConnection();

try {
    // إنشاء جدول فودافون كاش
    $sql = "
    CREATE TABLE IF NOT EXISTS vodafone_cash (
        id INT AUTO_INCREMENT PRIMARY KEY,
        phone_number VARCHAR(20) NOT NULL COMMENT 'رقم الهاتف',
        balance DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT 'الرصيد',
        account_type ENUM('main', 'personal') DEFAULT 'personal' COMMENT 'نوع الحساب',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    
    $db->exec($sql);
    echo "✅ تم إنشاء جدول vodafone_cash بنجاح!<br>";
    
    // التحقق من وجود بيانات
    $stmt = $db->prepare("SELECT COUNT(*) FROM vodafone_cash");
    $stmt->execute();
    $count = $stmt->fetchColumn();
    
    // إضافة بيانات افتراضية إذا كان الجدول فارغ
    if ($count == 0) {
        $stmt = $db->prepare("
            INSERT INTO vodafone_cash (phone_number, balance, account_type) VALUES
            ('***********', 16640.00, 'personal'),
            ('***********', 16640.00, 'personal'),
            ('***********', 16640.00, 'personal'),
            ('***********', 16640.00, 'personal'),
            ('***********', 16640.00, 'personal'),
            ('***********', 16640.00, 'personal'),
            ('***********', 16640.00, 'personal')
        ");
        $stmt->execute();
        echo "✅ تم إضافة البيانات الافتراضية بنجاح!<br>";
    }
    
    echo "✅ جدول فودافون كاش جاهز للاستخدام!<br>";
    
} catch (PDOException $e) {
    echo "❌ خطأ في إنشاء الجدول: " . $e->getMessage();
}
?>
