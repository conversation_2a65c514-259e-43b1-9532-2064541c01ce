<?php
// تضمين ملف الاتصال بقاعدة البيانات
require_once 'config/database.php';

// إنشاء اتصال بقاعدة البيانات
$database = new Database();
$db = $database->getConnection();

// التحقق من وجود اتصال بقاعدة البيانات
if (!isset($db)) {
    die("خطأ في الاتصال بقاعدة البيانات");
}

// إضافة الحقول الجديدة لجدول credit_cards إذا لم تكن موجودة
try {
    $stmt = $db->prepare("SHOW COLUMNS FROM credit_cards");
    $stmt->execute();
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);

    // إضافة حقل المستحق اليدوي
    if (!in_array('manual_due_amount', $columns)) {
        $db->exec("ALTER TABLE credit_cards ADD COLUMN manual_due_amount DECIMAL(10,2) DEFAULT NULL COMMENT 'المستحق اليدوي'");
    }

    // إضافة حقل المديونية اليدوية
    if (!in_array('manual_debt_amount', $columns)) {
        $db->exec("ALTER TABLE credit_cards ADD COLUMN manual_debt_amount DECIMAL(10,2) DEFAULT NULL COMMENT 'المديونية اليدوية'");
    }
} catch (PDOException $e) {
    // تجاهل الأخطاء في حالة عدم وجود الجدول
}

// جلب بيانات بطاقات الائتمان فقط
try {
    $creditCards = [];

    // جلب بطاقات الائتمان
    try {
        $stmt = $db->prepare("
            SELECT *,
                   'credit_card' as card_type,
                   DATEDIFF(payment_due_date, CURDATE()) as days_until_payment
            FROM credit_cards
            ORDER BY name
        ");
        $stmt->execute();
        $creditCards = $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        echo "جدول credit_cards غير موجود: " . $e->getMessage();
    }

    // ترتيب النتائج حسب الاسم
    if (!empty($creditCards)) {
        usort($creditCards, function($a, $b) {
            return strcmp($a['name'], $b['name']);
        });
    }

} catch (PDOException $e) {
    echo "حدث خطأ عند جلب البطاقات: " . $e->getMessage();
    $creditCards = [];
}

// جلب الحسابات الإعلانية المرتبطة بكل بطاقة ائتمان
$linkedAccounts = [];
$cardTransactions = [];

try {
    // جلب الحسابات الإعلانية المرتبطة ببطاقات الائتمان فقط
    $stmt = $db->prepare("
        SELECT a.*, cc.name as card_name, 'credit_card' as card_type
        FROM ad_accounts a
        JOIN credit_cards cc ON a.linked_account_id = cc.id
        WHERE a.linked_account_type = 'credit_card'
    ");
    $stmt->execute();
    $accounts = $stmt->fetchAll(PDO::FETCH_ASSOC);

    foreach ($accounts as $account) {
        if (!isset($linkedAccounts[$account['linked_account_id']])) {
            $linkedAccounts[$account['linked_account_id']] = [];
        }
        $linkedAccounts[$account['linked_account_id']][] = $account;
    }

    // جلب الحسابات والإعلانات لكل بطاقة
    foreach ($creditCards as $card) {
        $cardId = $card['id'];
        $cardType = 'credit_card'; // نستخدم فقط credit_card

        // جلب الحسابات الإعلانية المرتبطة بهذه البطاقة
        $stmt = $db->prepare("
            SELECT * FROM ad_accounts
            WHERE linked_account_type = :card_type
            AND linked_account_id = :card_id
            ORDER BY name ASC
        ");
        $stmt->bindParam(':card_id', $cardId);
        $stmt->bindParam(':card_type', $cardType);
        $stmt->execute();
        $cardAccounts = $stmt->fetchAll(PDO::FETCH_ASSOC);


        if (!empty($cardAccounts)) {
            $linkedAccounts[$cardId] = $cardAccounts;

            $accountIds = array_column($cardAccounts, 'id');


            if (!empty($accountIds)) {
                $accountIdsStr = implode(',', $accountIds);

                $stmt = $db->prepare("
                    SELECT a.*, aa.name as account_name,
                           COALESCE(ac.name, c.name, aa.name, 'غير محدد') as client_name,
                           a.date as ad_date,
                           a.type as ad_type,
                           a.post as ad_post,
                           a.cost as ad_cost,
                           a.egyptian_cost as ad_spent,
                           a.days as ad_days,
                           a.status as ad_status
                    FROM ads a
                    JOIN ad_accounts aa ON a.ad_account_id = aa.id
                    LEFT JOIN ad_clients ac ON a.client_id = ac.id
                    LEFT JOIN clients c ON a.client_id = c.id
                    WHERE a.ad_account_id IN ($accountIdsStr)
                    ORDER BY a.date DESC
                ");

                if (!$stmt->execute()) {

                    continue;
                }

                $cardAds = $stmt->fetchAll(PDO::FETCH_ASSOC);

                if (!empty($cardAds)) {
                    $cardTransactions[$cardId] = $cardAds;

                    // حساب القيم الإجمالية لكل الإعلانات (نشطة وغير نشطة)
                    $totalDailySpend = 0;
                    $totalRemainingSpend = 0;
                    $totalSpent = 0;

                    foreach ($cardAds as $ad) {
                        $cost = floatval($ad['cost']);
                        $spent = floatval($ad['egyptian_cost']);
                        $days = intval($ad['days']);

                        // الصرف اليومي = التكلفة ÷ الأيام (مجموع كل الأرقام في عمود "يومي")
                        $dailySpend = $days > 0 ? $cost / $days : 0;
                        $totalDailySpend += $dailySpend;

                        // المتبقي صرفه = التكلفة - المصروف (مجموع كل الأرقام في عمود "متبقي الصرف")
                        $remainingSpend = $cost - $spent;
                        $totalRemainingSpend += $remainingSpend;

                        // إجمالي المصروف (لحساب المديونية)
                        $totalSpent += $spent;
                    }

                    // حفظ القيم المحسوبة في مصفوفة البطاقة
                    foreach ($creditCards as &$cardRef) {
                        if ($cardRef['id'] == $cardId) {
                            $cardRef['calculated_daily_spend'] = $totalDailySpend;
                            $cardRef['calculated_remaining_spend'] = $totalRemainingSpend;
                            $cardRef['calculated_total_spent'] = $totalSpent;

                            // حساب المديونية = إجمالي الصرف - حد الصرف
                            $spendingLimit = floatval($cardRef['balance']); // حد الصرف
                            $debt = $totalSpent - $spendingLimit;
                            $cardRef['calculated_debt'] = $debt > 0 ? $debt : 0;

                            // حساب المستحق = حد الصرف - المديونية
                            $cardRef['calculated_due'] = $spendingLimit - $cardRef['calculated_debt'];

                            // حساب طلب السداد
                            $daysUntilPayment = intval($cardRef['days_until_payment']);
                            $cardRef['payment_request'] = ($daysUntilPayment <= 3 && $daysUntilPayment >= 0) ? $cardRef['calculated_debt'] : 0;

                            break;
                        }
                    }
                }
            }
        }
    }

} catch (PDOException $e) {
    // في حالة حدوث خطأ، نستخدم مصفوفة فارغة
    $linkedAccounts = [];
    $cardTransactions = [];
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اكونتات كريديت كارد وفيزا</title>
<style>
/* تأثيرات التعديل المباشر للمبالغ */
.editable-amount {
    transition: all 0.2s ease;
    position: relative;
}

.editable-amount:hover {
    background-color: #f8f9fa !important;
    border: 1px solid #dee2e6 !important;
    border-radius: 3px;
}

.editable-amount:hover::after {
    content: "انقر للتعديل";
    position: absolute;
    bottom: -20px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 10px;
    white-space: nowrap;
    z-index: 1000;
    pointer-events: none;
}

/* تأثيرات الرسائل */
.alert {
    border-radius: 5px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    animation: slideIn 0.3s ease;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* تحسين مظهر حقول الإدخال */
.editable-amount input {
    font-family: inherit;
    background: white;
    box-shadow: 0 0 5px rgba(0, 123, 255, 0.3);
}

.editable-amount input:focus {
    outline: none;
    box-shadow: 0 0 8px rgba(0, 123, 255, 0.5);
}
</style>
    <!-- Bootstrap RTL CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Google Fonts - Cairo -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap">
</head>
<body>
    <div class="container-fluid p-0">
        <!-- الهيدر الجديد مطابق للتصميم -->
        <header class="exact-header">
            <!-- الشريط العلوي الأسود -->
            <div class="top-black-bar"></div>

            <!-- محتوى الهيدر -->
            <div class="header-main">
                <!-- اللوجو -->
                <div class="logo-container">
                    <img src="assets/images/logo.png" alt="بسام ميديا" class="header-logo">
                </div>

                <!-- العنوان الأزرق في الوسط -->
                <div class="center-title">
                    <h1 class="blue-title">اكونتات كريديت كارد</h1>
                </div>

                <!-- منطقة فارغة للتوازن -->
                <div class="right-spacer"></div>
            </div>

            <!-- الشريط السفلي الرمادي مع البحث -->
            <div class="bottom-search-bar">
                <!-- أيقونة المجلد والنص -->
                <div class="folder-section">
                    <i class="fas fa-folder folder-icon"></i>
                    <a href="accounts_table.php" class="folder-link">
                        <span class="folder-text">اكونتات كريديت كارد</span>
                    </a>
                </div>

                <!-- منطقة فارغة -->
                <div class="middle-spacer"></div>

                <!-- شريط البحث -->
                <div class="search-box">
                    <input type="text" class="search-field" placeholder="البحث">
                    <i class="fas fa-search search-btn"></i>
                </div>
            </div>
        </header>

        <!-- Bootstrap JS Bundle with Popper -->
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <div class="row px-4">
        <?php foreach ($creditCards as $cardIndex => $card): ?>
        <?php
        // تسجيل للتشخيص
        error_log("Card Debug: " . print_r($card, true));
        // استخدام معرف البطاقة الأصلي أو فهرس البطاقة
        $originalCardId = isset($card['id']) ? $card['id'] : null;
        $cardId = $originalCardId && $originalCardId > 0 ? $originalCardId : ($cardIndex + 1);

        // تسجيل للتشخيص
        error_log("Card ID Debug: originalCardId=$originalCardId, cardId=$cardId, cardIndex=$cardIndex");
        ?>
        <div class="col-md-6 mb-4">
            <div class="credit-card-box credit-card" data-card-type="<?php echo isset($card['card_type']) ? $card['card_type'] : 'credit_card'; ?>">
                <div class="card-header">
                    <div class="dropdown">
                        <button class="btn btn-link dropdown-toggle" type="button" id="cardOptionsDropdown-<?php echo $cardId; ?>" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-ellipsis-v"></i>
                        </button>
                        <ul class="dropdown-menu" aria-labelledby="cardOptionsDropdown-<?php echo $cardId; ?>">
                            <li><a class="dropdown-item" href="#">تعديل</a></li>
                            <li><a class="dropdown-item" href="#">حذف</a></li>
                        </ul>
                    </div>
                    <div class="card-title card-title-clickable" onclick="showAllCardClients('<?php echo htmlspecialchars($card['name']); ?>', this)">
                        <?php echo htmlspecialchars($card['name']); ?>
                        <?php if (isset($card['card_type']) && $card['card_type'] === 'visa'): ?>
                            <span class="badge bg-warning text-dark ms-2">فيزا</span>
                        <?php else: ?>
                            <span class="badge bg-primary ms-2">كريديت كارد</span>
                        <?php endif; ?>
                    </div>
                    <div class="dropdown">
                        <button class="btn btn-link dropdown-toggle" type="button" id="linkedAccountsDropdown-<?php echo $cardId; ?>" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-chevron-down"></i>
                        </button>
                        <div class="dropdown-menu linked-accounts-dropdown" aria-labelledby="linkedAccountsDropdown-<?php echo $cardId; ?>" id="linkedAccountsMenu-<?php echo $cardId; ?>">
                            <h6 class="dropdown-header">الحسابات الإعلانية والإعلانات المرتبطة</h6>
                            <div class="text-center">
                                <div class="spinner-border spinner-border-sm" role="status">
                                    <span class="visually-hidden">جاري التحميل...</span>
                                </div>
                                <div class="mt-2">جاري تحميل البيانات...</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="credit-card-table-container">
                        <table class="table credit-card-table">
                            <thead>
                                <tr>
                                    <th>العميل</th>
                                    <th>البوست</th>
                                    <th>التكلفة</th>
                                    <th>المصروف</th>
                                    <th>الأيام</th>
                                    <th>يومي</th>
                                    <th>متبقي الصرف</th>
                                </tr>
                            </thead>
                        </table>
                        <div class="credit-card-table-body">
                            <table class="table credit-card-table">
                                <tbody>
                                    <?php
                                    // استخدام المعرف الأصلي للبحث في المعاملات
                                    $transactionKey = $originalCardId ? $originalCardId : $cardId;
                                    ?>
                                    <?php
                                    // تسجيل للتشخيص
                                    error_log("Transaction Key: $transactionKey");
                                    error_log("Card Transactions: " . print_r($cardTransactions, true));
                                    ?>
                                    <?php
                                    // إضافة بيانات تجريبية دائماً للاختبار
                                    $testTransactions = [];

                                    // دمج البيانات الحقيقية مع التجريبية
                                    $allTransactions = $testTransactions;
                                    if (isset($cardTransactions[$transactionKey]) && !empty($cardTransactions[$transactionKey])) {
                                        $allTransactions = array_merge($allTransactions, $cardTransactions[$transactionKey]);
                                    }
                                    ?>

                                    <?php foreach ($allTransactions as $transaction): ?>
                                        <?php
                                            // حساب القيم
                                            $cost = floatval($transaction['ad_cost']);
                                            $spent = floatval($transaction['ad_spent']);
                                            $days = intval($transaction['ad_days']);

                                            // يومي = التكلفة ÷ الأيام
                                            $dailySpend = $days > 0 ? $cost / $days : 0;

                                            // متبقي الصرف = التكلفة - المصروف
                                            $remainingSpend = $cost - $spent;
                                        ?>
                                        <tr class="client-row"
                                            data-ad-id="<?php echo isset($transaction['id']) ? $transaction['id'] : 'new'; ?>"
                                            data-client-name="<?php echo htmlspecialchars($transaction['client_name']); ?>"
                                            data-ad-date="<?php echo $transaction['ad_date']; ?>"
                                            data-ad-post="<?php echo htmlspecialchars($transaction['ad_post']); ?>"
                                            data-ad-type="<?php echo htmlspecialchars($transaction['ad_type']); ?>"
                                            data-ad-cost="<?php echo $cost; ?>"
                                            data-ad-spent="<?php echo $spent; ?>"
                                            data-ad-days="<?php echo $days; ?>"
                                            data-daily-spend="<?php echo $dailySpend; ?>"
                                            data-remaining-spend="<?php echo $remainingSpend; ?>"
                                            data-account-name="<?php echo htmlspecialchars($transaction['account_name']); ?>">
                                            <td>
                                                <a href="javascript:void(0)" class="text-decoration-none client-link">
                                                    <?php echo htmlspecialchars($transaction['client_name']); ?>
                                                </a>
                                            </td>
                                            <td class="editable-cell" data-field="ad_post" data-type="text"><?php echo htmlspecialchars($transaction['ad_post']); ?></td>
                                            <td class="editable-cell" data-field="ad_cost" data-type="number"><?php echo number_format($cost, 2); ?></td>
                                            <td class="editable-cell" data-field="ad_spent" data-type="number"><?php echo number_format($spent, 2); ?></td>
                                            <td class="editable-cell" data-field="ad_days" data-type="number"><?php echo $days; ?></td>
                                            <td class="calculated-cell"><?php echo number_format($dailySpend, 2); ?></td>
                                            <td class="calculated-cell"><?php echo number_format($remainingSpend, 2); ?></td>
                                        </tr>
                                        <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <div class="card-footer">
                    <?php
                    // القيم المحسوبة أو القيم الافتراضية
                    $dailySpend = isset($card['calculated_daily_spend']) ? $card['calculated_daily_spend'] : 0;
                    $remainingSpend = isset($card['calculated_remaining_spend']) ? $card['calculated_remaining_spend'] : 0;
                    $debt = isset($card['calculated_debt']) ? $card['calculated_debt'] : 0;
                    $due = isset($card['calculated_due']) ? $card['calculated_due'] : floatval($card['balance']);
                    $paymentRequest = isset($card['payment_request']) ? $card['payment_request'] : 0;
                    ?>
                    <div class="footer-row">
                        <div class="footer-item">
                            <div class="footer-label">الصرف اليومي</div>
                            <div class="footer-value"><?php echo number_format($dailySpend, 2); ?></div>
                        </div>
                        <div class="footer-item">
                            <div class="footer-label">المتبقي صرفه</div>
                            <div class="footer-value"><?php echo number_format($remainingSpend, 2); ?></div>
                        </div>
                        <div class="footer-item">
                            <div class="footer-label">طلب تسديد</div>
                            <div class="footer-value" style="color: <?php echo $paymentRequest > 0 ? 'red' : 'inherit'; ?>;">
                                <?php echo number_format($paymentRequest, 2); ?>
                                <?php if ($paymentRequest > 0): ?>
                                    <small>(مطلوب خلال <?php echo $card['days_until_payment']; ?> أيام)</small>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    <div class="footer-row">
                        <div class="footer-item">
                            <div class="footer-label">المستحق</div>
                            <div class="footer-value editable-amount"
                                 data-card-id="<?php echo $cardId; ?>"
                                 data-field="manual_due_amount"
                                 data-current-value="<?php echo isset($card['manual_due_amount']) && $card['manual_due_amount'] !== null ? $card['manual_due_amount'] : $due; ?>"
                                 style="cursor: pointer; border: 1px solid transparent; padding: 2px 5px; border-radius: 3px;"
                                 title="انقر للتعديل">
                                <?php
                                $displayDue = isset($card['manual_due_amount']) && $card['manual_due_amount'] !== null ? $card['manual_due_amount'] : $due;
                                echo number_format($displayDue, 2);
                                ?>
                            </div>
                        </div>
                        <div class="footer-item">
                            <div class="footer-label">المديونية</div>
                            <div class="footer-value editable-amount"
                                 data-card-id="<?php echo $cardId; ?>"
                                 data-field="manual_debt_amount"
                                 data-current-value="<?php echo isset($card['manual_debt_amount']) && $card['manual_debt_amount'] !== null ? $card['manual_debt_amount'] : $debt; ?>"
                                 style="cursor: pointer; border: 1px solid transparent; padding: 2px 5px; border-radius: 3px; color: <?php
                                 $displayDebt = isset($card['manual_debt_amount']) && $card['manual_debt_amount'] !== null ? $card['manual_debt_amount'] : $debt;
                                 echo $displayDebt > 0 ? 'red' : 'green'; ?>;"
                                 title="انقر للتعديل">
                                <?php
                                echo $displayDebt > 0 ? '-' . number_format($displayDebt, 2) : number_format($displayDebt, 2);
                                ?>
                            </div>
                        </div>
                        <div class="footer-item">
                            <div class="add-ad-container">
                                <div class="add-circle">
                                    <i class="fas fa-plus"></i>
                                </div>
                                <span class="add-text" onclick="showAddTransactionModal(<?php echo $cardId; ?>)">اضافة اعلان</span>
                            </div>
                        </div>
                    </div>


                </div>
            </div>
        </div>
        <?php endforeach; ?>
    </div>
</div>

<!-- نافذة إضافة إعلان جديد -->
<div class="modal fade" id="addTransactionModal" tabindex="-1" aria-labelledby="addTransactionModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header add-ad-header">
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                <h5 class="modal-title" id="addTransactionModalLabel">إضافة اعلان</h5>
            </div>
            <div class="modal-body p-0">
                <div class="add-ad-table-container">
                    <table class="table add-ad-table">
                        <thead>
                            <tr>
                                <th>تاريخ</th>
                                <th>نوع</th>
                                <th>يومي / اجمالي</th>
                                <th>بوست</th>
                                <th>عدد ايام</th>
                                <th>التكلفة</th>
                                <th>الصرف</th>
                                <th>الصفحة</th>
                                <th>الحساب الإعلاني</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>
                                    <input type="date" class="form-control form-control-sm" id="adDate" name="adDate">
                                </td>
                                <td>
                                    <select class="form-select form-select-sm" id="adType" name="adType">
                                        <option value="تفاعل">تفاعل</option>
                                        <option value="رسايل">رسايل</option>
                                    </select>
                                </td>
                                <td>
                                    <select class="form-select form-select-sm" id="adPaymentType" name="adPaymentType">
                                        <option value="يومي">يومي</option>
                                        <option value="اجمالي">اجمالي</option>
                                    </select>
                                </td>
                                <td>

                                    <input type="text" class="form-control form-control-sm" id="adPostType" name="adPostType">
                                </td>
                                <td>
                                    <input type="number" class="form-control form-control-sm" id="adDays" name="adDays" min="1" value="1">
                                </td>
                                <td>
                                    <input type="number" class="form-control form-control-sm" id="adCost" name="adCost" step="0.01">
                                </td>
                                <td>
                                    <input type="number" class="form-control form-control-sm" id="adSpend" name="adSpend" step="0.01">
                                </td>
                                <td>
                                    <select class="form-select form-select-sm" id="adPage" name="adPage">
                                        <option value="منة فوزي">منة فوزي</option>
                                        <option value="سارة أحمد">سارة أحمد</option>
                                        <option value="محمد علي">محمد علي</option>
                                    </select>
                                </td>
                                <td id="adAccountContainer" style="display: none;">
                                    <select class="form-select form-select-sm" id="adAccount" name="adAccount">
                                        <option value="">اختر الحساب الإعلاني</option>
                                    </select>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div class="add-ad-footer">
                    <button type="button" class="add-ad-save-btn" id="saveTransaction">إضافة اعلان</button>
                </div>
                <input type="hidden" id="cardId" name="cardId">
            </div>
        </div>
    </div>
</div>

<!-- نافذة تفاصيل العميل -->
<div class="modal fade" id="clientDetailsModal" tabindex="-1" aria-labelledby="clientDetailsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content client-details-modal">
            <div class="modal-header client-modal-header">
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                <h5 class="modal-title client-modal-title" id="clientDetailsModalLabel">إيمان إبراهيم</h5>
                <div class="dropdown">
                    <button class="btn btn-link dropdown-toggle" type="button" id="clientDetailsDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-chevron-down"></i>
                    </button>
                    <ul class="dropdown-menu" aria-labelledby="clientDetailsDropdown">
                        <li><a class="dropdown-item" href="#">تصدير التقرير</a></li>
                        <li><a class="dropdown-item" href="#">طباعة</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="#">إغلاق</a></li>
                    </ul>
                </div>
            </div>
            <div class="modal-body p-0">
                <div class="client-details-table-container">
                    <table class="table client-details-table">
                        <thead>
                            <tr>
                                <th>بدء</th>
                                <th>الصفحة</th>
                                <th>البوست</th>
                                <th>نوع</th>
                                <th>المبلغ</th>
                                <th>الصرف</th>
                                <th>ايام</th>
                                <th>ينتهي</th>
                                <th>صرف يومي</th>
                                <th>باقي يصرف</th>
                            </tr>
                        </thead>
                    </table>
                    <div class="table-scrollable-body">
                        <table class="table client-details-table">
                            <tbody>
                                <!-- البيانات ستُحمل هنا ديناميكياً -->
                                <tr>
                                    <td colspan="10" class="text-center text-muted">
                                        <i class="fas fa-info-circle me-1"></i>
                                        انقر على اسم العميل لعرض تفاصيله
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="client-details-footer">
                    <div class="footer-row">
                        <div class="footer-item">
                            <div class="footer-label">إجمالي الصرف يومي</div>
                            <div class="footer-value">0.00 EGP</div>
                        </div>
                        <div class="footer-item">
                            <div class="footer-label">إجمالي المتبقي صرفه</div>
                            <div class="footer-value">0.00 EGP</div>
                        </div>
                        <div class="footer-item">
                            <div class="footer-label">طلب التسديد</div>
                            <div class="footer-value">0.00 EGP</div>
                        </div>
                    </div>
                    <div class="footer-row">
                        <div class="footer-item">
                            <div class="footer-label">إجمالي المستحق</div>
                            <div class="footer-value">0.00 EGP</div>
                        </div>
                        <div class="footer-item">
                            <div class="footer-label">إجمالي المديونية</div>
                            <div class="footer-value">0.00 EGP</div>
                        </div>
                        <div class="footer-item">
                            <div class="add-ad-container">
                                <div class="add-circle">
                                    <i class="fas fa-plus"></i>
                                </div>
                                <span class="add-text" onclick="showAddTransactionModal()">اضافة اعلان</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// دالة لعرض نافذة إضافة إعلان جديد
function showAddTransactionModal(cardId) {
    if (cardId) {
        document.getElementById('cardId').value = cardId;

        // جلب الحسابات الإعلانية المرتبطة بهذه البطاقة
        fetchLinkedAccounts(cardId);
    }

    // تعيين التاريخ الحالي في حقل التاريخ
    var today = new Date();
    var dd = String(today.getDate()).padStart(2, '0');
    var mm = String(today.getMonth() + 1).padStart(2, '0');
    var yyyy = today.getFullYear();
    today = yyyy + '-' + mm + '-' + dd;
    document.getElementById('adDate').value = today;

    var modal = new bootstrap.Modal(document.getElementById('addTransactionModal'));
    modal.show();
}

// دالة لجلب الحسابات الإعلانية المرتبطة ببطاقة ائتمان
function fetchLinkedAccounts(cardId) {
    // إرسال طلب AJAX لجلب الحسابات المرتبطة
    fetch('api/ad_accounts/get_linked_accounts.php?card_id=' + cardId)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // تحديث قائمة الحسابات الإعلانية في النموذج
                const adAccountSelect = document.getElementById('adAccount');
                adAccountSelect.innerHTML = '<option value="">اختر الحساب الإعلاني</option>';

                if (data.accounts && data.accounts.length > 0) {
                    data.accounts.forEach(account => {
                        const option = document.createElement('option');
                        option.value = account.id;
                        option.textContent = account.name;
                        adAccountSelect.appendChild(option);
                    });

                    // إظهار حقل الحساب الإعلاني
                    document.getElementById('adAccountContainer').style.display = 'block';
                } else {
                    // إخفاء حقل الحساب الإعلاني إذا لم تكن هناك حسابات مرتبطة
                    document.getElementById('adAccountContainer').style.display = 'none';
                }
            } else {
                console.error('حدث خطأ أثناء جلب الحسابات المرتبطة:', data.message);
                // إخفاء حقل الحساب الإعلاني في حالة الخطأ
                document.getElementById('adAccountContainer').style.display = 'none';
            }
        })
        .catch(error => {
            console.error('Error fetching linked accounts:', error);
            // إخفاء حقل الحساب الإعلاني في حالة الخطأ
            document.getElementById('adAccountContainer').style.display = 'none';
        });
}

// دالة لعرض تفاصيل العميل من بيانات الجدول مباشرة
function showClientDetailsFromTable(clientName) {
    console.log('showClientDetailsFromTable called with:', clientName);

    // تحديث عنوان النافذة باسم العميل
    document.getElementById('clientDetailsModalLabel').textContent = clientName;

    // جمع جميع الصفوف التي تحتوي على بيانات العميل
    const allRows = document.querySelectorAll('.client-row');
    console.log('Found rows:', allRows.length);
    const clientAds = [];

    allRows.forEach(row => {
        const rowClientName = row.getAttribute('data-client-name');
        console.log('Checking row with client name:', rowClientName, 'against:', clientName);
        if (rowClientName === clientName) {
            console.log('Found matching row for client:', clientName);
            // جمع بيانات الإعلان من data attributes
            const ad = {
                date: row.getAttribute('data-ad-date'),
                post: row.getAttribute('data-ad-post'),
                type: row.getAttribute('data-ad-type'),
                cost: parseFloat(row.getAttribute('data-ad-cost')),
                spent: parseFloat(row.getAttribute('data-ad-spent')),
                days: parseInt(row.getAttribute('data-ad-days')),
                daily_spend: parseFloat(row.getAttribute('data-daily-spend')),
                remaining_spend: parseFloat(row.getAttribute('data-remaining-spend')),
                page: row.getAttribute('data-client-name') // استخدام اسم العميل كصفحة
            };

            // حساب تاريخ الانتهاء
            const startDate = new Date(ad.date);
            const endDate = new Date(startDate);
            endDate.setDate(startDate.getDate() + ad.days);
            ad.end_date = endDate.toISOString().split('T')[0];

            clientAds.push(ad);
        }
    });

    console.log('Found client ads:', clientAds.length);

    // تحديث محتوى الجدول
    updateClientDetailsTable(clientAds);

    // حساب الإجماليات
    const totals = calculateClientTotals(clientAds);

    // تحديث الإحصائيات في الأسفل
    updateClientDetailsFooter(totals);

    console.log('About to show modal');
    var modal = new bootstrap.Modal(document.getElementById('clientDetailsModal'));
    modal.show();
}

// دالة لحساب إجماليات العميل
function calculateClientTotals(ads) {
    let totalDailySpend = 0;
    let totalRemainingSpend = 0;

    ads.forEach(ad => {
        totalDailySpend += ad.daily_spend;
        totalRemainingSpend += ad.remaining_spend;
    });

    return {
        total_daily_spend: totalDailySpend,
        total_remaining_spend: totalRemainingSpend,
        total_payment_request: 0, // سيتم حسابه لاحقاً
        total_due: 0, // سيتم حسابه لاحقاً
        total_debt: 0 // سيتم حسابه لاحقاً
    };
}

// دالة لعرض جميع عملاء البطاقة عند النقر على اسم البطاقة
function showAllCardClients(cardName, clickedElement) {
    console.log('showAllCardClients called with:', cardName);

    // تحديث عنوان النافذة باسم البطاقة
    document.getElementById('clientDetailsModalLabel').textContent = `عملاء بطاقة: ${cardName}`;

    // العثور على البطاقة التي تم النقر عليها
    const cardBox = clickedElement.closest('.credit-card-box');
    if (!cardBox) {
        console.error('Could not find card box');
        return;
    }

    // جمع الصفوف من هذه البطاقة فقط
    const cardRows = cardBox.querySelectorAll('.client-row');
    console.log('Found rows in this card:', cardRows.length);
    const allAds = [];

    cardRows.forEach(row => {
        // جمع بيانات الإعلان من data attributes
        const ad = {
            client_name: row.getAttribute('data-client-name'),
            date: row.getAttribute('data-ad-date'),
            post: row.getAttribute('data-ad-post'),
            type: row.getAttribute('data-ad-type'),
            cost: parseFloat(row.getAttribute('data-ad-cost')),
            spent: parseFloat(row.getAttribute('data-ad-spent')),
            days: parseInt(row.getAttribute('data-ad-days')),
            daily_spend: parseFloat(row.getAttribute('data-daily-spend')),
            remaining_spend: parseFloat(row.getAttribute('data-remaining-spend')),
            page: row.getAttribute('data-client-name') // استخدام اسم العميل كصفحة
        };

        // حساب تاريخ الانتهاء
        const startDate = new Date(ad.date);
        const endDate = new Date(startDate);
        endDate.setDate(startDate.getDate() + ad.days);
        ad.end_date = endDate.toISOString().split('T')[0];

        allAds.push(ad);
    });

    console.log('Found ads in this card:', allAds.length);

    // تحديث محتوى الجدول
    updateAllAdsTable(allAds);

    // حساب الإجماليات
    const totals = calculateAllAdsTotals(allAds);

    // تحديث الإحصائيات في الأسفل
    updateClientDetailsFooter(totals);

    console.log('About to show modal for this card clients');
    var modal = new bootstrap.Modal(document.getElementById('clientDetailsModal'));
    modal.show();
}

// دالة احتياطية للتوافق مع الكود القديم
function showClientDetailsModal(clientName, cardId) {
    // استدعاء الدالة الجديدة
    showClientDetailsFromTable(clientName);
}

// دالة لتحديث جدول تفاصيل العميل
function updateClientDetailsTable(ads) {
    const tableBody = document.querySelector('#clientDetailsModal .table-scrollable-body tbody');

    if (!ads || ads.length === 0) {
        tableBody.innerHTML = `
            <tr>
                <td colspan="10" class="text-center text-muted">
                    <i class="fas fa-info-circle me-1"></i>
                    لا توجد إعلانات لهذا العميل
                </td>
            </tr>
        `;
        return;
    }

    let tableContent = '';
    ads.forEach(ad => {
        const startDate = new Date(ad.date).toLocaleDateString('ar-EG', {day: 'numeric', month: 'numeric'});
        const endDate = new Date(ad.end_date).toLocaleDateString('ar-EG', {day: 'numeric', month: 'numeric'});
        const dailySpend = ad.days > 0 ? (ad.cost / ad.days) : 0;
        const remainingSpend = ad.cost - ad.spent;

        tableContent += `
            <tr>
                <td>${startDate}</td>
                <td>${ad.page || 'غير محدد'}</td>
                <td>${ad.post}</td>
                <td>${ad.type}</td>
                <td>${parseFloat(ad.cost).toFixed(2)}</td>
                <td>${parseFloat(ad.spent).toFixed(2)}</td>
                <td>${ad.days}</td>
                <td>${endDate}</td>
                <td>${dailySpend.toFixed(2)}</td>
                <td>${remainingSpend.toFixed(2)}</td>
            </tr>
        `;
    });

    tableBody.innerHTML = tableContent;
}

// دالة لتحديث جدول جميع الإعلانات (عند النقر على اسم البطاقة)
function updateAllAdsTable(ads) {
    const tableBody = document.querySelector('#clientDetailsModal .table-scrollable-body tbody');

    if (!ads || ads.length === 0) {
        tableBody.innerHTML = `
            <tr>
                <td colspan="10" class="text-center text-muted">
                    <i class="fas fa-info-circle me-1"></i>
                    لا توجد إعلانات في هذه البطاقة
                </td>
            </tr>
        `;
        return;
    }

    let tableContent = '';
    ads.forEach(ad => {
        const startDate = new Date(ad.date).toLocaleDateString('ar-EG', {day: 'numeric', month: 'numeric'});
        const endDate = new Date(ad.end_date).toLocaleDateString('ar-EG', {day: 'numeric', month: 'numeric'});
        const dailySpend = ad.days > 0 ? (ad.cost / ad.days) : 0;
        const remainingSpend = ad.cost - ad.spent;

        tableContent += `
            <tr>
                <td>${startDate}</td>
                <td>${ad.page || 'غير محدد'}</td>
                <td>${ad.post}</td>
                <td>${ad.type}</td>
                <td>${parseFloat(ad.cost).toFixed(2)}</td>
                <td>${parseFloat(ad.spent).toFixed(2)}</td>
                <td>${ad.days}</td>
                <td>${endDate}</td>
                <td>${dailySpend.toFixed(2)}</td>
                <td>${remainingSpend.toFixed(2)}</td>
            </tr>
        `;
    });

    tableBody.innerHTML = tableContent;
}

// دالة لحساب إجماليات جميع الإعلانات
function calculateAllAdsTotals(ads) {
    let totalDailySpend = 0;
    let totalRemainingSpend = 0;

    ads.forEach(ad => {
        totalDailySpend += ad.daily_spend;
        totalRemainingSpend += ad.remaining_spend;
    });

    return {
        total_daily_spend: totalDailySpend,
        total_remaining_spend: totalRemainingSpend,
        total_payment_request: 0, // سيتم حسابه لاحقاً
        total_due: 0, // سيتم حسابه لاحقاً
        total_debt: 0 // سيتم حسابه لاحقاً
    };
}

// دالة لتحديث إحصائيات العميل في الأسفل
function updateClientDetailsFooter(totals) {
    const footerValues = document.querySelectorAll('#clientDetailsModal .footer-value');

    if (footerValues.length >= 5) {
        // ترتيب القيم حسب ترتيب العناصر في HTML
        footerValues[0].textContent = `${totals.total_daily_spend.toFixed(2)} EGP`; // إجمالي الصرف يومي
        footerValues[1].textContent = `${totals.total_remaining_spend.toFixed(2)} EGP`; // إجمالي المتبقي صرفه
        footerValues[2].textContent = `${totals.total_payment_request.toFixed(2)} EGP`; // طلب التسديد
        footerValues[3].textContent = `${totals.total_due.toFixed(2)} EGP`; // إجمالي المستحق
        footerValues[4].textContent = `${totals.total_debt.toFixed(2)} EGP`; // إجمالي المديونية
    }
}

// دالة لتحميل الحسابات الإعلانية المرتبطة فقط
function loadLinkedAccounts(cardId, cardType) {
    const menuElement = document.getElementById('linkedAccountsMenu-' + cardId);

    // التحقق من وجود العنصر
    if (!menuElement) {
        console.error('لا يمكن العثور على عنصر القائمة:', 'linkedAccountsMenu-' + cardId);
        return;
    }

    // إظهار مؤشر التحميل
    menuElement.innerHTML = `
        <h6 class="dropdown-header">الحسابات الإعلانية المرتبطة</h6>
        <div class="text-center">
            <div class="spinner-border spinner-border-sm" role="status">
                <span class="visually-hidden">جاري التحميل...</span>
            </div>
            <div class="mt-2">جاري تحميل البيانات...</div>
        </div>
    `;

    // جلب الحسابات الإعلانية المرتبطة فقط
    fetch(`api/ad_accounts/get_linked_accounts.php?card_id=${cardId}&card_type=${cardType}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                let content = '<h6 class="dropdown-header">الحسابات الإعلانية المرتبطة</h6>';

                if (data.accounts && data.accounts.length > 0) {
                    // عرض الحسابات الإعلانية فقط
                    data.accounts.forEach(account => {
                        const statusBadge = account.status === 'نشط' ? 'bg-success' : 'bg-danger';
                        content += `
                            <div class="linked-account-item dropdown-item">
                                <div class="linked-account-name">
                                    <i class="fas fa-bullhorn me-1"></i>
                                    <strong>${account.name}</strong>
                                </div>
                                <div class="linked-account-details mt-1">
                                    <span class="badge ${statusBadge}">${account.status}</span>
                                    <span class="ms-2">
                                        الرصيد: ${parseFloat(account.balance).toFixed(2)}
                                    </span>
                                    <span class="ms-2">
                                        حد الصرف: ${parseFloat(account.spending_limit).toFixed(2)}
                                    </span>
                                </div>
                            </div>
                        `;
                    });
                } else {
                    content += `
                        <div class="dropdown-item text-muted">
                            <i class="fas fa-info-circle me-1"></i>
                            لا توجد حسابات إعلانية مرتبطة بهذه البطاقة
                        </div>
                    `;
                }

                menuElement.innerHTML = content;
            } else {
                menuElement.innerHTML = `
                    <h6 class="dropdown-header">خطأ في التحميل</h6>
                    <div class="dropdown-item text-danger">
                        <i class="fas fa-exclamation-triangle me-1"></i>
                        ${data.message}
                    </div>
                `;
            }
        })
        .catch(error => {
            console.error('Error:', error);
            menuElement.innerHTML = `
                <h6 class="dropdown-header">خطأ في التحميل</h6>
                <div class="dropdown-item text-danger">
                    <i class="fas fa-exclamation-triangle me-1"></i>
                    حدث خطأ أثناء تحميل البيانات
                </div>
            `;
        });
}

// اختبار JavaScript
console.log('JavaScript loaded successfully');

// إضافة مستمعات الأحداث عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM Content Loaded');

    // اختبار وجود البوب بوكس
    const modal = document.getElementById('clientDetailsModal');
    console.log('Modal element found:', modal ? 'Yes' : 'No');

    // اختبار وجود الصفوف
    const rows = document.querySelectorAll('.client-row');
    console.log('Client rows found:', rows.length);

    // إضافة مستمعات أحداث للروابط
    const clientLinks = document.querySelectorAll('.client-link');
    console.log('Client links found:', clientLinks.length);

    clientLinks.forEach((link, index) => {
        console.log(`Adding event listener to link ${index}:`, link.textContent);
        link.addEventListener('click', function(e) {
            e.preventDefault();
            console.log('Link clicked!', this.textContent);

            const row = this.closest('.client-row');
            const clientName = row.getAttribute('data-client-name');
            console.log('Client name from row:', clientName);

            if (clientName) {
                console.log('About to call showClientDetailsFromTable');
                showClientDetailsFromTable(clientName);
            } else {
                console.error('No client name found in row');
            }
        });
    });

    // اختبار مباشر للبوب بوكس
    window.testModal = function() {
        console.log('Testing modal...');
        const modal = new bootstrap.Modal(document.getElementById('clientDetailsModal'));
        modal.show();
    };

    console.log('You can test the modal by typing: testModal() in console');
    // إضافة مستمع حدث لفتح dropdown الحسابات المرتبطة
    document.querySelectorAll('[id^="linkedAccountsDropdown-"]').forEach(button => {
        button.addEventListener('click', function() {
            const cardId = this.id.replace('linkedAccountsDropdown-', '');
            const cardElement = this.closest('.credit-card');

            // البحث عن نوع البطاقة من data attribute
            let cardType = 'credit_card'; // القيمة الافتراضية
            if (cardElement && cardElement.dataset.cardType) {
                cardType = cardElement.dataset.cardType;
            }

            // تحميل البيانات فقط عند أول فتح
            const menuElement = document.getElementById('linkedAccountsMenu-' + cardId);
            if (menuElement) {
                if (menuElement.innerHTML.includes('جاري تحميل البيانات')) {
                    loadLinkedAccounts(cardId, cardType);
                }
            } else {
                console.error('لا يمكن العثور على عنصر القائمة في مستمع الأحداث:', 'linkedAccountsMenu-' + cardId);
            }
        });
    });

    // إضافة مستمع حدث لزر الحفظ
    const saveButton = document.getElementById('saveTransaction');
    if (saveButton) {
        saveButton.addEventListener('click', function() {
        // التحقق من إدخال جميع البيانات المطلوبة
        var date = document.getElementById('adDate').value;
        var cost = document.getElementById('adCost').value;
        var spend = document.getElementById('adSpend').value;
        var cardId = document.getElementById('cardId').value;
        var adAccount = document.getElementById('adAccount').value;

        if (!date || !cost || !spend) {
            alert('يرجى إدخال جميع البيانات المطلوبة');
            return;
        }

        // إعداد البيانات للإرسال
        var formData = {
            date: date,
            type: document.getElementById('adType').value,
            payment_type: document.getElementById('adPaymentType').value,
            post: document.getElementById('adPostType').value,
            days: document.getElementById('adDays').value,
            cost: cost,
            spend: spend,
            page: document.getElementById('adPage').value,
            card_id: cardId,
            ad_account_id: adAccount
        };

        // إرسال البيانات عبر AJAX
        fetch('api/ads/add_from_card.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(formData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('تم حفظ الإعلان بنجاح!');
                var modal = bootstrap.Modal.getInstance(document.getElementById('addTransactionModal'));
                modal.hide();

                // إعادة تحميل الصفحة لعرض التغييرات
                window.location.reload();
            } else {
                alert('حدث خطأ أثناء حفظ الإعلان: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ أثناء حفظ الإعلان');
        });
        });
    } else {
        console.error('لا يمكن العثور على زر الحفظ');
    }
});
</script>

<style>
body {
    background-color: #f8f9fa;
    font-family: 'Cairo', sans-serif;
    margin: 0;
    padding: 0;
}

/* تنسيقات الهيدر المطابق للتصميم بالضبط */
.exact-header {
    width: 100%;
    margin-bottom: 20px;
}

/* الشريط الأسود العلوي */


/* الجزء الرئيسي من الهيدر */
.header-main {
    background-color: white;
    padding: 15px 30px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.logo-container {
    flex: 0 0 auto;
}

.header-logo {
    height: 45px;
    width: auto;
}

.center-title {
    flex: 1;
    text-align: center;
}

.blue-title {
    color: #4a4ad4;
    font-weight: bold;
    font-size: 22px;
    margin: 0;
    font-family: 'Cairo', sans-serif;
}

.right-spacer {
    flex: 0 0 auto;
    width: 45px; /* نفس عرض اللوجو للتوازن */
}

/* الشريط السفلي الرمادي */
.bottom-search-bar {
    background-color: #f5f5f5;
    padding: 12px 30px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid #e0e0e0;
}

.folder-section {
    display: flex;
    align-items: center;
    gap: 8px;
}

.folder-icon {
    color: #666;
    font-size: 16px;
}

.folder-text {
    color: #666;
    font-size: 14px;
    font-family: 'Cairo', sans-serif;
}

.folder-link {
    text-decoration: none;
    color: inherit;
}

.folder-link:hover .folder-text {
    color: #4a4ad4;
    text-decoration: underline;
}

.middle-spacer {
    flex: 1;
}

.search-box {
    display: flex;
    align-items: center;
    background-color: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 6px 12px;
    min-width: 200px;
}

.search-field {
    border: none;
    outline: none;
    background: transparent;
    flex: 1;
    font-size: 14px;
    color: #333;
    font-family: 'Cairo', sans-serif;
}

.search-field::placeholder {
    color: #999;
}

.search-btn {
    color: #999;
    font-size: 14px;
    cursor: pointer;
    margin-left: 8px;
}

.custom-header {
    background-color: white;
    padding: 10px 0;
}

.nav-link {
    color: #666;
    font-weight: 500;
}

.nav-link.active {
    color: #4a4ad4;
}

.red-line {
    height: 2px;
    background: linear-gradient(90deg, transparent, #ff0000, transparent);
    margin-bottom: 20px;
}

.main-title {
    color: #4a4ad4;
    font-weight: bold;
    font-size: 28px;
}

.credit-card-box {
    border: 1px solid #dcf343;
    border-radius: 10px;
    overflow: hidden;
    background-color: white;
    margin-bottom: 20px;
}

.credit-card-box .card-header {
    background-color: #4a4ad4;
    color: white;
    padding: 10px 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.credit-card-box .card-title {
    font-weight: bold;
    font-size: 18px;
    text-align: center;
    flex-grow: 1;
    cursor: pointer;
    transition: all 0.3s ease;
    padding: 5px;
    border-radius: 5px;
}

.credit-card-box .card-title:hover {
    background-color: rgba(255, 255, 255, 0.2);
    transform: scale(1.02);
}

.credit-card-box .dropdown-toggle {
    color: white;
    background: none;
    border: none;
    padding: 0;
}

.credit-card-box .dropdown-toggle::after {
    display: none;
}

.credit-card-box .card-body {
    padding: 0;
}

.credit-card-table {
    margin-bottom: 0;
    border-collapse: separate;
    border-spacing: 0;
    width: 100%;
}

.credit-card-table thead,
.credit-card-table tfoot {
    position: sticky;
    z-index: 2;
}

.credit-card-table thead {
    top: 0;
}

.credit-card-table th {
    background-color: #f8f9fa;
    color: #4a4ad4;
    font-weight: bold;
    text-align: center;
    border-bottom: 1px solid #dcf343;
    padding: 8px;
}

.credit-card-table td {
    text-align: center;
    padding: 8px;
    border: none;
}

.credit-card-table tr:nth-child(even) {
    background-color: #f8f9fa;
}

.credit-card-table-container {
    max-height: 300px;
    overflow: hidden;
    position: relative;
}

.credit-card-table-body {
    max-height: 250px;
    overflow-y: auto;
    display: block;
}

.credit-card-table-body table {
    width: 100%;
}

.credit-card-table-body tr {
    display: table;
    width: 100%;
    table-layout: fixed;
}

.card-footer {
    background-color: #f8f9fa;
    border-top: 1px solid #dcf343;
    padding: 0;
}

.footer-row {
    display: flex;
    border-bottom: 1px solid #dcf343;
}

.footer-row:last-child {
    border-bottom: none;
}

.footer-item {
    flex: 1;
    text-align: center;
    padding: 10px;
    border-left: 1px solid #dcf343;
}

.footer-item:last-child {
    border-left: none;
}

.footer-label {
    color: #4a4ad4;
    font-size: 14px;
}

.footer-value {
    font-weight: bold;
    font-size: 16px;
}

.add-ad-container {
    display: flex;
    align-items: center;
    cursor: pointer;
}

.add-circle {
    width: 30px;
    height: 30px;
    background-color: #4CAF50;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    margin-left: 10px;
}

.add-text {
    color: #4a4ad4;
    font-weight: bold;
}

/* تنسيقات نافذة إضافة إعلان */
.add-ad-header {
    background-color: #4a4ad4;
    color: white;
    padding: 10px 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.add-ad-header .modal-title {
    font-weight: bold;
    font-size: 20px;
    text-align: center;
    flex-grow: 1;
}

.add-ad-table-container {
    padding: 15px;
}

.add-ad-table {
    margin-bottom: 0;
    border-collapse: separate;
    border-spacing: 0;
    width: 100%;
}

.add-ad-table th {
    background-color: #f8f9fa;
    color: #4a4ad4;
    font-weight: bold;
    text-align: center;
    border-bottom: 1px solid #dcf343;
    padding: 8px;
}

.add-ad-table td {
    text-align: center;
    padding: 8px;
    vertical-align: middle;
}

.add-ad-footer {
    text-align: center;
    padding: 15px;
    border-top: 1px solid #dcf343;
}

.add-ad-save-btn {
    background-color: #4a4ad4;
    color: white;
    border: none;
    border-radius: 5px;
    padding: 8px 20px;
    font-weight: bold;
    cursor: pointer;
}

/* تنسيقات نافذة تفاصيل العميل */
.client-details-modal {
    border-radius: 15px;
    overflow: hidden;
}

.client-modal-header {
    background-color: #4a4ad4;
    color: white;
    padding: 10px 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.client-modal-title {
    font-weight: bold;
    font-size: 20px;
    text-align: center;
    flex-grow: 1;
}

.client-details-table-container {
    max-height: 300px;
    overflow: hidden;
    position: relative;
}

.client-details-table {
    margin-bottom: 0;
    border-collapse: separate;
    border-spacing: 0;
    width: 100%;
}

.client-details-table thead,
.client-details-table tfoot {
    position: sticky;
    z-index: 2;
}

.client-details-table thead {
    top: 0;
}

.client-details-table tbody {
    max-height: 250px;
    overflow-y: auto;
    display: block;
}

/* Hacer que las filas del cuerpo tengan el mismo ancho que la tabla */
.client-details-table tbody tr {
    display: table;
    width: 100%;
    table-layout: fixed;
}

/* Hacer que el contenedor del cuerpo sea desplazable */
.table-scrollable-body {
    max-height: 250px;
    overflow-y: auto;
    display: block;
    width: 100%;
}

.client-details-table th {
    background-color: #f8f9fa;
    color: #4a4ad4;
    font-weight: bold;
    text-align: center;
    border-bottom: 1px solid #dcf343;
    padding: 8px;
}

.client-details-table td {
    text-align: center;
    padding: 8px;
    border: none;
}

.client-details-table tr:nth-child(even) {
    background-color: #f8f9fa;
}

.client-details-footer {
    background-color: #f8f9fa;
    border-top: 1px solid #dcf343;
    padding: 0;
    position: sticky;
    bottom: 0;
    width: 100%;
}

/* تنسيقات القائمة المنسدلة للحسابات المرتبطة */
.linked-accounts-dropdown {
    width: 400px;
    max-height: 500px;
    overflow-y: auto;
    padding: 10px;
    border: 1px solid #dcf343;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.linked-accounts-dropdown .dropdown-header {
    color: #4a4ad4;
    font-weight: bold;
    font-size: 16px;
    padding: 8px 12px;
    border-bottom: 2px solid #dcf343;
    margin-bottom: 8px;
}

.linked-account-item.dropdown-item {
    padding: 10px;
    margin-bottom: 5px;
    border-radius: 5px;
    white-space: normal;
    border: 1px solid #f0f0f0;
}

.linked-account-item.dropdown-item:hover {
    background-color: #f8f9fa;
}

.linked-account-item.dropdown-item:active {
    background-color: #e9ecef;
    color: inherit;
}

.linked-account-name {
    font-weight: bold;
    color: #4a4ad4;
    margin-bottom: 5px;
}

.linked-account-details {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    font-size: 0.85em;
}

/* تنسيقات زر القائمة المنسدلة */
.dropdown-toggle {
    color: white;
    background: none;
    border: none;
    padding: 5px;
    border-radius: 50%;
    transition: all 0.3s ease;
    cursor: pointer;
}

.dropdown-toggle::after {
    display: none;
}

.dropdown-toggle:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

.dropdown-toggle:focus {
    outline: none;
    box-shadow: none;
}

/* تنسيقات الإعلانات */
.ads-list {
    max-height: 200px;
    overflow-y: auto;
}

.ad-item {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef !important;
    margin-bottom: 5px;
}

.ad-item:hover {
    background-color: #e9ecef;
}

.ad-name {
    font-weight: 500;
    color: #495057;
}

.ad-details {
    font-size: 0.8em;
    color: #6c757d;
}

.totals-summary {
    background-color: #f8f9fa;
    padding: 8px;
    border-radius: 4px;
    border: 1px solid #dcf343;
}

/* أنماط التعديل المباشر */
.editable-cell {
    cursor: pointer;
    position: relative;
    transition: all 0.3s ease;
}

.editable-cell:hover {
    background-color: #f8f9fa;
    border: 1px solid #007bff;
    border-radius: 4px;
}

.editable-cell.editing {
    padding: 0;
    background-color: white;
}

.editable-cell input {
    width: 100%;
    border: 2px solid #007bff;
    border-radius: 4px;
    padding: 8px;
    font-size: 14px;
    text-align: center;
    background-color: white;
    box-shadow: 0 2px 4px rgba(0,123,255,0.25);
}

.editable-cell input:focus {
    outline: none;
    border-color: #0056b3;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
}

.calculated-cell {
    background-color: #f8f9fa;
    color: #6c757d;
    font-style: italic;
}

.edit-indicator {
    position: absolute;
    top: 2px;
    right: 2px;
    font-size: 10px;
    color: #007bff;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.editable-cell:hover .edit-indicator {
    opacity: 1;
}

.save-indicator {
    position: absolute;
    top: 50%;
    right: 5px;
    transform: translateY(-50%);
    color: #28a745;
    font-size: 12px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.save-indicator.show {
    opacity: 1;
}
</style>

<script>
// وظائف التعديل المباشر
document.addEventListener('DOMContentLoaded', function() {
    // إضافة مؤشر التعديل لكل خلية قابلة للتعديل
    document.querySelectorAll('.editable-cell').forEach(cell => {
        const indicator = document.createElement('span');
        indicator.className = 'edit-indicator';
        indicator.innerHTML = '<i class="fas fa-edit"></i>';
        cell.appendChild(indicator);

        // إضافة حدث النقر
        cell.addEventListener('click', function() {
            if (!this.classList.contains('editing')) {
                makeEditable(this);
            }
        });
    });
});

function makeEditable(cell) {
    if (cell.classList.contains('editing')) return;

    const originalValue = cell.textContent.trim();
    const field = cell.getAttribute('data-field');
    const type = cell.getAttribute('data-type');

    // إضافة كلاس التعديل
    cell.classList.add('editing');

    // إنشاء حقل الإدخال
    const input = document.createElement('input');
    input.type = type === 'number' ? 'number' : 'text';
    input.value = type === 'number' ? originalValue.replace(/,/g, '') : originalValue;
    input.className = 'form-control form-control-sm';

    if (type === 'number') {
        input.step = '0.01';
        input.min = '0';
    }

    // استبدال محتوى الخلية
    cell.innerHTML = '';
    cell.appendChild(input);

    // التركيز على الحقل وتحديد النص
    input.focus();
    input.select();

    // حفظ عند الضغط على Enter
    input.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            saveEdit(cell, input, originalValue, field, type);
        }
    });

    // إلغاء عند الضغط على Escape
    input.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            cancelEdit(cell, originalValue);
        }
    });

    // حفظ عند فقدان التركيز
    input.addEventListener('blur', function() {
        setTimeout(() => {
            if (cell.classList.contains('editing')) {
                saveEdit(cell, input, originalValue, field, type);
            }
        }, 100);
    });
}

function saveEdit(cell, input, originalValue, field, type) {
    const newValue = input.value.trim();

    // التحقق من صحة القيمة
    if (type === 'number' && (isNaN(newValue) || newValue === '')) {
        alert('يرجى إدخال رقم صحيح');
        input.focus();
        return;
    }

    if (newValue === '' && type === 'text') {
        alert('لا يمكن ترك الحقل فارغاً');
        input.focus();
        return;
    }

    // إذا لم تتغير القيمة، إلغاء التعديل
    const originalNumValue = type === 'number' ? originalValue.replace(/,/g, '') : originalValue;
    if (newValue === originalNumValue) {
        cancelEdit(cell, originalValue);
        return;
    }

    // إرسال التحديث إلى الخادم
    const row = cell.closest('tr');
    const adId = row.getAttribute('data-ad-id') || 'new'; // إذا لم يكن هناك معرف، فهو إعلان جديد

    // إظهار مؤشر الحفظ
    showSaveIndicator(cell);

    // إرسال طلب AJAX
    const formData = new FormData();
    formData.append('action', 'update_field');
    formData.append('ad_id', adId);
    formData.append('field', field);
    formData.append('value', newValue);

    fetch('api/ads/update_field.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // تحديث القيمة في الخلية
            const displayValue = type === 'number' ? parseFloat(newValue).toLocaleString('en-US', {minimumFractionDigits: 2, maximumFractionDigits: 2}) : newValue;
            cell.innerHTML = displayValue;

            // تحديث data attributes
            row.setAttribute('data-' + field.replace('ad_', ''), newValue);

            // إعادة حساب القيم المحسوبة
            recalculateRow(row);

            // إظهار رسالة نجاح
            showSuccessMessage(cell);

        } else {
            alert('حدث خطأ أثناء الحفظ: ' + (data.message || 'خطأ غير معروف'));
            cancelEdit(cell, originalValue);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ أثناء الحفظ');
        cancelEdit(cell, originalValue);
    })
    .finally(() => {
        cell.classList.remove('editing');
        // إعادة إضافة مؤشر التعديل
        const indicator = document.createElement('span');
        indicator.className = 'edit-indicator';
        indicator.innerHTML = '<i class="fas fa-edit"></i>';
        cell.appendChild(indicator);
    });
}

function cancelEdit(cell, originalValue) {
    cell.classList.remove('editing');
    cell.innerHTML = originalValue;

    // إعادة إضافة مؤشر التعديل
    const indicator = document.createElement('span');
    indicator.className = 'edit-indicator';
    indicator.innerHTML = '<i class="fas fa-edit"></i>';
    cell.appendChild(indicator);
}

function recalculateRow(row) {
    const cost = parseFloat(row.getAttribute('data-cost')) || 0;
    const spent = parseFloat(row.getAttribute('data-spent')) || 0;
    const days = parseInt(row.getAttribute('data-days')) || 1;

    // حساب اليومي
    const dailySpend = days > 0 ? cost / days : 0;

    // حساب المتبقي
    const remainingSpend = cost - spent;

    // تحديث الخلايا المحسوبة
    const cells = row.querySelectorAll('td');
    if (cells.length >= 7) {
        cells[5].textContent = dailySpend.toLocaleString('en-US', {minimumFractionDigits: 2, maximumFractionDigits: 2}); // يومي
        cells[6].textContent = remainingSpend.toLocaleString('en-US', {minimumFractionDigits: 2, maximumFractionDigits: 2}); // متبقي الصرف
    }

    // تحديث data attributes
    row.setAttribute('data-daily-spend', dailySpend);
    row.setAttribute('data-remaining-spend', remainingSpend);
}

function showSaveIndicator(cell) {
    const indicator = document.createElement('span');
    indicator.className = 'save-indicator';
    indicator.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
    cell.appendChild(indicator);
    setTimeout(() => indicator.classList.add('show'), 10);
}

function showSuccessMessage(cell) {
    // إزالة مؤشر الحفظ
    const saveIndicator = cell.querySelector('.save-indicator');
    if (saveIndicator) {
        saveIndicator.remove();
    }

    // إظهار رسالة نجاح
    const successIndicator = document.createElement('span');
    successIndicator.className = 'save-indicator show';
    successIndicator.style.color = '#28a745';
    successIndicator.innerHTML = '<i class="fas fa-check"></i>';
    cell.appendChild(successIndicator);

    // إزالة الرسالة بعد ثانيتين
    setTimeout(() => {
        successIndicator.remove();
    }, 2000);
}

// تهيئة التعديل المباشر للمبالغ
document.addEventListener('DOMContentLoaded', function() {
    // إضافة معالج النقر لجميع العناصر القابلة للتعديل
    document.querySelectorAll('.editable-amount').forEach(element => {
        element.addEventListener('click', function() {
            makeEditableAmount(this);
        });

        // إضافة تأثير hover
        element.addEventListener('mouseenter', function() {
            this.style.backgroundColor = '#f8f9fa';
            this.style.border = '1px solid #dee2e6';
        });

        element.addEventListener('mouseleave', function() {
            if (!this.querySelector('input')) {
                this.style.backgroundColor = 'transparent';
                this.style.border = '1px solid transparent';
            }
        });
    });
});

// دالة لجعل العنصر قابل للتعديل
function makeEditableAmount(element) {
    // التحقق من أن العنصر ليس قيد التعديل بالفعل
    if (element.querySelector('input')) {
        return;
    }

    const cardId = element.getAttribute('data-card-id');
    const field = element.getAttribute('data-field');
    const currentValue = element.getAttribute('data-current-value');
    const originalText = element.textContent.trim();

    // إنشاء حقل الإدخال
    const input = document.createElement('input');
    input.type = 'number';
    input.step = '0.01';
    input.value = currentValue;
    input.style.width = '100px';
    input.style.border = '1px solid #007bff';
    input.style.borderRadius = '3px';
    input.style.padding = '2px 5px';
    input.style.textAlign = 'center';
    input.style.fontSize = 'inherit';

    // إخفاء النص الأصلي وإظهار حقل الإدخال
    element.innerHTML = '';
    element.appendChild(input);
    input.focus();
    input.select();

    // معالج حفظ القيمة عند الضغط على Enter
    input.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            saveAmountValue(element, input, cardId, field, originalText);
        }
    });

    // معالج حفظ القيمة عند فقدان التركيز
    input.addEventListener('blur', function() {
        saveAmountValue(element, input, cardId, field, originalText);
    });

    // معالج إلغاء التعديل عند الضغط على Escape
    input.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            cancelAmountEdit(element, originalText);
        }
    });
}

// دالة لحفظ المبلغ الجديد
function saveAmountValue(element, input, cardId, field, originalText) {
    const newValue = parseFloat(input.value) || 0;

    // إرسال البيانات إلى الخادم
    fetch('api/credit_cards/update_amount.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            card_id: cardId,
            field: field,
            value: newValue
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // تحديث العرض بالقيمة الجديدة
            const formattedValue = field === 'manual_debt_amount' && newValue > 0
                ? '-' + newValue.toFixed(2)
                : newValue.toFixed(2);

            element.innerHTML = formattedValue;
            element.setAttribute('data-current-value', newValue);

            // تحديث لون المديونية
            if (field === 'manual_debt_amount') {
                element.style.color = newValue > 0 ? 'red' : 'green';
            }

            // إظهار رسالة نجاح
            showAmountSuccessMessage('تم تحديث المبلغ بنجاح');
        } else {
            // في حالة الخطأ، إرجاع النص الأصلي
            element.innerHTML = originalText;
            showAmountErrorMessage('حدث خطأ أثناء تحديث المبلغ: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        element.innerHTML = originalText;
        showAmountErrorMessage('حدث خطأ أثناء تحديث المبلغ');
    })
    .finally(() => {
        // إعادة تعيين التأثيرات
        element.style.backgroundColor = 'transparent';
        element.style.border = '1px solid transparent';
    });
}

// دالة لإلغاء التعديل
function cancelAmountEdit(element, originalText) {
    element.innerHTML = originalText;
    element.style.backgroundColor = 'transparent';
    element.style.border = '1px solid transparent';
}

// دالة لإظهار رسالة نجاح
function showAmountSuccessMessage(message) {
    const toast = document.createElement('div');
    toast.className = 'alert alert-success position-fixed';
    toast.style.top = '20px';
    toast.style.right = '20px';
    toast.style.zIndex = '9999';
    toast.innerHTML = message;
    document.body.appendChild(toast);

    setTimeout(() => {
        toast.remove();
    }, 3000);
}

// دالة لإظهار رسالة خطأ
function showAmountErrorMessage(message) {
    const toast = document.createElement('div');
    toast.className = 'alert alert-danger position-fixed';
    toast.style.top = '20px';
    toast.style.right = '20px';
    toast.style.zIndex = '9999';
    toast.innerHTML = message;
    document.body.appendChild(toast);

    setTimeout(() => {
        toast.remove();
    }, 3000);
}
</script>

<?php
// لا نستخدم ملف التذييل
// include 'includes/footer.php';
?>
