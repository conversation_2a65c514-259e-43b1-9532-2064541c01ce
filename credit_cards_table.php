<?php
// تضمين ملف الاتصال بقاعدة البيانات
require_once 'config/database.php';

// إنشاء اتصال بقاعدة البيانات
$database = new Database();
$db = $database->getConnection();

// التحقق من وجود الحقول المطلوبة وإضافتها إذا لم تكن موجودة
try {
    // التحقق من الحقول الموجودة
    $stmt = $db->prepare("SHOW COLUMNS FROM credit_cards");
    $stmt->execute();
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);

    // إضافة الحقول المطلوبة إذا لم تكن موجودة
    if (!in_array('deposit', $columns)) {
        $db->exec("ALTER TABLE credit_cards ADD COLUMN deposit DECIMAL(10,2) DEFAULT 20000.00");
    }
    if (!in_array('basic_balance', $columns)) {
        $db->exec("ALTER TABLE credit_cards ADD COLUMN basic_balance DECIMAL(10,2) DEFAULT 15000.00");
    }
    if (!in_array('payment', $columns)) {
        $db->exec("ALTER TABLE credit_cards ADD COLUMN payment DECIMAL(10,2) DEFAULT 14000.00");
    }
    if (!in_array('payment_due_date', $columns)) {
        $db->exec("ALTER TABLE credit_cards ADD COLUMN payment_due_date DATE DEFAULT '2024-08-25'");
    }
} catch (PDOException $e) {
    // تجاهل الأخطاء في حالة عدم وجود الجدول
}

// جلب بيانات بطاقات الائتمان
try {
    $stmt = $db->prepare("
        SELECT *
        FROM credit_cards
        ORDER BY name
    ");
    $stmt->execute();
    $creditCards = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    echo "حدث خطأ عند جلب البطاقات: " . $e->getMessage();
    $creditCards = [];
}

// إضافة بيانات تجريبية إذا كان الجدول فارغ
if (empty($creditCards)) {
    try {
        $stmt = $db->prepare("
            INSERT INTO credit_cards (name, balance, deposit, basic_balance, payment, payment_due_date) VALUES
            ('Bassam CIB', 150000, 20000, 15000, 14000, '2024-08-25'),
            ('Bassam NBK', 49000, 16000, 4000, 5000, '2024-09-25'),
            ('Bassam CIB', 150000, 20000, 15000, 14000, '2024-08-25'),
            ('Bassam NBK', 49000, 16000, 4000, 5000, '2024-09-25'),
            ('Bassam CIB', 150000, 20000, 15000, 14000, '2024-08-25'),
            ('Bassam NBK', 49000, 16000, 4000, 5000, '2024-09-25'),
            ('Bassam CIB', 150000, 20000, 15000, 14000, '2024-08-25'),
            ('Bassam NBK', 49000, 16000, 4000, 5000, '2024-09-25'),
            ('Bassam CIB', 150000, 20000, 15000, 14000, '2024-08-25'),
            ('Bassam NBK', 49000, 16000, 4000, 5000, '2024-09-25'),
            ('Bassam CIB', 150000, 20000, 15000, 14000, '2024-08-25')
        ");
        $stmt->execute();

        // إعادة جلب البيانات
        $stmt = $db->prepare("SELECT * FROM credit_cards ORDER BY name");
        $stmt->execute();
        $creditCards = $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        // تجاهل الأخطاء
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>كريديت كارد</title>

    <!-- Bootstrap RTL CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Google Fonts - Cairo -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap">

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f8f9fa;
            direction: rtl;
        }

        /* Header Styles - مطابق للتصميم */
        .header-container {
            background-color: white;
            border-bottom: 1px solid #e0e0e0;
            padding: 0;
        }

        .header-top {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 15px 30px;
        }

        .logo-section {
            display: flex;
            align-items: center;
        }

        .logo-section img {
            height: 50px;
            width: auto;
        }

        .center-title {
            flex: 1;
            text-align: center;
        }

        .center-title h1 {
            color: #4a69bd;
            font-size: 24px;
            font-weight: 600;
            margin: 0;
        }

        .search-section {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .search-box {
            position: relative;
            width: 300px;
        }

        .search-input {
            width: 100%;
            padding: 8px 40px 8px 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
            background-color: #f8f9fa;
        }

        .search-icon {
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
            color: #666;
        }

        /* Table Container */
        .table-container {
            margin: 20px;
            background-color: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        /* Table Styles - مطابق للتصميم */
        .credit-table {
            width: 100%;
            border-collapse: collapse;
            margin: 0;
        }

        .credit-table thead {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .credit-table thead th {
            color: white;
            padding: 15px 20px;
            text-align: center;
            font-weight: 600;
            font-size: 16px;
            border: none;
        }

        .credit-table tbody tr {
            border-bottom: 2px solid #f0f0f0;
            transition: background-color 0.2s;
        }

        .credit-table tbody tr:hover {
            background-color: #f8f9fa;
        }

        .credit-table tbody td {
            padding: 15px 20px;
            text-align: center;
            font-size: 15px;
            color: #333;
            border: none;
        }

        .credit-table tbody td:first-child {
            color: #4a69bd;
            font-weight: 500;
        }

        /* Footer Row - الصف الأصفر */
        .totals-row {
            background-color: #fff3cd !important;
            font-weight: 600;
            color: #856404;
        }

        .totals-row td {
            border-top: 2px solid #ffeaa7;
            font-size: 16px;
        }

        .totals-row td:first-child {
            color: #856404;
            font-weight: 700;
        }

        .totals-clickable {
            cursor: pointer;
            text-decoration: underline;
            transition: color 0.2s;
        }

        .totals-clickable:hover {
            color: #d39e00;
        }

        /* Modal Styles */
        .modal-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 1000;
        }

        .modal-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            min-width: 400px;
            max-width: 90%;
        }

        .modal-header {
            text-align: center;
            margin-bottom: 20px;
            color: #4a69bd;
            font-size: 20px;
            font-weight: 600;
        }

        .modal-body {
            margin-bottom: 20px;
        }

        .totals-summary {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }

        .summary-item {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
        }

        .summary-label {
            font-size: 14px;
            color: #666;
            margin-bottom: 5px;
        }

        .summary-value {
            font-size: 18px;
            font-weight: 600;
            color: #333;
        }

        .modal-close {
            position: absolute;
            top: 10px;
            right: 15px;
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            color: #999;
        }

        .modal-close:hover {
            color: #333;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .header-top {
                flex-direction: column;
                gap: 15px;
                padding: 15px;
            }

            .search-box {
                width: 100%;
            }

            .table-container {
                margin: 10px;
                overflow-x: auto;
            }

            .credit-table {
                min-width: 800px;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header-container">
        <div class="header-top">
            <!-- Logo -->
            <div class="logo-section">
                <img src="assets/images/logo.png" alt="بسام ميديا">
            </div>

            <!-- Title -->
            <div class="center-title">
                <h1>كريديت كارد</h1>
            </div>

            <!-- Search -->
            <div class="search-section">
                <div class="search-box">
                    <input type="text" class="search-input" placeholder="البحث">
                    <i class="fas fa-search search-icon"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- Table -->
    <div class="table-container">
        <table class="credit-table">
            <thead>
                <tr>
                    <th>اسم الفيزا</th>
                    <th>ايداع</th>
                    <th>اساسي</th>
                    <th>رصيد</th>
                    <th>دفع</th>
                    <th>تاريخ</th>
                </tr>
            </thead>
            <tbody>
                <?php
                $totalDeposit = 0;
                $totalBasic = 0;
                $totalBalance = 0;
                $totalPayment = 0;

                // جلب البيانات من جدول credit_cards
                foreach ($creditCards as $card):
                    // استخدام البيانات من قاعدة البيانات مع قيم افتراضية
                    $deposit = isset($card['deposit']) ? floatval($card['deposit']) : 0;
                    $basic = isset($card['basic_balance']) ? floatval($card['basic_balance']) : 0;
                    $balance = isset($card['balance']) ? floatval($card['balance']) : 0;
                    $payment = isset($card['payment']) ? floatval($card['payment']) : 0;

                    // تنسيق التاريخ
                    if (isset($card['payment_due_date']) && !empty($card['payment_due_date'])) {
                        $date = date('j-n', strtotime($card['payment_due_date']));
                    } else {
                        $date = '-';
                    }

                    // إضافة للمجاميع
                    $totalDeposit += $deposit;
                    $totalBasic += $basic;
                    $totalBalance += $balance;
                    $totalPayment += $payment;
                ?>
                <tr>
                    <td><?php echo htmlspecialchars($card['name']); ?></td>
                    <td><?php echo number_format($deposit); ?></td>
                    <td><?php echo number_format($basic); ?></td>
                    <td><?php echo number_format($balance); ?></td>
                    <td><?php echo number_format($payment); ?></td>
                    <td><?php echo $date; ?></td>
                </tr>
                <?php endforeach; ?>

                <!-- Totals Row -->
                <tr class="totals-row">
                    <td><span class="totals-clickable" onclick="showTotalsModal()">اجماليات</span></td>
                    <td><?php echo number_format($totalDeposit); ?></td>
                    <td><?php echo number_format($totalBasic); ?></td>
                    <td><?php echo number_format($totalBalance); ?></td>
                    <td><?php echo number_format($totalPayment); ?></td>
                    <td>-</td>
                </tr>
            </tbody>
        </table>
    </div>

    <!-- Totals Modal -->
    <div class="modal-overlay" id="totalsModal">
        <div class="modal-content">
            <button class="modal-close" onclick="closeTotalsModal()">&times;</button>
            <div class="modal-header">
                إجماليات كريديت كارد
            </div>
            <div class="modal-body">
                <div class="totals-summary">
                    <div class="summary-item">
                        <div class="summary-label">إجمالي الإيداع</div>
                        <div class="summary-value"><?php echo number_format($totalDeposit); ?></div>
                    </div>
                    <div class="summary-item">
                        <div class="summary-label">إجمالي الأساسي</div>
                        <div class="summary-value"><?php echo number_format($totalBasic); ?></div>
                    </div>
                    <div class="summary-item">
                        <div class="summary-label">إجمالي الرصيد</div>
                        <div class="summary-value"><?php echo number_format($totalBalance); ?></div>
                    </div>
                    <div class="summary-item">
                        <div class="summary-label">إجمالي الدفع</div>
                        <div class="summary-value"><?php echo number_format($totalPayment); ?></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        function showTotalsModal() {
            document.getElementById('totalsModal').style.display = 'block';
        }

        function closeTotalsModal() {
            document.getElementById('totalsModal').style.display = 'none';
        }

        // إغلاق النافذة عند النقر خارجها
        document.getElementById('totalsModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeTotalsModal();
            }
        });

        // إغلاق النافذة بمفتاح Escape
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeTotalsModal();
            }
        });
    </script>
</body>
</html>
