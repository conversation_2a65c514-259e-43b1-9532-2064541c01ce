<?php
// Include configuration
require_once 'config/config.php';

// Include database connection
require_once 'includes/db.php';

// Include helper functions
require_once 'includes/functions.php';

// Include authentication functions
require_once 'includes/auth.php';

// Include permissions functions
require_once 'includes/permissions.php';

// Check if user is logged in
if (!isLoggedIn()) {
    $_SESSION['flash_message'] = 'يجب تسجيل الدخول للوصول إلى لوحة التحكم';
    $_SESSION['flash_type'] = 'danger';
    redirect(BASE_URL . 'index.php');
}

// Get user data
$user_id = $_SESSION['user_id'];
$query = "SELECT * FROM users WHERE id = :id LIMIT 1";
$stmt = $db->prepare($query);
$stmt->bindParam(':id', $user_id);
$stmt->execute();
$user = $stmt->fetch(PDO::FETCH_ASSOC);
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم - <?php echo SITE_TITLE; ?></title>

    <!-- Bootstrap RTL CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Google Fonts - Cairo -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>assets/css/styles.css">
    <style>
        body {
            background-color: white;
            margin: 0;
            padding: 0;
            font-family: 'Cairo', sans-serif;
        }
        .dashboard-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .dashboard-header {
            padding: 15px 0;
            border-bottom: 1px solid #eee;
        }
        .logo-link {
            display: block;
            transition: all 0.3s ease;
        }

        .logo-link:hover {
            transform: scale(1.05);
        }

        .dashboard-logo {
            max-width: 80px;
            height: auto;
            cursor: pointer;
        }
        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 20px;
            margin-top: 40px;
            max-width: 900px;
            margin-left: auto;
            margin-right: auto;
        }
        .dashboard-card {
            background-color: #f0f2ff;
            border-radius: 15px;
            padding: 20px 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 1px solid #e6e9ff;
        }
        .dashboard-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
        }

        /* Modal styles */
        .modal-content {
            border-radius: 15px;
            border: none;
            background-color: #f8f9ff;
        }

        .modal-header {
            background-color: #4a56e2;
            color: white;
            border-top-left-radius: 15px;
            border-top-right-radius: 15px;
            border-bottom: none;
        }

        .modal-title {
            font-weight: 600;
        }

        .modal-body {
            padding: 30px;
        }

        .btn-outline-primary {
            color: #4a56e2;
            border-color: #4a56e2;
            border-radius: 10px;
            transition: all 0.3s ease;
        }

        .btn-outline-primary:hover {
            background-color: #4a56e2;
            color: white;
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(74, 86, 226, 0.3);
        }
        .card-title {
            color: #4a56e2;
            font-size: 18px;
            font-weight: 600;
            text-align: right;
        }
        .card-icon {
            color: #4a56e2;
            font-size: 24px;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .card-icon i {
            font-size: 24px;
        }
        .card-icon img {
            width: 30px;
            height: auto;
        }
        @media (max-width: 768px) {
            .dashboard-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <div class="dashboard-header">
            <a href="<?php echo BASE_URL; ?>" class="logo-link">
                <img src="<?php echo BASE_URL; ?>assets/images/logo.png" alt="<?php echo SITE_TITLE; ?>" class="dashboard-logo">
            </a>
        </div>
        <br>
    <a href="ad_accounts_direct.php" class="dashboard-card" style="text-decoration: none;">
                <div class="card-title"> ادارة الاكونتات</div>
                <div class="card-icon">
                    <i class="fas fa-users"></i>
                </div>
            </a>
        <div class="dashboard-grid">
            <!-- Row 1 -->
            <!-- Card 1: ادارة صفحات -->
            <a href="manage_pages.php" class="dashboard-card" style="text-decoration: none;">
                <div class="card-title">ادارة صفحات</div>
                <div class="card-icon">
                    <i class="fas fa-chart-line"></i>
                </div>
            </a>

            <!-- Card 2: عملاء الاعلانات -->
            <a href="ad_clients.php" class="dashboard-card" style="text-decoration: none;">
                <div class="card-title">عملاء الاعلانات</div>
                <div class="card-icon">
                    <i class="fas fa-bullhorn"></i>
                </div>
            </a>

            <!-- Row 2 -->
            <!-- Card 3: اكونتات فوري -->
            <a href="accounts_fawry.php" class="dashboard-card" style="text-decoration: none;">
                <div class="card-title">اكونتات فوري</div>
                <div class="card-icon">
                    <img src="<?php echo BASE_URL; ?>assets/images/fawry-icon.svg" alt="Fawry">
                </div>
            </a>

            <!-- Card 4: اكونتات كريديت كارد -->
            <a href="credit_cards.php" class="dashboard-card" style="text-decoration: none;">
                <div class="card-title">اكونتات كريديت كارد</div>
                <div class="card-icon">
                    <i class="fas fa-credit-card"></i>
                </div>
            </a>

            <!-- Row 3 -->
            <!-- Card 5: فيز البنوك -->
            <a href="visa_banks_dynamic.php" class="dashboard-card" style="text-decoration: none;">
                <div class="card-title">فيز البنوك</div>
                <div class="card-icon">
                    <i class="fas fa-money-check-alt"></i>
                </div>
            </a>

            <!-- Card 6: الميزانيات -->
                <a href="salaries.php" class="dashboard-card" style="text-decoration: none;">
                <div class="card-title">المرتبات</div>
                <div class="card-icon">
                    <i class="fas fa-wallet"></i>
                </div>
                </a>


            <!-- Row 4 -->
            <!-- Card 7: حسابات   -->
            <a href="accounts_dashboard.php" class="dashboard-card" style="text-decoration: none;">
                <div class="card-title">حسابات</div>
                <div class="card-icon">
                    <i class="fas fa-chart-pie"></i>
                </div>
            </a>

            <!-- Card 8: تحضير حسابات و اعلانات -->
            <div class="dashboard-card" id="prepareAccountsAdsBtn">
                <div class="card-title">تحضير حسابات و اعلانات</div>
                <div class="card-icon">
                    <i class="fas fa-file-invoice"></i>
                </div>
            </div>

            <!-- Row 5 -->

            
            <!-- Card 9: تفاصيل اخرى للصفحات -->
            <a href="page_details.php" class="dashboard-card" style="text-decoration: none;">
                <div class="card-title">تفاصيل اخرى للصفحات</div>
                <div class="card-icon">
                    <i class="fas fa-list-ul"></i>
                </div>
            </a>

            <!-- Card 10: متابعة العملاء -->
            <a href="clients.php" class="dashboard-card" style="text-decoration: none;">
                <div class="card-title">متابعة العملاء</div>
                <div class="card-icon">
                    <i class="fas fa-desktop"></i>
                </div>
            </a>
        </div>
    </div>

    <!-- Modal تحضير حسابات و اعلانات -->
    <div class="modal fade" id="prepareAccountsAdsModal" tabindex="-1" aria-labelledby="prepareAccountsAdsModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="prepareAccountsAdsModalLabel">تحضيرات حسابات و اعلانات</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-6 text-center">
                            <a href="accounts_preparation.php" class="btn btn-lg btn-outline-primary w-100 py-4">
                                <i class="fas fa-calculator mb-2" style="font-size: 24px;"></i>
                                <div>حسابات</div>
                            </a>
                        </div>
                        <div class="col-6 text-center">
                            <a href="ads_preparation.php" class="btn btn-lg btn-outline-primary w-100 py-4">
                                <i class="fas fa-ad mb-2" style="font-size: 24px;"></i>
                                <div>اعلانات</div>
                            </a>
                        </div>
                        
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="<?php echo BASE_URL; ?>assets/js/scripts.js"></script>

    <script>
        // تحضير حسابات و اعلانات
        document.getElementById('prepareAccountsAdsBtn').addEventListener('click', function() {
            const modal = new bootstrap.Modal(document.getElementById('prepareAccountsAdsModal'));
            modal.show();
        });
    </script>
</body>
</html>
