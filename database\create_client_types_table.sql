-- إنشاء جدول فئات العملاء
CREATE TABLE IF NOT EXISTS client_types (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    display_name VARCHAR(100) NOT NULL,
    description TEXT,
    color VARCHAR(7) DEFAULT '#4a56e2',
    is_active TINYINT(1) DEFAULT 1,
    sort_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- إدراج البيانات الافتراضية
INSERT INTO client_types (name, display_name, description, color, sort_order) VALUES
('A', 'Client A', 'فئة العملاء A', '#4a56e2', 1),
('B', 'Client B', 'فئة العملاء B', '#28a745', 2),
('C', 'Client C', 'فئة العملاء C', '#dc3545', 3);

-- تحديث جدول العملاء لربطه بجدول الفئات
ALTER TABLE clients ADD COLUMN client_type_id INT DEFAULT NULL;
ALTER TABLE clients ADD FOREIGN KEY (client_type_id) REFERENCES client_types(id);

-- تحديث البيانات الموجودة
UPDATE clients SET client_type_id = (
    SELECT id FROM client_types WHERE name = 
    CASE 
        WHEN clients.type = 'Client A' THEN 'A'
        WHEN clients.type = 'Client B' THEN 'B'
        WHEN clients.type = 'Client C' THEN 'C'
        ELSE 'A'
    END
) WHERE client_type_id IS NULL;
