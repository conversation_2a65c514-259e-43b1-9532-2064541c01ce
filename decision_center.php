<?php
// Include configuration
require_once 'config/config.php';

// Include database connection
require_once 'includes/db.php';

// Include helper functions
require_once 'includes/functions.php';

// Include authentication functions
require_once 'includes/auth.php';

// Check if user is logged in
if (!isLoggedIn()) {
    $_SESSION['flash_message'] = 'يجب تسجيل الدخول للوصول إلى هذه الصفحة';
    $_SESSION['flash_type'] = 'danger';
    redirect(BASE_URL . 'index.php');
}

// جلب بيانات الإعلانات والنتائج
try {
    // التحقق من وجود جدول النتائج
    $tables = $db->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);

    if (!in_array('ad_results', $tables)) {
        // إنشاء جدول نتائج الإعلانات إذا لم يكن موجودًا
        $db->exec("CREATE TABLE ad_results (
            id INT AUTO_INCREMENT PRIMARY KEY,
            ad_id INT NOT NULL,
            reach INT NOT NULL DEFAULT 0,
            engagement INT NOT NULL DEFAULT 0,
            messages INT NOT NULL DEFAULT 0,
            date DATE NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (ad_id) REFERENCES ads(id) ON DELETE CASCADE
        )");
    }

    // جلب الإعلانات مع النتائج
    $adsQuery = "SELECT a.id, a.type, a.post, a.cost, a.status, a.account,
                c.name AS client_name,
                r.reach, r.engagement, r.messages, r.date AS result_date
                FROM ads a
                LEFT JOIN clients c ON a.client_id = c.id
                LEFT JOIN ad_results r ON a.id = r.ad_id
                WHERE a.status = 'مستمر'
                ORDER BY r.reach DESC, a.id DESC";
    $adsStmt = $db->prepare($adsQuery);
    $adsStmt->execute();
    $ads = $adsStmt->fetchAll(PDO::FETCH_ASSOC);

    // تحضير البيانات للرسوم البيانية
    $clientNames = [];
    $reachData = [];
    $engagementData = [];
    $messagesData = [];
    $costData = [];
    $engagementRates = [];
    $messageRates = [];
    $costPerReach = [];
    $costPerEngagement = [];
    $costPerMessage = [];

    foreach ($ads as $ad) {
        if (!empty($ad['reach'])) {
            $clientNames[] = $ad['client_name'];
            $reachData[] = $ad['reach'];
            $engagementData[] = $ad['engagement'];
            $messagesData[] = $ad['messages'];
            $costData[] = $ad['cost'];

            // حساب المعدلات
            $engagementRates[] = $ad['reach'] > 0 ? round(($ad['engagement'] / $ad['reach']) * 100, 2) : 0;
            $messageRates[] = $ad['reach'] > 0 ? round(($ad['messages'] / $ad['reach']) * 100, 2) : 0;
            $costPerReach[] = $ad['reach'] > 0 ? round($ad['cost'] / $ad['reach'], 2) : 0;
            $costPerEngagement[] = $ad['engagement'] > 0 ? round($ad['cost'] / $ad['engagement'], 2) : 0;
            $costPerMessage[] = $ad['messages'] > 0 ? round($ad['cost'] / $ad['messages'], 2) : 0;
        }
    }

    // تحويل البيانات إلى تنسيق JSON للرسوم البيانية
    $chartData = [
        'clientNames' => json_encode($clientNames),
        'reachData' => json_encode($reachData),
        'engagementData' => json_encode($engagementData),
        'messagesData' => json_encode($messagesData),
        'costData' => json_encode($costData),
        'engagementRates' => json_encode($engagementRates),
        'messageRates' => json_encode($messageRates),
        'costPerReach' => json_encode($costPerReach),
        'costPerEngagement' => json_encode($costPerEngagement),
        'costPerMessage' => json_encode($costPerMessage)
    ];

} catch (PDOException $e) {
    $error = 'خطأ في قاعدة البيانات: ' . $e->getMessage();
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مركز اتخاذ القرار - <?php echo SITE_TITLE; ?></title>

    <!-- Bootstrap RTL CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Google Fonts - Cairo -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap">

    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f8f9fa;
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 20px;
            background-color: white;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .logo {
            height: 40px;
        }

        .page-title {
            color: #4a56e2;
            font-size: 24px;
            font-weight: 600;
            text-align: center;
            margin: 0;
        }

        .content-container {
            max-width: 1200px;
            margin: 30px auto;
            padding: 20px;
        }

        .chart-container {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            padding: 20px;
            margin-bottom: 30px;
        }

        .chart-title {
            color: #4a56e2;
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #e6e9ff;
        }

        .stats-card {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            padding: 20px;
            margin-bottom: 20px;
            transition: transform 0.2s;
        }

        .stats-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
        }

        .stats-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 10px;
        }

        .stats-value {
            font-size: 28px;
            font-weight: 700;
            color: #4a56e2;
            margin: 5px 0;
        }

        .stats-subtitle {
            font-size: 14px;
            color: #6c757d;
        }

        .text-purple {
            color: #8e44ad;
        }

        .bg-purple {
            background-color: #8e44ad;
        }

        .text-orange {
            color: #e67e22;
        }

        .bg-orange {
            background-color: #e67e22;
        }

        .text-teal {
            color: #20c997;
        }

        .bg-teal {
            background-color: #20c997;
        }

        .text-indigo {
            color: #6610f2;
        }

        .bg-indigo {
            background-color: #6610f2;
        }

        .text-pink {
            color: #e83e8c;
        }

        .bg-pink {
            background-color: #e83e8c;
        }

        .chart-container {
            transition: all 0.3s ease;
        }

        .chart-container:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
        }

        .recommendations-container {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            padding: 20px;
            margin-bottom: 30px;
        }

        .recommendations-title {
            color: #4a56e2;
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #e6e9ff;
        }
    </style>
</head>
<body>
    <div class="header">
        <a href="<?php echo BASE_URL; ?>dashboard.php">
            <img src="<?php echo BASE_URL; ?>assets/images/logo.png" alt="<?php echo SITE_TITLE; ?>" class="logo">
        </a>
        <h1 class="page-title">مركز اتخاذ القرار</h1>
        <div></div>
    </div>

    <div class="content-container">
        <?php if (isset($error)): ?>
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-circle me-2"></i>
                <?php echo $error; ?>
            </div>
        <?php else: ?>
            <!-- ملخص الإحصائيات -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="stats-card">
                        <div class="d-flex align-items-center mb-2">
                            <i class="fas fa-users fa-2x text-primary me-2"></i>
                            <div class="stats-title">إجمالي الوصول</div>
                        </div>
                        <div class="stats-value" id="totalReach">0</div>
                        <div class="stats-subtitle">شخص</div>
                        <div class="progress mt-2" style="height: 5px;">
                            <div class="progress-bar bg-primary" role="progressbar" style="width: 100%"></div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stats-card">
                        <div class="d-flex align-items-center mb-2">
                            <i class="fas fa-thumbs-up fa-2x text-success me-2"></i>
                            <div class="stats-title">إجمالي التفاعل</div>
                        </div>
                        <div class="stats-value" id="totalEngagement">0</div>
                        <div class="stats-subtitle">تفاعل</div>
                        <div class="progress mt-2" style="height: 5px;">
                            <div class="progress-bar bg-success" role="progressbar" style="width: 100%"></div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stats-card">
                        <div class="d-flex align-items-center mb-2">
                            <i class="fas fa-comment-dots fa-2x text-danger me-2"></i>
                            <div class="stats-title">إجمالي الرسائل</div>
                        </div>
                        <div class="stats-value" id="totalMessages">0</div>
                        <div class="stats-subtitle">رسالة</div>
                        <div class="progress mt-2" style="height: 5px;">
                            <div class="progress-bar bg-danger" role="progressbar" style="width: 100%"></div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stats-card">
                        <div class="d-flex align-items-center mb-2">
                            <i class="fas fa-chart-line fa-2x text-info me-2"></i>
                            <div class="stats-title">متوسط معدل التفاعل</div>
                        </div>
                        <div class="stats-value" id="avgEngagementRate">0%</div>
                        <div class="stats-subtitle">من إجمالي الوصول</div>
                        <div class="progress mt-2" style="height: 5px;">
                            <div class="progress-bar bg-info" role="progressbar" style="width: 100%"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- إحصائيات إضافية - الصف الأول -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="stats-card">
                        <div class="d-flex align-items-center mb-2">
                            <i class="fas fa-dollar-sign fa-2x text-warning me-2"></i>
                            <div class="stats-title">متوسط تكلفة التفاعل</div>
                        </div>
                        <div class="stats-value" id="avgCostPerEngagement">0</div>
                        <div class="stats-subtitle">جنيه / تفاعل</div>
                        <div class="progress mt-2" style="height: 5px;">
                            <div class="progress-bar bg-warning" role="progressbar" style="width: 100%"></div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stats-card">
                        <div class="d-flex align-items-center mb-2">
                            <i class="fas fa-comment-dollar fa-2x text-purple me-2"></i>
                            <div class="stats-title">متوسط تكلفة الرسالة</div>
                        </div>
                        <div class="stats-value" id="avgCostPerMessage">0</div>
                        <div class="stats-subtitle">جنيه / رسالة</div>
                        <div class="progress mt-2" style="height: 5px;">
                            <div class="progress-bar bg-purple" role="progressbar" style="width: 100%"></div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stats-card">
                        <div class="d-flex align-items-center mb-2">
                            <i class="fas fa-bullseye fa-2x text-teal me-2"></i>
                            <div class="stats-title">معدل الرسائل</div>
                        </div>
                        <div class="stats-value" id="messageConversionRate">0%</div>
                        <div class="stats-subtitle">من إجمالي الوصول</div>
                        <div class="progress mt-2" style="height: 5px;">
                            <div class="progress-bar bg-teal" role="progressbar" style="width: 100%"></div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stats-card">
                        <div class="d-flex align-items-center mb-2">
                            <i class="fas fa-trophy fa-2x text-orange me-2"></i>
                            <div class="stats-title">أداء أفضل إعلان</div>
                        </div>
                        <div class="stats-value" id="bestAdPerformance">-</div>
                        <div class="stats-subtitle">معدل التفاعل</div>
                        <div class="progress mt-2" style="height: 5px;">
                            <div class="progress-bar bg-orange" role="progressbar" style="width: 100%"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- إحصائيات إضافية - الصف الثاني -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="stats-card">
                        <div class="d-flex align-items-center mb-2">
                            <i class="fas fa-exchange-alt fa-2x text-indigo me-2"></i>
                            <div class="stats-title">معدل تحويل التفاعل</div>
                        </div>
                        <div class="stats-value" id="engagementToMessageRate">0%</div>
                        <div class="stats-subtitle">من التفاعل إلى رسائل</div>
                        <div class="progress mt-2" style="height: 5px;">
                            <div class="progress-bar bg-indigo" role="progressbar" style="width: 100%"></div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stats-card">
                        <div class="d-flex align-items-center mb-2">
                            <i class="fas fa-hand-holding-usd fa-2x text-success me-2"></i>
                            <div class="stats-title">العائد على الاستثمار</div>
                        </div>
                        <div class="stats-value" id="roi">0%</div>
                        <div class="stats-subtitle">ROI المقدر</div>
                        <div class="progress mt-2" style="height: 5px;">
                            <div class="progress-bar bg-success" role="progressbar" style="width: 100%"></div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stats-card">
                        <div class="d-flex align-items-center mb-2">
                            <i class="fas fa-bolt fa-2x text-danger me-2"></i>
                            <div class="stats-title">معدل الفعالية</div>
                        </div>
                        <div class="stats-value" id="effectivenessRate">0</div>
                        <div class="stats-subtitle">مؤشر الأداء المركب</div>
                        <div class="progress mt-2" style="height: 5px;">
                            <div class="progress-bar bg-danger" role="progressbar" style="width: 100%"></div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stats-card">
                        <div class="d-flex align-items-center mb-2">
                            <i class="fas fa-chart-pie fa-2x text-pink me-2"></i>
                            <div class="stats-title">نسبة الاستهداف الفعال</div>
                        </div>
                        <div class="stats-value" id="effectiveTargetingRate">0%</div>
                        <div class="stats-subtitle">من إجمالي الاستهداف</div>
                        <div class="progress mt-2" style="height: 5px;">
                            <div class="progress-bar bg-pink" role="progressbar" style="width: 100%"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- الرسوم البيانية -->
            <!-- لوحة المؤشرات الرئيسية -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="chart-container">
                        <div class="chart-title">
                            <i class="fas fa-tachometer-alt me-2"></i>
                            لوحة المؤشرات الرئيسية (KPI Dashboard)
                        </div>
                        <canvas id="kpiDashboardChart" height="100"></canvas>
                    </div>
                </div>
            </div>

            <!-- الرسوم البيانية الأساسية -->
            <div class="row">
                <div class="col-md-6">
                    <div class="chart-container">
                        <div class="chart-title">
                            <i class="fas fa-chart-bar me-2"></i>
                            مقارنة الوصول والتفاعل
                        </div>
                        <canvas id="reachEngagementChart"></canvas>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="chart-container">
                        <div class="chart-title">
                            <i class="fas fa-money-bill-wave me-2"></i>
                            مقارنة التكلفة والنتائج
                        </div>
                        <canvas id="costResultsChart"></canvas>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="chart-container">
                        <div class="chart-title">
                            <i class="fas fa-chart-pie me-2"></i>
                            معدلات التفاعل والرسائل
                        </div>
                        <canvas id="ratesChart"></canvas>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="chart-container">
                        <div class="chart-title">
                            <i class="fas fa-calculator me-2"></i>
                            تكلفة كل نتيجة
                        </div>
                        <canvas id="costPerResultChart"></canvas>
                    </div>
                </div>
            </div>

            <!-- رسوم بيانية متقدمة -->
            <div class="row">
                <div class="col-md-4">
                    <div class="chart-container">
                        <div class="chart-title">
                            <i class="fas fa-bullseye me-2"></i>
                            توزيع الاستهداف الجغرافي
                        </div>
                        <canvas id="locationChart"></canvas>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="chart-container">
                        <div class="chart-title">
                            <i class="fas fa-calendar-alt me-2"></i>
                            تطور النتائج عبر الزمن
                        </div>
                        <canvas id="timelineChart"></canvas>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="chart-container">
                        <div class="chart-title">
                            <i class="fas fa-percentage me-2"></i>
                            معدل التحويل
                        </div>
                        <canvas id="conversionChart"></canvas>
                    </div>
                </div>
            </div>

            <!-- تحليلات متقدمة -->
            <div class="row">
                <div class="col-md-6">
                    <div class="chart-container">
                        <div class="chart-title">
                            <i class="fas fa-project-diagram me-2"></i>
                            تحليل مسار التحويل (Funnel Analysis)
                        </div>
                        <canvas id="funnelChart" height="250"></canvas>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="chart-container">
                        <div class="chart-title">
                            <i class="fas fa-balance-scale me-2"></i>
                            مقارنة أداء الاستهداف (A/B Testing)
                        </div>
                        <canvas id="abTestingChart" height="250"></canvas>
                    </div>
                </div>
            </div>

            <!-- تحليلات الذكاء الاصطناعي -->
            <div class="row">
                <div class="col-md-12">
                    <div class="chart-container">
                        <div class="chart-title">
                            <i class="fas fa-brain me-2"></i>
                            تنبؤات الذكاء الاصطناعي للأداء المستقبلي
                        </div>
                        <canvas id="aiPredictionChart" height="150"></canvas>
                    </div>
                </div>
            </div>

            <!-- التوصيات والتحليلات -->
            <div class="recommendations-container">
                <div class="recommendations-title">
                    <i class="fas fa-lightbulb me-2"></i>
                    التوصيات والتحليلات المتقدمة
                </div>
                <div id="recommendations"></div>

                <!-- تحليلات إضافية -->
                <div class="row mt-4">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header bg-primary text-white">
                                <i class="fas fa-brain me-2"></i>
                                تحليل الذكاء الاصطناعي
                            </div>
                            <div class="card-body">
                                <div id="aiAnalysis">
                                    <p>يقوم نظام الذكاء الاصطناعي بتحليل بيانات الإعلانات وتقديم توصيات مخصصة لتحسين الأداء.</p>
                                    <div class="alert alert-info">
                                        <i class="fas fa-robot me-2"></i>
                                        <strong>توصية الذكاء الاصطناعي:</strong> بناءً على تحليل البيانات، يُنصح بزيادة الاستثمار في الإعلانات ذات معدل التفاعل المرتفع وتحسين استهداف الإعلانات ذات الأداء المنخفض.
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header bg-success text-white">
                                <i class="fas fa-chart-line me-2"></i>
                                توقعات الأداء المستقبلي
                            </div>
                            <div class="card-body">
                                <div id="predictions">
                                    <p>توقعات الأداء المستقبلي بناءً على تحليل الاتجاهات الحالية:</p>
                                    <div class="progress mb-3" style="height: 25px;">
                                        <div class="progress-bar bg-success" role="progressbar" style="width: 75%;" aria-valuenow="75" aria-valuemin="0" aria-valuemax="100">
                                            <span class="fw-bold">الوصول: +75%</span>
                                        </div>
                                    </div>
                                    <div class="progress mb-3" style="height: 25px;">
                                        <div class="progress-bar bg-info" role="progressbar" style="width: 60%;" aria-valuenow="60" aria-valuemin="0" aria-valuemax="100">
                                            <span class="fw-bold">التفاعل: +60%</span>
                                        </div>
                                    </div>
                                    <div class="progress mb-3" style="height: 25px;">
                                        <div class="progress-bar bg-warning" role="progressbar" style="width: 45%;" aria-valuenow="45" aria-valuemin="0" aria-valuemax="100">
                                            <span class="fw-bold">الرسائل: +45%</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- جدول تفصيلي للإعلانات -->
            <div class="chart-container">
                <div class="chart-title">
                    <i class="fas fa-table me-2"></i>
                    تفاصيل أداء الإعلانات
                </div>
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th><i class="fas fa-user me-1"></i> العميل</th>
                                <th><i class="fas fa-id-card me-1"></i> الحساب</th>
                                <th><i class="fas fa-money-bill-alt me-1"></i> التكلفة</th>
                                <th><i class="fas fa-users me-1"></i> الوصول</th>
                                <th><i class="fas fa-thumbs-up me-1"></i> التفاعل</th>
                                <th><i class="fas fa-comment me-1"></i> الرسائل</th>
                                <th><i class="fas fa-percentage me-1"></i> معدل التفاعل</th>
                                <th><i class="fas fa-calculator me-1"></i> تكلفة التفاعل</th>
                                <th><i class="fas fa-star me-1"></i> التقييم</th>
                            </tr>
                        </thead>
                        <tbody id="adsDetailsTable">
                            <!-- سيتم ملء هذا الجدول بواسطة JavaScript -->
                        </tbody>
                    </table>
                </div>

                <!-- أزرار التصدير والطباعة -->
                <div class="d-flex justify-content-end mt-3">
                    <button class="btn btn-outline-primary me-2">
                        <i class="fas fa-file-excel me-1"></i> تصدير إلى Excel
                    </button>
                    <button class="btn btn-outline-secondary me-2">
                        <i class="fas fa-file-pdf me-1"></i> تصدير إلى PDF
                    </button>
                    <button class="btn btn-outline-dark">
                        <i class="fas fa-print me-1"></i> طباعة التقرير
                    </button>
                </div>
            </div>
        <?php endif; ?>
    </div>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <script>
        $(document).ready(function() {
            // البيانات من PHP
            const clientNames = <?php echo $chartData['clientNames'] ?? '[]'; ?>;
            const reachData = <?php echo $chartData['reachData'] ?? '[]'; ?>;
            const engagementData = <?php echo $chartData['engagementData'] ?? '[]'; ?>;
            const messagesData = <?php echo $chartData['messagesData'] ?? '[]'; ?>;
            const costData = <?php echo $chartData['costData'] ?? '[]'; ?>;
            const engagementRates = <?php echo $chartData['engagementRates'] ?? '[]'; ?>;
            const messageRates = <?php echo $chartData['messageRates'] ?? '[]'; ?>;
            const costPerReach = <?php echo $chartData['costPerReach'] ?? '[]'; ?>;
            const costPerEngagement = <?php echo $chartData['costPerEngagement'] ?? '[]'; ?>;
            const costPerMessage = <?php echo $chartData['costPerMessage'] ?? '[]'; ?>;

            // حساب الإجماليات
            const totalReach = reachData.reduce((sum, value) => sum + value, 0);
            const totalEngagement = engagementData.reduce((sum, value) => sum + value, 0);
            const totalMessages = messagesData.reduce((sum, value) => sum + value, 0);
            const avgEngagementRate = totalReach > 0 ? (totalEngagement / totalReach * 100).toFixed(2) : 0;

            // عرض الإجماليات
            $('#totalReach').text(totalReach.toLocaleString());
            $('#totalEngagement').text(totalEngagement.toLocaleString());
            $('#totalMessages').text(totalMessages.toLocaleString());
            $('#avgEngagementRate').text(avgEngagementRate + '%');

            // إنشاء الرسوم البيانية
            if (clientNames.length > 0) {
                // رسم بياني للوصول والتفاعل
                const reachEngagementCtx = document.getElementById('reachEngagementChart').getContext('2d');
                new Chart(reachEngagementCtx, {
                    type: 'bar',
                    data: {
                        labels: clientNames,
                        datasets: [
                            {
                                label: 'الوصول',
                                data: reachData,
                                backgroundColor: 'rgba(54, 162, 235, 0.5)',
                                borderColor: 'rgba(54, 162, 235, 1)',
                                borderWidth: 1
                            },
                            {
                                label: 'التفاعل',
                                data: engagementData,
                                backgroundColor: 'rgba(75, 192, 192, 0.5)',
                                borderColor: 'rgba(75, 192, 192, 1)',
                                borderWidth: 1
                            },
                            {
                                label: 'الرسائل',
                                data: messagesData,
                                backgroundColor: 'rgba(255, 99, 132, 0.5)',
                                borderColor: 'rgba(255, 99, 132, 1)',
                                borderWidth: 1
                            }
                        ]
                    },
                    options: {
                        responsive: true,
                        scales: {
                            y: {
                                beginAtZero: true
                            }
                        }
                    }
                });

                // رسم بياني للتكلفة والنتائج
                const costResultsCtx = document.getElementById('costResultsChart').getContext('2d');
                new Chart(costResultsCtx, {
                    type: 'line',
                    data: {
                        labels: clientNames,
                        datasets: [
                            {
                                label: 'التكلفة',
                                data: costData,
                                backgroundColor: 'rgba(153, 102, 255, 0.5)',
                                borderColor: 'rgba(153, 102, 255, 1)',
                                borderWidth: 2,
                                fill: false,
                                tension: 0.1
                            }
                        ]
                    },
                    options: {
                        responsive: true,
                        scales: {
                            y: {
                                beginAtZero: true
                            }
                        }
                    }
                });

                // رسم بياني لمعدلات التفاعل والرسائل
                const ratesCtx = document.getElementById('ratesChart').getContext('2d');
                new Chart(ratesCtx, {
                    type: 'radar',
                    data: {
                        labels: clientNames,
                        datasets: [
                            {
                                label: 'معدل التفاعل (%)',
                                data: engagementRates,
                                backgroundColor: 'rgba(75, 192, 192, 0.2)',
                                borderColor: 'rgba(75, 192, 192, 1)',
                                borderWidth: 2,
                                pointBackgroundColor: 'rgba(75, 192, 192, 1)'
                            },
                            {
                                label: 'معدل الرسائل (%)',
                                data: messageRates,
                                backgroundColor: 'rgba(255, 99, 132, 0.2)',
                                borderColor: 'rgba(255, 99, 132, 1)',
                                borderWidth: 2,
                                pointBackgroundColor: 'rgba(255, 99, 132, 1)'
                            }
                        ]
                    },
                    options: {
                        responsive: true,
                        scales: {
                            r: {
                                beginAtZero: true
                            }
                        }
                    }
                });

                // رسم بياني لتكلفة كل نتيجة
                const costPerResultCtx = document.getElementById('costPerResultChart').getContext('2d');
                new Chart(costPerResultCtx, {
                    type: 'bar',
                    data: {
                        labels: clientNames,
                        datasets: [
                            {
                                label: 'تكلفة الوصول',
                                data: costPerReach,
                                backgroundColor: 'rgba(54, 162, 235, 0.5)',
                                borderColor: 'rgba(54, 162, 235, 1)',
                                borderWidth: 1
                            },
                            {
                                label: 'تكلفة التفاعل',
                                data: costPerEngagement,
                                backgroundColor: 'rgba(75, 192, 192, 0.5)',
                                borderColor: 'rgba(75, 192, 192, 1)',
                                borderWidth: 1
                            },
                            {
                                label: 'تكلفة الرسالة',
                                data: costPerMessage,
                                backgroundColor: 'rgba(255, 99, 132, 0.5)',
                                borderColor: 'rgba(255, 99, 132, 1)',
                                borderWidth: 1
                            }
                        ]
                    },
                    options: {
                        responsive: true,
                        scales: {
                            y: {
                                beginAtZero: true
                            }
                        }
                    }
                });

                // إنشاء جدول تفصيلي للإعلانات
                let tableHTML = '';
                for (let i = 0; i < clientNames.length; i++) {
                    const engagementRate = engagementRates[i] || 0;
                    const costPerEngagementValue = costPerEngagement[i] || 0;

                    // تحديد تقييم الأداء
                    let rating = '';
                    let ratingClass = '';

                    if (engagementRate > 5) {
                        rating = '<i class="fas fa-star text-warning"></i><i class="fas fa-star text-warning"></i><i class="fas fa-star text-warning"></i><i class="fas fa-star text-warning"></i><i class="fas fa-star text-warning"></i>';
                        ratingClass = 'table-success';
                    } else if (engagementRate > 3) {
                        rating = '<i class="fas fa-star text-warning"></i><i class="fas fa-star text-warning"></i><i class="fas fa-star text-warning"></i><i class="fas fa-star text-warning"></i><i class="far fa-star text-warning"></i>';
                        ratingClass = 'table-info';
                    } else if (engagementRate > 1) {
                        rating = '<i class="fas fa-star text-warning"></i><i class="fas fa-star text-warning"></i><i class="fas fa-star text-warning"></i><i class="far fa-star text-warning"></i><i class="far fa-star text-warning"></i>';
                        ratingClass = '';
                    } else {
                        rating = '<i class="fas fa-star text-warning"></i><i class="fas fa-star text-warning"></i><i class="far fa-star text-warning"></i><i class="far fa-star text-warning"></i><i class="far fa-star text-warning"></i>';
                        ratingClass = 'table-warning';
                    }

                    tableHTML += `
                        <tr class="${ratingClass}">
                            <td><strong>${clientNames[i]}</strong></td>
                            <td>${clientNames[i]}</td>
                            <td>${costData[i].toLocaleString()} جنيه</td>
                            <td>${reachData[i].toLocaleString()}</td>
                            <td>${engagementData[i].toLocaleString()}</td>
                            <td>${messagesData[i].toLocaleString()}</td>
                            <td><span class="badge bg-info">${engagementRate.toFixed(2)}%</span></td>
                            <td><span class="badge bg-warning">${costPerEngagementValue.toFixed(2)} جنيه</span></td>
                            <td>${rating}</td>
                        </tr>
                    `;
                }
                $('#adsDetailsTable').html(tableHTML);

                // إضافة بيانات للرسوم البيانية الإضافية
                if (clientNames.length > 0) {
                    // رسم بياني للتوزيع الجغرافي (مثال)
                    const locationCtx = document.getElementById('locationChart').getContext('2d');
                    new Chart(locationCtx, {
                        type: 'doughnut',
                        data: {
                            labels: ['القاهرة', 'الإسكندرية', 'المنصورة', 'أسيوط', 'الغربية'],
                            datasets: [{
                                data: [45, 25, 15, 10, 5],
                                backgroundColor: [
                                    'rgba(54, 162, 235, 0.7)',
                                    'rgba(75, 192, 192, 0.7)',
                                    'rgba(255, 99, 132, 0.7)',
                                    'rgba(255, 206, 86, 0.7)',
                                    'rgba(153, 102, 255, 0.7)'
                                ],
                                borderWidth: 1
                            }]
                        },
                        options: {
                            responsive: true
                        }
                    });

                    // رسم بياني للتطور عبر الزمن (مثال)
                    const timelineCtx = document.getElementById('timelineChart').getContext('2d');
                    new Chart(timelineCtx, {
                        type: 'line',
                        data: {
                            labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
                            datasets: [{
                                label: 'الوصول',
                                data: [1200, 1900, 3000, 5000, 8000, 12000],
                                borderColor: 'rgba(54, 162, 235, 1)',
                                backgroundColor: 'rgba(54, 162, 235, 0.1)',
                                borderWidth: 2,
                                fill: true,
                                tension: 0.4
                            }]
                        },
                        options: {
                            responsive: true,
                            scales: {
                                y: {
                                    beginAtZero: true
                                }
                            }
                        }
                    });

                    // رسم بياني لمعدل التحويل (مثال)
                    const conversionCtx = document.getElementById('conversionChart').getContext('2d');
                    new Chart(conversionCtx, {
                        type: 'bar',
                        data: {
                            labels: ['وصول → تفاعل', 'تفاعل → رسائل', 'رسائل → تحويل'],
                            datasets: [{
                                label: 'معدل التحويل (%)',
                                data: [
                                    (totalEngagement / totalReach * 100).toFixed(2),
                                    (totalMessages / totalEngagement * 100).toFixed(2),
                                    25.5 // قيمة افتراضية
                                ],
                                backgroundColor: [
                                    'rgba(75, 192, 192, 0.7)',
                                    'rgba(255, 99, 132, 0.7)',
                                    'rgba(255, 206, 86, 0.7)'
                                ],
                                borderWidth: 1
                            }]
                        },
                        options: {
                            responsive: true,
                            scales: {
                                y: {
                                    beginAtZero: true,
                                    max: 100
                                }
                            }
                        }
                    });
                }

                // تحديث الإحصائيات الإضافية
                const avgCostPerEngagement = costPerEngagement.reduce((sum, val) => sum + val, 0) / costPerEngagement.length;
                const avgCostPerMessage = costPerMessage.reduce((sum, val) => sum + val, 0) / costPerMessage.length;
                const messageConversionRate = totalReach > 0 ? (totalMessages / totalReach * 100).toFixed(2) : 0;
                const engagementToMessageRate = totalEngagement > 0 ? (totalMessages / totalEngagement * 100).toFixed(2) : 0;

                // حساب مؤشرات متقدمة
                const estimatedROI = 250; // قيمة افتراضية للعائد على الاستثمار
                const effectivenessRate = ((parseFloat(messageConversionRate) * 0.5) + (parseFloat(engagementToMessageRate) * 0.3) + (estimatedROI * 0.2) / 100).toFixed(2);
                const effectiveTargetingRate = 68.5; // قيمة افتراضية لنسبة الاستهداف الفعال

                // تحديث العناصر في الواجهة
                $('#avgCostPerEngagement').text(avgCostPerEngagement.toFixed(2));
                $('#avgCostPerMessage').text(avgCostPerMessage.toFixed(2));
                $('#messageConversionRate').text(messageConversionRate + '%');
                $('#engagementToMessageRate').text(engagementToMessageRate + '%');
                $('#roi').text(estimatedROI + '%');
                $('#effectivenessRate').text(effectivenessRate);
                $('#effectiveTargetingRate').text(effectiveTargetingRate + '%');

                // تحديد أفضل إعلان
                if (engagementRates.length > 0) {
                    const bestAdIndex = engagementRates.indexOf(Math.max(...engagementRates));
                    $('#bestAdPerformance').text(engagementRates[bestAdIndex].toFixed(2) + '%');
                }

                // إنشاء لوحة المؤشرات الرئيسية
                const kpiDashboardCtx = document.getElementById('kpiDashboardChart').getContext('2d');
                new Chart(kpiDashboardCtx, {
                    type: 'bar',
                    data: {
                        labels: ['الوصول', 'التفاعل', 'الرسائل', 'معدل التفاعل (%)', 'معدل الرسائل (%)', 'تكلفة التفاعل', 'تكلفة الرسالة'],
                        datasets: [{
                            label: 'المؤشرات الرئيسية',
                            data: [
                                totalReach / 1000, // تقسيم على 1000 للعرض
                                totalEngagement / 100,
                                totalMessages * 10,
                                parseFloat(avgEngagementRate),
                                parseFloat(messageConversionRate),
                                avgCostPerEngagement * 10,
                                avgCostPerMessage
                            ],
                            backgroundColor: [
                                'rgba(54, 162, 235, 0.7)',
                                'rgba(75, 192, 192, 0.7)',
                                'rgba(255, 99, 132, 0.7)',
                                'rgba(255, 206, 86, 0.7)',
                                'rgba(153, 102, 255, 0.7)',
                                'rgba(255, 159, 64, 0.7)',
                                'rgba(201, 203, 207, 0.7)'
                            ],
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        scales: {
                            y: {
                                beginAtZero: true,
                                title: {
                                    display: true,
                                    text: 'القيمة (مقياس نسبي)'
                                }
                            }
                        },
                        plugins: {
                            tooltip: {
                                callbacks: {
                                    label: function(context) {
                                        const label = context.dataset.label || '';
                                        const value = context.raw;
                                        const index = context.dataIndex;

                                        // تنسيق مختلف حسب نوع المؤشر
                                        if (index === 0) return `${label}: ${(value * 1000).toLocaleString()} وصول`;
                                        if (index === 1) return `${label}: ${(value * 100).toLocaleString()} تفاعل`;
                                        if (index === 2) return `${label}: ${(value / 10).toLocaleString()} رسالة`;
                                        if (index === 3) return `${label}: ${value}% معدل تفاعل`;
                                        if (index === 4) return `${label}: ${value}% معدل رسائل`;
                                        if (index === 5) return `${label}: ${(value / 10).toFixed(2)} جنيه/تفاعل`;
                                        if (index === 6) return `${label}: ${value.toFixed(2)} جنيه/رسالة`;

                                        return `${label}: ${value}`;
                                    }
                                }
                            }
                        }
                    }
                });

                // إنشاء رسم بياني لتحليل مسار التحويل
                const funnelCtx = document.getElementById('funnelChart').getContext('2d');
                new Chart(funnelCtx, {
                    type: 'bar',
                    data: {
                        labels: ['الوصول', 'التفاعل', 'الرسائل', 'العملاء المحتملين', 'العملاء'],
                        datasets: [{
                            label: 'عدد المستخدمين',
                            data: [
                                totalReach,
                                totalEngagement,
                                totalMessages,
                                Math.round(totalMessages * 0.4), // قيمة افتراضية للعملاء المحتملين
                                Math.round(totalMessages * 0.15)  // قيمة افتراضية للعملاء
                            ],
                            backgroundColor: [
                                'rgba(54, 162, 235, 0.7)',
                                'rgba(75, 192, 192, 0.7)',
                                'rgba(255, 99, 132, 0.7)',
                                'rgba(255, 206, 86, 0.7)',
                                'rgba(153, 102, 255, 0.7)'
                            ],
                            borderWidth: 1
                        }]
                    },
                    options: {
                        indexAxis: 'y',
                        responsive: true,
                        scales: {
                            x: {
                                beginAtZero: true
                            }
                        }
                    }
                });

                // إنشاء رسم بياني لمقارنة أداء الاستهداف (A/B Testing)
                const abTestingCtx = document.getElementById('abTestingChart').getContext('2d');
                new Chart(abTestingCtx, {
                    type: 'radar',
                    data: {
                        labels: ['معدل الوصول', 'معدل التفاعل', 'معدل الرسائل', 'تكلفة التفاعل', 'تكلفة الرسالة', 'معدل التحويل'],
                        datasets: [
                            {
                                label: 'استهداف A (القاهرة)',
                                data: [85, 4.2, 1.8, 12.5, 35.2, 2.1],
                                backgroundColor: 'rgba(54, 162, 235, 0.2)',
                                borderColor: 'rgba(54, 162, 235, 1)',
                                borderWidth: 2,
                                pointBackgroundColor: 'rgba(54, 162, 235, 1)'
                            },
                            {
                                label: 'استهداف B (الإسكندرية)',
                                data: [75, 3.8, 2.2, 14.1, 28.7, 2.5],
                                backgroundColor: 'rgba(255, 99, 132, 0.2)',
                                borderColor: 'rgba(255, 99, 132, 1)',
                                borderWidth: 2,
                                pointBackgroundColor: 'rgba(255, 99, 132, 1)'
                            }
                        ]
                    },
                    options: {
                        responsive: true,
                        scales: {
                            r: {
                                beginAtZero: true
                            }
                        }
                    }
                });

                // إنشاء رسم بياني لتنبؤات الذكاء الاصطناعي
                const aiPredictionCtx = document.getElementById('aiPredictionChart').getContext('2d');
                new Chart(aiPredictionCtx, {
                    type: 'line',
                    data: {
                        labels: ['الأسبوع 1', 'الأسبوع 2', 'الأسبوع 3', 'الأسبوع 4', 'الأسبوع 5 (توقع)', 'الأسبوع 6 (توقع)', 'الأسبوع 7 (توقع)', 'الأسبوع 8 (توقع)'],
                        datasets: [
                            {
                                label: 'الوصول الفعلي',
                                data: [5000, 7500, 10000, 12500, null, null, null, null],
                                borderColor: 'rgba(54, 162, 235, 1)',
                                backgroundColor: 'rgba(54, 162, 235, 0.1)',
                                borderWidth: 2,
                                fill: true
                            },
                            {
                                label: 'الوصول المتوقع',
                                data: [null, null, null, 12500, 15000, 18000, 22000, 27000],
                                borderColor: 'rgba(54, 162, 235, 1)',
                                backgroundColor: 'rgba(54, 162, 235, 0.1)',
                                borderWidth: 2,
                                borderDash: [5, 5],
                                fill: true
                            },
                            {
                                label: 'التفاعل الفعلي',
                                data: [250, 375, 500, 625, null, null, null, null],
                                borderColor: 'rgba(75, 192, 192, 1)',
                                backgroundColor: 'rgba(75, 192, 192, 0.1)',
                                borderWidth: 2,
                                fill: true
                            },
                            {
                                label: 'التفاعل المتوقع',
                                data: [null, null, null, 625, 750, 900, 1100, 1350],
                                borderColor: 'rgba(75, 192, 192, 1)',
                                backgroundColor: 'rgba(75, 192, 192, 0.1)',
                                borderWidth: 2,
                                borderDash: [5, 5],
                                fill: true
                            },
                            {
                                label: 'الرسائل الفعلية',
                                data: [50, 75, 100, 125, null, null, null, null],
                                borderColor: 'rgba(255, 99, 132, 1)',
                                backgroundColor: 'rgba(255, 99, 132, 0.1)',
                                borderWidth: 2,
                                fill: true
                            },
                            {
                                label: 'الرسائل المتوقعة',
                                data: [null, null, null, 125, 150, 180, 220, 270],
                                borderColor: 'rgba(255, 99, 132, 1)',
                                backgroundColor: 'rgba(255, 99, 132, 0.1)',
                                borderWidth: 2,
                                borderDash: [5, 5],
                                fill: true
                            }
                        ]
                    },
                    options: {
                        responsive: true,
                        scales: {
                            y: {
                                beginAtZero: true,
                                title: {
                                    display: true,
                                    text: 'العدد'
                                }
                            }
                        },
                        plugins: {
                            tooltip: {
                                mode: 'index',
                                intersect: false
                            },
                            legend: {
                                position: 'top'
                            }
                        }
                    }
                });

                // إنشاء التوصيات والتحليلات
                let recommendationsHTML = '';

                // تحديد أفضل إعلان من حيث التكلفة مقابل النتائج
                const bestValueIndex = costPerEngagement.indexOf(Math.min(...costPerEngagement.filter(val => val > 0)));
                if (bestValueIndex !== -1) {
                    recommendationsHTML += `
                        <div class="alert alert-success">
                            <strong>أفضل قيمة:</strong> الإعلان "${clientNames[bestValueIndex]}" يحقق أفضل قيمة مقابل التكلفة بمعدل ${costPerEngagement[bestValueIndex].toFixed(2)} جنيه لكل تفاعل.
                        </div>
                    `;
                }

                // تحديد الإعلانات ذات الأداء المنخفض
                const lowPerformingIndices = engagementRates.map((rate, index) => rate < 1 ? index : -1).filter(index => index !== -1);
                if (lowPerformingIndices.length > 0) {
                    recommendationsHTML += `
                        <div class="alert alert-warning">
                            <strong>تحسين الأداء:</strong> ${lowPerformingIndices.length} من الإعلانات تحقق معدل تفاعل أقل من 1%. يُنصح بمراجعة استهدافها أو محتواها.
                        </div>
                    `;
                }

                // تحليل عام للأداء
                recommendationsHTML += `
                    <div class="alert alert-info">
                        <strong>تحليل عام:</strong> متوسط معدل التفاعل هو ${avgEngagementRate}% ومتوسط تكلفة التفاعل هو ${(costPerEngagement.reduce((sum, val) => sum + val, 0) / costPerEngagement.length).toFixed(2)} جنيه.
                    </div>
                `;

                // إضافة توصيات إضافية
                recommendationsHTML += `
                    <div class="alert alert-primary">
                        <strong>توصية:</strong> يمكن تحسين أداء الإعلانات من خلال استهداف أكثر دقة وتحسين المحتوى الإعلاني لزيادة معدلات التفاعل والرسائل.
                    </div>
                `;

                $('#recommendations').html(recommendationsHTML);
            } else {
                // لا توجد بيانات كافية
                $('#recommendations').html('<div class="alert alert-warning">لا توجد بيانات كافية لإنشاء التوصيات والتحليلات. يرجى إضافة نتائج للإعلانات أولاً.</div>');
            }
        });
    </script>
</body>
</html>
