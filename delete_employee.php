<?php
// تضمين ملف الإعدادات
require_once 'config/config.php';

// تضمين اتصال قاعدة البيانات
require_once 'includes/db.php';

// تضمين الدوال المساعدة
require_once 'includes/functions.php';

// تضمين دوال المصادقة
require_once 'includes/auth.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'يجب تسجيل الدخول للوصول إلى هذه الخدمة']);
    exit;
}

// التحقق من طريقة الطلب
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'طريقة الطلب غير صحيحة']);
    exit;
}

// التحقق من البيانات المطلوبة
if (!isset($_POST['employee_id']) || empty($_POST['employee_id'])) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'معرف الموظف مطلوب']);
    exit;
}

// إعداد البيانات
$employeeId = intval($_POST['employee_id']);

try {
    // التحقق من وجود الموظف
    $checkQuery = "SELECT id, employee_name FROM salaries WHERE id = :id";
    $stmt = $db->prepare($checkQuery);
    $stmt->bindParam(':id', $employeeId);
    $stmt->execute();
    
    if ($stmt->rowCount() === 0) {
        header('Content-Type: application/json');
        echo json_encode(['success' => false, 'message' => 'الموظف غير موجود']);
        exit;
    }
    
    $employee = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // بدء المعاملة
    $db->beginTransaction();
    
    // حذف الموظف
    $deleteQuery = "DELETE FROM salaries WHERE id = :id";
    $stmt = $db->prepare($deleteQuery);
    $stmt->bindParam(':id', $employeeId);
    $stmt->execute();
    
    // تأكيد المعاملة
    $db->commit();
    
    // إرجاع استجابة نجاح
    header('Content-Type: application/json');
    echo json_encode([
        'success' => true, 
        'message' => 'تم حذف الموظف ' . $employee['employee_name'] . ' بنجاح'
    ]);
    
} catch (PDOException $e) {
    // التراجع عن المعاملة في حالة حدوث خطأ
    if ($db->inTransaction()) {
        $db->rollBack();
    }
    
    // إرجاع استجابة خطأ
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'حدث خطأ أثناء حذف الموظف: ' . $e->getMessage()]);
}
