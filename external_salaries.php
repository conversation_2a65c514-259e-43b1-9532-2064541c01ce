<?php
// تضمين ملف الإعدادات
require_once 'config/config.php';

// تضمين اتصال قاعدة البيانات
require_once 'includes/db.php';

// تضمين الدوال المساعدة
require_once 'includes/functions.php';

// تضمين دوال المصادقة
require_once 'includes/auth.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    $_SESSION['flash_message'] = 'يجب تسجيل الدخول للوصول إلى صفحة المرتبات';
    $_SESSION['flash_type'] = 'danger';
    redirect(BASE_URL . 'index.php');
}

// الحصول على الفلتر المحدد
$roleFilter = isset($_GET['role']) ? $_GET['role'] : 'all';
$searchTerm = isset($_GET['search']) ? $_GET['search'] : '';

try {
    // جلب جميع الأدوار المتاحة
    $rolesQuery = "SELECT DISTINCT role FROM salaries WHERE is_office = 0 ORDER BY role ASC";
    $stmt = $db->prepare($rolesQuery);
    $stmt->execute();
    $availableRoles = $stmt->fetchAll(PDO::FETCH_COLUMN);

    // بناء استعلام جلب الموظفين
    $query = "SELECT id, employee_name, amount, payment_type, role FROM salaries WHERE is_office = 0";

    // إضافة شرط الفلتر حسب الوظيفة
    if ($roleFilter !== 'all') {
        $query .= " AND role = :role";
    }

    // إضافة شرط البحث
    if (!empty($searchTerm)) {
        $query .= " AND employee_name LIKE :search_term";
    }

    // ترتيب النتائج
    $query .= " ORDER BY employee_name ASC";

    $stmt = $db->prepare($query);

    // ربط معلمات الاستعلام
    if ($roleFilter !== 'all') {
        $stmt->bindParam(':role', $roleFilter);
    }

    if (!empty($searchTerm)) {
        $searchParam = '%' . $searchTerm . '%';
        $stmt->bindParam(':search_term', $searchParam);
    }

    $stmt->execute();
    $externalEmployees = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // حساب إجمالي المرتبات
    $totalQuery = "SELECT SUM(amount) as total FROM salaries WHERE is_office = 0";

    // إضافة شرط الفلتر حسب الوظيفة
    if ($roleFilter !== 'all') {
        $totalQuery .= " AND role = :role";
    }

    $stmt = $db->prepare($totalQuery);

    // ربط معلمات الاستعلام
    if ($roleFilter !== 'all') {
        $stmt->bindParam(':role', $roleFilter);
    }

    $stmt->execute();
    $totalSalaries = $stmt->fetch(PDO::FETCH_ASSOC)['total'] ?? 0;

} catch (PDOException $e) {
    $dbErrorMessage = '<div class="alert alert-danger">خطأ في قاعدة البيانات: ' . $e->getMessage() . '</div>';
}

// تنظيم الموظفين في أعمدة
$columns = [[], [], []];
$employeesCount = count($externalEmployees);
$employeesPerColumn = ceil($employeesCount / 3);

for ($i = 0; $i < $employeesCount; $i++) {
    $columnIndex = floor($i / $employeesPerColumn);
    if ($columnIndex > 2) $columnIndex = 2; // للتأكد من عدم تجاوز 3 أعمدة
    $columns[$columnIndex][] = $externalEmployees[$i];
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مرتبات الموظفين الخارجيين - <?php echo SITE_TITLE; ?></title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap');

        body {
            font-family: 'Tajawal', sans-serif;
            background-color: #fff;
            margin: 0;
            padding: 0;
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 20px;
            border-bottom: 1px solid #e0e0e0;
        }

        .logo {
            height: 40px;
        }

        .page-title {
            color: #4a4ad4;
            text-align: center;
            font-size: 24px;
            font-weight: bold;
        }

        .filter-bar {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 10px 20px;
            border-bottom: 1px solid #e0e0e0;
        }

        .filter-icon {
            color: #4a4ad4;
            font-size: 20px;
        }

        .filter-dropdown {
            margin-right: 10px;
        }

        .filter-dropdown select {
            border-radius: 20px;
            border-color: #e0e0e0;
            color: #4a4ad4;
            font-weight: bold;
            padding: 5px 15px;
            width: 150px;
        }

        .add-button {
            margin-right: 10px;
        }

        .add-button .btn-primary {
            background-color: #4a4ad4;
            border-color: #4a4ad4;
            border-radius: 20px;
        }

        .search-box {
            margin-right: auto;
            position: relative;
        }

        .search-input {
            border: 1px solid #e0e0e0;
            border-radius: 20px;
            padding: 5px 15px 5px 35px;
            width: 300px;
        }

        .search-icon {
            position: absolute;
            left: 10px;
            top: 50%;
            transform: translateY(-50%);
            color: #999;
        }

        .table-container {
            position: relative;
            margin-top: 20px;
            height: calc(100vh - 150px);
            overflow: hidden;
        }

        .salaries-table {
            width: 100%;
            border-collapse: collapse;
        }

        .salaries-table th {
            background-color: #4a4ad4;
            color: white;
            text-align: center;
            padding: 10px;
            font-weight: bold;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .salaries-table tfoot td {
            background-color: #f5f5f5;
            position: sticky;
            bottom: 0;
            z-index: 10;
            border-top: 2px solid #dcf343;
            font-weight: bold;
        }

        .table-body-container {
            height: calc(100vh - 230px);
            overflow-y: auto;
        }

        .salaries-table td {
            border: 1px solid #dcf343;
            padding: 10px;
            text-align: center;
        }

        .salaries-table tbody tr:hover {
            background-color: #f9f9f9;
        }

        .employee-name {
            color: #4a4ad4;
            font-weight: bold;
        }

        .payment-type {
            font-size: 0.8em;
            color: #666;
            display: flex;
            justify-content: space-between;
            margin-top: 3px;
        }

        .role-badge {
            background-color: #f0f0f0;
            padding: 2px 5px;
            border-radius: 3px;
            font-size: 0.9em;
            color: #4a4ad4;
        }

        .employee-actions {
            display: none;
            margin-top: 5px;
            text-align: center;
        }

        .employee-name:hover .employee-actions {
            display: flex;
            justify-content: center;
            gap: 5px;
        }

        .employee-actions .btn-sm {
            padding: 2px 5px;
            font-size: 0.7em;
        }

        .total-row {
            background-color: #f5f5f5;
            font-weight: bold;
        }

        .total-row td {
            border-top: 2px solid #dcf343;
        }
    </style>
</head>
<body>
    <?php if (isset($dbErrorMessage)) echo $dbErrorMessage; ?>

    <div class="header">
        <a href="<?php echo BASE_URL; ?>">
            <img src="<?php echo BASE_URL; ?>assets/images/logo.png" alt="<?php echo SITE_TITLE; ?>" class="logo">
        </a>
        <h1 class="page-title">المرتبات</h1>
        <div></div>
    </div>

    <div class="filter-bar">
        <div class="filter-icon">
            <i class="fas fa-filter"></i>
        </div>
        <div class="filter-dropdown">
            <select id="roleFilter" class="form-select form-select-sm">
                <option value="all" <?php echo $roleFilter === 'all' ? 'selected' : ''; ?>>الكل</option>
                <?php foreach ($availableRoles as $role): ?>
                <option value="<?php echo htmlspecialchars($role); ?>" <?php echo $roleFilter === $role ? 'selected' : ''; ?>>
                    <?php echo htmlspecialchars($role); ?>
                </option>
                <?php endforeach; ?>
            </select>
        </div>
        <div class="search-box">
            <input type="text" class="search-input" placeholder="البحث" id="searchInput" value="<?php echo htmlspecialchars($searchTerm); ?>">
            <i class="fas fa-search search-icon"></i>
        </div>
        <div class="add-button">
            <button type="button" class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#addEmployeeModal">
                <i class="fas fa-plus"></i> إضافة موظف
            </button>
        </div>
    </div>

    <div class="container-fluid mt-3">
        <div class="table-container">
            <table class="salaries-table" id="salariesTable">
                <thead>
                    <tr>
                        <th>الموظف</th>
                        <th>المرتب</th>
                        <th>الموظف</th>
                        <th>المرتب</th>
                        <th>الموظف</th>
                        <th>المرتب</th>
                    </tr>
                </thead>
            </table>

            <div class="table-body-container">
                <table class="salaries-table" id="salariesTableBody">
                    <tbody>
                        <?php for ($i = 0; $i < $employeesPerColumn; $i++): ?>
                        <tr>
                            <?php for ($j = 0; $j < 3; $j++): ?>
                                <?php if (isset($columns[$j][$i])): ?>
                                    <td class="employee-name" data-id="<?php echo $columns[$j][$i]['id']; ?>">
                                        <?php echo htmlspecialchars($columns[$j][$i]['employee_name']); ?>
                                        <div class="payment-type">
                                            <?php echo htmlspecialchars($columns[$j][$i]['payment_type']); ?>
                                            <span class="role-badge"><?php echo htmlspecialchars($columns[$j][$i]['role']); ?></span>
                                        </div>
                                        <div class="employee-actions">
                                            <button type="button" class="btn btn-sm btn-danger delete-employee" title="حذف">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                            <button type="button" class="btn btn-sm btn-info edit-employee" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                        </div>
                                    </td>
                                    <td><?php echo number_format($columns[$j][$i]['amount'], 3); ?></td>
                                <?php else: ?>
                                    <td></td>
                                    <td></td>
                                <?php endif; ?>
                            <?php endfor; ?>
                        </tr>
                        <?php endfor; ?>
                    </tbody>
                </table>
            </div>

            <table class="salaries-table" id="salariesTableFooter">
                <tfoot>
                    <tr class="total-row">
                        <td colspan="5" style="text-align: left;">إجمالي المرتبات</td>
                        <td><?php echo number_format($totalSalaries, 3); ?></td>
                    </tr>
                </tfoot>
            </table>
        </div>
    </div>

    <!-- Modal إضافة موظف -->
    <div class="modal fade" id="addEmployeeModal" tabindex="-1" aria-labelledby="addEmployeeModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addEmployeeModalLabel">إضافة موظف جديد</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="addEmployeeForm" action="add_employee.php" method="post">
                        <input type="hidden" name="is_office" value="0">
                        <div class="mb-3">
                            <label for="employeeName" class="form-label">اسم الموظف</label>
                            <input type="text" class="form-control" id="employeeName" name="employee_name" required>
                        </div>
                        <div class="mb-3">
                            <label for="employeeAmount" class="form-label">المرتب</label>
                            <input type="number" step="0.001" class="form-control" id="employeeAmount" name="amount" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">نوع الدفع</label>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="payment_type" id="paymentCash" value="نقدي" checked>
                                <label class="form-check-label" for="paymentCash">نقدي</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="payment_type" id="paymentVF" value="VF">
                                <label class="form-check-label" for="paymentVF">VF</label>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="employeeRole" class="form-label">الوظيفة</label>
                            <select class="form-select" id="employeeRole" name="role" required>
                                <?php foreach ($availableRoles as $role): ?>
                                <option value="<?php echo htmlspecialchars($role); ?>"><?php echo htmlspecialchars($role); ?></option>
                                <?php endforeach; ?>
                                <?php
                                // جلب الوظائف من قاعدة البيانات
                                try {
                                    $rolesQuery = "SELECT DISTINCT role FROM salaries ORDER BY role ASC";
                                    $stmt = $db->prepare($rolesQuery);
                                    $stmt->execute();
                                    $dbRoles = $stmt->fetchAll(PDO::FETCH_COLUMN);

                                    // التحقق من وجود جدول الوظائف
                                    $checkTableQuery = "SHOW TABLES LIKE 'roles'";
                                    $stmt = $db->prepare($checkTableQuery);
                                    $stmt->execute();
                                    $tableExists = $stmt->rowCount() > 0;

                                    if ($tableExists) {
                                        // جلب الوظائف من جدول الوظائف
                                        $rolesTableQuery = "SELECT role_name FROM roles ORDER BY role_name ASC";
                                        $stmt = $db->prepare($rolesTableQuery);
                                        $stmt->execute();
                                        $tableRoles = $stmt->fetchAll(PDO::FETCH_COLUMN);

                                        // دمج القوائم وإزالة التكرار
                                        $allRoles = array_unique(array_merge($dbRoles, $tableRoles));
                                    } else {
                                        $allRoles = $dbRoles;
                                    }

                                    // عرض الوظائف
                                    foreach ($allRoles as $role) {
                                        if (!in_array($role, $availableRoles)) {
                                            echo '<option value="' . htmlspecialchars($role) . '">' . htmlspecialchars($role) . '</option>';
                                        }
                                    }
                                } catch (PDOException $e) {
                                    // تجاهل الخطأ
                                }
                                ?>
                                <option value="وظيفة جديدة">وظيفة جديدة...</option>
                            </select>
                        </div>
                        <div class="mb-3 d-none" id="newRoleContainer">
                            <label for="newRole" class="form-label">اسم الوظيفة الجديدة</label>
                            <input type="text" class="form-control" id="newRole" name="new_role">
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" id="saveEmployeeBtn">حفظ</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal حذف موظف -->
    <div class="modal fade" id="deleteEmployeeModal" tabindex="-1" aria-labelledby="deleteEmployeeModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="deleteEmployeeModalLabel">حذف موظف</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>هل أنت متأكد من حذف الموظف <span id="deleteEmployeeName"></span>؟</p>
                    <form id="deleteEmployeeForm">
                        <input type="hidden" id="deleteEmployeeId" name="employee_id">
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-danger" id="confirmDeleteBtn">حذف</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // تغيير الفلتر
        document.getElementById('roleFilter').addEventListener('change', function() {
            const role = this.value;
            const searchTerm = document.getElementById('searchInput').value.trim();

            let url = 'external_salaries.php?role=' + encodeURIComponent(role);
            if (searchTerm) {
                url += '&search=' + encodeURIComponent(searchTerm);
            }

            window.location.href = url;
        });

        // البحث
        document.getElementById('searchInput').addEventListener('keyup', function(e) {
            if (e.key === 'Enter') {
                const searchTerm = this.value.trim();
                const role = document.getElementById('roleFilter').value;

                let url = 'external_salaries.php?role=' + encodeURIComponent(role);
                if (searchTerm) {
                    url += '&search=' + encodeURIComponent(searchTerm);
                }

                window.location.href = url;
            }
        });

        // البحث المباشر (بدون إعادة تحميل الصفحة)
        document.getElementById('searchInput').addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            const rows = document.querySelectorAll('#salariesTableBody tbody tr');

            rows.forEach(row => {
                const employeeNames = row.querySelectorAll('.employee-name');
                let found = false;

                employeeNames.forEach(name => {
                    if (name.textContent.toLowerCase().includes(searchTerm)) {
                        found = true;
                    }
                });

                if (found) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        });

        // إظهار حقل الوظيفة الجديدة عند اختيار "وظيفة جديدة"
        document.getElementById('employeeRole').addEventListener('change', function() {
            const newRoleContainer = document.getElementById('newRoleContainer');
            if (this.value === 'وظيفة جديدة') {
                newRoleContainer.classList.remove('d-none');
                document.getElementById('newRole').setAttribute('required', 'required');
            } else {
                newRoleContainer.classList.add('d-none');
                document.getElementById('newRole').removeAttribute('required');
            }
        });

        // حفظ الموظف الجديد
        document.getElementById('saveEmployeeBtn').addEventListener('click', function() {
            const form = document.getElementById('addEmployeeForm');

            // التحقق من صحة البيانات
            if (form.checkValidity()) {
                // التحقق من الوظيفة الجديدة
                const roleSelect = document.getElementById('employeeRole');
                const newRoleInput = document.getElementById('newRole');

                if (roleSelect.value === 'وظيفة جديدة' && !newRoleInput.value.trim()) {
                    alert('يرجى إدخال اسم الوظيفة الجديدة');
                    return;
                }

                // إرسال النموذج
                form.submit();
            } else {
                form.reportValidity();
            }
        });

        // حذف موظف
        document.querySelectorAll('.delete-employee').forEach(button => {
            button.addEventListener('click', function(e) {
                e.stopPropagation();

                const employeeCell = this.closest('.employee-name');
                const employeeId = employeeCell.getAttribute('data-id');
                const employeeName = employeeCell.textContent.trim().split('\n')[0].trim();

                document.getElementById('deleteEmployeeId').value = employeeId;
                document.getElementById('deleteEmployeeName').textContent = employeeName;

                const deleteModal = new bootstrap.Modal(document.getElementById('deleteEmployeeModal'));
                deleteModal.show();
            });
        });

        // تأكيد حذف الموظف
        document.getElementById('confirmDeleteBtn').addEventListener('click', function() {
            const employeeId = document.getElementById('deleteEmployeeId').value;

            // إرسال طلب الحذف
            fetch('delete_employee.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'employee_id=' + employeeId
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // إعادة تحميل الصفحة بعد الحذف
                    window.location.reload();
                } else {
                    alert(data.message || 'حدث خطأ أثناء حذف الموظف');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('حدث خطأ أثناء حذف الموظف');
            });
        });
    </script>
</body>
</html>
