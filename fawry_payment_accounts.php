<?php
// تضمين ملف التكوين
require_once 'config/config.php';

// تضمين ملف الاتصال بقاعدة البيانات
require_once 'includes/db.php';

// تضمين ملف الدوال المساعدة
require_once 'includes/functions.php';

// تضمين ملف دوال المصادقة
require_once 'includes/auth.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    header('Location: ' . BASE_URL . 'login.php');
    exit;
}

// جلب حسابات فوري
try {
    $stmt = $db->prepare("SELECT * FROM ad_accounts ORDER BY name");
    $stmt->execute();
    $fawryAccounts = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    die("خطأ في قاعدة البيانات: " . $e->getMessage());
}

// حساب إجماليات كل حساب
$accountTotals = [];
foreach ($fawryAccounts as $account) {
    $accountId = $account['id'];

    // جلب الإعلانات النشطة للحساب
    try {
        $stmt = $db->prepare("
            SELECT
                SUM(CASE WHEN daily_total = 'يومي' THEN cost ELSE cost / days END) as total_daily,
                SUM(cost) as total_cost,
                SUM(exchange_rate) as total_exchange
            FROM ads
            WHERE ad_account_id = :account_id AND status = 'نشط'
        ");
        $stmt->bindParam(':account_id', $accountId, PDO::PARAM_INT);
        $stmt->execute();
        $totals = $stmt->fetch(PDO::FETCH_ASSOC);

        $accountTotals[$accountId] = [
            'total_daily' => $totals['total_daily'] ?: 0,
            'total_cost' => $totals['total_cost'] ?: 0,
            'total_exchange' => $totals['total_exchange'] ?: 0
        ];

        // حساب المتبقي صرفه
        $accountTotals[$accountId]['remaining_exchange'] = $accountTotals[$accountId]['total_exchange'] - $accountTotals[$accountId]['total_cost'];

        // حساب مطلوب شحن
        $accountTotals[$accountId]['required_charge'] = max(0, $accountTotals[$accountId]['total_exchange'] - $account['balance']);
    } catch (PDOException $e) {
        // تجاهل الخطأ
        $accountTotals[$accountId] = [
            'total_daily' => 0,
            'total_cost' => 0,
            'total_exchange' => 0,
            'remaining_exchange' => 0,
            'required_charge' => 0
        ];
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اكونتات دفع فوري</title>

    <!-- تضمين Bootstrap RTL -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">

    <!-- تضمين Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- تضمين الأنماط المخصصة -->
    <link rel="stylesheet" href="assets/css/style.css">
    <style>
        @font-face {
            font-family: 'Cairo';
            src: url('assets/fonts/Cairo-Regular.ttf') format('truetype');
            font-weight: normal;
            font-style: normal;
        }

        @font-face {
            font-family: 'Cairo';
            src: url('assets/fonts/Cairo-Bold.ttf') format('truetype');
            font-weight: bold;
            font-style: normal;
        }

        body {
            margin: 0;
            padding: 0;
            font-family: 'Cairo', Arial, sans-serif;
            background-color: #fff;
        }

        .container {
            max-width: 100%;
            padding: 0;
            margin: 0;
        }

        .header {
            background-color: #fff;
            padding: 10px 0;
        }

        .header-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
            padding: 0 15px;
        }

        .logo-link {
            display: block;
        }

        .page-logo {
            height: 50px;
        }

        .page-title {
            color: #4a4ad4;
            text-align: center;
            margin: 0;
            padding: 10px 0;
            font-weight: bold;
            font-size: 24px;
            border-bottom: 1px solid #4a4ad4;
        }

        .totals-link {
            display: flex;
            align-items: center;
            color: #4a4ad4;
            text-decoration: none;
            margin-right: 15px;
            margin-left: 15px;
            font-size: 14px;
        }

        .totals-icon {
            margin-left: 5px;
            font-size: 16px;
        }

        .search-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 10px 0;
            padding: 0 15px;
        }

        .search-box {
            display: flex;
            align-items: center;
            border: 1px solid #ddd;
            border-radius: 0;
            padding: 5px 10px;
            width: 200px;
            background-color: #fff;
        }

        .search-icon {
            color: #999;
            margin-right: 10px;
        }

        .search-input {
            border: none;
            outline: none;
            width: 100%;
            padding: 5px;
            font-family: 'Cairo', Arial, sans-serif;
            text-align: right;
            font-size: 14px;
        }

        .table-container {
            margin: 0;
            padding: 0;
            width: 100%;
            overflow-x: auto;
        }

        .fawry-table {
            width: 100%;
            border-collapse: collapse;
            border-spacing: 0;
        }

        .fawry-table th {
            background-color: #4a4ad4;
            color: white;
            text-align: center;
            padding: 10px;
            font-weight: bold;
            border: none;
            font-size: 16px;
        }

        .fawry-table td {
            border: 1px solid #dcf343;
            padding: 10px;
            text-align: center;
            color: #333;
            font-size: 15px;
        }

        .fawry-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }

        .add-button {
            color: #4a4ad4;
            cursor: pointer;
            font-weight: bold;
            text-decoration: none;
        }

        .add-button:hover {
            text-decoration: underline;
        }

        .account-name {
            color: #4a4ad4;
            font-weight: bold;
            text-align: right;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <!-- شريط التنقل -->
    <div class="header">
        <div class="header-container">
            <a href="<?php echo BASE_URL; ?>" class="logo-link">
                <img src="<?php echo BASE_URL; ?>assets/images/logo.png" alt="<?php echo SITE_TITLE; ?>" class="page-logo">
            </a>

        </div>
    </div>

    <div class="container">
        <!-- عنوان الصفحة -->
        <h1 class="page-title">اكونتات دفع فوري</h1>

        <!-- مربع البحث وزر الإجماليات -->
        <div class="search-container">
            <div class="search-box">
                <i class="fas fa-search search-icon"></i>
                <input type="text" id="searchInput" class="search-input" placeholder="البحث" onkeyup="searchTable()">
            </div>
            <a href="#" class="totals-link" id="showTotalsBtn">
                <i class="fas fa-file-alt totals-icon"></i>
                <span>إجماليات</span>
            </a>
        </div>

        <!-- جدول الحسابات -->
        <div class="table-container">
            <table class="fawry-table" id="accountsTable">
                <thead>
                    <tr>
                        <th>الحسابات</th>
                        <th>الرصيد الحالي</th>
                        <th>صرف يومي</th>
                        <th>متبقي صرف</th>
                        <th>مطلوب شحن</th>
                        <th>رصيد بعد الإعلانات</th>
                        <th>Pages</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($fawryAccounts as $account): ?>
                        <tr>
                            <td class="account-name"><?php echo htmlspecialchars($account['name']); ?></td>
                            <td>EGP <?php echo number_format($account['balance'], 2); ?></td>
                            <td>EGP <?php echo number_format($accountTotals[$account['id']]['total_daily'], 2); ?></td>
                            <td>EGP <?php echo number_format($accountTotals[$account['id']]['remaining_exchange'], 2); ?></td>
                            <td>EGP <?php echo number_format($accountTotals[$account['id']]['required_charge'], 2); ?></td>
                            <td>EGP <?php echo number_format($account['balance'] - $accountTotals[$account['id']]['total_exchange'], 2); ?></td>
                            <td><a href="#" class="add-button">جديد</a></td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>

    <!-- تضمين Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- نافذة عرض رصيد حسابات فوري -->
    <div class="modal fade" id="balanceModal" tabindex="-1" aria-labelledby="balanceModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-md">
            <div class="modal-content">
                <div class="modal-header" style="background-color: #f0f8ff; border-bottom: 2px solid #dcf343;">
                    <div class="d-flex justify-content-between align-items-center w-100">
                        <div style="width: 30px;"></div>
                        <h5 class="modal-title" id="balanceModalLabel" style="color: #4a4ad4; font-weight: bold;">رصيد حسابات فوري</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                </div>
                <div class="modal-body">
                    <div style="border: 1px solid #dcf343; border-radius: 10px; padding: 15px; margin-bottom: 15px;">
                        <!-- الرصيد فوري -->
                        <div class="mb-4">
                            <h5 class="text-center mb-3" style="color: #4a4ad4; font-weight: bold;">الرصيد فوري</h5>
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="text-start" style="font-weight: bold; font-size: 1.2em;" id="balanceLeft">0.000</div>
                                <div class="text-center" style="color: #4CAF50; font-size: 1.5em;">←</div>
                                <div class="text-end" style="font-weight: bold; font-size: 1.2em;" id="balanceRight">0.000</div>
                            </div>
                        </div>

                        <!-- الصرف اليومي -->
                        <div class="mb-4">
                            <h5 class="text-center mb-3" style="color: #4a4ad4; font-weight: bold;">الصرف اليومي</h5>
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="text-start" style="font-weight: bold; font-size: 1.2em;" id="dailyExchangeLeft">0.000</div>
                                <div class="text-center" style="color: #4CAF50; font-size: 1.5em;">←</div>
                                <div class="text-end" style="font-weight: bold; font-size: 1.2em;" id="dailyExchangeRight">0.000</div>
                            </div>
                        </div>

                        <!-- الرصيد بعد الإعلانات بعد مرور يوم -->
                        <div class="mb-4">
                            <h5 class="text-center mb-3" style="color: #4a4ad4; font-weight: bold;">الرصيد بعد الإعلانات بعد مرور يوم</h5>
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="text-start" style="font-weight: bold; font-size: 1.2em;" id="balanceAfterDayLeft">0.000</div>
                                <div class="text-center" style="color: #4CAF50; font-size: 1.5em;">←</div>
                                <div class="text-end" style="font-weight: bold; font-size: 1.2em;" id="balanceAfterDayRight">0.000</div>
                            </div>
                        </div>

                        <!-- رصيد محطة الشحن -->
                        <div class="mb-2">
                            <h5 class="text-center mb-3" style="color: #4a4ad4; font-weight: bold;">رصيد محطة الشحن</h5>
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="text-start" style="font-weight: bold; font-size: 1.2em; cursor: pointer;" id="chargeStationLeft" ondblclick="editChargeStationBalance(this)">0.000</div>
                                <div class="text-center" style="color: #4CAF50; font-size: 1.5em;">←</div>
                                <div class="text-end" style="font-weight: bold; font-size: 1.2em; cursor: pointer;" id="chargeStationRight" ondblclick="editChargeStationBalance(this)">0.000</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // دالة لإزالة خلفية النوافذ المنبثقة عند إغلاقها
        function removeModalBackdrop() {
            const backdrop = document.querySelector('.modal-backdrop');
            if (backdrop) {
                backdrop.remove();
            }
            document.body.classList.remove('modal-open');
            document.body.style.overflow = '';
            document.body.style.paddingRight = '';
        }

        // دالة البحث في الجدول
        function searchTable() {
            const input = document.getElementById('searchInput');
            const filter = input.value.toUpperCase();
            const table = document.getElementById('accountsTable');
            const rows = table.getElementsByTagName('tr');

            for (let i = 1; i < rows.length; i++) {
                const cells = rows[i].getElementsByTagName('td');
                let found = false;

                for (let j = 0; j < cells.length; j++) {
                    const cell = cells[j];
                    if (cell) {
                        const textValue = cell.textContent || cell.innerText;
                        if (textValue.toUpperCase().indexOf(filter) > -1) {
                            found = true;
                            break;
                        }
                    }
                }

                rows[i].style.display = found ? '' : 'none';
            }
        }

        // إضافة مستمع لأحداث النقر على زر الإجماليات
        document.getElementById('showTotalsBtn').addEventListener('click', function() {
            calculateTotals();
        });

        // إضافة مستمع لأحداث النقر على زر الإجماليات في الهيدر
        document.getElementById('showTotalsBtn2')?.addEventListener('click', function() {
            calculateTotals();
        });

        // إضافة مستمع لأحداث النقر على زر رصيد حسابات فوري
        document.getElementById('showBalanceModalBtn')?.addEventListener('click', function(e) {
            e.preventDefault();
            showBalanceModal();
        });

        // دالة لعرض نافذة رصيد حسابات فوري
        function showBalanceModal() {
            // إزالة أي خلفية موجودة للنوافذ المنبثقة
            removeModalBackdrop();

            // حساب إجماليات الأرصدة
            calculateBalanceTotals();

            // عرض نافذة رصيد حسابات فوري
            const modal = new bootstrap.Modal(document.getElementById('balanceModal'));
            modal.show();
        }

        // دالة لحساب إجماليات الأرصدة
        function calculateBalanceTotals() {
            // حساب إجمالي الرصيد
            let totalBalance = 0;
            let totalDailyExchange = 0;
            let totalBalanceAfterDay = 0;

            // جمع الأرصدة من جميع الحسابات
            <?php foreach ($fawryAccounts as $account): ?>
                // الرصيد فوري: كل الأموال التي على الحسابات بعد صرف اليوم
                totalBalance += <?php echo $account['balance']; ?>;

                // الصرف اليومي: مجموع الصرف اليومي لكل الإعلانات النشطة
                totalDailyExchange += <?php echo $accountTotals[$account['id']]['total_daily']; ?>;

                // الرصيد بعد الإعلانات بعد مرور يوم: الرصيد الحالي ناقص الصرف اليومي
                totalBalanceAfterDay += <?php echo $account['balance'] - $accountTotals[$account['id']]['total_daily']; ?>;
            <?php endforeach; ?>

            // تحديث القيم في النافذة
            document.getElementById('balanceLeft').textContent = totalBalance.toFixed(3);
            document.getElementById('balanceRight').textContent = (totalBalance / 2).toFixed(3);

            document.getElementById('dailyExchangeLeft').textContent = totalDailyExchange.toFixed(3);
            document.getElementById('dailyExchangeRight').textContent = (totalDailyExchange / 2).toFixed(3);

            document.getElementById('balanceAfterDayLeft').textContent = totalBalanceAfterDay.toFixed(3);
            document.getElementById('balanceAfterDayRight').textContent = (totalBalanceAfterDay / 2).toFixed(3);

            // استرجاع قيمة رصيد محطة الشحن من localStorage إذا كانت موجودة
            const savedChargeStationBalance = localStorage.getItem('chargeStationBalance');
            if (savedChargeStationBalance) {
                const chargeStationBalance = parseFloat(savedChargeStationBalance);
                document.getElementById('chargeStationLeft').textContent = chargeStationBalance.toFixed(3);
                document.getElementById('chargeStationRight').textContent = (chargeStationBalance / 2).toFixed(3);
            } else {
                // قيمة افتراضية إذا لم تكن موجودة
                document.getElementById('chargeStationLeft').textContent = "0.000";
                document.getElementById('chargeStationRight').textContent = "0.000";
            }
        }

        // دالة لتعديل رصيد محطة الشحن
        function editChargeStationBalance(element) {
            const currentValue = parseFloat(element.textContent);
            const newValue = prompt('أدخل رصيد محطة الشحن الجديد:', currentValue.toFixed(3));

            if (newValue !== null && !isNaN(parseFloat(newValue))) {
                const parsedValue = parseFloat(newValue);

                // تحديث القيم في النافذة
                document.getElementById('chargeStationLeft').textContent = parsedValue.toFixed(3);
                document.getElementById('chargeStationRight').textContent = (parsedValue / 2).toFixed(3);

                // حفظ القيمة في localStorage
                localStorage.setItem('chargeStationBalance', parsedValue);
            }
        }

        // دالة لحساب الإجماليات
        function calculateTotals() {
            const table = document.getElementById('accountsTable');
            const rows = table.getElementsByTagName('tr');

            let totalBalance = 0;
            let totalDailyExchange = 0;
            let totalRemaining = 0;
            let totalRequired = 0;
            let totalAfterAds = 0;

            for (let i = 1; i < rows.length; i++) {
                if (rows[i].style.display !== 'none') {
                    const cells = rows[i].getElementsByTagName('td');

                    // استخراج القيم من الخلايا وإزالة "EGP" والفواصل
                    totalBalance += parseFloat(cells[1].textContent.replace('EGP', '').replace(/,/g, ''));
                    totalDailyExchange += parseFloat(cells[2].textContent.replace('EGP', '').replace(/,/g, ''));
                    totalRemaining += parseFloat(cells[3].textContent.replace('EGP', '').replace(/,/g, ''));
                    totalRequired += parseFloat(cells[4].textContent.replace('EGP', '').replace(/,/g, ''));
                    totalAfterAds += parseFloat(cells[5].textContent.replace('EGP', '').replace(/,/g, ''));
                }
            }

            // تقريب الأرقام وتحويلها إلى تنسيق مناسب
            totalBalance = Math.round(totalBalance);
            totalDailyExchange = Math.round(totalDailyExchange);
            totalRemaining = Math.round(totalRemaining);
            totalRequired = Math.round(totalRequired);
            totalAfterAds = Math.round(totalAfterAds);

            // إنشاء نافذة منبثقة مخصصة
            const totalsDiv = document.createElement('div');
            totalsDiv.style.position = 'fixed';
            totalsDiv.style.top = '50%';
            totalsDiv.style.left = '50%';
            totalsDiv.style.transform = 'translate(-50%, -50%)';
            totalsDiv.style.backgroundColor = 'white';
            totalsDiv.style.padding = '0';
            totalsDiv.style.border = '1px solid #dcf343';
            totalsDiv.style.borderRadius = '5px';
            totalsDiv.style.boxShadow = '0 0 10px rgba(0,0,0,0.2)';
            totalsDiv.style.zIndex = '9999';
            totalsDiv.style.width = '500px';
            totalsDiv.style.direction = 'rtl';
            totalsDiv.style.fontFamily = 'Cairo, Arial, sans-serif';

            // إنشاء جدول الإجماليات
            totalsDiv.innerHTML = `
                <div style="display: flex; width: 100%;">
                    <div style="flex: 1; text-align: center; padding: 15px; border-left: 1px solid #dcf343; border-bottom: 1px solid #dcf343;">
                        <div style="color: #4a4ad4; font-weight: bold; margin-bottom: 10px;">إجمالي الرصيد الحالي</div>
                        <div style="color: #dcf343; font-size: 20px; font-weight: bold;">${totalBalance}</div>
                    </div>
                    <div style="flex: 1; text-align: center; padding: 15px; border-left: 1px solid #dcf343; border-bottom: 1px solid #dcf343;">
                        <div style="color: #4a4ad4; font-weight: bold; margin-bottom: 10px;">إجمالي الصرف اليومي</div>
                        <div style="color: #dcf343; font-size: 20px; font-weight: bold;">${totalDailyExchange}</div>
                    </div>
                    <div style="flex: 1; text-align: center; padding: 15px; border-bottom: 1px solid #dcf343;">
                        <div style="color: #4a4ad4; font-weight: bold; margin-bottom: 10px;">إجمالي المتبقي صرفه</div>
                        <div style="color: #dcf343; font-size: 20px; font-weight: bold;">${totalRemaining}</div>
                    </div>
                </div>
                <div style="display: flex; width: 100%;">
                    <div style="flex: 1; text-align: center; padding: 15px; border-left: 1px solid #dcf343;">
                        <div style="color: #4a4ad4; font-weight: bold; margin-bottom: 10px;">إجمالي رصيد بعد الإعلانات</div>
                        <div style="color: #dcf343; font-size: 20px; font-weight: bold;">${totalAfterAds}</div>
                    </div>
                    <div style="flex: 1; text-align: center; padding: 15px;">
                        <div style="color: #4a4ad4; font-weight: bold; margin-bottom: 10px;">إجمالي مطلوب شحن</div>
                        <div style="color: #dcf343; font-size: 20px; font-weight: bold;">${totalRequired}</div>
                    </div>
                </div>
            `;

            // إضافة طبقة شفافة خلف النافذة المنبثقة
            const overlay = document.createElement('div');
            overlay.style.position = 'fixed';
            overlay.style.top = '0';
            overlay.style.left = '0';
            overlay.style.width = '100%';
            overlay.style.height = '100%';
            overlay.style.backgroundColor = 'rgba(0,0,0,0.5)';
            overlay.style.zIndex = '9998';
            overlay.onclick = function() {
                document.body.removeChild(totalsDiv);
                document.body.removeChild(overlay);
            };

            // إضافة العناصر إلى الصفحة
            document.body.appendChild(overlay);
            document.body.appendChild(totalsDiv);
        }

        // إضافة مستمعي أحداث لإزالة الخلفية عند إغلاق النوافذ المنبثقة
        document.addEventListener('DOMContentLoaded', function() {
            document.querySelectorAll('.modal').forEach(modal => {
                modal.addEventListener('hidden.bs.modal', function() {
                    removeModalBackdrop();
                });
            });
        });
    </script>
</body>
</html>
