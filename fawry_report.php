<?php
// تضمين ملف الاتصال بقاعدة البيانات
require_once 'config/database.php';

// إنشاء اتصال بقاعدة البيانات
$database = new Database();
$db = $database->getConnection();

// إنشاء الجدول إذا لم يكن موجوداً
try {
    $db->exec("
        CREATE TABLE IF NOT EXISTS fawry_report (
            id INT AUTO_INCREMENT PRIMARY KEY,
            basic_balance DECIMAL(10,2) NOT NULL DEFAULT 14034.00,
            cash_out DECIMAL(10,2) NOT NULL DEFAULT 0.00,
            total_balance DECIMAL(10,2) NOT NULL DEFAULT 14034.00,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )
    ");
    
    // التحقق من وجود بيانات
    $stmt = $db->prepare("SELECT COUNT(*) FROM fawry_report");
    $stmt->execute();
    $count = $stmt->fetchColumn();
    
    if ($count == 0) {
        $db->exec("INSERT INTO fawry_report (basic_balance, cash_out, total_balance) VALUES (14034.00, 0.00, 14034.00)");
    }
} catch (PDOException $e) {
    // تجاهل الأخطاء
}

// جلب البيانات
try {
    $stmt = $db->prepare("SELECT * FROM fawry_report ORDER BY id DESC LIMIT 1");
    $stmt->execute();
    $fawryData = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$fawryData) {
        $fawryData = [
            'basic_balance' => 14034.00,
            'cash_out' => 0.00,
            'total_balance' => 14034.00
        ];
    }
} catch (PDOException $e) {
    $fawryData = [
        'basic_balance' => 14034.00,
        'cash_out' => 0.00,
        'total_balance' => 14034.00
    ];
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فوري</title>
    
    <!-- Bootstrap RTL CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Google Fonts - Cairo -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f8f9fa;
            direction: rtl;
        }

        /* Modal Overlay */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* Modal Content */
        .fawry-modal {
            background-color: white;
            border-radius: 15px;
            padding: 0;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            width: 400px;
            max-width: 90%;
            position: relative;
        }

        /* Modal Header */
        .modal-header {
            background-color: #e8ecf4;
            padding: 20px;
            border-radius: 15px 15px 0 0;
            text-align: center;
            position: relative;
        }

        .modal-title {
            color: #4a69bd;
            font-size: 18px;
            font-weight: 600;
            margin: 0;
        }

        .modal-close {
            position: absolute;
            top: 15px;
            left: 20px;
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            color: #666;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-close:hover {
            color: #333;
        }

        /* Modal Body */
        .modal-body {
            padding: 30px;
        }

        /* Report Container */
        .report-container {
            border: 2px solid #ffd700;
            border-radius: 10px;
            padding: 20px;
            background-color: #fffef7;
        }

        .report-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding: 10px 0;
        }

        .report-row:not(:last-child) {
            border-bottom: 1px solid #e0e0e0;
        }

        .report-label {
            font-size: 16px;
            color: #333;
            font-weight: 500;
        }

        .report-value {
            font-size: 16px;
            color: #4a69bd;
            font-weight: 600;
            cursor: pointer;
            padding: 5px 10px;
            border-radius: 5px;
            transition: background-color 0.2s;
        }

        .report-value:hover {
            background-color: #f0f0f0;
        }

        .report-value.editable {
            border: 1px solid transparent;
        }

        .report-value.editing {
            background-color: white;
            border: 1px solid #4a69bd;
            outline: none;
        }

        .total-row {
            border-top: 2px solid #ffd700;
            margin-top: 10px;
            padding-top: 15px;
            font-weight: 700;
            font-size: 18px;
        }

        .total-row .report-value {
            color: #2c5aa0;
        }

        /* Save Button */
        .save-btn {
            background-color: #4a69bd;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            font-size: 14px;
            cursor: pointer;
            margin-top: 20px;
            width: 100%;
            transition: background-color 0.2s;
        }

        .save-btn:hover {
            background-color: #3d5aa0;
        }

        .save-btn:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }

        /* Success Message */
        .success-message {
            background-color: #d4edda;
            color: #155724;
            padding: 10px;
            border-radius: 5px;
            margin-top: 10px;
            text-align: center;
            display: none;
        }
    </style>
</head>
<body>
    <!-- Fawry Report Modal -->
    <div class="modal-overlay" id="fawryModal">
        <div class="fawry-modal">
            <div class="modal-header">
                <button class="modal-close" onclick="closeFawryModal()">&times;</button>
                <h5 class="modal-title">تقرير فوري</h5>
            </div>
            <div class="modal-body">
                <div class="report-container">
                    <div class="report-row">
                        <span class="report-label">رصيد اساسي</span>
                        <span class="report-value editable" 
                              contenteditable="true" 
                              data-field="basic_balance" 
                              id="basicBalance"><?php echo number_format($fawryData['basic_balance'], 0); ?></span>
                    </div>
                    
                    <div class="report-row">
                        <span class="report-label">كاش اوت</span>
                        <span class="report-value editable" 
                              contenteditable="true" 
                              data-field="cash_out" 
                              id="cashOut"><?php echo number_format($fawryData['cash_out'], 0); ?></span>
                    </div>
                    
                    <div class="report-row total-row">
                        <span class="report-label">اجمالي رصيد فوري</span>
                        <span class="report-value" id="totalBalance"><?php echo number_format($fawryData['total_balance'], 0); ?></span>
                    </div>
                </div>
                
                <button class="save-btn" onclick="saveFawryData()">حفظ التغييرات</button>
                <div class="success-message" id="successMessage">تم حفظ البيانات بنجاح!</div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // إظهار المودال عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('fawryModal').style.display = 'flex';
        });

        function closeFawryModal() {
            // العودة للصفحة السابقة
            window.history.back();
        }

        // تحديث الإجمالي عند تغيير القيم
        function updateTotal() {
            const basicBalance = parseFloat(document.getElementById('basicBalance').textContent.replace(/,/g, '')) || 0;
            const cashOut = parseFloat(document.getElementById('cashOut').textContent.replace(/,/g, '')) || 0;
            const total = basicBalance - cashOut;
            
            document.getElementById('totalBalance').textContent = total.toLocaleString();
        }

        // إضافة مستمعين للأحداث
        document.getElementById('basicBalance').addEventListener('input', updateTotal);
        document.getElementById('cashOut').addEventListener('input', updateTotal);

        // حفظ البيانات
        function saveFawryData() {
            const basicBalance = parseFloat(document.getElementById('basicBalance').textContent.replace(/,/g, '')) || 0;
            const cashOut = parseFloat(document.getElementById('cashOut').textContent.replace(/,/g, '')) || 0;
            const totalBalance = basicBalance - cashOut;

            const data = {
                basic_balance: basicBalance,
                cash_out: cashOut,
                total_balance: totalBalance
            };

            fetch('api/fawry/update_report.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    document.getElementById('successMessage').style.display = 'block';
                    setTimeout(() => {
                        document.getElementById('successMessage').style.display = 'none';
                    }, 3000);
                } else {
                    alert('حدث خطأ في حفظ البيانات');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('حدث خطأ في الاتصال');
            });
        }

        // إغلاق المودال عند النقر خارجه
        document.getElementById('fawryModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeFawryModal();
            }
        });

        // إغلاق المودال بمفتاح Escape
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeFawryModal();
            }
        });
    </script>
</body>
</html>
