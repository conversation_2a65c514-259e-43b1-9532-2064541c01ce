<?php
// تضمين ملف الإعدادات
require_once 'config/config.php';

// تضمين اتصال قاعدة البيانات
require_once 'includes/db.php';

// تضمين الدوال المساعدة
require_once 'includes/functions.php';

// تضمين دوال المصادقة
require_once 'includes/auth.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    header('Content-Type: application/json');
    echo json_encode(['error' => 'يجب تسجيل الدخول للوصول إلى هذه الخدمة']);
    exit;
}

// التحقق من وجود معرف الموظف
if (!isset($_GET['employee_id']) || empty($_GET['employee_id'])) {
    header('Content-Type: application/json');
    echo json_encode(['error' => 'معرف الموظف مطلوب']);
    exit;
}

$employeeId = intval($_GET['employee_id']);

try {
    // جلب بيانات الموظف
    $employeeQuery = "SELECT * FROM salaries WHERE id = :id";
    $stmt = $db->prepare($employeeQuery);
    $stmt->bindParam(':id', $employeeId);
    $stmt->execute();
    
    if ($stmt->rowCount() === 0) {
        header('Content-Type: application/json');
        echo json_encode(['error' => 'الموظف غير موجود']);
        exit;
    }
    
    $employee = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // جلب صفحات الموظف
    $pagesQuery = "SELECT * FROM employee_pages WHERE employee_id = :employee_id ORDER BY id ASC";
    $stmt = $db->prepare($pagesQuery);
    $stmt->bindParam(':employee_id', $employeeId);
    $stmt->execute();
    $pages = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // إعداد البيانات للإرجاع
    $response = [
        'employee_id' => $employee['id'],
        'employee_name' => $employee['employee_name'],
        'role' => $employee['role'],
        'payment_type' => $employee['payment_type'],
        'base_salary' => $employee['amount'],
        'pages' => $pages
    ];
    
    // إرجاع البيانات بتنسيق JSON
    header('Content-Type: application/json');
    echo json_encode($response);
    
} catch (PDOException $e) {
    header('Content-Type: application/json');
    echo json_encode(['error' => 'حدث خطأ أثناء جلب البيانات: ' . $e->getMessage()]);
}
?>
