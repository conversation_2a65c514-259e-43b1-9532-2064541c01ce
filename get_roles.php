<?php
// تضمين ملف الإعدادات
require_once 'config/config.php';

// تضمين اتصال قاعدة البيانات
require_once 'includes/db.php';

// تضمين الدوال المساعدة
require_once 'includes/functions.php';

// تضمين دوال المصادقة
require_once 'includes/auth.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'يجب تسجيل الدخول للوصول إلى هذه الخدمة']);
    exit;
}

try {
    // التحقق من وجود جدول الوظائف
    $checkTableQuery = "SHOW TABLES LIKE 'roles'";
    $stmt = $db->prepare($checkTableQuery);
    $stmt->execute();
    $tableExists = $stmt->rowCount() > 0;
    
    if (!$tableExists) {
        // إنشاء جدول الوظائف
        $createTableQuery = "CREATE TABLE roles (
            id INT AUTO_INCREMENT PRIMARY KEY,
            role_name VARCHAR(100) NOT NULL UNIQUE,
            role_description TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )";
        $db->exec($createTableQuery);
        
        // إضافة الوظائف الافتراضية
        $insertDefaultRolesQuery = "INSERT INTO roles (role_name) VALUES 
            ('مودريتور'), 
            ('مصمم'), 
            ('اكونتات'), 
            ('مسؤول')";
        $db->exec($insertDefaultRolesQuery);
    }
    
    // جلب جميع الوظائف المخزنة في جدول الوظائف
    $rolesQuery = "SELECT role_name FROM roles ORDER BY role_name ASC";
    $stmt = $db->prepare($rolesQuery);
    $stmt->execute();
    $rolesFromTable = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    // جلب جميع الوظائف المستخدمة في جدول المرتبات
    $usedRolesQuery = "SELECT DISTINCT role FROM salaries ORDER BY role ASC";
    $stmt = $db->prepare($usedRolesQuery);
    $stmt->execute();
    $usedRoles = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    // دمج القوائم وإزالة التكرار
    $allRoles = array_unique(array_merge($rolesFromTable, $usedRoles));
    sort($allRoles);
    
    // إرجاع النتيجة
    header('Content-Type: application/json');
    echo json_encode(['success' => true, 'roles' => $allRoles]);
    
} catch (PDOException $e) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'حدث خطأ أثناء جلب الوظائف: ' . $e->getMessage()]);
}
