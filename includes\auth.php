<?php
/**
 * Authentication Functions
 */

/**
 * Authenticate user
 *
 * @param string $email User email
 * @param string $password User password
 * @return array|boolean User data if authentication successful, false otherwise
 */
function authenticateUser($email, $password) {
    global $db;

    $query = "SELECT * FROM users WHERE email = :email LIMIT 1";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':email', $email);
    $stmt->execute();

    if ($stmt->rowCount() > 0) {
        $user = $stmt->fetch(PDO::FETCH_ASSOC);

        // Verify password
        if (password_verify($password, $user['password'])) {
            // Remove password from user data
            unset($user['password']);
            return $user;
        }
    }

    return false;
}

/**
 * Register new user
 *
 * @param string $name User name
 * @param string $email User email
 * @param string $password User password
 * @param bool $is_admin Is admin user (default: false)
 * @param int $created_by User ID who created this user
 * @return int|boolean User ID if registration successful, false otherwise
 */
function registerUser($name, $email, $password, $is_admin = false, $created_by = null) {
    global $db;

    // Check if email already exists
    $query = "SELECT id FROM users WHERE email = :email LIMIT 1";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':email', $email);
    $stmt->execute();

    if ($stmt->rowCount() > 0) {
        return false; // Email already exists
    }

    // Hash password
    $hashed_password = password_hash($password, PASSWORD_DEFAULT);

    // Insert new user
    $query = "INSERT INTO users (name, email, password, is_admin, status, created_at, created_by)
              VALUES (:name, :email, :password, :is_admin, 'active', NOW(), :created_by)";

    $stmt = $db->prepare($query);
    $stmt->bindParam(':name', $name);
    $stmt->bindParam(':email', $email);
    $stmt->bindParam(':password', $hashed_password);
    $stmt->bindParam(':is_admin', $is_admin, PDO::PARAM_BOOL);
    $stmt->bindParam(':created_by', $created_by);

    if ($stmt->execute()) {
        return $db->lastInsertId();
    }

    return false;
}

/**
 * Create user session
 *
 * @param array $user User data
 * @return void
 */
function createUserSession($user) {
    $_SESSION['user_id'] = $user['id'];
    $_SESSION['user_name'] = $user['name'];
    $_SESSION['user_email'] = $user['email'];
    $_SESSION['user_is_admin'] = $user['is_admin'];
    $_SESSION['logged_in'] = true;
}

/**
 * Destroy user session
 *
 * @return void
 */
function destroyUserSession() {
    unset($_SESSION['user_id']);
    unset($_SESSION['user_name']);
    unset($_SESSION['user_email']);
    unset($_SESSION['user_is_admin']);
    unset($_SESSION['logged_in']);

    session_destroy();
}

/**
 * Generate JWT token
 *
 * @param array $user User data
 * @return string JWT token
 */
function generateJWTToken($user) {
    $issuedAt = time();
    $expirationTime = $issuedAt + JWT_EXPIRATION;

    $payload = [
        'iat' => $issuedAt,
        'exp' => $expirationTime,
        'data' => [
            'id' => $user['id'],
            'name' => $user['name'],
            'email' => $user['email'],
            'is_admin' => $user['is_admin']
        ]
    ];

    // Use a JWT library here to generate the token
    // For now, we'll just return a placeholder
    return base64_encode(json_encode($payload));
}

/**
 * Validate JWT token
 *
 * @param string $token JWT token
 * @return array|boolean User data if token is valid, false otherwise
 */
function validateJWTToken($token) {
    // Use a JWT library here to validate the token
    // For now, we'll just decode the placeholder
    $payload = json_decode(base64_decode($token), true);

    if ($payload && isset($payload['exp']) && $payload['exp'] > time()) {
        return $payload['data'];
    }

    return false;
}

// دوال isLoggedIn() و isAdmin() معرفة بالفعل في ملف functions.php
?>
