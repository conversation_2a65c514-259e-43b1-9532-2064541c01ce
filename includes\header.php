<?php
// Include configuration
require_once __DIR__ . '/../config/config.php';

// Include database connection
require_once __DIR__ . '/../includes/db.php';

// Include helper functions
require_once __DIR__ . '/../includes/functions.php';

// Include authentication functions
require_once __DIR__ . '/../includes/auth.php';

// Include permissions functions
require_once __DIR__ . '/../includes/permissions.php';
?>
<!DOCTYPE html>
<html lang="ar" dir="">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo SITE_TITLE; ?></title>

    <!-- Bootstrap RTL CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Google Fonts - Cairo -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>assets/css/styles.css">
</head>
<body>
    <div class="container">
        <header class="mb-4">
            <nav class="navbar navbar-expand-lg navbar-light bg-light">
                <div class="container-fluid">
                    <a class="navbar-brand" href="<?php echo BASE_URL; ?>">
                        <img src="<?php echo BASE_URL; ?>assets/images/logo.svg" alt="<?php echo SITE_TITLE; ?>" height="40">
                        <?php echo SITE_TITLE; ?>
                    </a>
                    <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                        <span class="navbar-toggler-icon"></span>
                    </button>
                    <div class="collapse navbar-collapse" id="navbarNav">
                        <ul class="navbar-nav me-auto mb-2 mb-lg-0">
                            <li class="nav-item">
                                <a class="nav-link" href="<?php echo BASE_URL; ?>">الرئيسية</a>
                            </li>
                            <?php if (isLoggedIn()): ?>
                                <li class="nav-item">
                                    <a class="nav-link" href="<?php echo BASE_URL; ?>dashboard.php">لوحة التحكم</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" href="<?php echo BASE_URL; ?>ad_clients.php">عملاء الإعلانات</a>
                                </li>
                                <?php if (isAdmin()): ?>
                                <li class="nav-item">
                                    <a class="nav-link" href="<?php echo BASE_URL; ?>ad_accounts.php">الحسابات الإعلانية</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" href="<?php echo BASE_URL; ?>ad_accounts_report.php">حسابات الإعلانات</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" href="<?php echo BASE_URL; ?>accounts_fawry.php">اكونتات فوري</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" href="<?php echo BASE_URL; ?>salaries.php">المرتبات</a>
                                </li>
                                <?php endif; ?>
                                <?php if (isAdmin()): ?>
                                    <li class="nav-item">
                                        <a class="nav-link" href="<?php echo BASE_URL; ?>admin/">الإدارة</a>
                                    </li>
                                <?php endif; ?>
                            <?php endif; ?>
                        </ul>
                        <ul class="navbar-nav">
                            <?php if (isLoggedIn()): ?>
                                <li class="nav-item dropdown">
                                    <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                        <i class="fas fa-user"></i> <?php echo $_SESSION['user_name']; ?>
                                    </a>
                                    <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="navbarDropdown">
                                        <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>profile.php">الملف الشخصي</a></li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>logout.php">تسجيل الخروج</a></li>
                                    </ul>
                                </li>
                            <?php else: ?>
                                <li class="nav-item">
                                    <a class="nav-link" href="<?php echo BASE_URL; ?>login.php">تسجيل الدخول</a>
                                </li>

                            <?php endif; ?>
                        </ul>
                    </div>
                </div>
            </nav>
        </header>
        <main>
            <?php if (isset($_SESSION['flash_message'])): ?>
                <div class="alert alert-<?php echo $_SESSION['flash_type']; ?> alert-dismissible fade show" role="alert">
                    <?php echo $_SESSION['flash_message']; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php
                    unset($_SESSION['flash_message']);
                    unset($_SESSION['flash_type']);
                ?>
            <?php endif; ?>
