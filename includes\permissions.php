<?php
/**
 * Permissions Functions
 */

/**
 * Check if user has permission
 *
 * @param int $user_id User ID
 * @param string $permission_identifier Permission identifier
 * @return boolean True if user has permission, false otherwise
 */
function hasPermission($user_id, $permission_identifier) {
    global $db;

    // Check if user is admin
    $query = "SELECT is_admin FROM users WHERE id = :user_id LIMIT 1";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':user_id', $user_id);
    $stmt->execute();
    $is_admin = $stmt->fetchColumn();

    // Admin has all permissions
    if ($is_admin) {
        return true;
    }

    // Check direct user permissions
    $query = "SELECT COUNT(*) FROM user_permissions up
              JOIN permissions p ON up.permission_id = p.id
              WHERE up.user_id = :user_id AND p.identifier = :identifier";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':user_id', $user_id);
    $stmt->bindParam(':identifier', $permission_identifier);
    $stmt->execute();
    $direct_permission = $stmt->fetchColumn();

    if ($direct_permission > 0) {
        return true;
    }

    // Check group permissions
    $query = "SELECT COUNT(*) FROM user_groups ug
              JOIN group_permissions gp ON ug.group_id = gp.group_id
              JOIN permissions p ON gp.permission_id = p.id
              WHERE ug.user_id = :user_id AND p.identifier = :identifier";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':user_id', $user_id);
    $stmt->bindParam(':identifier', $permission_identifier);
    $stmt->execute();
    $group_permission = $stmt->fetchColumn();

    return $group_permission > 0;
}

/**
 * Check if current user has permission
 *
 * @param string $permission_identifier Permission identifier
 * @return boolean True if current user has permission, false otherwise
 */
function currentUserHasPermission($permission_identifier) {
    if (!isLoggedIn()) {
        return false;
    }

    return hasPermission($_SESSION['user_id'], $permission_identifier);
}

/**
 * Get all permissions
 *
 * @return array Array of permissions
 */
function getAllPermissions() {
    global $db;

    $query = "SELECT * FROM permissions ORDER BY category, name";
    $stmt = $db->prepare($query);
    $stmt->execute();

    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

/**
 * Get permissions by category
 *
 * @return array Array of permissions grouped by category
 */
function getPermissionsByCategory() {
    global $db;

    $query = "SELECT * FROM permissions ORDER BY category, name";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $permissions = $stmt->fetchAll(PDO::FETCH_ASSOC);

    $grouped = [];
    foreach ($permissions as $permission) {
        $category = $permission['category'];
        if (!isset($grouped[$category])) {
            $grouped[$category] = [];
        }
        $grouped[$category][] = $permission;
    }

    return $grouped;
}

/**
 * Get user permissions
 *
 * @param int $user_id User ID
 * @return array Array of permission IDs
 */
function getUserPermissions($user_id) {
    global $db;

    // Get direct permissions
    $query = "SELECT permission_id FROM user_permissions WHERE user_id = :user_id";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':user_id', $user_id);
    $stmt->execute();
    $direct_permissions = $stmt->fetchAll(PDO::FETCH_COLUMN);

    // Get group permissions
    $query = "SELECT gp.permission_id FROM user_groups ug
              JOIN group_permissions gp ON ug.group_id = gp.group_id
              WHERE ug.user_id = :user_id";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':user_id', $user_id);
    $stmt->execute();
    $group_permissions = $stmt->fetchAll(PDO::FETCH_COLUMN);

    // Merge and remove duplicates
    return array_unique(array_merge($direct_permissions, $group_permissions));
}

/**
 * Get all permission groups
 *
 * @return array Array of permission groups
 */
function getAllPermissionGroups() {
    global $db;

    $query = "SELECT * FROM permission_groups ORDER BY name";
    $stmt = $db->prepare($query);
    $stmt->execute();

    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

/**
 * Get group permissions
 *
 * @param int $group_id Group ID
 * @return array Array of permission IDs
 */
function getGroupPermissions($group_id) {
    global $db;

    $query = "SELECT permission_id FROM group_permissions WHERE group_id = :group_id";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':group_id', $group_id);
    $stmt->execute();

    return $stmt->fetchAll(PDO::FETCH_COLUMN);
}

/**
 * Get user groups
 *
 * @param int $user_id User ID
 * @return array Array of group IDs
 */
function getUserGroups($user_id) {
    global $db;

    $query = "SELECT group_id FROM user_groups WHERE user_id = :user_id";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':user_id', $user_id);
    $stmt->execute();

    return $stmt->fetchAll(PDO::FETCH_COLUMN);
}

/**
 * Require permission to access page
 *
 * @param string $permission_identifier Permission identifier
 * @return void
 */
function requirePermission($permission_identifier) {
    if (!currentUserHasPermission($permission_identifier)) {
        $_SESSION['flash_message'] = 'ليس لديك صلاحية للوصول إلى هذه الصفحة';
        $_SESSION['flash_type'] = 'danger';
        redirect(BASE_URL);
    }
}

/**
 * Check if permission exists
 *
 * @param string $identifier Permission identifier
 * @return boolean True if permission exists, false otherwise
 */
function permissionExists($identifier) {
    global $db;

    $query = "SELECT id FROM permissions WHERE identifier = :identifier LIMIT 1";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':identifier', $identifier);
    $stmt->execute();

    return $stmt->rowCount() > 0;
}

/**
 * Add new permission
 *
 * @param string $identifier Permission identifier
 * @param string $name Permission name
 * @param string $description Permission description
 * @param string $category Permission category
 * @return int|boolean Permission ID if successful, false otherwise
 */
function addPermission($identifier, $name, $description, $category = 'عام') {
    global $db;

    // Check if permission already exists
    if (permissionExists($identifier)) {
        return false;
    }

    $query = "INSERT INTO permissions (identifier, name, description, category) VALUES (:identifier, :name, :description, :category)";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':identifier', $identifier);
    $stmt->bindParam(':name', $name);
    $stmt->bindParam(':description', $description);
    $stmt->bindParam(':category', $category);

    if ($stmt->execute()) {
        return $db->lastInsertId();
    }

    return false;
}
?>
