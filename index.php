<?php
// Include configuration
require_once 'config/config.php';

// Include database connection
require_once 'includes/db.php';

// Include helper functions
require_once 'includes/functions.php';

// Include authentication functions
require_once 'includes/auth.php';

// Include permissions functions
require_once 'includes/permissions.php';

// Check if user is already logged in
if (isLoggedIn()) {
    redirect(BASE_URL . 'dashboard.php');
}

// Process login form
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo SITE_TITLE; ?></title>

    <!-- Bootstrap RTL CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Google Fonts - Cairo -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>assets/css/styles.css">
    <style>
        body {
            margin: 0;
            padding: 0;
            background-color: white;
            font-family: 'Cairo', sans-serif;
        }
        .login-wrapper {
            width: 100%;
            min-height: 100vh;
            display: flex;
            position: relative;
        }
        .logo-top-right {
            position: absolute;
            top: 20px;
            right: 20px;
            z-index: 100;
        }
        .logo-top-right .logo-link {
            display: block;
            transition: all 0.3s ease;
        }

        .logo-top-right .logo-link:hover {
            transform: scale(1.05);
        }

        .logo-top-right img {
            width: 70px;
            height: auto;
            cursor: pointer;
        }
        .login-left {
            width: 40%;
            padding: 40px;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }
        .login-right {
            width: 60%;
            background-image: url('<?php echo BASE_URL; ?>assets/images/login.png');
            background-size: cover;
            background-position: center;
        }
        .login-title {
            color: #4a56e2;
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 40px;
        }
        .login-form {
            max-width: 400px;
        }
        .login-input {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 12px 15px;
            font-size: 16px;
            text-align: right;
            margin-bottom: 20px;
            height: 50px;
        }
        .login-btn {
            background-color: #4a56e2;
            border: none;
            border-radius: 8px;
            padding: 12px;
            font-size: 16px;
            font-weight: 600;
            height: 50px;
            margin-top: 10px;
        }
        .login-links {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-top: 20px;
        }
        .login-question {
            color: #666;
            margin-left: 5px;
        }
        .login-signup-link {
            color: #f8c40c;
            text-decoration: none;
            font-weight: 600;
        }
        @media (max-width: 768px) {
            .login-right {
                display: none;
            }
            .login-left {
                width: 100%;
                padding: 20px;
            }
        }
    </style>
</head>
<body>
<div class="logo-top-right">
    <a href="<?php echo BASE_URL; ?>" class="logo-link">
        <img src="<?php echo BASE_URL; ?>assets/images/logo.png" alt="Logo">
    </a>
</div>

<?php
// Process login form
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $email = sanitize($_POST['email']);
    $password = $_POST['password'];
    $remember = isset($_POST['remember']) ? true : false;

    // Validate input
    $errors = [];

    if (empty($email)) {
        $errors[] = 'البريد الإلكتروني مطلوب';
    }

    if (empty($password)) {
        $errors[] = 'كلمة المرور مطلوبة';
    }

    // If no errors, attempt to authenticate user
    if (empty($errors)) {
        $user = authenticateUser($email, $password);

        if ($user) {
            // Create user session
            createUserSession($user);

            // Set remember me cookie if requested
            if ($remember) {
                $token = generateRandomString(32);
                $expiry = time() + 30 * 24 * 60 * 60; // 30 days

                // Store token in database
                $query = "INSERT INTO remember_tokens (user_id, token, expires_at) VALUES (:user_id, :token, :expires_at)";
                $stmt = $db->prepare($query);
                $stmt->bindParam(':user_id', $user['id']);
                $stmt->bindParam(':token', $token);

                // Fix the date issue by creating a variable first
                $expiryDate = date('Y-m-d H:i:s', $expiry);
                $stmt->bindParam(':expires_at', $expiryDate);

                $stmt->execute();

                // Set cookie
                setcookie('remember_token', $token, $expiry, '/', '', false, true);
            }

            // Log activity
            logActivity('login', 'تم تسجيل الدخول بنجاح', $user['id']);

            // Redirect to dashboard
            $_SESSION['flash_message'] = 'تم تسجيل الدخول بنجاح';
            $_SESSION['flash_type'] = 'success';
            redirect(BASE_URL . 'dashboard.php');
        } else {
            $errors[] = 'البريد الإلكتروني أو كلمة المرور غير صحيحة';
        }
    }
}
?>

<div class="login-wrapper">
    <div class="login-left">
        <h2 class="login-title">تسجيل الدخول</h2>

        <?php if (!empty($errors)): ?>
            <div class="alert alert-danger">
                <ul class="mb-0">
                    <?php foreach ($errors as $error): ?>
                        <li><?php echo $error; ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>
        <?php endif; ?>

        <form method="POST" action="<?php echo $_SERVER['PHP_SELF']; ?>" class="login-form">
            <input type="text" class="form-control login-input" name="email" placeholder="اسم المستخدم" value="<?php echo $email ?? ''; ?>" required>

            <input type="password" class="form-control login-input" name="password" placeholder="كلمة السر" required>

            <button type="submit" class="btn btn-primary login-btn w-100">تسجيل الدخول</button>


        </form>
    </div>
    <div class="login-right"></div>
</div>

<!-- Include Bootstrap JS -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<!-- Custom JS -->
<script src="<?php echo BASE_URL; ?>assets/js/scripts.js"></script>
</body>
</html>
