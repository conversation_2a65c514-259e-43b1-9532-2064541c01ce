<?php
// تضمين ملف الإعدادات
require_once 'config/config.php';

// تضمين اتصال قاعدة البيانات
require_once 'includes/db.php';

// تضمين الدوال المساعدة
require_once 'includes/functions.php';

// تضمين دوال المصادقة
require_once 'includes/auth.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    $_SESSION['flash_message'] = 'يجب تسجيل الدخول للوصول إلى هذه الصفحة';
    $_SESSION['flash_type'] = 'danger';
    redirect(BASE_URL . 'index.php');
}

// الحصول على الشهر الحالي
$currentMonth = date('n');
$currentYear = date('Y');

try {
    // التحقق من وجود موظفين في المكتب
    $checkOfficeEmployeesQuery = "SELECT COUNT(*) FROM salaries WHERE is_office = 1";
    $stmt = $db->prepare($checkOfficeEmployeesQuery);
    $stmt->execute();
    $officeEmployeesCount = $stmt->fetchColumn();
    
    if ($officeEmployeesCount == 0) {
        // تحديث بعض الموظفين ليكونوا موظفي مكتب
        $updateEmployeesQuery = "UPDATE salaries SET is_office = 1 WHERE id IN (1, 2, 5, 6, 9, 10, 12, 15)";
        $db->exec($updateEmployeesQuery);
        
        $_SESSION['flash_message'] = 'تم تحديث بيانات موظفي المكتب بنجاح';
        $_SESSION['flash_type'] = 'success';
    }
    
    // التحقق من وجود جدول مرتبات المكتب
    $checkTableQuery = "SHOW TABLES LIKE 'office_salaries'";
    $stmt = $db->prepare($checkTableQuery);
    $stmt->execute();
    $tableExists = $stmt->rowCount() > 0;
    
    if (!$tableExists) {
        // إنشاء جدول مرتبات المكتب
        $createTableQuery = "CREATE TABLE office_salaries (
            id INT AUTO_INCREMENT PRIMARY KEY,
            employee_id INT NOT NULL,
            month INT NOT NULL,
            year INT NOT NULL,
            salary DECIMAL(10,2) NOT NULL,
            advance DECIMAL(10,2) DEFAULT 0,
            deductions VARCHAR(255) DEFAULT NULL,
            deduction_amount DECIMAL(10,2) DEFAULT 0,
            commission DECIMAL(10,2) DEFAULT 0,
            overtime VARCHAR(255) DEFAULT NULL,
            overtime_amount DECIMAL(10,2) DEFAULT 0,
            net_salary DECIMAL(10,2) DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (employee_id) REFERENCES salaries(id) ON DELETE CASCADE,
            UNIQUE KEY (employee_id, month, year)
        )";
        $db->exec($createTableQuery);
        
        $_SESSION['flash_message'] = 'تم إنشاء جدول مرتبات المكتب بنجاح';
        $_SESSION['flash_type'] = 'success';
    }
    
    // التحقق من وجود بيانات لهذا الشهر
    $checkDataQuery = "SELECT COUNT(*) FROM office_salaries WHERE month = ? AND year = ?";
    $stmt = $db->prepare($checkDataQuery);
    $stmt->execute([$currentMonth, $currentYear]);
    $dataExists = $stmt->fetchColumn() > 0;
    
    if (!$dataExists) {
        // جلب موظفي المكتب
        $officeEmployeesQuery = "SELECT id, employee_name, amount FROM salaries WHERE is_office = 1";
        $stmt = $db->prepare($officeEmployeesQuery);
        $stmt->execute();
        $officeEmployees = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // إضافة بيانات مرتبات المكتب للشهر الحالي
        foreach ($officeEmployees as $employee) {
            // إنشاء بيانات عشوائية للاختبار
            $commission = rand(500, 1000);
            $advance = rand(0, 1) ? rand(500, 1000) : 0;
            $deductions = rand(0, 1) ? '3 أيام' : '';
            $deductionAmount = !empty($deductions) ? rand(300, 500) : 0;
            $overtime = rand(0, 1) ? 'يوم' : '';
            $overtimeAmount = !empty($overtime) ? rand(100, 200) : 0;
            
            // حساب صافي المرتب
            $netSalary = $employee['amount'] + $commission + $overtimeAmount - $advance - $deductionAmount;
            
            // إدخال البيانات
            $insertQuery = "INSERT INTO office_salaries 
                (employee_id, month, year, salary, advance, deductions, deduction_amount, commission, overtime, overtime_amount, net_salary) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
            $stmt = $db->prepare($insertQuery);
            $stmt->execute([
                $employee['id'],
                $currentMonth,
                $currentYear,
                $employee['amount'],
                $advance,
                $deductions,
                $deductionAmount,
                $commission,
                $overtime,
                $overtimeAmount,
                $netSalary
            ]);
        }
        
        $_SESSION['flash_message'] = 'تم إضافة بيانات مرتبات المكتب للشهر الحالي بنجاح';
        $_SESSION['flash_type'] = 'success';
    } else {
        $_SESSION['flash_message'] = 'بيانات مرتبات المكتب موجودة بالفعل للشهر الحالي';
        $_SESSION['flash_type'] = 'info';
    }
} catch (PDOException $e) {
    $_SESSION['flash_message'] = 'حدث خطأ أثناء تهيئة بيانات مرتبات المكتب: ' . $e->getMessage();
    $_SESSION['flash_type'] = 'danger';
}

// إعادة التوجيه إلى صفحة مرتبات المكتب
redirect(BASE_URL . 'office_salaries.php');
?>
