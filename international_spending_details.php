<?php
/**
 * صفحة تفاصيل الصرف الدولي
 */

// تضمين ملف الاتصال بقاعدة البيانات
require_once 'config/database.php';

// إنشاء اتصال بقاعدة البيانات
$database = new Database();
$db = $database->getConnection();

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تفاصيل الصرف الدولي</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body { font-family: 'Cairo', sans-serif; background-color: #f8f9fa; }
        .container { margin-top: 30px; }
        .card-container { background: white; border-radius: 15px; padding: 25px; margin-bottom: 30px; box-shadow: 0 4px 20px rgba(0,0,0,0.1); }
        .section-title { color: #4a56e2; font-weight: 600; margin-bottom: 20px; border-bottom: 2px solid #4a56e2; padding-bottom: 10px; }
        .progress-container { background: #f8f9fa; border-radius: 10px; padding: 15px; margin-bottom: 20px; }
        .progress-bar-custom { height: 25px; border-radius: 12px; }
        .spending-item { background: #f8f9fa; border-radius: 10px; padding: 15px; margin-bottom: 15px; border-left: 4px solid; }
        .spending-international { border-left-color: #f44336; }
        .spending-local { border-left-color: #4caf50; }
        .alert-critical { background: #ffebee; border: 2px solid #f44336; }
        .alert-warning { background: #fff3e0; border: 2px solid #ff9800; }
        .alert-safe { background: #e8f5e8; border: 2px solid #4caf50; }
        .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .stat-card { background: linear-gradient(135deg, #5a67d8 0%, #667eea 100%); color: white; border-radius: 15px; padding: 20px; text-align: center; }
        .refresh-btn { position: fixed; bottom: 20px; right: 20px; z-index: 1000; }
    </style>
</head>
<body>
    <div class="container">
        <div class="row">
            <div class="col-12">
                <h1 class="text-center mb-4" style="color: #4a56e2;">
                    <i class="fas fa-globe"></i>
                    تفاصيل الصرف الدولي
                </h1>
            </div>
        </div>

        <?php
        try {
            // جلب بطاقات الفيزا
            $stmt = $db->prepare("SELECT * FROM visa_cards ORDER BY name");
            $stmt->execute();
            $cards = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // حساب الصرف الدولي الشهري لكل بطاقة
            foreach ($cards as &$card) {
                $cardId = $card['id'];

                // حساب الصرف الدولي للشهر الحالي
                $stmt = $db->prepare("
                    SELECT COALESCE(SUM(spent_amount), 0) as monthly_international_spent
                    FROM visa_spent_transactions
                    WHERE visa_card_id = ?
                    AND is_international = TRUE
                    AND YEAR(spent_date) = YEAR(CURDATE())
                    AND MONTH(spent_date) = MONTH(CURDATE())
                    AND status = 'نشط'
                ");
                $stmt->execute([$cardId]);
                $result = $stmt->fetch(PDO::FETCH_ASSOC);

                $card['current_international_spent'] = $result['monthly_international_spent'];
                $card['international_percentage'] = ($card['daily_limit'] > 0) ?
                    ($card['current_international_spent'] / $card['daily_limit'] * 100) : 0;
                $card['international_remaining'] = $card['daily_limit'] - $card['current_international_spent'];
            }

            // ترتيب البطاقات حسب النسبة المئوية
            usort($cards, function($a, $b) {
                return $b['international_percentage'] <=> $a['international_percentage'];
            });

            foreach ($cards as $card):
                $cardId = $card['id'];
                $internationalPercentage = round($card['international_percentage'], 1);
                $alertClass = 'alert-safe';
                $progressColor = '#4caf50';
                $statusText = 'آمن';
                $statusIcon = 'fa-check-circle';

                if ($internationalPercentage >= 90) {
                    $alertClass = 'alert-critical';
                    $progressColor = '#f44336';
                    $statusText = 'حرج';
                    $statusIcon = 'fa-exclamation-circle';
                } elseif ($internationalPercentage >= 75) {
                    $alertClass = 'alert-warning';
                    $progressColor = '#ff9800';
                    $statusText = 'تحذير';
                    $statusIcon = 'fa-exclamation-triangle';
                } elseif ($internationalPercentage >= 50) {
                    $progressColor = '#9c27b0';
                    $statusText = 'مراقبة';
                    $statusIcon = 'fa-eye';
                }
        ?>

        <div class="card-container <?php echo $alertClass; ?>">
            <div class="row align-items-center mb-3">
                <div class="col-md-8">
                    <h3 style="color: #4a56e2; margin: 0;">
                        <i class="fas fa-credit-card"></i>
                        <?php echo htmlspecialchars($card['name']); ?>
                    </h3>
                </div>
                <div class="col-md-4 text-end">
                    <span class="badge" style="background: <?php echo $progressColor; ?>; font-size: 14px; padding: 8px 15px;">
                        <i class="fas <?php echo $statusIcon; ?>"></i>
                        <?php echo $statusText; ?>
                    </span>
                </div>
            </div>

            <!-- شريط التقدم -->
            <div class="progress-container">
                <div class="d-flex justify-content-between mb-2">
                    <span><strong>الصرف الدولي الشهري (<?php echo date('Y-m'); ?>)</strong></span>
                    <span><strong><?php echo $internationalPercentage; ?>%</strong></span>
                </div>
                <div class="progress progress-bar-custom">
                    <div class="progress-bar" style="width: <?php echo $internationalPercentage; ?>%; background-color: <?php echo $progressColor; ?>;">
                        <?php echo number_format($card['current_international_spent'], 0); ?>
                    </div>
                </div>
                <div class="d-flex justify-content-between mt-2">
                    <small>مصروف: <?php echo number_format($card['current_international_spent'], 0); ?></small>
                    <small>متبقي: <?php echo number_format($card['international_remaining'], 0); ?></small>
                    <small>الحد الأقصى: <?php echo number_format($card['daily_limit'], 0); ?></small>
                </div>
            </div>

            <!-- إحصائيات سريعة -->
            <div class="stats-grid">
                <div class="stat-card">
                    <h4><?php echo number_format($card['international_remaining'], 0); ?></h4>
                    <small>متبقي دولي</small>
                </div>
                <div class="stat-card" style="background: linear-gradient(135deg, #f44336 0%, #e57373 100%);">
                    <h4><?php echo number_format($card['current_international_spent'], 0); ?></h4>
                    <small>مصروف دولي هذا الشهر</small>
                </div>
                <div class="stat-card" style="background: linear-gradient(135deg, #4caf50 0%, #81c784 100%);">
                    <h4><?php echo number_format($card['daily_limit'], 0); ?></h4>
                    <small>الحد الشهري</small>
                </div>
                <div class="stat-card" style="background: linear-gradient(135deg, #9c27b0 0%, #ba68c8 100%);">
                    <h4><?php echo $internationalPercentage; ?>%</h4>
                    <small>نسبة الاستخدام</small>
                </div>
            </div>

            <!-- المعاملات الدولية هذا الشهر -->
            <h5 class="section-title">المعاملات الدولية هذا الشهر (<?php echo date('Y-m'); ?>)</h5>
            <?php
            // جلب المعاملات الدولية لهذه البطاقة للشهر الحالي
            $stmt = $db->prepare("
                SELECT * FROM visa_spent_transactions
                WHERE visa_card_id = ?
                AND is_international = TRUE
                AND YEAR(spent_date) = YEAR(CURDATE())
                AND MONTH(spent_date) = MONTH(CURDATE())
                AND status = 'نشط'
                ORDER BY spent_date DESC
            ");
            $stmt->execute([$cardId]);
            $internationalTransactions = $stmt->fetchAll(PDO::FETCH_ASSOC);

            if (!empty($internationalTransactions)):
                foreach ($internationalTransactions as $transaction):
            ?>
                <div class="spending-item spending-international">
                    <div class="row align-items-center">
                        <div class="col-md-3">
                            <strong><?php echo htmlspecialchars($transaction['account_name']); ?></strong>
                        </div>
                        <div class="col-md-3">
                            <span class="badge bg-danger">دولي</span>
                            <?php echo number_format($transaction['spent_amount'], 2); ?>
                        </div>
                        <div class="col-md-3">
                            <?php echo date('d-m H:i', strtotime($transaction['spent_date'])); ?>
                        </div>
                        <div class="col-md-3">
                            <small><?php echo htmlspecialchars($transaction['description']); ?></small>
                        </div>
                    </div>
                </div>
            <?php
                endforeach;
            else:
            ?>
                <div class="text-center text-muted py-4">
                    <i class="fas fa-globe fa-3x mb-3"></i>
                    <p>لا توجد معاملات دولية هذا الشهر</p>
                </div>
            <?php endif; ?>

            <!-- المعاملات المحلية هذا الشهر للمقارنة -->
            <h5 class="section-title">المعاملات المحلية هذا الشهر (آخر 10)</h5>
            <?php
            // جلب المعاملات المحلية لهذه البطاقة للشهر الحالي
            $stmt = $db->prepare("
                SELECT * FROM visa_spent_transactions
                WHERE visa_card_id = ?
                AND (is_international = FALSE OR is_international IS NULL)
                AND YEAR(spent_date) = YEAR(CURDATE())
                AND MONTH(spent_date) = MONTH(CURDATE())
                AND status = 'نشط'
                ORDER BY spent_date DESC
                LIMIT 10
            ");
            $stmt->execute([$cardId]);
            $localTransactions = $stmt->fetchAll(PDO::FETCH_ASSOC);

            if (!empty($localTransactions)):
                foreach ($localTransactions as $transaction):
            ?>
                <div class="spending-item spending-local">
                    <div class="row align-items-center">
                        <div class="col-md-3">
                            <strong><?php echo htmlspecialchars($transaction['account_name']); ?></strong>
                        </div>
                        <div class="col-md-3">
                            <span class="badge bg-success">محلي</span>
                            <?php echo number_format($transaction['spent_amount'], 2); ?>
                        </div>
                        <div class="col-md-3">
                            <?php echo date('d-m H:i', strtotime($transaction['spent_date'])); ?>
                        </div>
                        <div class="col-md-3">
                            <small><?php echo htmlspecialchars($transaction['description']); ?></small>
                        </div>
                    </div>
                </div>
            <?php
                endforeach;
            else:
            ?>
                <div class="text-center text-muted py-3">
                    <p>لا توجد معاملات محلية هذا الشهر</p>
                </div>
            <?php endif; ?>
        </div>

        <?php
            endforeach;
        } catch (PDOException $e) {
            echo '<div class="alert alert-danger">خطأ في جلب البيانات: ' . $e->getMessage() . '</div>';
        }
        ?>

        <div class="text-center mb-4">
            <a href="visa_banks_dynamic.php" class="btn btn-primary">
                <i class="fas fa-arrow-left"></i> العودة للصفحة الرئيسية
            </a>
            <a href="check_international_alerts.php" class="btn btn-warning">
                <i class="fas fa-bell"></i> التنبيهات
            </a>
            <button class="btn btn-success" onclick="location.reload()">
                <i class="fas fa-sync-alt"></i> تحديث
            </button>
        </div>
    </div>

    <!-- زر التحديث العائم -->
    <button class="btn btn-success refresh-btn" onclick="location.reload()">
        <i class="fas fa-sync-alt"></i>
    </button>

    <script>
        // تحديث تلقائي كل دقيقة
        setInterval(function() {
            location.reload();
        }, 60000);

        // تنبيه صوتي للحالات الحرجة
        document.addEventListener('DOMContentLoaded', function() {
            const criticalCards = document.querySelectorAll('.alert-critical');
            if (criticalCards.length > 0) {
                console.log('تحذير: يوجد ' + criticalCards.length + ' بطاقة في حالة حرجة!');
                // يمكن إضافة تنبيه صوتي هنا
            }
        });
    </script>
</body>
</html>
