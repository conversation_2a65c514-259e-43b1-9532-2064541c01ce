<?php
// Include configuration
require_once 'config/config.php';

// Include database connection
require_once 'includes/db.php';

// Include helper functions
require_once 'includes/functions.php';

// Include authentication functions
require_once 'includes/auth.php';

// Check if user is logged in
if (isLoggedIn()) {
    // Log activity
    logActivity('logout', 'تم تسجيل الخروج بنجاح', $_SESSION['user_id']);
    
    // Remove remember token if exists
    if (isset($_COOKIE['remember_token'])) {
        $token = $_COOKIE['remember_token'];
        
        // Delete token from database
        $query = "DELETE FROM remember_tokens WHERE token = :token";
        $stmt = $db->prepare($query);
        $stmt->bindParam(':token', $token);
        $stmt->execute();
        
        // Delete cookie
        setcookie('remember_token', '', time() - 3600, '/', '', false, true);
    }
    
    // Destroy session
    destroyUserSession();
}

// Redirect to login page
$_SESSION['flash_message'] = 'تم تسجيل الخروج بنجاح';
$_SESSION['flash_type'] = 'success';
redirect(BASE_URL . 'login.php');
?>
