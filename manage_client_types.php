<?php
// تضمين ملف التكوين
require_once 'config/config.php';

// تضمين اتصال قاعدة البيانات
require_once 'includes/db.php';

// تضمين الدوال المساعدة
require_once 'includes/functions.php';

// تضمين دوال المصادقة
require_once 'includes/auth.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    $_SESSION['flash_message'] = 'يجب تسجيل الدخول للوصول إلى إدارة فئات العملاء';
    $_SESSION['flash_type'] = 'danger';
    redirect(BASE_URL . 'index.php');
    exit;
}

// الحصول على بيانات المستخدم
$user_id = $_SESSION['user_id'];
$query = "SELECT * FROM users WHERE id = :id LIMIT 1";
$stmt = $db->prepare($query);
$stmt->bindParam(':id', $user_id);
$stmt->execute();
$user = $stmt->fetch(PDO::FETCH_ASSOC);
?>
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة فئات العملاء - <?php echo SITE_TITLE; ?></title>

    <!-- Bootstrap RTL CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Google Fonts - Cairo -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>assets/css/styles.css">
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>assets/css/manage_client_types.css">
</head>
<body>
    <!-- Main Content -->
    <div class="page-container">
        <div class="page-header">
            <a href="<?php echo BASE_URL; ?>" class="logo-link">
                <img src="<?php echo BASE_URL; ?>assets/images/logo.png" alt="<?php echo SITE_TITLE; ?>" class="page-logo">
            </a>
            <h1 class="page-title">إدارة فئات العملاء</h1>
            <a href="ad_clients.php" class="back-btn">
                <i class="fas fa-arrow-right"></i> العودة
            </a>
        </div>

        <!-- Content Area -->
        <div class="content-area">
            <div class="client-types-container">
                <div class="client-types-header">
                    <div class="header-actions">
                        <button id="addClientTypeBtn" class="btn btn-primary add-client-type-btn">
                            <i class="fas fa-plus"></i> إضافة فئة جديدة
                        </button>
                        <button id="refreshBtn" class="btn btn-secondary refresh-btn">
                            <i class="fas fa-sync-alt"></i> تحديث
                        </button>
                    </div>
                </div>

                <div class="client-types-content" id="clientTypesContent">
                    <!-- سيتم إضافة محتوى الفئات هنا بواسطة JavaScript -->
                    <div class="loading-message">جاري تحميل البيانات...</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal إضافة/تعديل فئة -->
    <div class="modal fade" id="clientTypeModal" tabindex="-1" aria-labelledby="clientTypeModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="clientTypeModalLabel">إضافة فئة جديدة</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="clientTypeForm">
                        <input type="hidden" id="clientTypeId" name="id">
                        
                        <div class="mb-3">
                            <label for="clientTypeName" class="form-label">اسم الفئة (بالإنجليزية)</label>
                            <input type="text" class="form-control" id="clientTypeName" name="name" required>
                            <small class="form-text text-muted">مثال: A, B, C, VIP, Premium</small>
                        </div>
                        
                        <div class="mb-3">
                            <label for="clientTypeDisplayName" class="form-label">اسم العرض</label>
                            <input type="text" class="form-control" id="clientTypeDisplayName" name="display_name" required>
                            <small class="form-text text-muted">مثال: Client A, عملاء مميزين, عملاء VIP</small>
                        </div>
                        
                        <div class="mb-3">
                            <label for="clientTypeDescription" class="form-label">الوصف</label>
                            <textarea class="form-control" id="clientTypeDescription" name="description" rows="3"></textarea>
                        </div>
                        
                        <div class="mb-3">
                            <label for="clientTypeColor" class="form-label">اللون</label>
                            <div class="color-input-container">
                                <input type="color" class="form-control color-picker" id="clientTypeColor" name="color" value="#4a56e2">
                                <input type="text" class="form-control color-text" id="clientTypeColorText" value="#4a56e2">
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="clientTypeSortOrder" class="form-label">ترتيب العرض</label>
                            <input type="number" class="form-control" id="clientTypeSortOrder" name="sort_order" min="0" value="0">
                            <small class="form-text text-muted">الرقم الأصغر يظهر أولاً</small>
                        </div>
                        
                        <div class="text-center">
                            <button type="submit" class="btn btn-primary">حفظ الفئة</button>
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal تأكيد الحذف -->
    <div class="modal fade" id="deleteConfirmModal" tabindex="-1" aria-labelledby="deleteConfirmModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="deleteConfirmModalLabel">تأكيد الحذف</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>هل أنت متأكد من حذف هذه الفئة؟</p>
                    <p class="text-danger"><strong>تحذير:</strong> لا يمكن التراجع عن هذا الإجراء.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-danger" id="confirmDeleteBtn">حذف</button>
                </div>
            </div>
        </div>
    </div>

    <!-- jQuery and Bootstrap JS -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Custom JS -->
    <script src="<?php echo BASE_URL; ?>assets/js/scripts.js"></script>
    <script src="<?php echo BASE_URL; ?>assets/js/manage_client_types.js"></script>
</body>
</html>
