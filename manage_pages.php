<?php
// Include configuration
require_once 'config/config.php';

// Include database connection
require_once 'includes/db.php';

// Include helper functions
require_once 'includes/functions.php';

// Include authentication functions
require_once 'includes/auth.php';

// Include permissions functions
require_once 'includes/permissions.php';

// Check if user is logged in
if (!isLoggedIn()) {
    $_SESSION['flash_message'] = 'يجب تسجيل الدخول للوصول إلى إدارة الصفحات';
    $_SESSION['flash_type'] = 'danger';
    redirect(BASE_URL . 'index.php');
}

// Get user data
$user_id = $_SESSION['user_id'];
$query = "SELECT * FROM users WHERE id = :id LIMIT 1";
$stmt = $db->prepare($query);
$stmt->bindParam(':id', $user_id);
$stmt->execute();
$user = $stmt->fetch(PDO::FETCH_ASSOC);

// Function to get clients data
function getClientsData() {
    global $db;

    try {
        // Check if update_database.php has been run
        $tableExists = false;
        $tables = $db->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);

        if (in_array('clients', $tables)) {
            $tableExists = true;
        }

        if (!$tableExists) {
            // Redirect to update_database.php if the table doesn't exist
            header("Location: update_database.php");
            exit;
        }

        // Check if the required columns exist
        try {
            $db->query("SELECT page_management, ads, payments, previous_balance, end_month_balance, payment_date FROM clients LIMIT 1");
        } catch (Exception $e) {
            // Redirect to update_database.php if the columns don't exist
            header("Location: update_database.php");
            exit;
        }

        // Get clients directly from database
        $query = "SELECT * FROM clients ORDER BY id DESC";
        $stmt = $db->prepare($query);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (Exception $e) {
        // If there's an error, return empty array
        error_log("Error getting clients data: " . $e->getMessage());
        return [];
    }
}

// Handle API requests
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_GET['api'])) {
    header('Content-Type: application/json');

    // Add new client - Removed as we're using API

    // جلب خدمات إدارة الصفحة
    if ($_GET['api'] === 'get_page_services' && isset($_GET['client_id'])) {
        try {
            $client_id = (int)$_GET['client_id'];

            // التحقق من وجود العميل
            $query = "SELECT id, name FROM clients WHERE id = :id";
            $stmt = $db->prepare($query);
            $stmt->bindParam(':id', $client_id);
            $stmt->execute();

            $client = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$client) {
                throw new Exception('العميل غير موجود');
            }

            // جلب خدمات إدارة الصفحة
            $query = "SELECT id, name, price FROM page_services WHERE client_id = :client_id ORDER BY id ASC";
            $stmt = $db->prepare($query);
            $stmt->bindParam(':client_id', $client_id);
            $stmt->execute();

            $services = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // حساب المجموع
            $total = 0;
            foreach ($services as $service) {
                $total += (float)$service['price'];
            }

            $response = [
                'success' => true,
                'client' => $client,
                'services' => $services,
                'total' => $total
            ];

            echo json_encode($response);
            exit;
        } catch (Exception $e) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => $e->getMessage()]);
            exit;
        }
    }

    // Delete client
    if ($_GET['api'] === 'delete_client' && isset($_POST['id'])) {
        try {
            $client_id = (int)$_POST['id'];

            // Delete from database
            $query = "DELETE FROM clients WHERE id = :id";
            $stmt = $db->prepare($query);
            $stmt->bindParam(':id', $client_id);
            $stmt->execute();

            if ($stmt->rowCount() === 0) {
                throw new Exception('العميل غير موجود');
            }

            $response = [
                'success' => true,
                'message' => 'تم حذف العميل بنجاح'
            ];

            echo json_encode($response);
            exit;
        } catch (Exception $e) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => $e->getMessage()]);
            exit;
        }
    }

    // حفظ خدمات إدارة الصفحة
    if ($_GET['api'] === 'save_services' && $_SERVER['CONTENT_TYPE'] === 'application/json') {
        try {
            // قراءة البيانات من الطلب
            $json = file_get_contents('php://input');
            $data = json_decode($json, true);

            if (!$data || !isset($data['client_id']) || !isset($data['services']) || !isset($data['total'])) {
                throw new Exception('بيانات غير صالحة');
            }

            $client_id = (int)$data['client_id'];
            $services = $data['services'];
            $total = (float)$data['total'];

            // التحقق من وجود العميل
            $query = "SELECT id FROM clients WHERE id = :id";
            $stmt = $db->prepare($query);
            $stmt->bindParam(':id', $client_id);
            $stmt->execute();

            if ($stmt->rowCount() === 0) {
                throw new Exception('العميل غير موجود');
            }

            // بدء المعاملة
            $db->beginTransaction();

            try {
                // تحديث إجمالي إدارة الصفحة للعميل
                $query = "UPDATE clients SET page_management = :total WHERE id = :id";
                $stmt = $db->prepare($query);
                $stmt->bindParam(':total', $total);
                $stmt->bindParam(':id', $client_id);
                $stmt->execute();

                // حذف الخدمات الحالية للعميل
                $query = "DELETE FROM page_services WHERE client_id = :client_id";
                $stmt = $db->prepare($query);
                $stmt->bindParam(':client_id', $client_id);
                $stmt->execute();

                // إضافة الخدمات الجديدة
                if (!empty($services)) {
                    $query = "INSERT INTO page_services (client_id, name, price) VALUES (:client_id, :name, :price)";
                    $stmt = $db->prepare($query);

                    foreach ($services as $service) {
                        $name = $service['name'];
                        $price = $service['price'];

                        $stmt->bindParam(':client_id', $client_id);
                        $stmt->bindParam(':name', $name);
                        $stmt->bindParam(':price', $price);
                        $stmt->execute();
                    }
                }

                // تأكيد المعاملة
                $db->commit();
            } catch (Exception $e) {
                // التراجع عن المعاملة في حالة حدوث خطأ
                $db->rollBack();
                throw $e;
            }

            $response = [
                'success' => true,
                'message' => 'تم حفظ خدمات إدارة الصفحة بنجاح'
            ];

            echo json_encode($response);
            exit;
        } catch (Exception $e) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => $e->getMessage()]);
            exit;
        }
    }

    // Invalid API request
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'طلب غير صالح']);
    exit;
}

// Get clients data
$pages = getClientsData();
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الصفحات - <?php echo SITE_TITLE; ?></title>

    <!-- Bootstrap RTL CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Google Fonts - Cairo -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>assets/css/styles.css">
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>assets/css/manage_pages.css">
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>assets/css/ads.css">
</head>
<body>
    <div class="page-container">
        <div class="page-header">
            <a href="<?php echo BASE_URL; ?>" class="logo-link">
                <img src="<?php echo BASE_URL; ?>assets/images/logo.png" alt="<?php echo SITE_TITLE; ?>" class="page-logo">
            </a>
            <h1 class="page-title">إدارة الصفحات</h1>
            <button class="add-client-btn">
                إضافة عميل
                <i class="fas fa-user-circle"></i>
            </button>
        </div>

        <div class="search-container">
            <a href="admin_reports.php" class="admin-accounts">حسابات الإدارة</a>
            <div class="search-box">
                <input type="text" id="searchInput" class="search-input" placeholder="ابحث عن اسم العميل...">
                <i class="fas fa-search search-icon"></i>
            </div>
        </div>

        <!-- رسالة عدم وجود نتائج -->
        <div class="no-results" id="noResults">
            لا توجد نتائج مطابقة لبحثك
        </div>

        <div class="pages-grid">
            <?php foreach ($pages as $page): ?>
            <div class="page-card">
                <div class="page-card-header">
                    <div class="page-name"><?php echo $page['name']; ?></div>
                    <div class="page-options dropdown">
                        <i class="fas fa-ellipsis-h dropdown-toggle" data-page-id="<?php echo $page['id']; ?>"></i>
                        <div class="dropdown-menu dropdown-menu-end">
                            <a href="#" class="dropdown-item payment-day">تاريخ الدفع: <?php echo $page['payment_day']; ?></a>
                            <a href="#" class="dropdown-item delete-client">إزالة العميل</a>
                            <a href="#" class="dropdown-item">ملاحظات</a>
                            <a href="#" class="dropdown-item">استخدامات</a>
                        </div>
                    </div>
                </div>
                <table class="page-details">
                    <tr class="page-management-row" data-client-id="<?php echo $page['id']; ?>">
                        <td style="display: block; width: 60%;" class="clickable-cell">إدارة الصفحة</td>
                        <td class="clickable-cell"><?php echo number_format($page['page_management']); ?></td>
                    </tr>
                    <tr class="ads-row" data-client-id="<?php echo $page['id']; ?>">
                        <td style="display: block; width: 60%;" class="clickable-cell">إعلانات</td>
                        <td class="clickable-cell">
                            <?php echo number_format($page['ads_ratio'] ?? $page['ads']); ?>
                        </td>
                    </tr>
                    <tr class="payments-row" data-client-id="<?php echo $page['id']; ?>">
                        <td style=" width: 60%;" class="clickable-cell">المدفوعات</td>
                        <td class="clickable-cell"><?php echo number_format($page['payments']); ?></td>
                    </tr>
                    <tr class="account-row" data-client-id="<?php echo $page['id']; ?>">
                        <td style="width: 60%;" class="clickable-cell">اجمالي حساب سابق</td>
                        <td class="clickable-cell"><?php echo number_format($page['previous_balance']); ?></td>
                    </tr>

                    <tr>
                        <td style=" display: table-cell; width: 60%;">اجمالي حساب العميل نهاية الشهر</td>
                        <td class="balance"><?php echo number_format($page['end_month_balance']); ?></td>
                    </tr>

                </table>
            </div>
            <?php endforeach; ?>
        </div>
    </div>

    <!-- Modal تفاصيل إدارة الصفحة -->
    <div class="modal fade" id="pageManagementModal" tabindex="-1" aria-labelledby="pageManagementModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="pageManagementModalLabel">إدارة الصفحة</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="services-grid" id="pageManagementDetails">
                        <div class="service-card" data-id="1" data-name="Content + Graphic" data-price="2500">
                            <div class="edit-icons">
                                <div class="edit-service"><i class="fas fa-edit"></i></div>
                                <div class="delete-service"><i class="fas fa-times"></i></div>
                            </div>
                            <div class="service-price">2500</div>
                            <div class="service-name">Content + Graphic</div>
                        </div>
                        <div class="service-card" data-id="2" data-name="لجنة إلكترونية" data-price="5500">
                            <div class="edit-icons">
                                <div class="edit-service"><i class="fas fa-edit"></i></div>
                                <div class="delete-service"><i class="fas fa-times"></i></div>
                            </div>
                            <div class="service-price">5500</div>
                            <div class="service-name">لجنة إلكترونية</div>
                        </div>
                        <div class="service-card" data-id="3" data-name="لجنة متابعة" data-price="7500">
                            <div class="edit-icons">
                                <div class="edit-service"><i class="fas fa-edit"></i></div>
                                <div class="delete-service"><i class="fas fa-times"></i></div>
                            </div>
                            <div class="service-price">7500</div>
                            <div class="service-name">لجنة متابعة</div>
                        </div>
                    </div>

                    <div class="add-service-container">
                        <button id="addServiceBtn" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-plus"></i> إضافة خدمة
                        </button>
                    </div>

                    <div id="addServiceForm" style="display: none; margin-top: 15px;">
                        <div class="row">
                            <div class="col-md-6 mb-2">
                                <input type="text" class="form-control" id="newServiceName" placeholder="اسم الخدمة">
                            </div>
                            <div class="col-md-4 mb-2">
                                <input type="number" class="form-control" id="newServicePrice" placeholder="السعر">
                            </div>
                            <div class="col-md-2 mb-2">
                                <button id="saveServiceBtn" class="btn btn-success w-100">
                                    <i class="fas fa-check"></i> إضافة
                                </button>
                            </div>
                        </div>
                    </div>

                    <div id="editServiceForm" style="display: none; margin-top: 15px;">
                        <div class="row">
                            <div class="col-md-6 mb-2">
                                <input type="text" class="form-control" id="editServiceName" placeholder="اسم الخدمة">
                                <input type="hidden" id="editServiceId">
                            </div>
                            <div class="col-md-4 mb-2">
                                <input type="number" class="form-control" id="editServicePrice" placeholder="السعر">
                            </div>
                            <div class="col-md-2 mb-2">
                                <button id="updateServiceBtn" class="btn btn-primary w-100">
                                    <i class="fas fa-save"></i> تحديث
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="separator"></div>

                    <div class="total-row">
                        <div class="total-label">إجمالي إدارة الصفحة</div>
                        <div class="total-value" id="totalPageManagement">11.700</div>
                    </div>

                    <div class="save-changes-container">
                        <button id="saveChangesBtn" class="btn btn-primary mt-3">
                            <i class="fas fa-save"></i> حفظ التغييرات
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal الإعلانات -->
    <div class="modal fade" id="adsModal" tabindex="-1" aria-labelledby="adsModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <div class="modal-title-container">
                        <div class="filter-dropdown">
                            <button type="button" class="btn-collapse">
                                <i class="fas fa-chevron-down"></i>
                                <span>الكل</span>
                            </button>
                            <ul class="filter-menu" style="display: none; position: absolute; z-index: 1000;">
                                <li><a class="dropdown-item active" href="#" data-filter="all">الكل</a></li>
                                <li><a class="dropdown-item" href="#" data-filter="مستمر">مستمر</a></li>
                                <li><a class="dropdown-item" href="#" data-filter="متوقف">متوقف</a></li>
                            </ul>
                        </div>
                        <h5 class="modal-title" id="adsModalLabel">الإعلانات</h5>
                    </div>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="ads-container">
                        <div class="ads-header">
                            <div class="row">
                                <div class="col">تاريخ</div>
                                <div class="col">نوع</div>
                                <div class="col">يومي / إجمالي</div>
                                <div class="col">بوست</div>
                                <div class="col">تكلفة</div>
                                <div class="col">حالة الاعلان</div>
                                <div class="col">الأكونت</div>
                                <div class="col">الصرف بالمصري</div>
                            </div>
                        </div>
                        <div class="ads-list" id="adsList">
                            <!-- سيتم إضافة الإعلانات هنا بواسطة JavaScript -->
                        </div>
                        <div class="ads-footer">
                            <div class="row">
                                <div class="col-5 text-start">
                                    <div class="ads-total">
                                        <span>إجمالي الصرف بالنسبة:</span>
                                        <span id="totalAdsSpendRatio" class="total-value editable-percentage">910</span>
                                    </div>
                                </div>
                                <div class="col-5 text-end">
                                    <div class="ads-total">
                                        <span>إجمالي الصرف:</span>
                                        <span id="totalAdsSpend" class="total-value">910</span>
                                    </div>
                                </div>
                                <div class="col-2">
                                    <div class="add-ad-container">
                                        <button id="addAdBtn" class="btn-add-ad">
                                            <i class="fas fa-plus"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal المدفوعات -->
    <div class="modal fade" id="paymentsModal" tabindex="-1" aria-labelledby="paymentsModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="paymentsModalLabel">المدفوعات</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="payments-container">
                        <div class="payments-header">
                            <div class="row">
                                <div class="col">تاريخ الدفع</div>
                                <div class="col">طريقة الدفع</div>
                                <div class="col">المبلغ</div>
                            </div>
                        </div>
                        <div class="payments-list" id="paymentsList">
                            <!-- سيتم إضافة المدفوعات هنا بواسطة JavaScript -->
                        </div>
                        <div class="payments-footer">
                            <div class="row">
                                <div class="col-6 text-start">
                                    <div class="payments-total">
                                        <span>إجمالي المدفوعات:</span>
                                        <span id="totalPayments" class="total-value">0</span>
                                    </div>
                                </div>
                                <div class="col-6 text-end" style="display: flex ; position: relative; right: 93px;">
                                    <button id="payPreviousMonthBtn" class="btn-pay-previous" title="دفع المتبقي من الشهر السابق">
                                        <i class="fas fa-history"></i>
                                    </button>
                                    <button id="addPaymentBtn" class="btn-add-payment">
                                        <i class="fas fa-plus"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal إضافة مبلغ -->
    <div class="modal fade" id="addPaymentModal" tabindex="-1" aria-labelledby="addPaymentModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addPaymentModalLabel">إضافة مبلغ</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="add-payment-container">
                        <div class="add-payment-header">
                            <div class="row">
                                <div class="col">تاريخ الدفع</div>
                                <div class="col">طريقة الدفع</div>
                                <div class="col">المبلغ</div>
                            </div>
                        </div>
                        <div class="add-payment-form">
                            <div class="row">
                                <div class="col">
                                    <input type="date" class="form-control" id="paymentDate" required>
                                </div>
                                <div class="col">
                                    <select class="form-control" id="paymentMethod" required>
                                        <option value="نقدي">نقدي</option>
                                        <option value="كاش">كاش</option>
                                        <option value="انستا باي">انستا باي</option>
                                    </select>
                                </div>
                                <div class="col">
                                    <input type="number" class="form-control" id="paymentAmount" required>
                                </div>
                            </div>
                        </div>
                        <div class="separator-line"></div>
                        <div class="add-payment-footer">
                            <div class="row">
                                <div class="col-8 text-start">
                                    <div class="payment-total">
                                        <span>إجمالي المبلغ بعد التزويد:</span>
                                        <span id="totalAfterAddition" class="total-value">0</span>
                                    </div>
                                </div>
                                <div class="col-4 text-end">
                                    <button id="submitPaymentBtn" class="btn-add-payment-submit">إضافة</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal اختيار النسبة -->
    <div class="modal fade" id="percentageModal" tabindex="-1" aria-labelledby="percentageModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="percentageModalLabel">اختيار الصرف بالنسبة</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="percentage-container">
                        <div class="percentage-header">
                            <span>اختيار النسبة</span>
                            <span id="currentPercentage" class="percentage-value">% 45</span>
                        </div>
                        <div class="percentage-slider-container">
                            <input type="range" class="form-range" id="percentageSlider" min="0" max="100" step="5" value="45">
                        </div>
                        <div class="percentage-input-container">
                            <div class="input-group">
                                <input type="number" class="form-control" id="percentageInput" min="0" max="100" value="45">
                                <span class="input-group-text">%</span>
                            </div>
                        </div>
                        <div class="percentage-footer">
                            <button type="button" class="btn btn-primary" id="savePercentageBtn">حفظ</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal إضافة إعلان -->
    <div class="modal fade" id="addAdModal" tabindex="-1" aria-labelledby="addAdModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addAdModalLabel">إضافة اعلان</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="addAdForm">
                        <input type="hidden" id="adClientId">

                        <div class="add-ad-header">
                            <div class="row">
                                <div class="col">تاريخ</div>
                                <div class="col">نوع</div>
                                <div class="col">يومي / إجمالي</div>
                                <div class="col">بوست</div>
                                <div class="col">عدد أيام</div>
                                <div class="col">التكلفة</div>
                                <div class="col">الصرف</div>
                                <div class="col">الأكونت</div>
                            </div>
                        </div>

                        <div class="add-ad-content">
                            <div class="row">
                                <div class="col">
                                    <input type="date" class="form-control" id="adDate" required>
                                </div>
                                <div class="col">
                                    <select class="form-control" id="adType" required>
                                        <option value="جديد">جديد</option>
                                        <option value="قديم">قديم</option>
                                    </select>
                                </div>
                                <div class="col">
                                    <select class="form-control" id="adPaymentType" required>
                                        <option value="يومي">يومي</option>
                                        <option value="إجمالي">إجمالي</option>
                                    </select>
                                </div>
                                <div class="col">
                                    <input type="text" class="form-control" id="adPost" placeholder="بوست" required>
                                </div>
                                <div class="col">
                                    <input type="number" class="form-control" id="adDays" value="1" min="1" required>
                                </div>
                                <div class="col">
                                    <input type="number" class="form-control" id="adCost" required>
                                </div>
                                <div class="col">
                                    <input type="number" class="form-control" id="adExchangeRate" value="30" required>
                                </div>
                                <div class="col">
                                    <select class="form-control" id="adAccount" required>
                                        <option value="">-- اختر الحساب --</option>
                                        <?php
                                        // جلب الحسابات الإعلانية النشطة
                                        try {
                                            $stmt = $db->prepare("SELECT id, name FROM ad_accounts WHERE status = 'نشط' ORDER BY name ASC");
                                            $stmt->execute();
                                            $adAccounts = $stmt->fetchAll(PDO::FETCH_ASSOC);

                                            foreach ($adAccounts as $account) {
                                                echo '<option value="' . $account['id'] . '">' . htmlspecialchars($account['name']) . '</option>';
                                            }
                                        } catch (PDOException $e) {
                                            // في حالة حدوث خطأ، عرض خيار افتراضي
                                            echo '<option value="1">ليلى اسامة</option>';
                                        }
                                        ?>
                                    </select>
                                </div>
                            </div>
                            <div class="separator-line"></div>
                        </div>

                        <div class="add-ad-footer">
                            <button type="submit" class="btn btn-add-ad-submit">إضافة اعلان</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal إضافة عميل -->
    <div class="modal fade" id="addClientModal" tabindex="-1" aria-labelledby="addClientModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header" style="background-color: #4a56e2; color: white;">
                    <h5 class="modal-title" id="addClientModalLabel">إضافة عميل جديد</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close" style="color: white;"></button>
                </div>
                <div class="modal-body">
                    <form id="addClientForm">
                        <div class="mb-3">
                            <label for="clientName" class="form-label">اسم العميل</label>
                            <input type="text" class="form-control" id="clientName" name="name" required>
                        </div>
                        <div class="mb-3">
                            <label for="previousDebt" class="form-label">المديونية السابقة</label>
                            <input type="number" class="form-control" id="previousDebt" name="previous_debt" required>
                        </div>
                        <div class="mb-3">
                            <label for="paymentDay" class="form-label">يوم الدفع من كل شهر</label>
                            <select class="form-control" id="paymentDay" name="payment_day" required>
                                <?php for ($i = 1; $i <= 31; $i++) : ?>
                                    <option value="<?php echo $i; ?>"><?php echo $i; ?></option>
                                <?php endfor; ?>
                            </select>
                        </div>
                        <div class="text-center">
                            <button type="submit" class="btn" style="background-color: #4a56e2; color: white;">إضافة العميل</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal إجمالي حساب سابق -->
    <div class="modal fade prev-account-modal" id="previousAccountModal" tabindex="-1" aria-labelledby="previousAccountModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="prev-print-btn" title="طباعة" id="printAccountBtn">
                        <i class="fas fa-print"></i>
                    </button>
                    <h5 class="modal-title" id="previousAccountModalLabel">إجمالي حساب سابق</h5>
                    <button type="button" class="prev-close-btn" data-bs-dismiss="modal" aria-label="Close">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="prev-account-content" id="previousAccountContent">
                        <!-- سيتم إضافة محتوى الحساب السابق هنا بواسطة JavaScript -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal حساب العميل نهاية الشهر -->
    <div class="modal fade" id="endMonthBalanceModal" tabindex="-1" aria-labelledby="endMonthBalanceModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="end-month-print-btn" title="طباعة" id="printEndMonthBtn">
                        <i class="fas fa-print"></i>
                    </button>
                    <h5 class="modal-title" id="endMonthBalanceModalLabel">حساب العميل نهاية الشهر</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="end-month-balance-container">
                        <div class="end-month-tabs">
                            <ul class="nav nav-tabs" id="endMonthTabs" role="tablist">
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link active" id="services-tab" data-bs-toggle="tab" data-bs-target="#services-content" type="button" role="tab" aria-controls="services-content" aria-selected="true">إدارة الصفحة</button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="ads-tab" data-bs-toggle="tab" data-bs-target="#ads-content" type="button" role="tab" aria-controls="ads-content" aria-selected="false">إعلانات</button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="payments-tab" data-bs-toggle="tab" data-bs-target="#payments-content" type="button" role="tab" aria-controls="payments-content" aria-selected="false">المدفوعات</button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="previous-tab" data-bs-toggle="tab" data-bs-target="#previous-content" type="button" role="tab" aria-controls="previous-content" aria-selected="false">حسابات سابقة</button>
                                </li>
                            </ul>
                            <div class="tab-content" id="endMonthTabContent">
                                <div class="tab-pane fade show active" id="services-content" role="tabpanel" aria-labelledby="services-tab">
                                    <div class="services-content" id="servicesContent">
                                        <!-- سيتم إضافة محتوى إدارة الصفحة هنا بواسطة JavaScript -->
                                        <div class="loading-spinner">جاري التحميل...</div>
                                    </div>
                                </div>
                                <div class="tab-pane fade" id="ads-content" role="tabpanel" aria-labelledby="ads-tab">
                                    <div class="ads-content" id="adsContent">
                                        <!-- سيتم إضافة محتوى الإعلانات هنا بواسطة JavaScript -->
                                        <div class="loading-spinner">جاري التحميل...</div>
                                    </div>
                                </div>
                                <div class="tab-pane fade" id="payments-content" role="tabpanel" aria-labelledby="payments-tab">
                                    <div class="payments-content" id="paymentsContent">
                                        <!-- سيتم إضافة محتوى المدفوعات هنا بواسطة JavaScript -->
                                        <div class="loading-spinner">جاري التحميل...</div>
                                    </div>
                                </div>
                                <div class="tab-pane fade" id="previous-content" role="tabpanel" aria-labelledby="previous-tab">
                                    <div class="previous-content" id="previousContent">
                                        <!-- سيتم إضافة محتوى الحسابات السابقة هنا بواسطة JavaScript -->
                                        <div class="loading-spinner">جاري التحميل...</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="end-month-footer">
                            <div class="totals-summary">
                                <div class="totals-row">
                                    <div class="total-item">
                                        <span class="total-item-label">إدارة الصفحة:</span>
                                        <span class="total-item-value" id="totalServices">0</span>
                                    </div>
                                    <div class="total-item">
                                        <span class="total-item-label">الإعلانات:</span>
                                        <span class="total-item-value" id="totalAds">0</span>
                                    </div>
                                    <div class="total-item">
                                        <span class="total-item-label">المدفوعات:</span>
                                        <span class="total-item-value" id="totalPayments">0</span>
                                    </div>
                                    <div class="total-item">
                                        <span class="total-item-label">حسابات سابقة:</span>
                                        <span class="total-item-value" id="totalPrevious">0</span>
                                    </div>
                                </div>
                            </div>
                            <div class="total-balance">
                                <span class="total-label" style="color:#3a46d2;">إجمالي حساب العميل نهاية الشهر:</span>
                                <span class="total-value" id="totalEndMonthBalance">0</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- jQuery and Bootstrap JS -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Custom JS -->
    <script src="<?php echo BASE_URL; ?>assets/js/scripts.js"></script>
    <script src="<?php echo BASE_URL; ?>assets/js/manage_pages.js"></script>
    <script src="<?php echo BASE_URL; ?>assets/js/ads.js"></script>
    <script src="<?php echo BASE_URL; ?>assets/js/payments.js"></script>
    <script src="<?php echo BASE_URL; ?>assets/js/previous_account.js"></script>
    <script src="<?php echo BASE_URL; ?>assets/js/end_month_balance.js"></script>

</body>
</html>
