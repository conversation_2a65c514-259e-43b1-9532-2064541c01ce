<?php
// تضمين ملف الإعدادات
require_once 'config/config.php';

// تضمين اتصال قاعدة البيانات
require_once 'includes/db.php';

// تضمين الدوال المساعدة
require_once 'includes/functions.php';

// تضمين دوال المصادقة
require_once 'includes/auth.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    $_SESSION['flash_message'] = 'يجب تسجيل الدخول للوصول إلى صفحة مرتبات المكتب';
    $_SESSION['flash_type'] = 'danger';
    redirect(BASE_URL . 'index.php');
}

// الحصول على الشهر الحالي
$currentMonth = date('n');
$currentYear = date('Y');
$selectedMonth = isset($_GET['month']) ? intval($_GET['month']) : $currentMonth;
$selectedYear = isset($_GET['year']) ? intval($_GET['year']) : $currentYear;

// التحقق من وجود جدول مرتبات المكتب وإنشائه إذا لم يكن موجودًا
try {
    $checkTableQuery = "SHOW TABLES LIKE 'office_salaries'";
    $stmt = $db->prepare($checkTableQuery);
    $stmt->execute();
    $tableExists = $stmt->rowCount() > 0;

    if (!$tableExists) {
        // إنشاء جدول مرتبات المكتب
        $createTableQuery = "CREATE TABLE office_salaries (
            id INT AUTO_INCREMENT PRIMARY KEY,
            employee_id INT NOT NULL,
            month INT NOT NULL,
            year INT NOT NULL,
            salary DECIMAL(10,2) NOT NULL,
            advance DECIMAL(10,2) DEFAULT 0,
            deductions VARCHAR(255) DEFAULT NULL,
            deduction_amount DECIMAL(10,2) DEFAULT 0,
            commission DECIMAL(10,2) DEFAULT 0,
            overtime VARCHAR(255) DEFAULT NULL,
            overtime_amount DECIMAL(10,2) DEFAULT 0,
            net_salary DECIMAL(10,2) DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (employee_id) REFERENCES salaries(id) ON DELETE CASCADE,
            UNIQUE KEY (employee_id, month, year)
        )";
        $db->exec($createTableQuery);

        // لا نحتاج لإضافة بيانات تجريبية هنا، سنستخدم ملف initialize_office_salaries.php بدلاً من ذلك
    }

    // جلب بيانات موظفي المكتب للشهر المحدد
    $query = "SELECT os.*, s.employee_name
              FROM office_salaries os
              JOIN salaries s ON os.employee_id = s.id
              WHERE os.month = ? AND os.year = ? AND s.is_office = 1
              ORDER BY s.employee_name ASC";
    $stmt = $db->prepare($query);
    $stmt->execute([$selectedMonth, $selectedYear]);
    $officeSalaries = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // حساب إجمالي المرتبات
    $totalQuery = "SELECT SUM(net_salary) as total
                  FROM office_salaries
                  WHERE month = ? AND year = ?";
    $stmt = $db->prepare($totalQuery);
    $stmt->execute([$selectedMonth, $selectedYear]);
    $totalSalaries = $stmt->fetch(PDO::FETCH_ASSOC)['total'] ?? 0;

} catch (PDOException $e) {
    $dbErrorMessage = '<div class="alert alert-danger">خطأ في قاعدة البيانات: ' . $e->getMessage() . '</div>';
}

// أسماء الشهور بالعربية
$arabicMonths = [
    1 => 'يناير',
    2 => 'فبراير',
    3 => 'مارس',
    4 => 'أبريل',
    5 => 'مايو',
    6 => 'يونيو',
    7 => 'يوليو',
    8 => 'أغسطس',
    9 => 'سبتمبر',
    10 => 'أكتوبر',
    11 => 'نوفمبر',
    12 => 'ديسمبر'
];

$monthName = $arabicMonths[$selectedMonth];
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مرتبات المكتب - <?php echo SITE_TITLE; ?></title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap');

        body {
            font-family: 'Tajawal', sans-serif;
            background-color: #fff;
            margin: 0;
            padding: 0;
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 20px;
            border-bottom: 1px solid #e0e0e0;
        }

        .logo {
            height: 40px;
        }

        .page-title {
            color: #4a4ad4;
            text-align: center;
            font-size: 24px;
            font-weight: bold;
        }

        .month-selector {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 10px 20px;
            border-bottom: 1px solid #e0e0e0;
        }

        .month-icon {
            color: #4a4ad4;
            font-size: 20px;
        }

        .month-name {
            font-weight: bold;
            color: #4a4ad4;
        }

        .current-month {
            background-color: #f0f0f0;
            padding: 5px 10px;
            border-radius: 5px;
            margin-right: 10px;
        }

        .filter-tabs {
            display: flex;
            gap: 10px;
        }

        .filter-tab {
            padding: 5px 15px;
            border-radius: 20px;
            color: #4a4ad4;
            text-decoration: none;
            font-weight: bold;
        }

        .filter-tab.active {
            background-color: #4a4ad4;
            color: white;
        }

        .search-box {
            margin-right: auto;
            position: relative;
        }

        .search-input {
            border: 1px solid #e0e0e0;
            border-radius: 20px;
            padding: 5px 15px 5px 35px;
            width: 300px;
        }

        .search-icon {
            position: absolute;
            left: 10px;
            top: 50%;
            transform: translateY(-50%);
            color: #999;
        }

        .table-container {
            position: relative;
            margin-top: 20px;
            height: calc(100vh - 150px);
            overflow: hidden;
        }

        .salaries-table {
            width: 100%;
            border-collapse: collapse;
        }

        .salaries-table th {
            background-color: #4a4ad4;
            color: white;
            text-align: center;
            padding: 10px;
            font-weight: bold;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .salaries-table tfoot td {
            background-color: #f5f5f5;
            position: sticky;
            bottom: 0;
            z-index: 10;
            border-top: 2px solid #dcf343;
            font-weight: bold;
        }

        .table-body-container {
            height: calc(100vh - 230px);
            overflow-y: auto;
        }

        .salaries-table td {
            border: 1px solid #dcf343;
            padding: 10px;
            text-align: center;
        }

        .salaries-table tbody tr:hover {
            background-color: #f9f9f9;
        }

        .editable {
            cursor: pointer;
        }

        .editable:hover {
            background-color: #f0f0f0;
        }

        .total-row {
            background-color: #f5f5f5;
            font-weight: bold;
        }

        .total-row td {
            border-top: 2px solid #dcf343;
        }

        .employee-name {
            color: #4a4ad4;
            font-weight: bold;
        }

        .modal-header {
            background-color: #4a4ad4;
            color: white;
        }

        .modal-footer .btn-primary {
            background-color: #4a4ad4;
            border-color: #4a4ad4;
        }
    </style>
</head>
<body>
    <?php if (isset($dbErrorMessage)) echo $dbErrorMessage; ?>

    <div class="header">
        <a href="<?php echo BASE_URL; ?>">
            <img src="<?php echo BASE_URL; ?>assets/images/logo.png" alt="<?php echo SITE_TITLE; ?>" class="logo">
        </a>
        <h1 class="page-title">المرتبات</h1>
        <div></div>
    </div>

    <div class="month-selector">
        <div class="month-icon">
            <i class="fas fa-calendar-alt"></i>
        </div>
        <div class="month-name">شهر <?php echo $monthName; ?></div>
        <div class="current-month">شهر <?php echo $currentMonth; ?></div>
        <div class="filter-tabs" style="margin-right: 20px;">
            <a href="external_salaries.php" class="filter-tab">مرتبات</a>
            <a href="office_salaries.php" class="filter-tab active">مرتبات المكتب</a>
        </div>
        <div class="search-box">
            <input type="text" class="search-input" placeholder="البحث" id="searchInput">
            <i class="fas fa-search search-icon"></i>
        </div>
    </div>

    <div class="container-fluid mt-3">
        <div class="table-container">
            <table class="salaries-table" id="salariesTable">
                <thead>
                    <tr>
                        <th>الموظف</th>
                        <th>المرتب</th>
                        <th>سلف</th>
                        <th>خصومات</th>
                        <th>كوميشن</th>
                        <th>اوفرتايم</th>
                        <th>صافي</th>
                    </tr>
                </thead>
            </table>

            <div class="table-body-container">
                <table class="salaries-table" id="salariesTableBody">
                    <tbody>
                        <?php foreach ($officeSalaries as $salary): ?>
                        <tr data-id="<?php echo $salary['id']; ?>">
                            <td class="employee-name"><?php echo htmlspecialchars($salary['employee_name']); ?></td>
                            <td class="editable" data-field="salary"><?php echo number_format($salary['salary'], 3); ?></td>
                            <td class="editable" data-field="advance"><?php echo $salary['advance'] > 0 ? number_format($salary['advance'], 3) : '—'; ?></td>
                            <td class="editable" data-field="deduction_amount"><?php echo $salary['deduction_amount'] > 0 ? number_format($salary['deduction_amount'], 3) : '—'; ?></td>
                            <td class="editable" data-field="commission"><?php echo number_format($salary['commission'], 3); ?></td>
                            <td class="editable" data-field="overtime_amount"><?php echo $salary['overtime_amount'] > 0 ? number_format($salary['overtime_amount'], 3) : '—'; ?></td>
                            <td class="editable" data-field="net_salary"><?php echo number_format($salary['net_salary'], 3); ?></td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>

            <table class="salaries-table" id="salariesTableFooter">
                <tfoot>
                    <tr class="total-row">
                        <td colspan="6" style="text-align: left;">إجمالي المرتبات</td>
                        <td><?php echo number_format($totalSalaries, 3); ?></td>
                    </tr>
                </tfoot>
            </table>
        </div>
    </div>

    <!-- Modal تعديل الحقل -->
    <div class="modal fade" id="editFieldModal" tabindex="-1" aria-labelledby="editFieldModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editFieldModalLabel">تعديل الحقل</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="editFieldForm">
                        <input type="hidden" id="salaryId" name="salary_id">
                        <input type="hidden" id="fieldName" name="field_name">
                        <div class="mb-3">
                            <label for="fieldValue" class="form-label" id="fieldLabel">القيمة</label>
                            <input type="text" class="form-control" id="fieldValue" name="field_value">
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" id="saveFieldBtn">حفظ</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // تعديل الحقول
        const editableCells = document.querySelectorAll('.editable');
        const editFieldModal = new bootstrap.Modal(document.getElementById('editFieldModal'));

        editableCells.forEach(cell => {
            cell.addEventListener('dblclick', function() {
                const row = this.closest('tr');
                const salaryId = row.getAttribute('data-id');
                const fieldName = this.getAttribute('data-field');
                const fieldValue = this.textContent.replace('—', '').replace(/,/g, '');

                document.getElementById('salaryId').value = salaryId;
                document.getElementById('fieldName').value = fieldName;
                document.getElementById('fieldValue').value = fieldValue;

                // تعيين عنوان الحقل
                let fieldLabel = '';
                switch(fieldName) {
                    case 'salary': fieldLabel = 'المرتب'; break;
                    case 'advance': fieldLabel = 'سلف'; break;
                    case 'deduction_amount': fieldLabel = 'خصومات'; break;
                    case 'commission': fieldLabel = 'كوميشن'; break;
                    case 'overtime_amount': fieldLabel = 'اوفرتايم'; break;
                    case 'net_salary': fieldLabel = 'صافي'; break;
                }
                document.getElementById('fieldLabel').textContent = fieldLabel;

                editFieldModal.show();
            });
        });

        // حفظ التعديلات
        document.getElementById('saveFieldBtn').addEventListener('click', function() {
            const salaryId = document.getElementById('salaryId').value;
            const fieldName = document.getElementById('fieldName').value;
            const fieldValue = document.getElementById('fieldValue').value;

            // إرسال البيانات إلى الخادم
            fetch('update_office_salary.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `salary_id=${salaryId}&field_name=${fieldName}&field_value=${fieldValue}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // تحديث الخلية في الجدول
                    const cell = document.querySelector(`tr[data-id="${salaryId}"] td[data-field="${fieldName}"]`);

                    // تنسيق القيمة
                    let displayValue = fieldValue;
                    if (fieldName !== 'deductions' && fieldName !== 'overtime') {
                        displayValue = parseFloat(fieldValue) > 0 ?
                            parseFloat(fieldValue).toLocaleString('en-US', {minimumFractionDigits: 3, maximumFractionDigits: 3}) :
                            '—';
                    } else {
                        displayValue = fieldValue.trim() !== '' ? fieldValue : '—';
                    }

                    cell.textContent = displayValue;

                    // تحديث صافي المرتب إذا تم تغيير أي حقل آخر
                    if (fieldName !== 'net_salary') {
                        const netSalaryCell = document.querySelector(`tr[data-id="${salaryId}"] td[data-field="net_salary"]`);
                        netSalaryCell.textContent = parseFloat(data.net_salary).toLocaleString('en-US', {minimumFractionDigits: 3, maximumFractionDigits: 3});
                    }

                    // تحديث إجمالي المرتبات
                    const totalCell = document.querySelector('.total-row td:last-child');
                    totalCell.textContent = parseFloat(data.total_salaries).toLocaleString('en-US', {minimumFractionDigits: 3, maximumFractionDigits: 3});

                    editFieldModal.hide();
                } else {
                    alert(data.message || 'حدث خطأ أثناء تحديث البيانات');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('حدث خطأ أثناء تحديث البيانات');
            });
        });

        // البحث
        document.getElementById('searchInput').addEventListener('keyup', function() {
            const searchTerm = this.value.toLowerCase();
            const rows = document.querySelectorAll('#salariesTableBody tbody tr');

            rows.forEach(row => {
                const employeeName = row.querySelector('.employee-name').textContent.toLowerCase();
                if (employeeName.includes(searchTerm)) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        });
    </script>
</body>
</html>
