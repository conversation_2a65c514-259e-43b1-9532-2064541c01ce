<?php
// Include configuration
require_once 'config/config.php';

// Include database connection
require_once 'includes/db.php';

// Include helper functions
require_once 'includes/functions.php';

// Include authentication functions
require_once 'includes/auth.php';

// Include permissions functions
require_once 'includes/permissions.php';

// Check if user is logged in
if (!isLoggedIn()) {
    $_SESSION['flash_message'] = 'يجب تسجيل الدخول للوصول إلى تفاصيل الصفحات';
    $_SESSION['flash_type'] = 'danger';
    redirect(BASE_URL . 'index.php');
}

// Get user data
$user_id = $_SESSION['user_id'];
$query = "SELECT * FROM users WHERE id = :id LIMIT 1";
$stmt = $db->prepare($query);
$stmt->bindParam(':id', $user_id);
$stmt->execute();
$user = $stmt->fetch(PDO::FETCH_ASSOC);

// Function to get clients data
function getClientsData() {
    global $db;

    try {
        // Check if clients table exists
        $tableExists = false;
        $tables = $db->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);

        if (in_array('clients', $tables)) {
            $tableExists = true;
        }

        if (!$tableExists) {
            // Redirect to update_database.php if the table doesn't exist
            header("Location: update_database.php");
            exit;
        }

        // Check if design_cost column exists
        $checkColumnQuery = "SHOW COLUMNS FROM clients LIKE 'design_cost'";
        $checkColumnStmt = $db->prepare($checkColumnQuery);
        $checkColumnStmt->execute();
        $columnExists = $checkColumnStmt->rowCount() > 0;

        if (!$columnExists) {
            // Add design_cost column
            $addColumnQuery = "ALTER TABLE clients ADD COLUMN design_cost DECIMAL(10,2) DEFAULT 0";
            $db->exec($addColumnQuery);
        }

        // Get clients directly from database
        $query = "SELECT * FROM clients ORDER BY id DESC";
        $stmt = $db->prepare($query);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (Exception $e) {
        // If there's an error, return empty array
        error_log("Error getting clients data: " . $e->getMessage());
        return [];
    }
}

// Handle API requests
if (isset($_GET['api'])) {
    header('Content-Type: application/json');

    // Update design cost
    if ($_GET['api'] === 'update_design_cost' && $_SERVER['REQUEST_METHOD'] === 'POST') {
        try {
            // Get POST data
            $client_id = isset($_POST['client_id']) ? (int)$_POST['client_id'] : 0;
            $design_cost = isset($_POST['design_cost']) ? (float)$_POST['design_cost'] : 0;

            // Validate data
            if (!$client_id) {
                throw new Exception('معرف العميل مطلوب');
            }

            // Check if client exists
            $query = "SELECT id FROM clients WHERE id = :id";
            $stmt = $db->prepare($query);
            $stmt->bindParam(':id', $client_id);
            $stmt->execute();

            if ($stmt->rowCount() === 0) {
                throw new Exception('العميل غير موجود');
            }

            // Check if design_cost column exists
            $checkColumnQuery = "SHOW COLUMNS FROM clients LIKE 'design_cost'";
            $checkColumnStmt = $db->prepare($checkColumnQuery);
            $checkColumnStmt->execute();
            $columnExists = $checkColumnStmt->rowCount() > 0;

            if (!$columnExists) {
                // Add design_cost column
                $addColumnQuery = "ALTER TABLE clients ADD COLUMN design_cost DECIMAL(10,2) DEFAULT 0";
                $db->exec($addColumnQuery);
            }

            // Update design cost
            $updateQuery = "UPDATE clients SET design_cost = :design_cost WHERE id = :client_id";
            $updateStmt = $db->prepare($updateQuery);
            $updateStmt->bindParam(':design_cost', $design_cost);
            $updateStmt->bindParam(':client_id', $client_id);
            $updateStmt->execute();

            // Get updated client data
            $clientQuery = "SELECT id, page_management, design_cost FROM clients WHERE id = :id";
            $clientStmt = $db->prepare($clientQuery);
            $clientStmt->bindParam(':id', $client_id);
            $clientStmt->execute();
            $client = $clientStmt->fetch(PDO::FETCH_ASSOC);

            // Get total salaries
            $salariesQuery = "SELECT COALESCE(SUM(salary), 0) as total FROM client_employees_new WHERE client_id = :client_id";
            $salariesStmt = $db->prepare($salariesQuery);
            $salariesStmt->bindParam(':client_id', $client_id);
            $salariesStmt->execute();
            $salariesTotal = $salariesStmt->fetch(PDO::FETCH_ASSOC)['total'];

            // Calculate expected profit
            $pageManagement = $client['page_management'];
            $designCost = $client['design_cost'];
            $expectedProfit = $pageManagement - $salariesTotal - $designCost;

            $response = [
                'success' => true,
                'message' => 'تم تحديث تكلفة التصميم بنجاح',
                'design_cost' => $designCost,
                'design_cost_formatted' => number_format($designCost),
                'expected_profit' => $expectedProfit,
                'expected_profit_formatted' => number_format($expectedProfit),
                'salaries_total' => $salariesTotal,
                'salaries_total_formatted' => number_format($salariesTotal)
            ];

            echo json_encode($response);
            exit;
        } catch (Exception $e) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => $e->getMessage()]);
            exit;
        }
    }

    // Get page services
    if ($_GET['api'] === 'get_page_services' && isset($_GET['client_id'])) {
        try {
            $client_id = (int)$_GET['client_id'];

            // Check if client exists
            $query = "SELECT id, name FROM clients WHERE id = :id";
            $stmt = $db->prepare($query);
            $stmt->bindParam(':id', $client_id);
            $stmt->execute();

            $client = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$client) {
                throw new Exception('العميل غير موجود');
            }

            // Get page services
            $query = "SELECT id, name, price FROM page_services WHERE client_id = :client_id ORDER BY id ASC";
            $stmt = $db->prepare($query);
            $stmt->bindParam(':client_id', $client_id);
            $stmt->execute();

            $services = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // Calculate total
            $total = 0;
            foreach ($services as $service) {
                $total += (float)$service['price'];
            }

            $response = [
                'success' => true,
                'client' => $client,
                'services' => $services,
                'total' => $total
            ];

            echo json_encode($response);
            exit;
        } catch (Exception $e) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => $e->getMessage()]);
            exit;
        }
    }

    // Get client employees
    if ($_GET['api'] === 'get_client_employees' && isset($_GET['client_id'])) {
        try {
            $client_id = (int)$_GET['client_id'];

            // Check if client exists
            $query = "SELECT id, name FROM clients WHERE id = :id";
            $stmt = $db->prepare($query);
            $stmt->bindParam(':id', $client_id);
            $stmt->execute();

            $client = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$client) {
                throw new Exception('العميل غير موجود');
            }

            // Check if client_employees_new table exists (we'll create a new table to avoid conflicts)
            $checkTableQuery = "SHOW TABLES LIKE 'client_employees_new'";
            $checkTableStmt = $db->prepare($checkTableQuery);
            $checkTableStmt->execute();
            $tableExists = $checkTableStmt->rowCount() > 0;

            // Create new table if it doesn't exist
            if (!$tableExists) {
                $createTableQuery = "CREATE TABLE client_employees_new (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    client_id INT NOT NULL,
                    employee_name VARCHAR(255) NOT NULL,
                    role VARCHAR(100) NOT NULL,
                    salary DECIMAL(10,2) NOT NULL,
                    work_hours INT DEFAULT 0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    INDEX (client_id),
                    FOREIGN KEY (client_id) REFERENCES clients(id) ON DELETE CASCADE
                )";
                $db->exec($createTableQuery);

                // Try to copy data from old table if it exists
                try {
                    $oldTableQuery = "SHOW TABLES LIKE 'client_employees'";
                    $oldTableStmt = $db->prepare($oldTableQuery);
                    $oldTableStmt->execute();
                    $oldTableExists = $oldTableStmt->rowCount() > 0;

                    if ($oldTableExists) {
                        // Check if the old table has the required columns
                        $columnsQuery = "SHOW COLUMNS FROM client_employees";
                        $columnsStmt = $db->prepare($columnsQuery);
                        $columnsStmt->execute();
                        $columns = $columnsStmt->fetchAll(PDO::FETCH_COLUMN);

                        if (in_array('client_id', $columns) && in_array('employee_name', $columns) &&
                            in_array('role', $columns) && in_array('salary', $columns)) {
                            // Copy data from old table
                            $copyQuery = "INSERT INTO client_employees_new (client_id, employee_name, role, salary, work_hours)
                                         SELECT client_id, employee_name, role, salary, work_hours
                                         FROM client_employees";
                            $db->exec($copyQuery);
                        }
                    }
                } catch (Exception $e) {
                    // Ignore errors during data migration
                    error_log("Error migrating data: " . $e->getMessage());
                }
            }

            // Get client employees from new table
            $query = "SELECT id, employee_name, role, salary, work_hours FROM client_employees_new WHERE client_id = :client_id ORDER BY id ASC";
            $stmt = $db->prepare($query);
            $stmt->bindParam(':client_id', $client_id);
            $stmt->execute();

            $employees = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // Calculate total
            $total = 0;
            foreach ($employees as $employee) {
                $total += (float)$employee['salary'];
            }

            $response = [
                'success' => true,
                'client' => $client,
                'employees' => $employees,
                'total' => $total
            ];

            echo json_encode($response);
            exit;
        } catch (Exception $e) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => $e->getMessage()]);
            exit;
        }
    }

    // Add employee to client
    if ($_GET['api'] === 'add_client_employee' && $_SERVER['REQUEST_METHOD'] === 'POST') {
        try {
            // Get POST data
            $client_id = isset($_POST['client_id']) ? (int)$_POST['client_id'] : 0;
            $employee_name = isset($_POST['employee_name']) ? trim($_POST['employee_name']) : '';
            $role = isset($_POST['role']) ? trim($_POST['role']) : '';
            $salary = isset($_POST['salary']) ? (float)$_POST['salary'] : 0;
            $work_hours = isset($_POST['work_hours']) ? (int)$_POST['work_hours'] : 0;

            // Debug
            error_log("Adding employee: client_id=$client_id, name=$employee_name, role=$role, salary=$salary, hours=$work_hours");

            // Validate data
            if (!$client_id || empty($employee_name) || empty($role) || $salary <= 0) {
                throw new Exception('جميع الحقول مطلوبة');
            }

            // Check if client exists
            $query = "SELECT id FROM clients WHERE id = :id";
            $stmt = $db->prepare($query);
            $stmt->bindParam(':id', $client_id);
            $stmt->execute();

            if ($stmt->rowCount() === 0) {
                throw new Exception('العميل غير موجود');
            }

            // Insert employee into new table
            $query = "INSERT INTO client_employees_new (client_id, employee_name, role, salary, work_hours) VALUES (:client_id, :employee_name, :role, :salary, :work_hours)";
            $stmt = $db->prepare($query);
            $stmt->bindParam(':client_id', $client_id);
            $stmt->bindParam(':employee_name', $employee_name);
            $stmt->bindParam(':role', $role);
            $stmt->bindParam(':salary', $salary);
            $stmt->bindParam(':work_hours', $work_hours);
            $stmt->execute();

            $employee_id = $db->lastInsertId();

            $response = [
                'success' => true,
                'message' => 'تمت إضافة الموظف بنجاح',
                'employee' => [
                    'id' => $employee_id,
                    'employee_name' => $employee_name,
                    'role' => $role,
                    'salary' => $salary,
                    'work_hours' => $work_hours
                ]
            ];

            echo json_encode($response);
            exit;
        } catch (Exception $e) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => $e->getMessage()]);
            exit;
        }
    }

    // Invalid API request
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'طلب غير صالح']);
    exit;
}

// Get clients data
$clients = getClientsData();
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تفاصيل اخرى للصفحات - <?php echo SITE_TITLE; ?></title>

    <!-- Bootstrap RTL CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Google Fonts - Cairo -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>assets/css/styles.css">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f8f9fa;
            margin: 0;
            padding: 0;
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 20px;
            background-color: white;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }

        .logo {
            height: 40px;
        }

        .page-title {
            font-size: 22px;
            color: #4a56e2;
            text-align: center;
            flex-grow: 1;
        }

        .add-employee-btn {
            background-color: #4a56e2;
            color: white;
            border: none;
            border-radius: 5px;
            padding: 8px 15px;
            font-size: 14px;
            cursor: pointer;
        }

        .search-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 20px;
            background-color: white;
            border-bottom: 1px solid #eee;
        }

        .accounts-link {
            color: #4a56e2;
            text-decoration: none;
            font-weight: 500;
        }

        .search-box {
            position: relative;
            width: 300px;
        }

        .search-input {
            width: 100%;
            padding: 8px 15px 8px 35px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }

        .search-icon {
            position: absolute;
            left: 10px;
            top: 50%;
            transform: translateY(-50%);
            color: #aaa;
        }

        .pages-container {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
            gap: 20px;
            padding: 20px;
        }

        .page-box {
            width: 100%;
            max-width: 360px;
            margin: 0 auto;
            background-color: white;
        }

        .page-header {
            background-color: #4a56e2;
            color: white;
            padding: 10px;
            text-align: center;
            font-weight: 600;
        }

        .page-content table {
            width: 100%;
            border-collapse: collapse;
        }

        .page-content table tr {
            border-bottom: 1px solid #dcf343;
        }

        .page-content table tr:last-child {
            border-bottom: none;
        }

        .page-content table td {
            padding: 8px 10px;
            font-size: 14px;
            text-align: center;
            border-right: 1px solid #dcf343;
        }

        .page-content table td:first-child {
            border-right: none;
            color: #4a56e2;
        }

        .page-content table td:nth-child(2) {
            text-align: right;
            color: #4a56e2;
        }

        .page-management {
            cursor: pointer;
        }

        /* Modal Styles - Common */
        .modal-common {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            justify-content: center;
            align-items: center;
        }

        .modal-content {
            background-color: white;
            border-radius: 5px;
            width: 90%;
            max-width: 700px;
            max-height: 80vh;
            overflow-y: auto;
        }

        .modal-header {
            background-color: #4a56e2;
            color: white;
            padding: 10px 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-title {
            font-weight: 600;
            font-size: 18px;
            text-align: center;
            flex-grow: 1;
        }

        .close-btn {
            background: none;
            border: none;
            color: white;
            font-size: 24px;
            cursor: pointer;
        }

        .modal-divider {
            width: 100%;
            height: 1px;
            background-color: #dcf343;
            margin: 10px 0 20px 0;
        }

        .modal-footer {
            background-color: #f8f9fa;
            padding: 10px 15px;
            display: flex;
            justify-content: space-between;
            font-weight: 600;
            border-top: 1px solid #ddd;
        }

        .footer-label {
            color: #666;
            font-size: 16px;
        }

        .footer-value {
            color: #4a56e2;
            font-size: 18px;
        }

        /* Services Modal Specific */
        .services-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            justify-content: center;
            align-items: center;
        }

        .services-content {
            background-color: white;
            border-radius: 5px;
            width: 90%;
            max-width: 700px;
            max-height: 80vh;
            overflow-y: auto;
        }

        .services-header {
            background-color: #4a56e2;
            color: white;
            padding: 10px 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .services-title {
            font-weight: 600;
            font-size: 18px;
            text-align: center;
            flex-grow: 1;
        }

        .services-list {
            padding: 15px;
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
        }

        .service-item {
            width: 30%;
            text-align: center;
            margin-bottom: 20px;
        }

        .service-price {
            font-weight: 600;
            color: #4a56e2;
            font-size: 18px;
            margin-bottom: 5px;
        }

        .service-name {
            font-weight: 500;
            color: #666;
        }

        .services-divider {
            width: 100%;
            height: 1px;
            background-color: #dcf343;
            margin: 10px 0 20px 0;
        }

        .services-total {
            background-color: #f8f9fa;
            padding: 10px 15px;
            display: flex;
            justify-content: space-between;
            font-weight: 600;
            border-top: 1px solid #ddd;
        }

        .total-label {
            color: #666;
            font-size: 16px;
        }

        .total-value {
            color: #4a56e2;
            font-size: 18px;
        }

        /* Salaries Modal Specific */
        .salaries-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            justify-content: center;
            align-items: center;
        }

        .salaries-content {
            background-color: white;
            border-radius: 5px;
            width: 90%;
            max-width: 900px;
            max-height: 80vh;
            overflow-y: auto;
        }

        .salaries-header {
            background-color: #4a56e2;
            color: white;
            padding: 10px 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .salaries-title {
            font-weight: 600;
            font-size: 18px;
            text-align: center;
            flex-grow: 1;
        }

        .salaries-table {
            width: 100%;
            border-collapse: collapse;
        }

        .salaries-table td {
            padding: 10px;
            text-align: center;
            border: 1px solid #dcf343;
        }

        .salaries-table td:nth-child(odd) {
            color: #4a56e2;
        }

        .salaries-table td:nth-child(even) {
            color: #4a56e2;
        }

        .salaries-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            background-color: #f8f9fa;
            border-top: 1px solid #ddd;
        }

        .salaries-total-label {
            font-weight: 600;
            color: #4a56e2;
            font-size: 18px;
            text-align: right;
        }

        .salaries-total-value {
            font-weight: 600;
            color: #4a56e2;
            font-size: 18px;
        }

        .add-salary-btn {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: #4CAF50;
            color: white;
            border: none;
            font-size: 24px;
            cursor: pointer;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        /* Add Employee Modal Specific */
        .add-employee-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 1100;
            justify-content: center;
            align-items: center;
        }

        .add-employee-content {
            background-color: white;
            border-radius: 5px;
            width: 90%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
        }

        .add-employee-header {
            background-color: #4a56e2;
            color: white;
            padding: 10px 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .add-employee-title {
            font-weight: 600;
            font-size: 18px;
            text-align: center;
            flex-grow: 1;
        }

        .add-employee-form {
            padding: 20px;
        }

        .form-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
        }

        .form-group {
            width: 30%;
            text-align: center;
        }

        .form-group label {
            display: block;
            margin-bottom: 10px;
            color: #4a56e2;
            font-weight: 600;
        }

        .form-group input, .form-group select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            text-align: center;
        }

        .form-divider {
            width: 100%;
            height: 1px;
            background-color: #dcf343;
            margin: 20px 0;
        }

        .add-employee-btn {
            display: block;
            width: 150px;
            padding: 10px;
            background-color: #4a56e2;
            color: white;
            border: none;
            border-radius: 5px;
            margin: 20px auto 0;
            cursor: pointer;
            font-weight: 600;
        }

        .no-results {
            display: none;
            text-align: center;
            padding: 20px;
            background-color: white;
            margin: 20px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body>
    <div class="header">
        <a href="<?php echo BASE_URL; ?>">
            <img src="<?php echo BASE_URL; ?>assets/images/logo.png" alt="<?php echo SITE_TITLE; ?>" class="logo">
        </a>
        <div class="page-title">تفاصيل اخرى للصفحات</div>
        <button class="add-employee-btn">اضافة موظف <i class="fas fa-user-plus"></i></button>
    </div>

    <div class="search-container">
        <a href="<?php echo BASE_URL; ?>profit_accounts.php" class="accounts-link">حسابات الربح</a>
        <div class="search-box">
            <input type="text" id="searchInput" class="search-input" placeholder="البحث...">
            <i class="fas fa-search search-icon"></i>
        </div>
    </div>

    <!-- رسالة عدم وجود نتائج -->
    <div class="no-results" id="noResults">
        لا توجد نتائج مطابقة لبحثك
    </div>

    <div class="pages-container" id="pagesContainer">
        <?php foreach ($clients as $client): ?>
        <div class="page-box" data-client-id="<?php echo $client['id']; ?>">
            <div class="page-header"><?php echo $client['name']; ?></div>
            <div class="page-content">
                <table>
                    <tr class="page-management" data-client-id="<?php echo $client['id']; ?>">
                        <td><?php echo number_format($client['page_management']); ?></td>
                        <td>ادارة الصفحة</td>
                    </tr>
                    <tr class="page-budgets" data-client-id="<?php echo $client['id']; ?>">
                        <td><?php echo number_format($client['ads']); ?></td>
                        <td>الميزانيات</td>
                    </tr>
                    <tr class="page-salaries" data-client-id="<?php echo $client['id']; ?>">
                        <td class="salaries-value" id="salaries-<?php echo $client['id']; ?>">
                            <?php
                                // Get total salaries for this client
                                $salariesQuery = "SELECT COALESCE(SUM(salary), 0) as total FROM client_employees_new WHERE client_id = :client_id";
                                $salariesStmt = $db->prepare($salariesQuery);
                                $salariesStmt->bindParam(':client_id', $client['id']);
                                $salariesStmt->execute();
                                $salariesTotal = $salariesStmt->fetch(PDO::FETCH_ASSOC)['total'];
                                echo number_format($salariesTotal);
                            ?>
                        </td>
                        <td>اجمالي مرتبات</td>
                    </tr>
                    <tr class="page-designs" data-client-id="<?php echo $client['id']; ?>">
                        <td class="designs-value" id="designs-<?php echo $client['id']; ?>" data-value="<?php echo $client['design_cost'] ?? 0; ?>" ondblclick="editDesignCost(<?php echo $client['id']; ?>)">
                            <?php echo number_format($client['design_cost'] ?? 0); ?>
                        </td>
                        <td>تقدير تكلفة التصميمات</td>
                    </tr>
                    <tr class="page-profit" data-client-id="<?php echo $client['id']; ?>">
                        <td class="profit-value" id="profit-<?php echo $client['id']; ?>">
                            <?php
                                // Calculate expected profit
                                $pageManagement = $client['page_management'];
                                $designCost = $client['design_cost'] ?? 0;
                                $expectedProfit = $pageManagement - $salariesTotal - $designCost;
                                echo number_format($expectedProfit);
                            ?>
                        </td>
                        <td>اجمالي الربح المتوقع</td>
                    </tr>
                </table>
            </div>
        </div>
        <?php endforeach; ?>
    </div>

    <!-- Modal للخدمات -->
    <div class="services-modal" id="servicesModal">
        <div class="services-content">
            <div class="services-header">
                <div></div>
                <div class="services-title" id="servicesTitle">ادارة الصفحة</div>
                <button class="close-btn" id="closeServicesBtn">×</button>
            </div>
            <div class="services-list" id="servicesList">
                <!-- سيتم إضافة الخدمات هنا بواسطة JavaScript -->
            </div>
            <div class="services-divider"></div>
            <div class="services-total">
                <div class="total-label">اجمالي ادارة الصفحة</div>
                <div class="total-value" id="servicesTotal">0</div>
            </div>
        </div>
    </div>

    <!-- Modal للمرتبات -->
    <div class="salaries-modal" id="salariesModal">
        <div class="salaries-content">
            <div class="salaries-header">
                <div></div>
                <div class="salaries-title">المرتبات</div>
                <button class="close-btn" id="closeSalariesBtn">×</button>
            </div>
            <table class="salaries-table" id="salariesTable">
                <!-- سيتم إضافة الموظفين هنا بواسطة JavaScript -->
            </table>
            <div class="salaries-footer">
                <button class="add-salary-btn" id="addSalaryBtn">+</button>
                <div class="salaries-total-label">اجمالي المرتبات</div>
                <div class="salaries-total-value" id="salariesTotalValue">0</div>
            </div>
        </div>
    </div>

    <!-- Modal لإضافة موظف -->
    <div class="add-employee-modal" id="addEmployeeModal">
        <div class="add-employee-content">
            <div class="add-employee-header">
                <div></div>
                <div class="add-employee-title">اضافة موظف</div>
                <button class="close-btn" id="closeAddEmployeeBtn">×</button>
            </div>
            <form id="addEmployeeForm" class="add-employee-form">
                <input type="hidden" id="clientId" name="client_id">
                <div class="form-row">
                    <div class="form-group">
                        <label>اسم الموظف</label>
                        <input type="text" id="employeeName" name="employee_name" placeholder="اسم الموظف" required>
                    </div>
                    <div class="form-group">
                        <label>الوظيفة</label>
                        <select id="employeeRole" name="role" required>
                            <option value="" disabled selected>الوظيفة</option>
                            <option value="مدير">مدير</option>
                            <option value="مصمم">مصمم</option>
                            <option value="مسوق">مسوق</option>
                            <option value="كاتب محتوى">كاتب محتوى</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>عدد ساعات العمل</label>
                        <input type="number" id="workHours" name="work_hours" placeholder="عدد الساعات" value="0">
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label>المرتب</label>
                        <input type="number" id="salary" name="salary" placeholder="المرتب" required>
                    </div>
                    <div class="form-group">
                        <!-- فارغ للمحاذاة -->
                    </div>
                    <div class="form-group">
                        <!-- فارغ للمحاذاة -->
                    </div>
                </div>
                <div class="form-divider"></div>
                <button type="submit" class="add-employee-btn">اضافة موظف</button>
            </form>
        </div>
    </div>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        $(document).ready(function() {
            // البحث في الصفحات
            $("#searchInput").on("keyup", function() {
                const value = $(this).val().toLowerCase();
                let visibleCount = 0;

                $(".page-box").each(function() {
                    const clientName = $(this).find(".page-header").text().toLowerCase();
                    const isVisible = clientName.indexOf(value) > -1;
                    $(this).toggle(isVisible);

                    if (isVisible) {
                        visibleCount++;
                    }
                });

                // إظهار رسالة عدم وجود نتائج إذا لم يتم العثور على أي عميل
                $("#noResults").toggle(visibleCount === 0);
            });

            // إظهار نافذة الخدمات عند النقر على إدارة الصفحة
            $(".page-management").on("click", function() {
                const clientId = $(this).data("client-id");
                const clientName = $(this).closest(".page-box").find(".page-header").text();

                // تحديث عنوان النافذة
                $("#servicesTitle").text("ادارة الصفحة");

                // تحميل الخدمات
                loadClientServices(clientId);

                // إظهار النافذة
                $("#servicesModal").css("display", "flex");
            });

            // إظهار نافذة المرتبات عند النقر على المرتبات
            $(".page-content table tr:nth-child(2)").on("click", function() {
                const clientId = $(this).closest(".page-box").data("client-id");
                const clientName = $(this).closest(".page-box").find(".page-header").text();

                // تحميل الموظفين
                loadClientEmployees(clientId);

                // إظهار النافذة
                $("#salariesModal").css("display", "flex");
            });

            // إغلاق نافذة الخدمات
            $("#closeServicesBtn").on("click", function() {
                $("#servicesModal").css("display", "none");
            });

            // إغلاق نافذة المرتبات
            $("#closeSalariesBtn").on("click", function() {
                $("#salariesModal").css("display", "none");
            });

            // فتح نافذة إضافة موظف عند النقر على زر +
            $("#addSalaryBtn").on("click", function() {
                // التأكد من أن معرف العميل موجود
                if (!$("#clientId").val()) {
                    const clientId = $(".page-box").first().data("client-id");
                    $("#clientId").val(clientId);
                }
                $("#addEmployeeModal").css("display", "flex");
            });

            // إغلاق نافذة إضافة موظف
            $("#closeAddEmployeeBtn").on("click", function() {
                $("#addEmployeeModal").css("display", "none");
            });

            // إغلاق النوافذ عند النقر خارجها
            $(window).on("click", function(event) {
                if ($(event.target).is("#servicesModal")) {
                    $("#servicesModal").css("display", "none");
                }
                if ($(event.target).is("#salariesModal")) {
                    $("#salariesModal").css("display", "none");
                }
                if ($(event.target).is("#addEmployeeModal")) {
                    $("#addEmployeeModal").css("display", "none");
                }
            });

            // دالة لتحميل خدمات العميل
            function loadClientServices(clientId) {
                $.ajax({
                    url: `page_details.php?api=get_page_services&client_id=${clientId}`,
                    type: "GET",
                    dataType: "json",
                    success: function(data) {
                        if (data.success) {
                            // عرض الخدمات
                            const servicesList = $("#servicesList");
                            servicesList.empty();

                            if (data.services.length === 0) {
                                servicesList.html('<div class="no-services">لا توجد خدمات</div>');
                            } else {
                                // عرض الخدمات من قاعدة البيانات بنفس التصميم
                                data.services.forEach(function(service) {
                                    servicesList.append(`
                                        <div class="service-item">
                                            <div class="service-price">${Number(service.price).toLocaleString()}</div>
                                            <div class="service-name">${service.name}</div>
                                        </div>
                                    `);
                                });
                            }

                            // تحديث الإجمالي من قاعدة البيانات
                            $("#servicesTotal").text(Number(data.total).toLocaleString());
                        } else {
                            $("#servicesList").html(`<div class="error-message">${data.message || 'حدث خطأ أثناء تحميل الخدمات'}</div>`);
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error("AJAX Error:", status, error);
                        $("#servicesList").html('<div class="error-message">حدث خطأ أثناء تحميل الخدمات</div>');
                    }
                });
            }

            // دالة لتحميل موظفي العميل
            function loadClientEmployees(clientId) {
                $.ajax({
                    url: `page_details.php?api=get_client_employees&client_id=${clientId}`,
                    type: "GET",
                    dataType: "json",
                    success: function(data) {
                        if (data.success) {
                            // تخزين معرف العميل للاستخدام في إضافة موظف جديد
                            $("#clientId").val(clientId);

                            // عرض الموظفين
                            const salariesTable = $("#salariesTable");
                            salariesTable.empty();

                            if (data.employees.length === 0) {
                                salariesTable.html('<tr><td colspan="4" style="text-align: center;">لا يوجد موظفين</td></tr>');
                                $("#salariesTotalValue").text("0");
                            } else {
                                // تنظيم الموظفين في صفوف بحيث يكون هناك موظفين في كل صف
                                let employeesHTML = '';
                                for (let i = 0; i < data.employees.length; i += 2) {
                                    employeesHTML += '<tr>';

                                    // الموظف الأول
                                    employeesHTML += `<td>${data.employees[i].employee_name}</td>`;
                                    employeesHTML += `<td>${Number(data.employees[i].salary).toLocaleString()}</td>`;

                                    // الموظف الثاني (إذا وجد)
                                    if (i + 1 < data.employees.length) {
                                        employeesHTML += `<td>${data.employees[i+1].employee_name}</td>`;
                                        employeesHTML += `<td>${Number(data.employees[i+1].salary).toLocaleString()}</td>`;
                                    } else {
                                        employeesHTML += '<td></td><td></td>';
                                    }

                                    employeesHTML += '</tr>';
                                }

                                salariesTable.html(employeesHTML);

                                // تحديث الإجمالي
                                $("#salariesTotalValue").text(Number(data.total).toLocaleString());
                            }
                        } else {
                            $("#salariesTable").html(`<tr><td colspan="4" style="text-align: center;">${data.message || 'حدث خطأ أثناء تحميل الموظفين'}</td></tr>`);
                            $("#salariesTotalValue").text("0");
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error("AJAX Error:", status, error);
                        $("#salariesTable").html('<tr><td colspan="4" style="text-align: center;">حدث خطأ أثناء تحميل الموظفين</td></tr>');
                        $("#salariesTotalValue").text("0");
                    }
                });
            }

            // معالجة نموذج إضافة موظف
            $("#addEmployeeForm").on("submit", function(e) {
                e.preventDefault();

                // طباعة البيانات للتصحيح
                console.log("Form data:", $(this).serialize());
                console.log("Client ID:", $("#clientId").val());

                // التحقق من البيانات
                const clientId = $("#clientId").val();
                const employeeName = $("#employeeName").val();
                const role = $("#employeeRole").val();
                const salary = $("#salary").val();

                if (!clientId || !employeeName || !role || !salary) {
                    alert("يرجى ملء جميع الحقول المطلوبة");
                    return;
                }

                $.ajax({
                    url: "page_details.php?api=add_client_employee",
                    type: "POST",
                    data: $(this).serialize(),
                    dataType: "json",
                    success: function(data) {
                        if (data.success) {
                            // إعادة تحميل الموظفين
                            loadClientEmployees($("#clientId").val());

                            // إغلاق نافذة إضافة موظف
                            $("#addEmployeeModal").css("display", "none");

                            // إعادة تعيين النموذج
                            $("#addEmployeeForm")[0].reset();
                        } else {
                            alert(data.message || "حدث خطأ أثناء إضافة الموظف");
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error("AJAX Error:", status, error);
                        console.error("Response:", xhr.responseText);
                        alert("حدث خطأ أثناء إضافة الموظف");
                    }
                });
            });
        });

        // وظيفة لتحرير تكلفة التصميم
        function editDesignCost(clientId) {
            const designCell = document.getElementById(`designs-${clientId}`);
            const currentValue = designCell.getAttribute('data-value') || 0;

            // إنشاء حقل الإدخال
            const input = document.createElement('input');
            input.type = 'number';
            input.value = currentValue;
            input.style.width = '100%';
            input.style.textAlign = 'center';
            input.style.border = '1px solid #4a56e2';
            input.style.borderRadius = '4px';
            input.style.padding = '5px';

            // استبدال النص بحقل الإدخال
            designCell.innerHTML = '';
            designCell.appendChild(input);
            input.focus();

            // معالجة الضغط على Enter
            input.addEventListener('keydown', function(e) {
                if (e.key === 'Enter') {
                    updateDesignCost(clientId, input.value);
                }
            });

            // معالجة فقدان التركيز
            input.addEventListener('blur', function() {
                updateDesignCost(clientId, input.value);
            });
        }

        // وظيفة لتحديث تكلفة التصميم في قاعدة البيانات
        function updateDesignCost(clientId, designCost) {
            $.ajax({
                url: 'page_details.php?api=update_design_cost',
                type: 'POST',
                data: {
                    client_id: clientId,
                    design_cost: designCost
                },
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        // تحديث خلية تكلفة التصميم
                        const designCell = document.getElementById(`designs-${clientId}`);
                        designCell.innerHTML = response.design_cost_formatted;
                        designCell.setAttribute('data-value', response.design_cost);

                        // تحديث خلية الربح المتوقع
                        const profitCell = document.getElementById(`profit-${clientId}`);
                        profitCell.innerHTML = response.expected_profit_formatted;

                        // تحديث خلية إجمالي المرتبات (للتأكد)
                        const salariesCell = document.getElementById(`salaries-${clientId}`);
                        salariesCell.innerHTML = response.salaries_total_formatted;
                    } else {
                        alert(response.message || 'حدث خطأ أثناء تحديث تكلفة التصميم');

                        // إعادة القيمة القديمة
                        const designCell = document.getElementById(`designs-${clientId}`);
                        designCell.innerHTML = designCell.getAttribute('data-value');
                    }
                },
                error: function(xhr, status, error) {
                    console.error('AJAX Error:', status, error);
                    alert('حدث خطأ أثناء تحديث تكلفة التصميم');

                    // إعادة القيمة القديمة
                    const designCell = document.getElementById(`designs-${clientId}`);
                    designCell.innerHTML = designCell.getAttribute('data-value');
                }
            });
        }
    </script>
</body>
</html>
