<?php
// Include configuration
require_once 'config/config.php';

// Include database connection
require_once 'includes/db.php';

// Include helper functions
require_once 'includes/functions.php';

// Include authentication functions
require_once 'includes/auth.php';

// Include permissions functions
require_once 'includes/permissions.php';

// Check if user is logged in
if (!isLoggedIn()) {
    $_SESSION['flash_message'] = 'يجب تسجيل الدخول للوصول إلى حسابات الربح';
    $_SESSION['flash_type'] = 'danger';
    redirect(BASE_URL . 'index.php');
}

// Get user data
$user_id = $_SESSION['user_id'];
$query = "SELECT * FROM users WHERE id = :id LIMIT 1";
$stmt = $db->prepare($query);
$stmt->bindParam(':id', $user_id);
$stmt->execute();
$user = $stmt->fetch(PDO::FETCH_ASSOC);

// Function to get profit accounts data
function getProfitAccountsData() {
    global $db;

    try {
        // Check if clients table exists
        $tableExists = false;
        $tables = $db->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);

        if (in_array('clients', $tables)) {
            $tableExists = true;
        }

        if (!$tableExists) {
            // Redirect to update_database.php if the table doesn't exist
            header("Location: update_database.php");
            exit;
        }

        // Get clients with profit calculations
        $query = "SELECT
                    c.id,
                    c.name,
                    c.page_management as total_income,
                    COALESCE(c.design_cost, 0) as design_cost,
                    COALESCE((SELECT SUM(salary) FROM client_employees_new WHERE client_id = c.id), 0) as salaries,
                    c.page_management as base_profit,
                    COALESCE(c.design_cost, 0) + COALESCE((SELECT SUM(salary) FROM client_employees_new WHERE client_id = c.id), 0) as total_expenses,
                    c.page_management - (COALESCE(c.design_cost, 0) + COALESCE((SELECT SUM(salary) FROM client_employees_new WHERE client_id = c.id), 0)) as actual_profit
                FROM
                    clients c
                ORDER BY
                    c.id DESC";
        $stmt = $db->prepare($query);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (Exception $e) {
        // If there's an error, return empty array
        error_log("Error getting profit accounts data: " . $e->getMessage());
        return [];
    }
}

// Get profit accounts data
$accounts = getProfitAccountsData();

// Calculate totals
$totalIncome = 0;
$totalDesignCost = 0;
$totalSalaries = 0;
$totalBaseProfit = 0;
$totalExpenses = 0;
$totalActualProfit = 0;

foreach ($accounts as $account) {
    $totalIncome += $account['total_income'];
    $totalDesignCost += $account['design_cost'];
    $totalSalaries += $account['salaries'];
    $totalBaseProfit += $account['base_profit'];
    $totalExpenses += $account['total_expenses'];
    $totalActualProfit += $account['actual_profit'];
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>حسابات الربح - <?php echo SITE_TITLE; ?></title>

    <!-- Bootstrap RTL CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Google Fonts - Cairo -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>assets/css/styles.css">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f8f9fa;
            margin: 0;
            padding: 0;
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 20px;
            background-color: white;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }

        .logo {
            height: 40px;
        }

        .page-title {
            font-size: 22px;
            color: #4a56e2;
            text-align: center;
            flex-grow: 1;
        }

        .search-container {
            display: flex;
            justify-content: flex-end;
            align-items: center;
            padding: 10px 20px;
            background-color: white;
            border-bottom: 1px solid #eee;
        }

        .search-box {
            position: relative;
            width: 300px;
        }

        .search-input {
            width: 100%;
            padding: 8px 15px 8px 35px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }

        .search-icon {
            position: absolute;
            left: 10px;
            top: 50%;
            transform: translateY(-50%);
            color: #aaa;
        }

        .table-container {
            position: relative;
            height: calc(100vh - 150px);
            margin-top: 20px;
            overflow: hidden;
            border: 1px solid #dcf343;
        }

        .profit-table {
            width: 100%;
            border-collapse: collapse;
        }

        .table-header-container {
            position: sticky;
            top: 0;
            z-index: 10;
            background-color: #4a56e2;
        }

        .table-body-container {
            height: calc(100vh - 250px);
            overflow-y: auto;
            overflow-x: hidden;
        }

        .table-footer-container {
            position: sticky;
            bottom: 0;
            z-index: 10;
            background-color: #f8f9fa;
        }

        .profit-table th {
            background-color: #4a56e2;
            color: white;
            padding: 12px;
            text-align: center;
            font-weight: 600;
            border: 1px solid #dcf343;
        }

        .profit-table td {
            padding: 10px;
            text-align: center;
            border: 1px solid #dcf343;
        }

        .profit-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }

        .profit-table tr:hover {
            background-color: #f0f0f0;
        }

        .currency {
            color: #4a56e2;
            font-weight: 500;
        }

        .positive {
            color: #28a745;
        }

        .negative {
            color: #dc3545;
        }

        .footer-row th {
            background-color: #f8f9fa;
            color: #4a56e2;
            font-weight: 600;
            border: 1px solid #dcf343;
            border-top: 2px solid #4a56e2;
        }
    </style>
</head>
<body>
    <div class="header">
        <a href="<?php echo BASE_URL; ?>dashboard.php">
            <img src="<?php echo BASE_URL; ?>assets/images/logo.png" alt="Logo" class="logo">
        </a>
        <div class="page-title">حسابات الربح</div>
        <div></div>
    </div>

    <div class="search-container">
        <div class="search-box">
            <input type="text" id="searchInput" class="search-input" placeholder="بحث...">
            <i class="fas fa-search search-icon"></i>
        </div>
    </div>

    <div class="container-fluid mt-3">
        <div class="table-container">
            <div class="table-header-container">
                <table class="profit-table" id="profitTableHeader">
                    <thead>
                        <tr>
                            <th>الحسابات</th>
                            <th>اجمالي دخل</th>
                            <th>مرتبات</th>
                            <th>ربح اساسي</th>
                            <th>تصميمات</th>
                            <th>ربح فعلي</th>
                        </tr>
                    </thead>
                </table>
            </div>

            <div class="table-body-container">
                <table class="profit-table" id="profitTable">
                    <tbody>
                        <?php foreach ($accounts as $account): ?>
                        <tr>
                            <td><?php echo $account['name']; ?></td>
                            <td><span class="currency">EGP</span> <?php echo number_format($account['total_income']); ?></td>
                            <td><span class="currency">EGP</span> <?php echo number_format($account['salaries']); ?></td>
                            <td><span class="currency">EGP</span> <?php echo number_format($account['base_profit']); ?></td>
                            <td><span class="currency">EGP</span> <?php echo number_format($account['design_cost']); ?></td>
                            <td class="<?php echo $account['actual_profit'] >= 0 ? 'positive' : 'negative'; ?>">
                                <?php echo number_format($account['actual_profit']); ?>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>

            <div class="table-footer-container">
                <table class="profit-table" id="profitTableFooter">
                    <tfoot>
                        <tr class="footer-row">
                            <th>اجمالي</th>
                            <th><span class="currency">EGP</span> <?php echo number_format($totalIncome); ?></th>
                            <th><span class="currency">EGP</span> <?php echo number_format($totalSalaries); ?></th>
                            <th><span class="currency">EGP</span> <?php echo number_format($totalBaseProfit); ?></th>
                            <th><span class="currency">EGP</span> <?php echo number_format($totalDesignCost); ?></th>
                            <th class="<?php echo $totalActualProfit >= 0 ? 'positive' : 'negative'; ?>">
                                <?php echo number_format($totalActualProfit); ?>
                            </th>
                        </tr>
                    </tfoot>
                </table>
            </div>
        </div>
    </div>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        $(document).ready(function() {
            // Ensure all tables have the same column widths
            function adjustColumnWidths() {
                // Get the widths of each column in the header
                var headerCols = $("#profitTableHeader th");
                var headerWidths = [];

                headerCols.each(function(index) {
                    headerWidths[index] = $(this).outerWidth();
                });

                // Apply these widths to the body and footer tables
                $("#profitTable td").each(function(index) {
                    var colIndex = index % headerCols.length;
                    $(this).css('width', headerWidths[colIndex] + 'px');
                });

                $("#profitTableFooter th").each(function(index) {
                    $(this).css('width', headerWidths[index] + 'px');
                });
            }

            // Call on page load
            adjustColumnWidths();

            // Call on window resize
            $(window).resize(function() {
                adjustColumnWidths();
            });

            // Search functionality
            $("#searchInput").on("keyup", function() {
                var value = $(this).val().toLowerCase();
                $("#profitTable tbody tr").filter(function() {
                    $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1)
                });
            });
        });
    </script>
</body>
</html>
