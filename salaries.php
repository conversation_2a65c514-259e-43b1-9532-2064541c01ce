<?php
// تضمين ملف الإعدادات
require_once 'config/config.php';

// تضمين اتصال قاعدة البيانات
require_once 'includes/db.php';

// تضمين الدوال المساعدة
require_once 'includes/functions.php';

// تضمين دوال المصادقة
require_once 'includes/auth.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    $_SESSION['flash_message'] = 'يجب تسجيل الدخول للوصول إلى صفحة المرتبات';
    $_SESSION['flash_type'] = 'danger';
    redirect(BASE_URL . 'index.php');
}

// التحقق من وجود جدول المرتبات وإنشائه إذا لم يكن موجودًا
try {
    $checkTableQuery = "SHOW TABLES LIKE 'salaries'";
    $stmt = $db->prepare($checkTableQuery);
    $stmt->execute();
    $tableExists = $stmt->rowCount() > 0;

    if (!$tableExists) {
        // إنشاء جدول المرتبات
        $createTableQuery = "CREATE TABLE salaries (
            id INT AUTO_INCREMENT PRIMARY KEY,
            employee_name VARCHAR(255) NOT NULL,
            amount DECIMAL(10,2) NOT NULL,
            payment_type ENUM('نقدي', 'VF') NOT NULL,
            role VARCHAR(50) NOT NULL,
            is_office TINYINT(1) NOT NULL DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )";
        $db->exec($createTableQuery);

        // إنشاء جدول صفحات الموظفين
        $createEmployeePagesTableQuery = "CREATE TABLE employee_pages (
            id INT AUTO_INCREMENT PRIMARY KEY,
            employee_id INT NOT NULL,
            page_name VARCHAR(255) NOT NULL,
            salary DECIMAL(10,2) NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (employee_id) REFERENCES salaries(id) ON DELETE CASCADE
        )";
        $db->exec($createEmployeePagesTableQuery);

        // إضافة بيانات تجريبية
        $insertDataQuery = "INSERT INTO salaries (employee_name, amount, payment_type, role, is_office) VALUES
            ('نوال حسن', 2500, 'نقدي', 'مودريتور', 1),
            ('نوال حسن', 2500, 'VF', 'مودريتور', 1),
            ('نوال حسن', 2500, 'نقدي', 'مودريتور', 0),
            ('نوال حسن', 2500, 'VF', 'مودريتور', 0),
            ('نوال حسن', 2500, 'نقدي', 'مودريتور', 1),
            ('نوال حسن', 2500, 'VF', 'مودريتور', 1),
            ('نوال حسن', 2500, 'نقدي', 'مودريتور', 0),
            ('نوال حسن', 2500, 'VF', 'مودريتور', 0),
            ('أحمد محمد', 3000, 'نقدي', 'مصمم', 1),
            ('محمد علي', 3500, 'VF', 'اكونتات', 1),
            ('سارة أحمد', 2800, 'نقدي', 'مصمم', 0),
            ('خالد محمود', 4000, 'VF', 'مسؤول', 1),
            ('فاطمة حسن', 3200, 'نقدي', 'اكونتات', 0),
            ('عمر خالد', 2700, 'VF', 'مودريتور', 0),
            ('ليلى سعيد', 3100, 'نقدي', 'مودريتور', 1)";
        $db->exec($insertDataQuery);

        // إضافة بيانات تجريبية لصفحات الموظفين
        $insertEmployeePagesQuery = "INSERT INTO employee_pages (employee_id, page_name, salary) VALUES
            (1, 'Nihal Anbar', 200),
            (1, 'Nihal Anbar', 500),
            (1, 'Nihal Anbar', 200),
            (1, 'Nihal Anbar', 500),
            (3, 'Dr M7med El4af3y', 300),
            (3, 'Dr M7med El4af3y', 700),
            (5, 'Nihal Anbar', 250),
            (5, 'Dr M7med El4af3y', 450)";
        $db->exec($insertEmployeePagesQuery);
    }

    // تحديد نوع المرتبات المعروضة (الكل أو المكتب فقط)
    $salaryType = isset($_GET['type']) && $_GET['type'] === 'office' ? 'office' : 'all';

    // البحث
    $searchTerm = isset($_GET['search']) ? $_GET['search'] : '';

    // استعلام لجلب المرتبات
    $salariesQuery = "SELECT s.*,
                     (SELECT SUM(salary) FROM employee_pages WHERE employee_id = s.id) as total_pages_salary
                     FROM salaries s
                     WHERE 1=1";

    // إضافة شرط نوع المرتبات
    if ($salaryType === 'office') {
        $salariesQuery .= " AND s.is_office = 1";
    }

    // إضافة شرط البحث
    if (!empty($searchTerm)) {
        $salariesQuery .= " AND s.employee_name LIKE :search_term";
    }

    // ترتيب النتائج
    $salariesQuery .= " ORDER BY s.id DESC";

    $stmt = $db->prepare($salariesQuery);

    // ربط معلمة البحث إذا كانت موجودة
    if (!empty($searchTerm)) {
        $searchParam = '%' . $searchTerm . '%';
        $stmt->bindParam(':search_term', $searchParam);
    }

    $stmt->execute();
    $salaries = $stmt->fetchAll(PDO::FETCH_ASSOC);

} catch (PDOException $e) {
    $dbErrorMessage = '<div class="alert alert-danger">خطأ في قاعدة البيانات: ' . $e->getMessage() . '</div>';
}

// تم نقل هذا الكود إلى داخل كتلة try-catch
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>المرتبات - <?php echo SITE_TITLE; ?></title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap');

        body {
            font-family: 'Tajawal', sans-serif;
            background-color: #fff;
            margin: 0;
            padding: 0;
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 20px;
            border-bottom: 1px solid #e0e0e0;
        }

        .logo {
            height: 40px;
        }

        .page-title {
            color: #4a4ad4;
            text-align: center;
            font-size: 24px;
            font-weight: bold;
        }

        .add-employee-btn {
            background-color: transparent;
            color: #4a4ad4;
            border: none;
            display: flex;
            align-items: center;
            font-weight: bold;
        }

        .add-employee-btn i {
            font-size: 24px;
            margin-left: 5px;
        }

        .filter-bar {
            display: flex;
            padding: 10px 20px;
            border-bottom: 1px solid #e0e0e0;
            align-items: center;
        }

        .add-role-btn {
            margin-right: 15px;
        }

        .filter-icon {
            color: #4a4ad4;
            font-size: 24px;
            margin-left: 15px;
        }

        .filter-tabs {
            display: flex;
            gap: 20px;
        }

        .filter-tab {
            color: #4a4ad4;
            font-weight: bold;
            cursor: pointer;
            padding: 5px 10px;
            border-bottom: 2px solid transparent;
        }

        .filter-tab.active {
            border-bottom-color: #4a4ad4;
        }

        .search-box {
            margin-right: auto;
            position: relative;
        }

        .search-input {
            border: 1px solid #e0e0e0;
            border-radius: 20px;
            padding: 5px 15px 5px 35px;
            width: 300px;
        }

        .search-icon {
            position: absolute;
            left: 10px;
            top: 50%;
            transform: translateY(-50%);
            color: #999;
        }

        .salaries-grid {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            gap: 15px;
            padding: 20px;
        }

        .salary-card {
            border: 1px solid #dcf343;
            border-radius: 5px;
            overflow: hidden;
        }

        .salary-header {
            display: flex;
            justify-content: space-between;
            padding: 10px;
        }

        .salary-amount {
            color: #4a4ad4;
            font-weight: bold;
        }

        .salary-menu {
            color: #4a4ad4;
            cursor: pointer;
        }

        .salary-body {
            padding: 10px;
            display: flex;
            justify-content: space-between;
        }

        .employee-name {
            color: #4a4ad4;
            font-weight: bold;
        }

        .salary-footer {
            display: flex;
            justify-content: space-between;
        }

        .role-badge {
            background-color: #4a4ad4;
            color: white;
            padding: 5px 10px;
            border-top-right-radius: 5px;
        }

        .payment-type {
            padding: 5px 10px;
            text-align: left;
            color: #4a4ad4;
        }

        .payment-type.cash {
            color: #333;
        }

        /* Modal Styles */
        .modal-header {
            background-color: #4a4ad4;
            color: white;
        }

        .modal-footer .btn-primary {
            background-color: #4a4ad4;
            border-color: #4a4ad4;
        }
    </style>
</head>
<body>
    <?php if (isset($dbErrorMessage)) echo $dbErrorMessage; ?>

    <div class="header">
        <img src="<?php echo BASE_URL; ?>assets/images/logo.png" alt="<?php echo SITE_TITLE; ?>" class="logo">
        <h1 class="page-title">المرتبات</h1>
        <button class="add-employee-btn" data-bs-toggle="modal" data-bs-target="#addEmployeeModal">
            <i class="fas fa-user-plus"></i>
            اضافة موظف
        </button>
    </div>

    <div class="filter-bar">
        <div class="filter-icon">
            <i class="fas fa-filter"></i>
        </div>
        <div class="filter-tabs">
            <a href="external_salaries.php" class="filter-tab">مرتبات</a>
            <a href="office_salaries.php" class="filter-tab">مرتبات المكتب</a>
        </div>
        <div class="search-box">
            <input type="text" class="search-input" placeholder="البحث" id="searchInput" value="<?php echo htmlspecialchars($searchTerm); ?>">
            <i class="fas fa-search search-icon"></i>
        </div>
        <div class="add-role-btn">
            <button type="button" class="btn btn-outline-primary btn-sm" data-bs-toggle="modal" data-bs-target="#addRoleModal">
                <i class="fas fa-plus-circle"></i> إضافة وظيفة
            </button>
        </div>
    </div>

    <div class="salaries-grid">
        <?php if (!empty($salaries)): ?>
            <?php foreach ($salaries as $salary): ?>
                <div class="salary-card" data-employee-id="<?php echo $salary['id']; ?>" data-role="<?php echo htmlspecialchars($salary['role']); ?>" data-office="<?php echo $salary['is_office']; ?>">
                    <div class="salary-header">
                        <div class="salary-amount">
                            <?php
                            // عرض إجمالي المرتب من الصفحات إذا كان متوفرًا وكان الدور مودريتور، وإلا عرض المرتب الأساسي
                            $displayAmount = ($salary['role'] === 'مودريتور' && !empty($salary['total_pages_salary'])) ? $salary['total_pages_salary'] : $salary['amount'];
                            echo number_format($displayAmount, 3);
                            ?>
                        </div>
                        <div class="salary-menu">...</div>
                    </div>
                    <div class="salary-body">
                        <div class="employee-name"><?php echo htmlspecialchars($salary['employee_name']); ?></div>
                    </div>
                    <div class="salary-footer">
                        <div class="role-badge"><?php echo htmlspecialchars($salary['role']); ?></div>
                        <div class="payment-type <?php echo $salary['payment_type'] === 'نقدي' ? 'cash' : ''; ?>">
                            <?php echo htmlspecialchars($salary['payment_type']); ?>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        <?php else: ?>
            <div class="col-12 text-center">
                <p>لا توجد مرتبات لعرضها.</p>
            </div>
        <?php endif; ?>
    </div>

    <!-- Modal إضافة موظف -->
    <div class="modal fade" id="addEmployeeModal" tabindex="-1" aria-labelledby="addEmployeeModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addEmployeeModalLabel">إضافة موظف جديد</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form action="add_employee.php" method="post" id="addEmployeeForm">
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="employeeName" class="form-label">اسم الموظف</label>
                            <input type="text" class="form-control" id="employeeName" name="employee_name" required>
                        </div>
                        <div class="mb-3">
                            <label for="salaryAmount" class="form-label">المرتب الأساسي</label>
                            <input type="number" step="0.01" class="form-control" id="salaryAmount" name="amount" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">نوع الدفع</label>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="payment_type" id="paymentCash" value="نقدي" checked>
                                <label class="form-check-label" for="paymentCash">نقدي</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="payment_type" id="paymentVF" value="VF">
                                <label class="form-check-label" for="paymentVF">VF</label>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="employeeRole" class="form-label">الدور</label>
                            <select class="form-select" id="employeeRole" name="role" required>
                                <option value="مودريتور">مودريتور</option>
                                <option value="مصمم">مصمم</option>
                                <option value="اكونتات">اكونتات</option>
                                <option value="مسؤول">مسؤول</option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="isOffice" name="is_office">
                                <label class="form-check-label" for="isOffice">
                                    موظف من المكتب
                                </label>
                            </div>
                        </div>

                        <div id="pagesContainer" class="mb-3 d-none">
                            <label class="form-label">الصفحات</label>
                            <div class="pages-list">
                                <div class="page-item mb-2">
                                    <div class="row">
                                        <div class="col-6">
                                            <input type="text" class="form-control" name="page_names[]" placeholder="اسم الصفحة">
                                        </div>
                                        <div class="col-5">
                                            <input type="number" class="form-control" name="page_salaries[]" placeholder="المرتب">
                                        </div>
                                        <div class="col-1">
                                            <button type="button" class="btn btn-danger btn-sm remove-page"><i class="fas fa-times"></i></button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <button type="button" class="btn btn-sm btn-success mt-2" id="addPageBtn">
                                <i class="fas fa-plus"></i> إضافة صفحة
                            </button>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-primary">إضافة</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Modal تفاصيل المرتب -->
    <div class="modal fade" id="salaryDetailsModal" tabindex="-1" aria-labelledby="salaryDetailsModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="salaryDetailsModalLabel">تفاصيل المرتب</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="employee-details-header mb-3">
                        <h4 id="employeeNameDetails" class="text-center"></h4>
                    </div>

                    <!-- قسم تفاصيل المودريتور -->
                    <div id="pagesDetailsSection">
                        <div class="salary-details-table mb-4">
                            <div class="row border-bottom py-2 fw-bold">
                                <div class="col-6 text-center">المرتب</div>
                                <div class="col-6 text-center">الصفحة</div>
                            </div>
                            <div id="pagesDetailsList">
                                <!-- سيتم ملء هذا القسم بالبيانات من خلال JavaScript -->
                            </div>
                            <div class="row border-top py-2 fw-bold">
                                <div class="col-6 text-center" id="totalSalary">إجمالي المرتب</div>
                                <div class="col-6 text-center"></div>
                            </div>
                        </div>

                        <!-- نموذج إضافة صفحة جديدة -->
                        <div class="add-page-form">
                            <h5 class="mb-3">إضافة صفحة جديدة</h5>
                            <form id="addNewPageForm">
                                <input type="hidden" id="addPageEmployeeId" name="employee_id">
                                <div class="row mb-3">
                                    <div class="col-6">
                                        <input type="text" class="form-control" name="page_name" placeholder="اسم الصفحة" required>
                                    </div>
                                    <div class="col-5">
                                        <input type="number" class="form-control" name="salary" placeholder="المرتب" required>
                                    </div>
                                    <div class="col-1">
                                        <button type="submit" class="btn btn-success btn-sm"><i class="fas fa-plus"></i></button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- قسم تفاصيل الوظائف الأخرى -->
                    <div id="otherRoleSection" class="d-none">
                        <div id="otherRoleInfo" class="p-3 border rounded">
                            <!-- سيتم ملء هذا القسم بالبيانات من خلال JavaScript -->
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal إضافة وظيفة جديدة -->
    <div class="modal fade" id="addRoleModal" tabindex="-1" aria-labelledby="addRoleModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addRoleModalLabel">إضافة وظيفة جديدة</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="addRoleForm" action="add_role.php" method="post">
                        <div class="mb-3">
                            <label for="roleName" class="form-label">اسم الوظيفة</label>
                            <input type="text" class="form-control" id="roleName" name="role_name" required>
                        </div>
                        <div class="mb-3">
                            <label for="roleDescription" class="form-label">وصف الوظيفة (اختياري)</label>
                            <textarea class="form-control" id="roleDescription" name="role_description" rows="3"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" id="saveRoleBtn">حفظ</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // جلب الوظائف المتاحة
        function fetchRoles() {
            fetch('get_roles.php')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // تحديث قائمة الوظائف في نموذج إضافة الموظف
                        const roleSelect = document.getElementById('employeeRole');

                        // حفظ الخيار المحدد حاليًا
                        const selectedRole = roleSelect.value;

                        // إفراغ القائمة مع الاحتفاظ بالخيارات الافتراضية
                        const defaultOptions = Array.from(roleSelect.options).filter(option =>
                            ['مودريتور', 'مصمم', 'اكونتات', 'مسؤول'].includes(option.value)
                        );

                        roleSelect.innerHTML = '';

                        // إعادة إضافة الخيارات الافتراضية
                        defaultOptions.forEach(option => {
                            roleSelect.appendChild(option);
                        });

                        // إضافة الوظائف المخصصة
                        data.roles.forEach(role => {
                            if (!['مودريتور', 'مصمم', 'اكونتات', 'مسؤول'].includes(role)) {
                                const option = document.createElement('option');
                                option.value = role;
                                option.textContent = role;
                                roleSelect.appendChild(option);
                            }
                        });

                        // إعادة تحديد الخيار المحدد سابقًا
                        if (selectedRole) {
                            roleSelect.value = selectedRole;
                        }
                    }
                })
                .catch(error => {
                    console.error('Error fetching roles:', error);
                });
        }

        // تحميل الوظائف عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', fetchRoles);

        // حفظ الوظيفة الجديدة
        document.getElementById('saveRoleBtn').addEventListener('click', function() {
            const form = document.getElementById('addRoleForm');
            const roleName = document.getElementById('roleName').value.trim();

            if (!roleName) {
                alert('يرجى إدخال اسم الوظيفة');
                return;
            }

            // إرسال البيانات إلى الخادم
            const formData = new FormData(form);

            fetch('add_role.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // إغلاق النافذة المنبثقة
                    const modal = bootstrap.Modal.getInstance(document.getElementById('addRoleModal'));
                    modal.hide();

                    // إعادة تعيين النموذج
                    form.reset();

                    // تحديث قائمة الوظائف
                    fetchRoles();

                    // عرض رسالة نجاح
                    alert(data.message || 'تمت إضافة الوظيفة بنجاح');
                } else {
                    alert(data.message || 'حدث خطأ أثناء إضافة الوظيفة');
                }
            })
            .catch(error => {
                console.error('Error adding role:', error);
                alert('حدث خطأ أثناء إضافة الوظيفة');
            });
        });

        // البحث
        document.getElementById('searchInput').addEventListener('keyup', function(e) {
            if (e.key === 'Enter') {
                const searchTerm = this.value.trim();
                window.location.href = `?search=${encodeURIComponent(searchTerm)}&type=<?php echo $salaryType; ?>`;
            }
        });

        // إظهار حقول الصفحات عند اختيار دور "مودريتور"
        document.getElementById('employeeRole').addEventListener('change', function() {
            const pagesContainer = document.getElementById('pagesContainer');
            if (this.value === 'مودريتور') {
                pagesContainer.classList.remove('d-none');
            } else {
                pagesContainer.classList.add('d-none');
            }
        });

        // إضافة صفحة جديدة في نموذج إضافة موظف
        document.getElementById('addPageBtn').addEventListener('click', function() {
            const pagesList = document.querySelector('.pages-list');
            const pageItem = document.querySelector('.page-item').cloneNode(true);

            // إعادة تعيين قيم الحقول
            pageItem.querySelector('input[name="page_names[]"]').value = '';
            pageItem.querySelector('input[name="page_salaries[]"]').value = '';

            // إضافة حدث إزالة الصفحة
            pageItem.querySelector('.remove-page').addEventListener('click', function() {
                this.closest('.page-item').remove();
            });

            pagesList.appendChild(pageItem);
        });

        // إزالة صفحة في نموذج إضافة موظف
        document.querySelectorAll('.remove-page').forEach(button => {
            button.addEventListener('click', function() {
                this.closest('.page-item').remove();
            });
        });

        // إضافة صفحة جديدة للموظف من نافذة التفاصيل
        document.getElementById('addNewPageForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const employeeId = document.getElementById('addPageEmployeeId').value;
            const pageName = this.elements['page_name'].value;
            const salary = this.elements['salary'].value;

            if (!pageName || !salary) {
                alert('يرجى ملء جميع الحقول المطلوبة');
                return;
            }

            // إرسال البيانات إلى الخادم
            const formData = new FormData();
            formData.append('employee_id', employeeId);
            formData.append('page_name', pageName);
            formData.append('salary', salary);

            fetch('add_employee_page.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // إعادة تحميل بيانات الموظف
                    fetch(`get_employee_pages.php?employee_id=${employeeId}`)
                        .then(response => response.json())
                        .then(data => {
                            // إفراغ قائمة الصفحات
                            const pagesDetailsList = document.getElementById('pagesDetailsList');
                            pagesDetailsList.innerHTML = '';

                            // إضافة صفحات الموظف إلى القائمة
                            let totalSalary = 0;

                            if (data.pages.length > 0) {
                                data.pages.forEach(page => {
                                    const pageRow = document.createElement('div');
                                    pageRow.className = 'row py-2 border-bottom';
                                    pageRow.innerHTML = `
                                        <div class="col-6 text-center">${page.salary}</div>
                                        <div class="col-6 text-center">${page.page_name}</div>
                                    `;
                                    pagesDetailsList.appendChild(pageRow);
                                    totalSalary += parseFloat(page.salary);
                                });
                            }

                            // عرض إجمالي المرتب
                            document.getElementById('totalSalary').textContent = `إجمالي المرتب: ${totalSalary.toFixed(2)}`;

                            // إعادة تعيين النموذج
                            document.getElementById('addNewPageForm').reset();

                            // تحديث عرض المرتب في بطاقة الموظف
                            const salaryCard = document.querySelector(`.salary-card[data-employee-id="${employeeId}"]`);
                            if (salaryCard) {
                                const salaryAmount = salaryCard.querySelector('.salary-amount');
                                salaryAmount.textContent = totalSalary.toFixed(3);
                            }
                        });
                } else {
                    alert(data.message || 'حدث خطأ أثناء إضافة الصفحة');
                }
            })
            .catch(error => {
                console.error('Error adding page:', error);
                alert('حدث خطأ أثناء إضافة الصفحة');
            });
        });

        // عرض تفاصيل المرتب عند النقر على بطاقة الموظف
        document.querySelectorAll('.salary-card').forEach(card => {
            card.addEventListener('click', function() {
                const employeeId = this.getAttribute('data-employee-id');
                const employeeRole = this.getAttribute('data-role');

                // جلب بيانات صفحات الموظف من الخادم
                fetch(`get_employee_pages.php?employee_id=${employeeId}`)
                    .then(response => response.json())
                    .then(data => {
                        // عرض اسم الموظف
                        document.getElementById('employeeNameDetails').textContent = data.employee_name;

                        // التعامل مع المودريتور بشكل مختلف
                        if (data.role === 'مودريتور') {
                            // إظهار قسم الصفحات
                            document.getElementById('pagesDetailsSection').classList.remove('d-none');
                            document.getElementById('otherRoleSection').classList.add('d-none');

                            // إفراغ قائمة الصفحات
                            const pagesDetailsList = document.getElementById('pagesDetailsList');
                            pagesDetailsList.innerHTML = '';

                            // إضافة صفحات الموظف إلى القائمة
                            let totalSalary = 0;

                            if (data.pages.length > 0) {
                                data.pages.forEach(page => {
                                    const pageRow = document.createElement('div');
                                    pageRow.className = 'row py-2 border-bottom';
                                    pageRow.innerHTML = `
                                        <div class="col-6 text-center">${page.salary}</div>
                                        <div class="col-6 text-center">${page.page_name}</div>
                                    `;
                                    pagesDetailsList.appendChild(pageRow);
                                    totalSalary += parseFloat(page.salary);
                                });
                            } else {
                                // إذا لم تكن هناك صفحات، عرض رسالة
                                const noDataRow = document.createElement('div');
                                noDataRow.className = 'row py-2 border-bottom';
                                noDataRow.innerHTML = `
                                    <div class="col-12 text-center">لا توجد صفحات مسجلة</div>
                                `;
                                pagesDetailsList.appendChild(noDataRow);
                            }

                            // عرض إجمالي المرتب
                            document.getElementById('totalSalary').textContent = `إجمالي المرتب: ${totalSalary.toFixed(2)}`;

                            // تحديث معرف الموظف في نموذج إضافة الصفحة
                            document.getElementById('addPageEmployeeId').value = employeeId;
                        } else {
                            // إخفاء قسم الصفحات وإظهار قسم الوظائف الأخرى
                            document.getElementById('pagesDetailsSection').classList.add('d-none');
                            document.getElementById('otherRoleSection').classList.remove('d-none');

                            // عرض معلومات الوظيفة الأخرى
                            document.getElementById('otherRoleInfo').innerHTML = `
                                <p>الدور: ${data.role}</p>
                                <p>نوع الدفع: ${data.payment_type}</p>
                                <p>المرتب: ${data.base_salary}</p>
                            `;
                        }

                        // عرض النافذة المنبثقة
                        const modal = new bootstrap.Modal(document.getElementById('salaryDetailsModal'));
                        modal.show();
                    })
                    .catch(error => {
                        console.error('Error fetching employee pages:', error);
                        alert('حدث خطأ أثناء جلب بيانات الموظف');
                    });
            });
        });
    </script>
</body>
</html>
