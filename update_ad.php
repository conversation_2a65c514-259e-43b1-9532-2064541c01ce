<?php
// تضمين ملف الإعدادات
require_once 'config/config.php';

// تضمين اتصال قاعدة البيانات
require_once 'includes/db.php';

// تضمين الدوال المساعدة
require_once 'includes/functions.php';

// تضمين دوال المصادقة
require_once 'includes/auth.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'يجب تسجيل الدخول للوصول إلى هذه الخدمة']);
    exit;
}

// التحقق من طريقة الطلب
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'طريقة الطلب غير صحيحة']);
    exit;
}

// التحقق من البيانات المطلوبة
if (
    !isset($_POST['ad_id']) || empty($_POST['ad_id']) ||
    !isset($_POST['field']) || empty($_POST['field']) ||
    !isset($_POST['value'])
) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'البيانات المطلوبة غير مكتملة']);
    exit;
}

// إعداد البيانات
$adId = intval($_POST['ad_id']);
$field = $_POST['field'];
$value = $_POST['value'];

// التحقق من صحة الحقل
$allowedFields = ['status', 'cost', 'days', 'total_cost'];
if (!in_array($field, $allowedFields)) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'الحقل غير مسموح به']);
    exit;
}

// تعيين الحقل المناسب في قاعدة البيانات
$dbField = $field;
if ($field === 'cost') {
    $dbField = 'cost';
}

try {
    // التحقق من وجود الإعلان
    $checkQuery = "SELECT id FROM ads WHERE id = :id";
    $stmt = $db->prepare($checkQuery);
    $stmt->bindParam(':id', $adId);
    $stmt->execute();

    if ($stmt->rowCount() === 0) {
        header('Content-Type: application/json');
        echo json_encode(['success' => false, 'message' => 'الإعلان غير موجود']);
        exit;
    }

    // تحديث الحقل المحدد
    $updateQuery = "UPDATE ads SET $dbField = :value, updated_at = NOW()";

    // إذا تم تحديث التكلفة أو عدد الأيام، نقوم بتحديث التكلفة الإجمالية
    if ($field === 'cost' || $field === 'days') {
        // لا نحتاج لتحديث حقل total_cost لأنه يتم حسابه في الاستعلام
    } elseif ($field === 'total_cost') {
        // إذا تم تعديل إجمالي التكلفة مباشرة، نحسب التكلفة اليومية بناءً على عدد الأيام
        $getAdQuery = "SELECT days FROM ads WHERE id = :id";
        $adStmt = $db->prepare($getAdQuery);
        $adStmt->bindParam(':id', $adId);
        $adStmt->execute();
        $adData = $adStmt->fetch(PDO::FETCH_ASSOC);

        if ($adData && $adData['days'] > 0) {
            $dailyCost = floatval($value) / $adData['days'];
            $updateQuery = "UPDATE ads SET cost = :daily_cost, updated_at = NOW() WHERE id = :id";
            $stmt = $db->prepare($updateQuery);
            $stmt->bindParam(':daily_cost', $dailyCost);
            $stmt->bindParam(':id', $adId);
            $stmt->execute();

            // تحديث القيمة لاستخدامها في الاستجابة
            $value = $dailyCost;
            $field = 'cost';
            $dbField = 'cost';
        }
    }

    if ($field !== 'total_cost') {
        $updateQuery .= " WHERE id = :id";

        $stmt = $db->prepare($updateQuery);

        // تحويل القيمة إلى النوع المناسب
        if ($field === 'cost') {
            $value = floatval($value);
        } elseif ($field === 'days') {
            $value = intval($value);
        }

        $stmt->bindParam(':value', $value);
        $stmt->bindParam(':id', $adId);
        $stmt->execute();
    }



    // جلب البيانات المحدثة
    $selectQuery = "SELECT status, cost AS daily_budget, days, (cost * days) AS total_cost, updated_at FROM ads WHERE id = :id";
    $stmt = $db->prepare($selectQuery);
    $stmt->bindParam(':id', $adId);
    $stmt->execute();
    $updatedAd = $stmt->fetch(PDO::FETCH_ASSOC);

    // إضافة معلومات عما إذا كان الإعلان تم تعديله اليوم
    $updatedAd['updated_today'] = (date('Y-m-d', strtotime($updatedAd['updated_at'])) === date('Y-m-d'));

    header('Content-Type: application/json');
    echo json_encode([
        'success' => true,
        'message' => 'تم تحديث البيانات بنجاح',
        'data' => $updatedAd
    ]);

} catch (PDOException $e) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'حدث خطأ أثناء تحديث البيانات: ' . $e->getMessage()]);
}
