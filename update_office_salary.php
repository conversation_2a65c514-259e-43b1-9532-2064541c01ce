<?php
// تضمين ملف الإعدادات
require_once 'config/config.php';

// تضمين اتصال قاعدة البيانات
require_once 'includes/db.php';

// تضمين الدوال المساعدة
require_once 'includes/functions.php';

// تضمين دوال المصادقة
require_once 'includes/auth.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'يجب تسجيل الدخول للوصول إلى هذه الخدمة']);
    exit;
}

// التحقق من طريقة الطلب
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'طريقة الطلب غير صحيحة']);
    exit;
}

// التحقق من البيانات المطلوبة
if (
    !isset($_POST['salary_id']) || empty($_POST['salary_id']) ||
    !isset($_POST['field_name']) || empty($_POST['field_name']) ||
    !isset($_POST['field_value'])
) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'البيانات المطلوبة غير مكتملة']);
    exit;
}

// إعداد البيانات
$salaryId = intval($_POST['salary_id']);
$fieldName = $_POST['field_name'];
$fieldValue = $_POST['field_value'];

// التحقق من صحة اسم الحقل
$allowedFields = ['salary', 'advance', 'deductions', 'deduction_amount', 'commission', 'overtime', 'overtime_amount', 'net_salary'];
if (!in_array($fieldName, $allowedFields)) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'اسم الحقل غير صالح: ' . $fieldName]);
    exit;
}

try {
    // بدء المعاملة
    $db->beginTransaction();
    
    // تحديث الحقل المحدد
    $updateQuery = "UPDATE office_salaries SET $fieldName = :value WHERE id = :id";
    $stmt = $db->prepare($updateQuery);
    
    // تحويل القيمة إلى الشكل المناسب
    if (in_array($fieldName, ['salary', 'advance', 'commission', 'deduction_amount', 'overtime_amount', 'net_salary'])) {
        $value = !empty($fieldValue) ? floatval($fieldValue) : 0;
    } else {
        $value = $fieldValue;
    }
    
    $stmt->bindParam(':value', $value);
    $stmt->bindParam(':id', $salaryId);
    $stmt->execute();
    
    // إذا تم تغيير أي حقل غير صافي المرتب، نقوم بإعادة حساب صافي المرتب
    if ($fieldName !== 'net_salary') {
        // جلب بيانات المرتب الحالية
        $salaryQuery = "SELECT salary, advance, commission, deduction_amount, overtime_amount FROM office_salaries WHERE id = :id";
        $stmt = $db->prepare($salaryQuery);
        $stmt->bindParam(':id', $salaryId);
        $stmt->execute();
        $salaryData = $stmt->fetch(PDO::FETCH_ASSOC);
        
        // حساب صافي المرتب
        $netSalary = $salaryData['salary'] + $salaryData['commission'] + $salaryData['overtime_amount'] - $salaryData['advance'] - $salaryData['deduction_amount'];
        
        // تحديث صافي المرتب
        $updateNetQuery = "UPDATE office_salaries SET net_salary = :net_salary WHERE id = :id";
        $stmt = $db->prepare($updateNetQuery);
        $stmt->bindParam(':net_salary', $netSalary);
        $stmt->bindParam(':id', $salaryId);
        $stmt->execute();
    }
    
    // إذا تم تغيير حقل الخصومات أو الأوفرتايم، نقوم بتحديث قيمة المبلغ المقابل
    if ($fieldName === 'deductions') {
        // تحديث قيمة الخصم بناءً على النص المدخل
        $deductionAmount = 0;
        if (strpos($value, 'أيام') !== false) {
            // استخراج عدد الأيام من النص
            preg_match('/(\d+)/', $value, $matches);
            if (isset($matches[1])) {
                $days = intval($matches[1]);
                // حساب قيمة الخصم (مثال: 100 لكل يوم)
                $deductionAmount = $days * 100;
            }
        }
        
        // تحديث قيمة الخصم
        $updateDeductionQuery = "UPDATE office_salaries SET deduction_amount = :amount WHERE id = :id";
        $stmt = $db->prepare($updateDeductionQuery);
        $stmt->bindParam(':amount', $deductionAmount);
        $stmt->bindParam(':id', $salaryId);
        $stmt->execute();
    } elseif ($fieldName === 'overtime') {
        // تحديث قيمة الأوفرتايم بناءً على النص المدخل
        $overtimeAmount = 0;
        if (strpos($value, 'يوم') !== false) {
            // استخراج عدد الأيام من النص
            preg_match('/(\d+)/', $value, $matches);
            if (isset($matches[1])) {
                $days = intval($matches[1]);
                // حساب قيمة الأوفرتايم (مثال: 150 لكل يوم)
                $overtimeAmount = $days * 150;
            } else {
                // إذا كان النص "يوم" فقط بدون رقم، نفترض أنه يوم واحد
                $overtimeAmount = 150;
            }
        }
        
        // تحديث قيمة الأوفرتايم
        $updateOvertimeQuery = "UPDATE office_salaries SET overtime_amount = :amount WHERE id = :id";
        $stmt = $db->prepare($updateOvertimeQuery);
        $stmt->bindParam(':amount', $overtimeAmount);
        $stmt->bindParam(':id', $salaryId);
        $stmt->execute();
    }
    
    // جلب صافي المرتب المحدث
    $netQuery = "SELECT net_salary FROM office_salaries WHERE id = :id";
    $stmt = $db->prepare($netQuery);
    $stmt->bindParam(':id', $salaryId);
    $stmt->execute();
    $netSalary = $stmt->fetch(PDO::FETCH_ASSOC)['net_salary'];
    
    // حساب إجمالي المرتبات
    $totalQuery = "SELECT SUM(net_salary) as total FROM office_salaries";
    $stmt = $db->prepare($totalQuery);
    $stmt->execute();
    $totalSalaries = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
    
    // تأكيد المعاملة
    $db->commit();
    
    // إرجاع استجابة نجاح
    header('Content-Type: application/json');
    echo json_encode([
        'success' => true,
        'message' => 'تم تحديث البيانات بنجاح',
        'net_salary' => $netSalary,
        'total_salaries' => $totalSalaries
    ]);
    
} catch (PDOException $e) {
    // التراجع عن المعاملة في حالة حدوث خطأ
    $db->rollBack();
    
    // إرجاع استجابة خطأ
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'حدث خطأ أثناء تحديث البيانات: ' . $e->getMessage()]);
}
