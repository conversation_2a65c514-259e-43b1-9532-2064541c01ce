<?php
// تضمين ملف الاتصال بقاعدة البيانات
require_once 'config/database.php';

// إنشاء اتصال بقاعدة البيانات
$database = new Database();
$db = $database->getConnection();

// التحقق من وجود اتصال بقاعدة البيانات
if (!isset($db)) {
    die("خطأ في الاتصال بقاعدة البيانات");
}

// جلب بيانات بطاقات الفيزا فقط
try {
    $visaCards = [];

    // جلب بطاقات الفيزا
    try {
        $stmt = $db->prepare("
            SELECT *,
                   'visa' as card_type,
                   0 as days_until_payment,
                   base_balance as balance
            FROM visa_cards
            ORDER BY name
        ");
        $stmt->execute();
        $visaCards = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // إذا لم توجد بطاقات فيزا، أضف بطاقة تجريبية
        if (empty($visaCards)) {
            // إنشاء بطاقة فيزا تجريبية
            try {
                $stmt = $db->prepare("
                    INSERT INTO visa_cards (name, base_balance, daily_limit, remaining_balance, total_debt, status)
                    VALUES ('فيزا الأهلي الذهبية', 5000.00, 100.00, 5000.00, 0.00, 'نشط')
                ");
                $stmt->execute();

                // جلب البطاقة المضافة
                $stmt = $db->prepare("
                    SELECT *,
                           'visa' as card_type,
                           0 as days_until_payment,
                           base_balance as balance
                    FROM visa_cards
                    ORDER BY name
                ");
                $stmt->execute();
                $visaCards = $stmt->fetchAll(PDO::FETCH_ASSOC);
            } catch (PDOException $e) {
                // في حالة فشل الإضافة، استخدم بيانات تجريبية
                $visaCards = [
                    [
                        'id' => 999,
                        'name' => 'فيزا الأهلي الذهبية',
                        'base_balance' => 5000.00,
                        'daily_limit' => 100.00,
                        'remaining_balance' => 5000.00,
                        'total_debt' => 0.00,
                        'status' => 'نشط',
                        'card_type' => 'visa',
                        'days_until_payment' => 0,
                        'balance' => 5000.00
                    ]
                ];
            }
        }

    } catch (PDOException $e) {
        echo "جدول visa_cards غير موجود: " . $e->getMessage();
    }

} catch (PDOException $e) {
    echo "حدث خطأ عند جلب بطاقات الفيزا: " . $e->getMessage();
    $visaCards = [];
}

// جلب المعاملات من الجداول المخصصة
$cardTransactions = [];

try {

    // جلب المعاملات لكل بطاقة فيزا من الجداول المخصصة
    foreach ($visaCards as $card) {
        $cardId = $card['id'];

        // جلب المعاملات المتوقعة
        $stmt = $db->prepare("
            SELECT payment_date as ad_date, account_name, expected_amount as ad_cost,
                   0 as ad_spent, description, 'متوقع' as transaction_type
            FROM visa_expected_transactions
            WHERE visa_card_id = ? AND status = 'نشط'
            ORDER BY payment_date DESC
        ");
        $stmt->execute([$cardId]);
        $expectedTransactions = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // جلب المعاملات المصروفة
        $stmt = $db->prepare("
            SELECT spent_date as ad_date, account_name, 0 as ad_cost,
                   spent_amount as ad_spent, description, transaction_type
            FROM visa_spent_transactions
            WHERE visa_card_id = ? AND status = 'نشط'
            ORDER BY spent_date DESC
        ");
        $stmt->execute([$cardId]);
        $spentTransactions = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // دمج المعاملات
        $allTransactions = array_merge($expectedTransactions, $spentTransactions);

        if (!empty($allTransactions)) {
            $cardTransactions[$cardId] = $allTransactions;

            // حساب القيم الإجمالية
            $totalExpected = 0;
            $totalSpent = 0;
            $totalInternationalSpent = 0;

            foreach ($allTransactions as $transaction) {
                $totalExpected += floatval($transaction['ad_cost']);
                $totalSpent += floatval($transaction['ad_spent']);

                // حساب الصرف الدولي الشهري (من المعاملات التي نوعها صرف دولي)
                if ($transaction['transaction_type'] == 'صرف دولي' && $transaction['ad_spent'] > 0) {
                    // التحقق من أن المعاملة في الشهر الحالي
                    $transactionMonth = date('Y-m', strtotime($transaction['ad_date']));
                    $currentMonth = date('Y-m');
                    if ($transactionMonth == $currentMonth) {
                        $totalInternationalSpent += floatval($transaction['ad_spent']);
                    }
                }
            }

            // حفظ القيم المحسوبة في مصفوفة البطاقة
            foreach ($visaCards as &$cardRef) {
                if ($cardRef['id'] == $cardId) {
                    $cardRef['calculated_remaining_spend'] = $totalExpected;
                    $cardRef['calculated_total_spent'] = $totalSpent;
                    $cardRef['calculated_international_spent'] = $totalInternationalSpent;
                    $cardRef['calculated_international_remaining'] = $cardRef['daily_limit'] - $totalInternationalSpent;
                    $cardRef['calculated_debt'] = $totalSpent;
                    $cardRef['calculated_due'] = $cardRef['base_balance'] - $totalSpent;
                    break;
                }
            }
        }
    }

} catch (PDOException $e) {
    // في حالة حدوث خطأ، نستخدم مصفوفة فارغة
    $linkedAccounts = [];
    $cardTransactions = [];
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فيز البنوك</title>

    <!-- Bootstrap RTL CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Google Fonts - Cairo -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap">

    <style>
    body {
        font-family: 'Cairo', sans-serif;
        background-color: #f9fafb;
        margin: 0;
        padding: 0;
        color: #111827;
    }

    .header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 30px;
        padding: 0 20px;
    }

    .logo {
        width: 60px;
        height: 60px;
    }

    .page-title {
        font-size: 28px;
        font-weight: 600;
        color: #4a56e2;
        text-align: center;
        flex-grow: 1;
    }

    .search-container {
        position: relative;
        width: 300px;
    }

    .search-input {
        width: 100%;
        padding: 12px 45px 12px 15px;
        border: 2px solid #e0e0e0;
        border-radius: 25px;
        font-size: 14px;
        background-color: white;
    }

    .search-icon {
        position: absolute;
        right: 15px;
        top: 50%;
        transform: translateY(-50%);
        color: #999;
    }

    .cards-container {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 20px;
        max-width: 1400px;
        margin: 0 auto;
    }

    .visa-card {
        background: linear-gradient(135deg, #5a67d8 0%, #667eea 100%);
        border-radius: 15px;
        padding: 20px;
        color: white;
        border: 2px solid #4c51bf;
    }

    .card-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 20px;
        background: rgba(255, 255, 255, 0.1);
        padding: 10px 15px;
        border-radius: 8px;
    }

    .card-title {
        display: contents;
        align-items: center;
        gap: 10px;
        font-size: 16px;
        font-weight: 600;
    }

    .add-icon {
        background: rgb(220 242 78);
        color: #5a67d8;
        border-radius: 50%;
        width: 25px;
        height: 25px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        font-size: 12px;
    }


    .menu-icon {
        cursor: pointer;
        padding: 5px;
        font-size: 16px;
    }

    .section {
        margin-bottom: 15px;
    }

    .section-title {
        background: rgba(255, 255, 255, 0.15);
        padding: 8px 12px;
        border-radius: 6px;
        font-size: 14px;
        font-weight: 600;
        text-align: center;
        margin-bottom: 10px;
        color: #e2e8f0;
    }

    .table-container {
        background: rgba(255, 255, 255, 0.05);
        border-radius: 6px;
        overflow: hidden;
        max-height: 200px;
        position: relative;
    }

    .data-table {
        width: 100%;
        border-collapse: collapse;
    }

    .data-table thead {
        position: sticky;
        top: 0;
        z-index: 10;
    }

    .data-table th {
        background: rgba(255, 255, 255, 0.15);
        padding: 8px 6px;
        text-align: center;
        font-size: 12px;
        font-weight: 600;
        border: 1px solid rgba(255, 255, 255, 0.2);
        position: sticky;
        top: 0;
    }

    .table-body {
        max-height: 150px;
        overflow-y: auto;
        display: block;
    }

    .data-table tbody {
        display: block;
    }

    .data-table tr {
        display: table;
        width: 100%;
        table-layout: fixed;
    }

    .data-table td {
        padding: 6px;
        text-align: center;
        font-size: 12px;
        border: 1px solid rgba(255, 255, 255, 0.2);
        background: rgba(255, 255, 255, 0.03);
    }

    /* تخصيص شريط التمرير */
    .table-body::-webkit-scrollbar {
        width: 6px;
    }

    .table-body::-webkit-scrollbar-track {
        background: rgba(255, 255, 255, 0.1);
        border-radius: 3px;
    }

    .table-body::-webkit-scrollbar-thumb {
        background: rgba(255, 255, 255, 0.3);
        border-radius: 3px;
    }

    .table-body::-webkit-scrollbar-thumb:hover {
        background: rgba(255, 255, 255, 0.5);
    }

    .total-row {
        background: rgba(255, 255, 255, 0.1);
        padding: 8px;
        text-align: center;
        font-weight: 600;
        font-size: 13px;
        border-radius: 4px;
        margin-top: 8px;
    }

    .summary-grid {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 8px;
        margin-top: 15px;
    }

    .summary-item {
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 6px;
        padding: 8px;
        text-align: center;
    }

    .summary-label {
        font-size: 11px;
        margin-bottom: 4px;
        color: #e2e8f0;
    }

    .summary-value {
        font-size: 14px;
        font-weight: 600;
    }

    .bottom-grid {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 8px;
        margin-top: 8px;
    }

    @media (max-width: 768px) {
        .cards-container {
            grid-template-columns: 1fr;
        }

        /* تجاوب الهيدر مع الشاشات الصغيرة */
        .header-container {
            flex-direction: column !important;
            gap: 12px !important;
            padding: 16px !important;
        }

        .header-title {
            order: -1;
            font-size: 20px !important;
        }

        .header-search {
            width: 100% !important;
            max-width: none !important;
        }

        .header-search input {
            width: 100% !important;
        }

        .header-search-label {
            display: none !important;
        }
    }
    </style>
</head>

<body>
    <!-- Header Section - مطابق للتصميم الأصلي -->
    <div style="background: white; border-bottom: 1px solid #e5e7eb; padding: 12px 0;">
        <div style="max-width: 1400px; margin: 0 auto; display: flex; align-items: center; justify-content: space-between; padding: 0 24px;">

            <!-- Logo Section -->
            <div style="display: flex; align-items: center; gap: 12px;">
                <img src="assets/images/logo.png" alt="Bassam Media Logo" style="width: 48px; height: 48px; object-fit: contain;">

            </div>

            <!-- Title -->
            <h1 style="color: #6366f1; font-size: 24px; font-weight: 600; margin: 0; text-align: center; flex-grow: 1; letter-spacing: -0.025em;">فيز البنوك</h1>

            <!-- Search Section -->

        </div>
    </div>
    <br>
            <div style="display: flex; align-items: center; gap: 8px; display: contents;">
                <span style="color: #6b7280; font-size: 14px; font-weight: 500; float: left; position: relative; left: 4%; cursor: pointer; transition: color 0.2s;" onclick="window.location.href='visa_purchases.php'" onmouseover="this.style.color='#6366f1'" onmouseout="this.style.color='#6b7280'">فيز البنوك</span>
                <div style="position: relative;">
                    <input type="text" placeholder="البحث" style="
                        width: 80%;
                        height: 36px;
                        padding: 8px 36px 8px 12px;
                        border: 1px solid #d1d5db;
                        border-radius: 6px;
                        font-size: 14px;
                        background: white;
                        color: #374151;
                        outline: none;
                        transition: border-color 0.2s;
                        position: relative; right: 4%;
                    " onfocus="this.style.borderColor='#6366f1'" onblur="this.style.borderColor='#d1d5db'">
                    <div style="
                        position: absolute;
                        right: 8px;
                        top: 50%;
                        transform: translateY(-50%);
                        width: 24px;
                        height: 24px;
                        background: #f3f4f6;
                        border-radius: 4px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        cursor: pointer;
                       right: 4.5%;
                    ">
                        <i class="fas fa-search" style="color: #6b7280; font-size: 12px; "></i>
                    </div>
                </div>
            </div>
            <br>
    <div style="padding: 0 20px;">
        <div class="cards-container">
            <?php foreach ($visaCards as $cardIndex => $card): ?>
        <?php
        $cardId = $card['id'];
        $transactionKey = $cardId;
        ?>
        <!-- Card <?php echo $cardIndex + 1; ?> -->
        <div class="visa-card">
            <div class="card-header">
                <div class="card-title">
                    <div class="add-icon" onclick="showAddTransactionModal(<?php echo $cardId; ?>)">
                        <i class="fas fa-plus"></i>
                    </div>
                    <?php echo htmlspecialchars($card['name']); ?>
                </div>
                <div class="menu-icon">
                    <i class="fas fa-ellipsis-h"></i>
                </div>
            </div>

            <div class="section">
                <div class="section-title">الصرفالمتوقع</div>
                <div class="table-container">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>تاريخ دفع</th>
                                <th>اسم اكونت</th>
                                <th>مبلغ متوقع</th>
                                <th>تاريخ دفع</th>
                                <th>اسم اكونت</th>
                                <th>مبلغ متوقع</th>
                            </tr>
                        </thead>
                    </table>
                    <div class="table-body">
                        <table class="data-table">
                            <tbody>
                                <?php
                                // جلب المعاملات المتوقعة من الجدول المخصص
                                $expectedTransactions = [];
                                if (isset($cardTransactions[$transactionKey]) && !empty($cardTransactions[$transactionKey])) {
                                    foreach ($cardTransactions[$transactionKey] as $transaction) {
                                        // إضافة المعاملات المتوقعة فقط (التي لها ad_cost > 0 ونوعها متوقع)
                                        if ($transaction['ad_cost'] > 0 && $transaction['transaction_type'] == 'متوقع') {
                                            $expectedTransactions[] = $transaction;
                                        }
                                    }
                                }

                                if (!empty($expectedTransactions)):
                                    $transactionIndex = 0;
                                    foreach ($expectedTransactions as $transaction):
                                        // عرض كل معاملة في صف منفصل مع تكرار البيانات في 6 أعمدة
                                ?>
                                <tr>
                                    <td><?php echo date('j-n', strtotime($transaction['ad_date'])); ?></td>
                                    <td><?php echo htmlspecialchars($transaction['account_name']); ?></td>
                                    <td><?php echo number_format($transaction['ad_cost'], 0); ?></td>
                                    <td><?php echo date('j-n', strtotime($transaction['ad_date'])); ?></td>
                                    <td><?php echo htmlspecialchars($transaction['account_name']); ?></td>
                                    <td><?php echo number_format($transaction['ad_cost'], 0); ?></td>
                                </tr>
                                <?php
                                    endforeach;
                                endif;

                                // إذا لم توجد معاملات، عرض رسالة
                                if (empty($expectedTransactions)):
                                ?>
                                <tr>
                                    <td colspan="6" style="text-align: center; color: #999; padding: 20px;">
                                        لا توجد معاملات متوقعة
                                    </td>
                                </tr>
                                <?php endif; ?>

                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="total-row">
                    اجمالي الصرفالمتوقع:
                    <?php echo number_format(isset($card['calculated_remaining_spend']) ? $card['calculated_remaining_spend'] : 0, 0); ?>
                </div>
            </div>

            <div class="section">
                <div class="section-title">
                    المبلغ المصروف من الفيزا
                    <div class="add-icon" style="position: relative; right: 4%; display: inline-grid;" onclick="showAddSpentModal(<?php echo $cardId; ?>)">
                        <i class="fas fa-plus"></i>
                    </div>
                </div>
                <div class="table-container">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>تاريخ تسديد</th>
                                <th>اسم اكونت</th>
                                <th>مبلغ فعلي</th>
                                <th>تاريخ تسديد</th>
                                <th>اسم اكونت</th>
                                <th>مبلغ فعلي</th>
                            </tr>
                        </thead>
                    </table>
                    <div class="table-body">
                        <table class="data-table">
                            <tbody>
                                <?php
                                // جلب المعاملات المصروفة من الجدول المخصص
                                $spentTransactions = [];
                                if (isset($cardTransactions[$transactionKey]) && !empty($cardTransactions[$transactionKey])) {
                                    foreach ($cardTransactions[$transactionKey] as $transaction) {
                                        // إضافة المعاملات المصروفة فقط (التي لها ad_spent > 0 وليست من النوع متوقع)
                                        if ($transaction['ad_spent'] > 0 && $transaction['transaction_type'] != 'متوقع') {
                                            $spentTransactions[] = $transaction;
                                        }
                                    }
                                }

                                if (!empty($spentTransactions)):
                                    foreach ($spentTransactions as $transaction):
                                ?>
                                <tr>
                                    <td><?php echo date('j-n', strtotime($transaction['ad_date'])); ?></td>
                                    <td><?php echo htmlspecialchars($transaction['account_name']); ?></td>
                                    <td><?php echo number_format($transaction['ad_spent'], 0); ?></td>
                                    <td><?php echo date('j-n', strtotime($transaction['ad_date'])); ?></td>
                                    <td><?php echo htmlspecialchars($transaction['account_name']); ?></td>
                                    <td><?php echo number_format($transaction['ad_spent'], 0); ?></td>
                                </tr>
                                <?php
                                    endforeach;
                                endif;

                                // إذا لم توجد معاملات مصروفة، عرض رسالة
                                if (empty($spentTransactions)):
                                ?>
                                <tr>
                                    <td colspan="6" style="text-align: center; color: #999; padding: 20px;">
                                        لا توجد مبالغ مصروفة
                                    </td>
                                </tr>
                                <?php endif; ?>

                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <div class="summary-grid">
                <div class="summary-item">
                    <div class="summary-label">مطلوب تسديد</div>
                    <div class="summary-value">
                        <?php echo number_format(isset($card['calculated_debt']) ? $card['calculated_debt'] : 1000, 0); ?>
                    </div>
                </div>
                <div class="summary-item">
                    <div class="summary-label">رصيد حالي</div>
                    <div class="summary-value"><?php echo number_format($card['remaining_balance'], 0); ?></div>
                </div>
                <div class="summary-item">
                    <div class="summary-label">رصيد فيزا اساسي</div>
                    <div class="summary-value"><?php echo number_format($card['base_balance'], 0); ?></div>
                </div>
            </div>

            <div class="bottom-grid">
                <div class="summary-item">
                    <div class="summary-label">باقي دفع</div>
                    <div class="summary-value">
                        <?php echo number_format(isset($card['calculated_due']) ? $card['calculated_due'] : 1000, 0); ?>
                    </div>
                </div>
                <div class="summary-item">
                    <div class="summary-label">تم صرف</div>
                    <div class="summary-value">
                        <?php echo number_format(isset($card['calculated_total_spent']) ? $card['calculated_total_spent'] : 1000, 0); ?>
                    </div>
                </div>
                <div class="summary-item" onclick="window.open('international_spending_details.php', '_blank')" style="cursor: pointer; transition: transform 0.2s;" onmouseover="this.style.transform='scale(1.05)'" onmouseout="this.style.transform='scale(1)'">
                    <div class="summary-label">
                        دولي متبقي (شهري)
                        <i class="fas fa-external-link-alt" style="font-size: 10px; opacity: 0.7; margin-right: 5px;"></i>
                    </div>
                    <div class="summary-value" style="<?php
                        $internationalRemaining = isset($card['calculated_international_remaining']) ? $card['calculated_international_remaining'] : $card['daily_limit'];
                        $percentage = ($card['daily_limit'] > 0) ? (($card['daily_limit'] - $internationalRemaining) / $card['daily_limit']) * 100 : 0;

                        if ($percentage >= 90) {
                            echo 'color: #f44336; font-weight: bold;'; // أحمر للحرج
                        } elseif ($percentage >= 75) {
                            echo 'color: #ff9800; font-weight: bold;'; // برتقالي للخطر
                        } elseif ($percentage >= 50) {
                            echo 'color: #9c27b0; font-weight: bold;'; // بنفسجي للتحذير
                        } else {
                            echo 'color: #4caf50;'; // أخضر للآمن
                        }
                    ?>">
                        <?php echo number_format($internationalRemaining, 0); ?>
                        <?php if ($card['daily_limit'] > 0): ?>
                            <small style="display: block; font-size: 10px; opacity: 0.8;">
                                (<?php echo round($percentage, 1); ?>% مستخدم)
                            </small>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- تنبيه الحد الدولي إذا كان قريب من النفاد -->
            <?php
            $internationalRemaining = isset($card['calculated_international_remaining']) ? $card['calculated_international_remaining'] : $card['daily_limit'];
            $percentage = ($card['daily_limit'] > 0) ? (($card['daily_limit'] - $internationalRemaining) / $card['daily_limit']) * 100 : 0;

            if ($percentage >= 75): ?>
                <div style="background: <?php
                    if ($percentage >= 90) echo '#ffebee';
                    elseif ($percentage >= 75) echo '#fff3e0';
                ?>; border: 1px solid <?php
                    if ($percentage >= 90) echo '#f44336';
                    elseif ($percentage >= 75) echo '#ff9800';
                ?>; border-radius: 8px; padding: 10px; margin-top: 10px; text-align: center;">
                    <i class="fas fa-exclamation-triangle" style="color: <?php
                        if ($percentage >= 90) echo '#f44336';
                        elseif ($percentage >= 75) echo '#ff9800';
                    ?>; margin-left: 5px;"></i>
                    <strong style="color: <?php
                        if ($percentage >= 90) echo '#f44336';
                        elseif ($percentage >= 75) echo '#ff9800';
                    ?>;">
                        <?php if ($percentage >= 90): ?>
                            تحذير حرج: الحد الدولي على وشك النفاد!
                        <?php else: ?>
                            تحذير: اقتراب من حد الصرف الدولي
                        <?php endif; ?>
                    </strong>
                    <br>
                    <small>متبقي <?php echo number_format($internationalRemaining, 0); ?> من <?php echo number_format($card['daily_limit'], 0); ?></small>
                </div>
            <?php endif; ?>
        </div>
        <?php endforeach; ?>
        </div>
    </div>

    <!-- نافذة إضافة معاملة جديدة -->
    <div class="modal fade" id="addTransactionModal" tabindex="-1" aria-labelledby="addTransactionModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header" style="background: linear-gradient(135deg, #5a67d8 0%, #667eea 100%); color: white; border-bottom: none;">
                    <h5 class="modal-title" id="addTransactionModalLabel"> الصرف المتوقع</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body" style="background-color: #f8f9fa; padding: 0;">
                    <div style="background: white; margin: 20px; border-radius: 10px; padding: 20px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                        <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 15px; margin-bottom: 20px;">
                            <div>
                                <label style="display: block; margin-bottom: 5px; font-weight: 600; color: #4a56e2;">تاريخ دفع</label>
                                <input type="date" class="form-control" id="paymentDate" style="border: 2px solid #e0e0e0; border-radius: 8px; padding: 10px;">
                            </div>
                            <div style="position: relative;">
                                <label style="display: block; margin-bottom: 5px; font-weight: 600; color: #4a56e2;">اسم الاكونت</label>
                                <input type="text" class="form-control" id="accountName" placeholder="ابحث عن اسم الاكونت..." autocomplete="off" style="border: 2px solid #e0e0e0; border-radius: 8px; padding: 10px;">
                                <div id="accountDropdown" style="position: absolute; top: 100%; left: 0; right: 0; background: white; border: 2px solid #e0e0e0; border-top: none; border-radius: 0 0 8px 8px; max-height: 200px; overflow-y: auto; z-index: 1000; display: none;">
                                    <!-- سيتم ملء القائمة هنا بواسطة JavaScript -->
                                </div>
                            </div>
                            <div>
                                <label style="display: block; margin-bottom: 5px; font-weight: 600; color: #4a56e2;">مبلغ متوقع</label>
                                <input type="number" class="form-control" id="expectedAmount" placeholder="مبلغ متوقع" style="border: 2px solid #e0e0e0; border-radius: 8px; padding: 10px;">
                            </div>
                        </div>

                        <hr style="border: 1px solid #e0e0e0; margin: 20px 0;">

                        <div style="text-align: center;">
                            <button type="button" class="btn" id="addTransactionBtn" style="background: linear-gradient(135deg, #5a67d8 0%, #667eea 100%); color: white; border: none; padding: 12px 30px; border-radius: 25px; font-weight: 600; font-size: 16px; box-shadow: 0 4px 15px rgba(90, 103, 216, 0.3);">
                                إضافة
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة المبلغ المصروف من الفيزا  -->
    <div class="modal fade" id="addSpentModal" tabindex="-1" aria-labelledby="addSpentModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header" style="background: linear-gradient(135deg, #5a67d8 0%, #667eea 100%); color: white; border-bottom: none;">
                    <h5 class="modal-title" id="addSpentModalLabel">المبلغ المصروف من الفيزا </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body" style="background-color: #f8f9fa; padding: 0;">
                    <div style="background: white; margin: 20px; border-radius: 10px; padding: 20px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                        <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 15px; margin-bottom: 20px;">
                            <div>
                                <label style="display: block; margin-bottom: 5px; font-weight: 600; color: #4a56e2;">تاريخ دفع</label>
                                <input type="date" class="form-control" id="spentDate" style="border: 2px solid #e0e0e0; border-radius: 8px; padding: 10px;">
                            </div>
                            <div style="position: relative;">
                                <label style="display: block; margin-bottom: 5px; font-weight: 600; color: #4a56e2;">اسم الاكونت</label>
                                <input type="text" class="form-control" id="spentAccountName" placeholder="ابحث عن اسم الاكونت..." autocomplete="off" style="border: 2px solid #e0e0e0; border-radius: 8px; padding: 10px;">
                                <div id="spentAccountDropdown" style="position: absolute; top: 100%; left: 0; right: 0; background: white; border: 2px solid #e0e0e0; border-top: none; border-radius: 0 0 8px 8px; max-height: 200px; overflow-y: auto; z-index: 1000; display: none;">
                                    <!-- سيتم ملء القائمة هنا بواسطة JavaScript -->
                                </div>
                            </div>
                            <div>
                                <label style="display: block; margin-bottom: 5px; font-weight: 600; color: #4a56e2;">مبلغ مصروف</label>
                                <input type="number" class="form-control" id="spentAmount" placeholder="مبلغ مصروف" style="border: 2px solid #e0e0e0; border-radius: 8px; padding: 10px;">
                            </div>
                        </div>

                        <!-- تنبيه الصرف الدولي -->
                        <div style="background: #e3f2fd; border: 1px solid #2196f3; border-radius: 8px; padding: 15px; margin-bottom: 15px;">
                            <div style="display: flex; align-items: center; color: #1976d2;">
                                <i class="fas fa-globe" style="margin-left: 10px; font-size: 18px;"></i>
                                <div>
                                    <strong>ملاحظة: صرف دولي</strong>
                                    <p style="margin: 5px 0 0 0; font-size: 14px;">جميع العمليات على بطاقات الفيزا هي عمليات دولية وسيتم خصمها من الحد الشهري.</p>
                                </div>
                            </div>
                        </div>

                        <hr style="border: 1px solid #e0e0e0; margin: 20px 0;">

                        <div style="text-align: center;">
                            <button type="button" class="btn" id="addSpentBtn" style="background: linear-gradient(135deg, #5a67d8 0%, #667eea 100%); color: white; border: none; padding: 12px 30px; border-radius: 25px; font-weight: 600; font-size: 16px; box-shadow: 0 4px 15px rgba(90, 103, 216, 0.3);">
                                إضافة
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // متغير لحفظ معرف البطاقة المحددة
        let selectedCardId = null;
        let allAccounts = []; // مصفوفة لحفظ جميع الحسابات

        // دالة لإظهار نافذة إضافة المعاملة
        function showAddTransactionModal(cardId) {
            selectedCardId = cardId;
            const modal = new bootstrap.Modal(document.getElementById('addTransactionModal'));
            modal.show();

            // جلب الحسابات عند فتح النافذة
            loadAccounts();
        }

        // دالة لإظهار نافذة إضافة المبلغ المصروف
        function showAddSpentModal(cardId) {
            selectedCardId = cardId;
            const modal = new bootstrap.Modal(document.getElementById('addSpentModal'));
            modal.show();

            // جلب الحسابات عند فتح النافذة
            loadAccounts();
        }

        // دالة لجلب الحسابات من قاعدة البيانات
        function loadAccounts() {
            fetch('api/ad_accounts/get_all.php')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    allAccounts = data.accounts;
                } else {
                    console.error('فشل في جلب الحسابات:', data.message);
                }
            })
            .catch(error => {
                console.error('خطأ في جلب الحسابات:', error);
            });
        }

        // دالة للبحث في الحسابات
        function searchAccounts(searchTerm) {
            if (!searchTerm || searchTerm.length < 1) {
                hideAccountDropdown();
                return;
            }

            const filteredAccounts = allAccounts.filter(account =>
                account.name.toLowerCase().includes(searchTerm.toLowerCase())
            );

            showAccountDropdown(filteredAccounts);
        }

        // دالة لإظهار قائمة الحسابات
        function showAccountDropdown(accounts) {
            const dropdown = document.getElementById('accountDropdown');
            dropdown.innerHTML = '';

            if (accounts.length === 0) {
                dropdown.innerHTML = '<div style="padding: 10px; color: #999; text-align: center;">لا توجد حسابات مطابقة</div>';
            } else {
                accounts.forEach(account => {
                    const item = document.createElement('div');
                    item.style.cssText = 'padding: 10px; cursor: pointer; border-bottom: 1px solid #f0f0f0; transition: background-color 0.2s;';
                    item.innerHTML = `
                        <div style="font-weight: 600; color: #333;">${account.name}</div>
                        <div style="font-size: 12px; color: #666;">الحالة: ${account.status}</div>
                    `;

                    // تأثير hover
                    item.addEventListener('mouseenter', function() {
                        this.style.backgroundColor = '#f8f9fa';
                    });

                    item.addEventListener('mouseleave', function() {
                        this.style.backgroundColor = 'white';
                    });

                    // عند النقر على الحساب
                    item.addEventListener('click', function() {
                        document.getElementById('accountName').value = account.name;
                        hideAccountDropdown();
                    });

                    dropdown.appendChild(item);
                });
            }

            dropdown.style.display = 'block';
        }

        // دالة لإخفاء قائمة الحسابات
        function hideAccountDropdown() {
            document.getElementById('accountDropdown').style.display = 'none';
        }

        // دالة للبحث في الحسابات للبوب أب الثاني
        function searchSpentAccounts(searchTerm) {
            if (!searchTerm || searchTerm.length < 1) {
                hideSpentAccountDropdown();
                return;
            }

            const filteredAccounts = allAccounts.filter(account =>
                account.name.toLowerCase().includes(searchTerm.toLowerCase())
            );

            showSpentAccountDropdown(filteredAccounts);
        }

        // دالة لإظهار قائمة الحسابات للبوب أب الثاني
        function showSpentAccountDropdown(accounts) {
            const dropdown = document.getElementById('spentAccountDropdown');
            dropdown.innerHTML = '';

            if (accounts.length === 0) {
                dropdown.innerHTML = '<div style="padding: 10px; color: #999; text-align: center;">لا توجد حسابات مطابقة</div>';
            } else {
                accounts.forEach(account => {
                    const item = document.createElement('div');
                    item.style.cssText = 'padding: 10px; cursor: pointer; border-bottom: 1px solid #f0f0f0; transition: background-color 0.2s;';
                    item.innerHTML = `
                        <div style="font-weight: 600; color: #333;">${account.name}</div>
                        <div style="font-size: 12px; color: #666;">الحالة: ${account.status}</div>
                    `;

                    // تأثير hover
                    item.addEventListener('mouseenter', function() {
                        this.style.backgroundColor = '#f8f9fa';
                    });

                    item.addEventListener('mouseleave', function() {
                        this.style.backgroundColor = 'white';
                    });

                    // عند النقر على الحساب
                    item.addEventListener('click', function() {
                        document.getElementById('spentAccountName').value = account.name;
                        hideSpentAccountDropdown();
                    });

                    dropdown.appendChild(item);
                });
            }

            dropdown.style.display = 'block';
        }

        // دالة لإخفاء قائمة الحسابات للبوب أب الثاني
        function hideSpentAccountDropdown() {
            document.getElementById('spentAccountDropdown').style.display = 'none';
        }

        // إضافة مستمع للبحث في حقل اسم الحساب
        document.addEventListener('DOMContentLoaded', function() {
            const accountNameInput = document.getElementById('accountName');
            const spentAccountNameInput = document.getElementById('spentAccountName');

            // البحث أثناء الكتابة - البوب أب الأول
            accountNameInput.addEventListener('input', function() {
                searchAccounts(this.value);
            });

            // إظهار جميع الحسابات عند التركيز - البوب أب الأول
            accountNameInput.addEventListener('focus', function() {
                if (allAccounts.length > 0) {
                    showAccountDropdown(allAccounts);
                }
            });

            // البحث أثناء الكتابة - البوب أب الثاني
            spentAccountNameInput.addEventListener('input', function() {
                searchSpentAccounts(this.value);
            });

            // إظهار جميع الحسابات عند التركيز - البوب أب الثاني
            spentAccountNameInput.addEventListener('focus', function() {
                if (allAccounts.length > 0) {
                    showSpentAccountDropdown(allAccounts);
                }
            });

            // إخفاء القائمة عند النقر خارجها
            document.addEventListener('click', function(event) {
                const dropdown = document.getElementById('accountDropdown');
                const input = document.getElementById('accountName');
                const spentDropdown = document.getElementById('spentAccountDropdown');
                const spentInput = document.getElementById('spentAccountName');

                if (!dropdown.contains(event.target) && event.target !== input) {
                    hideAccountDropdown();
                }

                if (!spentDropdown.contains(event.target) && event.target !== spentInput) {
                    hideSpentAccountDropdown();
                }
            });
        });

        // دالة لإضافة المعاملة
        document.getElementById('addTransactionBtn').addEventListener('click', function() {
            const paymentDate = document.getElementById('paymentDate').value;
            const accountName = document.getElementById('accountName').value;
            const expectedAmount = document.getElementById('expectedAmount').value;

            // التحقق من البيانات
            if (!paymentDate || !accountName || !expectedAmount) {
                alert('يرجى ملء جميع الحقول');
                return;
            }

            // إرسال البيانات إلى الخادم
            const formData = {
                card_id: selectedCardId,
                payment_date: paymentDate,
                account_name: accountName,
                expected_amount: expectedAmount,
                card_type: 'visa'
            };

            fetch('api/visa/add_expected_transaction.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(formData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('تم إضافة المعاملة بنجاح!');
                    const modal = bootstrap.Modal.getInstance(document.getElementById('addTransactionModal'));
                    modal.hide();

                    // إعادة تحميل الصفحة لعرض التغييرات
                    window.location.reload();
                } else {
                    alert('حدث خطأ أثناء إضافة المعاملة: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('حدث خطأ أثناء إضافة المعاملة');
            });
        });

        // دالة لإضافة المبلغ المصروف
        document.getElementById('addSpentBtn').addEventListener('click', function() {
            const spentDate = document.getElementById('spentDate').value;
            const spentAccountName = document.getElementById('spentAccountName').value;
            const spentAmount = document.getElementById('spentAmount').value;

            // التحقق من البيانات
            if (!spentDate || !spentAccountName || !spentAmount) {
                alert('يرجى ملء جميع الحقول');
                return;
            }

            // تأكيد للصرف الدولي
            if (!confirm('سيتم إضافة هذا المبلغ كصرف دولي وخصمه من الحد الشهري. هل تريد المتابعة؟')) {
                return;
            }

            // إرسال البيانات إلى الخادم (كلها دولية)
            const formData = {
                card_id: selectedCardId,
                spent_date: spentDate,
                account_name: spentAccountName,
                spent_amount: spentAmount,
                transaction_type: 'صرف دولي',
                description: 'صرف دولي من بطاقة الفيزا',
                is_international: true,
                card_type: 'visa'
            };

            fetch('api/visa/add_spent_transaction.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(formData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    let message = 'تم إضافة المبلغ المصروف بنجاح!';

                    // إضافة تنبيه الحد الدولي إذا وجد
                    if (data.international_alert) {
                        message += '\n\n⚠️ تنبيه الحد الدولي:\n' + data.international_alert;

                        // فتح صفحة التنبيهات في تبويب جديد إذا كان التنبيه حرج
                        if (data.international_alert.includes('حرج')) {
                            setTimeout(() => {
                                window.open('check_international_alerts.php', '_blank');
                            }, 2000);
                        }
                    }

                    alert(message);
                    const modal = bootstrap.Modal.getInstance(document.getElementById('addSpentModal'));
                    modal.hide();

                    // إعادة تحميل الصفحة لعرض التغييرات
                    window.location.reload();
                } else {
                    alert('حدث خطأ أثناء إضافة المبلغ المصروف: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('حدث خطأ أثناء إضافة المبلغ المصروف');
            });
        });

        // إعادة تعيين النموذج عند إغلاق النافذة
        document.getElementById('addTransactionModal').addEventListener('hidden.bs.modal', function () {
            document.getElementById('paymentDate').value = '';
            document.getElementById('accountName').value = '';
            document.getElementById('expectedAmount').value = '';
            selectedCardId = null;
        });

        // إعادة تعيين النموذج الثاني عند إغلاق النافذة
        document.getElementById('addSpentModal').addEventListener('hidden.bs.modal', function () {
            document.getElementById('spentDate').value = '';
            document.getElementById('spentAccountName').value = '';
            document.getElementById('spentAmount').value = '';
            selectedCardId = null;
        });
    </script>
</body>

</html>