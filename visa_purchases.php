s<?php
// الاتصال بقاعدة البيانات
include 'config/database.php';

// إنشاء اتصال قاعدة البيانات
$database = new Database();
$pdo = $database->getConnection();

// جلب بيانات المشتريات من قاعدة البيانات - نفس منطق visa_banks_dynamic.php
try {
    $visaCards = [];

    // جلب بطاقات الفيزا
    try {
        $stmt = $pdo->prepare("
            SELECT *,
                   'visa' as card_type,
                   0 as days_until_payment,
                   base_balance as balance
            FROM visa_cards
            ORDER BY name
        ");
        $stmt->execute();
        $visaCards = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // إذا لم توجد بطاقات فيزا، أضف بطاقة تجريبية
        if (empty($visaCards)) {
            // إنشاء بطاقة فيزا تجريبية
            try {
                $stmt = $pdo->prepare("
                    INSERT INTO visa_cards (name, base_balance, daily_limit, remaining_balance, total_debt, status)
                    VALUES ('فيزا الأهلي الذهبية', 5000.00, 100.00, 5000.00, 0.00, 'نشط')
                ");
                $stmt->execute();

                // جلب البطاقة المضافة
                $stmt = $pdo->prepare("
                    SELECT *,
                           'visa' as card_type,
                           0 as days_until_payment,
                           base_balance as balance
                    FROM visa_cards
                    ORDER BY name
                ");
                $stmt->execute();
                $visaCards = $stmt->fetchAll(PDO::FETCH_ASSOC);
            } catch (PDOException $e) {
                // في حالة فشل الإضافة، استخدم بيانات تجريبية
                $visaCards = [
                    [
                        'id' => 999,
                        'name' => 'فيزا الأهلي الذهبية',
                        'base_balance' => 5000.00,
                        'daily_limit' => 100.00,
                        'remaining_balance' => 5000.00,
                        'total_debt' => 0.00,
                        'status' => 'نشط',
                        'card_type' => 'visa',
                        'days_until_payment' => 0,
                        'balance' => 5000.00
                    ]
                ];
            }
        }

    } catch (PDOException $e) {
        echo "جدول visa_cards غير موجود: " . $e->getMessage();
    }

    // جلب المعاملات من الجداول المخصصة
    $cardTransactions = [];

    // جلب المعاملات لكل بطاقة فيزا من الجداول المخصصة
    foreach ($visaCards as $card) {
        $cardId = $card['id'];

        // جلب المعاملات المتوقعة
        $stmt = $pdo->prepare("
            SELECT payment_date as ad_date, account_name, expected_amount as ad_cost,
                   0 as ad_spent, description, 'متوقع' as transaction_type
            FROM visa_expected_transactions
            WHERE visa_card_id = ? AND status = 'نشط'
            ORDER BY payment_date DESC
        ");
        $stmt->execute([$cardId]);
        $expectedTransactions = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // جلب المعاملات المصروفة
        $stmt = $pdo->prepare("
            SELECT spent_date as ad_date, account_name, 0 as ad_cost,
                   spent_amount as ad_spent, description, transaction_type
            FROM visa_spent_transactions
            WHERE visa_card_id = ? AND status = 'نشط'
            ORDER BY spent_date DESC
        ");
        $stmt->execute([$cardId]);
        $spentTransactions = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // دمج المعاملات
        $allTransactions = array_merge($expectedTransactions, $spentTransactions);

        if (!empty($allTransactions)) {
            $cardTransactions[$cardId] = $allTransactions;

            // حساب القيم الإجمالية
            $totalExpected = 0;
            $totalSpent = 0;
            $totalInternationalSpent = 0;

            foreach ($allTransactions as $transaction) {
                $totalExpected += floatval($transaction['ad_cost']);
                $totalSpent += floatval($transaction['ad_spent']);

                // حساب الصرف الدولي الشهري (من المعاملات التي نوعها صرف دولي)
                if ($transaction['transaction_type'] == 'صرف دولي' && $transaction['ad_spent'] > 0) {
                    // التحقق من أن المعاملة في الشهر الحالي
                    $transactionMonth = date('Y-m', strtotime($transaction['ad_date']));
                    $currentMonth = date('Y-m');
                    if ($transactionMonth == $currentMonth) {
                        $totalInternationalSpent += floatval($transaction['ad_spent']);
                    }
                }
            }

            // حفظ القيم المحسوبة في مصفوفة البطاقة
            foreach ($visaCards as &$cardRef) {
                if ($cardRef['id'] == $cardId) {
                    $cardRef['calculated_remaining_spend'] = $totalExpected;
                    $cardRef['calculated_total_spent'] = $totalSpent;
                    $cardRef['calculated_international_spent'] = $totalInternationalSpent;
                    $cardRef['calculated_international_remaining'] = $cardRef['daily_limit'] - $totalInternationalSpent;
                    $cardRef['calculated_debt'] = $totalSpent;
                    $cardRef['calculated_due'] = $cardRef['base_balance'] - $totalSpent;
                    break;
                }
            }
        }
    }

    // تحويل البيانات إلى تنسيق الجدول
    $purchases = [];
    foreach ($visaCards as $card) {
        $purchases[] = [
            'اسم_الفيزا' => $card['name'],
            'كود' => (strpos($card['name'], 'CIB') !== false || strpos($card['name'], 'الأهلي') !== false) ? 'CIB' :
                     ((strpos($card['name'], 'NBK') !== false || strpos($card['name'], 'الكويت') !== false) ? 'NBK' : 'OTHER'),
            'دولي_داخل' => $card['daily_limit'],
            'اساسي' => $card['base_balance'],
            'رصيد' => $card['remaining_balance'],
            'مطلوب_شحن' => isset($card['calculated_remaining_spend']) ? $card['calculated_remaining_spend'] : 0,
            'صرف' => isset($card['calculated_total_spent']) ? $card['calculated_total_spent'] : 0,
            'متوقع_يصرف' => isset($card['calculated_remaining_spend']) ? $card['calculated_remaining_spend'] : 0,
            'باقي_يصرف' => isset($card['calculated_due']) ? $card['calculated_due'] : $card['remaining_balance']
        ];
    }

    // إذا لم توجد بيانات، استخدم بيانات تجريبية
    if (empty($purchases)) {
        $purchases = [
            [
                'اسم_الفيزا' => 'فيزا الأهلي الذهبية',
                'كود' => 'CIB',
                'دولي_داخل' => 100,
                'اساسي' => 5000,
                'رصيد' => 5000,
                'مطلوب_شحن' => 0,
                'صرف' => 0,
                'متوقع_يصرف' => 0,
                'باقي_يصرف' => 5000
            ]
        ];
    }

    // استخدام البيانات مرة واحدة فقط
    $allPurchases = $purchases;

} catch (PDOException $e) {
    $allPurchases = [];
    error_log("Database error: " . $e->getMessage());

    // بيانات احتياطية في حالة فشل الاتصال
    $fallbackData = [
        [
            'اسم_الفيزا' => 'فيزا الأهلي الذهبية',
            'كود' => 'CIB',
            'دولي_داخل' => 100,
            'اساسي' => 5000,
            'رصيد' => 5000,
            'مطلوب_شحن' => 0,
            'صرف' => 0,
            'متوقع_يصرف' => 0,
            'باقي_يصرف' => 5000
        ]
    ];

    // استخدام البيانات الاحتياطية مرة واحدة فقط
    $allPurchases = $fallbackData;
}

// حساب الإجماليات من البيانات الفعلية
$totals = [
    'اسم_الفيزا' => 'اجماليات',
    'كود' => '',
    'دولي_داخل' => 0,
    'اساسي' => 0,
    'رصيد' => 0,
    'مطلوب_شحن' => 0,
    'صرف' => 0,
    'متوقع_يصرف' => 0,
    'باقي_يصرف' => 0
];

// حساب الإجماليات الفعلية
foreach ($allPurchases as $purchase) {
    $totals['دولي_داخل'] += $purchase['دولي_داخل'];
    $totals['اساسي'] += $purchase['اساسي'];
    $totals['رصيد'] += $purchase['رصيد'];
    $totals['مطلوب_شحن'] += $purchase['مطلوب_شحن'];
    $totals['صرف'] += $purchase['صرف'];
    $totals['متوقع_يصرف'] += $purchase['متوقع_يصرف'];
    $totals['باقي_يصرف'] += $purchase['باقي_يصرف'];
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فيز المشتريات - Bassam Media</title>

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f8fafc;
            color: #1a202c;
            line-height: 1.6;
        }

        /* Header Styles - مطابق للتصميم الأصلي */
        .header {
            background: white;
            border-bottom: 1px solid #e5e7eb;
            padding: 12px 0;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
        }

        .header-container {
            max-width: 1400px;
            margin: 0 auto;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 24px;
        }

        .logo-section {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .logo-section img {
            width: 48px;
            height: 48px;
            object-fit: contain;
        }

        .logo-text {
            display: flex;
            flex-direction: column;
        }

        .logo-text span {
            color: #6b7280;
            font-size: 11px;
            font-weight: 500;
            line-height: 1;
        }

        .page-title {
            color: #6366f1;
            font-size: 24px;
            font-weight: 600;
            margin: 0;
            text-align: center;
            flex-grow: 1;
            letter-spacing: -0.025em;
        }

        .search-section {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .search-label {
            color: #6b7280;
            font-size: 14px;
            font-weight: 500;
            white-space: nowrap;
        }

        .search-container {
            position: relative;
        }

        .search-input {
            width: 200px;
            height: 36px;
            padding: 8px 36px 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 14px;
            background: white;
            color: #374151;
            outline: none;
            transition: border-color 0.2s;
        }

        .search-input:focus {
            border-color: #6366f1;
        }

        .search-icon {
            position: absolute;
            right: 8px;
            top: 50%;
            transform: translateY(-50%);
            width: 24px;
            height: 24px;
            background: #f3f4f6;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
        }

        .search-icon i {
            color: #6b7280;
            font-size: 12px;
        }

        /* Main Content */
        .main-content {
            margin-top: 80px;
            padding: 20px;
        }

        /* Table Container */
        .table-wrapper {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            margin: 20px auto;
            max-width: 1400px;
        }

        /* Fixed Header */
        .table-header {
            background: #6366f1;
            color: white;
            position: sticky;
            top: تمام بس طبعهم اكثر من مرة انا عايز يطبع الحاجة مرتها بس0px;
            z-index: 100;
        }

        .table-header table {
            width: 100%;
            border-collapse: collapse;
        }

        .table-header th {
            padding: 15px 12px;
            text-align: center;
            font-weight: 600;
            font-size: 14px;
            border-right: 1px solid rgba(255, 255, 255, 0.2);
        }

        .table-header th:last-child {
            border-right: none;
        }

        /* Scrollable Body */
        .table-body {
            max-height: 60vh;
            overflow-y: auto;
        }

        .table-body table {
            width: 100%;
            border-collapse: collapse;
        }

        .table-body td {
            padding: 12px;
            text-align: center;
            border-bottom: 1px solid #e5e7eb;
            border-right: 1px solid #e5e7eb;
            font-size: 14px;
        }

        .table-body td:last-child {
            border-right: none;
        }

        .table-body tr:hover {
            background-color: #f8fafc;
        }

        /* Fixed Footer */
        .table-footer {
            background: #f8fafc;
            border-top: 2px solid #6366f1;
            position: sticky;
            bottom: 0;
            z-index: 100;
        }

        .table-footer table {
            width: 100%;
            border-collapse: collapse;
        }

        .table-footer td {
            padding: 15px 12px;
            text-align: center;
            font-weight: 600;
            font-size: 14px;
            color: #059669;
            border-right: 1px solid #d1d5db;
        }

        .table-footer td:last-child {
            border-right: none;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .header-container {
                flex-direction: column;
                gap: 12px;
                padding: 12px;
            }

            .page-title {
                font-size: 20px;
            }

            .search-section {
                width: 100%;
                justify-content: center;
            }

            .search-label {
                display: none;
            }

            .search-input {
                width: 100%;
                max-width: 300px;
            }

            .main-content {
                margin-top: 120px;
                padding: 10px;
            }

            .table-header {
                top: 120px;
            }

            .table-header th,
            .table-body td,
            .table-footer td {
                padding: 8px 4px;
                font-size: 12px;
            }
        }
    </style>
</head>
<body>
    <!-- Header Section - مطابق للتصميم الأصلي -->
    <div class="header">
        <div class="header-container">
            <!-- Logo Section -->
            <div class="logo-section">
                <img src="assets/images/logo.png" alt="Bassam Media Logo">
                <div class="logo-text">
                    <span>BASSAM MEDIA</span>
                </div>
            </div>

            <!-- Title -->
            <h1 class="page-title">فيز المشتريات</h1>

            <!-- Search Section -->
            <div class="search-section">
                <span class="search-label">البحث</span>
                <div class="search-container">
                    <input type="text" class="search-input" placeholder="البحث">
                    <div class="search-icon">
                        <i class="fas fa-search"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <div class="table-wrapper">
            <!-- Fixed Header -->
            <div class="table-header">
                <table>
                    <thead>
                        <tr>
                            <th>اسم الفيزا</th>
                            <th>كود</th>
                            <th>دولي داخل</th>
                            <th>اساسي</th>
                            <th>رصيد</th>
                            <th>مطلوب شحن</th>
                            <th>صرف</th>
                            <th>متوقع يصرف</th>
                            <th>باقي يصرف</th>
                        </tr>
                    </thead>
                </table>
            </div>

            <!-- Scrollable Body -->
            <div class="table-body">
                <table>
                    <tbody>
                        <?php foreach ($allPurchases as $purchase): ?>
                        <tr>
                            <td style="width: 12%;"><?php echo htmlspecialchars($purchase['اسم_الفيزا']); ?></td>
                            <td style="width: 7.2%;"><?php echo htmlspecialchars($purchase['كود']); ?></td>
                            <td style="width: 12.7%;"><?php echo number_format($purchase['دولي_داخل']); ?></td>
                            <td style="width: 10%;"><?php echo number_format($purchase['اساسي']); ?></td>
                            <td style="width: 7.6%;"><?php echo number_format($purchase['رصيد']); ?></td>
                            <td style="width: 15%;"><?php echo number_format($purchase['مطلوب_شحن']); ?></td>
                            <td style="width: 7.8%;"><?php echo number_format($purchase['صرف']); ?></td>
                            <td><?php echo number_format($purchase['متوقع_يصرف']); ?></td>
                            <td><?php echo number_format($purchase['باقي_يصرف']); ?></td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>

            <!-- Fixed Footer -->
            <div class="table-footer">
                <table>
                    <tbody>
                        <tr>
                            <td style="width: 12%;"><?php echo $totals['اسم_الفيزا']; ?></td>
                            <td style="width: 7.2%;"><?php echo $totals['كود']; ?></td>
                            <td style="width: 12.7%;"><?php echo number_format($totals['دولي_داخل']); ?></td>
                            <td style="width: 10%;"><?php echo number_format($totals['اساسي']); ?></td>
                            <td style="width: 7.6%;"><?php echo number_format($totals['رصيد']); ?></td>
                            <td style="width: 15%;"><?php echo number_format($totals['مطلوب_شحن']); ?></td>
                            <td style="width: 7.8%;"><?php echo number_format($totals['صرف']); ?></td>
                            <td><?php echo number_format($totals['متوقع_يصرف']); ?></td>
                            <td><?php echo number_format($totals['باقي_يصرف']); ?></td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</body>
</html>
